{"error_config_not_found": "Error: Configuration file '{config_filename}' not found.", "error_config_create_prompt": "Please ensure you have created a config.json file, or copy one from the default template.", "error_config_missing_keys": "Error: The configuration file '{config_filename}' is missing the following required keys: {keys}", "loading_config": "  > Loading configuration from {config_filename}...", "error_config_invalid": "Error: Configuration file '{config_filename}' is invalid. Please check the JSON syntax.", "details": "Details: {e}", "error_ffprobe_duration": "  Error: ffprobe could not get the duration for file '{filename}'.", "error_ffprobe_not_found": "  Diagnosis: ffprobe program not found. Please ensure ffmpeg.exe and ffprobe.exe are placed in the script directory or system PATH.", "error_ffmpeg_general": "  FFmpeg Error: {error}", "error_get_duration_unknown": "  An unknown error occurred while getting duration for file '{filename}': {error}", "error_model_not_in_config": "Error: Model key '{model_key}' not found in the configured models dictionary.", "model_found_locally": "Model '{model_key}' found locally. Path: {model_path}", "model_downloading": "Model '{model_key}' not found locally. Starting download from Hugging Face...", "using_mirror": "  > Using mirror: {endpoint}", "no_mirror": "  > Not using mirror, downloading directly from the official Hugging Face Hub.", "model_download_success": "Model '{model_key}' downloaded successfully!", "error_model_download": "Error: A network or file write error occurred while downloading the model. {error}", "checking_cover_art": "  > Checking for embedded cover art...", "cover_art_found": "  > Cover art found and extracted to temporary file: {cover_path}", "no_cover_art_found": "  > No cover art found.", "error_extracting_cover_art": "  > Could not extract cover art: {error}", "checking_embedded_chapters": "  > Checking for embedded chapter metadata...", "found_embedded_chapters": "  > Found {count} embedded chapters. Skipping transcription.", "no_embedded_chapters": "  > No embedded chapters found. Proceeding with transcription workflow.", "parsing_srt": "  > Parsing SRT file: {filename}", "error_parsing_srt": "  > An error occurred while parsing the SRT file: {error}", "chapter_detected_with_title": "    > Chapter detected: Chapter {number} - '{title}' at {time:.2f}s", "chapter_detected_no_title": "    > Chapter detected: Chapter {number} at {time:.2f}s", "title_from_next_line": "    > Chapter {number} title is empty, getting from next line: '{title}'", "error_writing_srt": "  > An error occurred while writing the SRT file: {error}", "program_start": "--- Starting Chapter Splitter Program ---", "ffmpeg_path_info": "The script will automatically look for ffmpeg.exe and ffprobe.exe in the system PATH or the script's directory.", "ffmpeg_path_ensure": "Please ensure these files are placed correctly.", "processing_start": "\n--- Starting to process audio files ---", "scanning_folder": "Scanning '{input_dir}' folder...", "no_audio_files_found": "No supported audio files found in the 'input' folder. The script will now exit.", "processing_file": "\n--- Processing file: {filename} ---", "output_will_be_saved_to": "Output will be saved to: {output_sub_dir}", "error_getting_duration": "Could not get file duration, skipping file '{filename}'.", "found_chapter_cache": "  > Found chapter cache file: {json_path_name}. Loading...", "cache_format_mismatch": "  > Cache format does not match current configuration (extract_chapter_title={extract_title}). Reparsing.", "cache_load_success": "  > Successfully loaded {count} chapters.", "cache_load_fail": "  > Failed to load JSON file: {error}. Reparsing.", "srt_not_found": "  > SRT file not found, starting speech recognition...", "loading_model": "    > Preparing and loading Whisper model...", "model_prep_fail": "Model preparation failed, terminating script.", "model_load_success": "    > Model loaded successfully!", "error_loading_model": "Error: Could not load model. {error}", "transcribing": "  [Transcribing]... {progress:5.1f}%", "transcription_chunk_export": "    > Exporting chunk {current}/{total}...", "transcription_chunk_process": "    > Transcribing chunk {current}/{total}...", "transcription_memory_save": "  > Audio file is large, processing in chunks to conserve memory...", "transcription_complete": "Transcription complete.", "saving_srt": "  > Saving subtitles to SRT file: {srt_path_name}", "error_transcribing": "\nError: An error occurred while transcribing file '{filename}'. {error}", "found_existing_srt": "  > Found existing SRT file: {srt_path_name}", "parsing_chapters": "\nDetecting chapters...", "saving_chapters_to_cache": "  > Saving parsed {count} chapters to cache: {json_path_name}", "error_saving_json": "  > An error occurred while saving the JSON file: {error}", "no_chapters_found": "No chapter information found, skipping split.", "splitting_audio": "\nSplitting and exporting audio...", "loading_full_audio": "  > Loading full audio for splitting...", "first_chapter_from_start": "  > Setting first chapter to start from 0 to include intro.", "exporting_file": "  > Exporting {output_filename} (from {start_sec:.2f}s to {end_sec:.2f}s)...", "file_process_complete": "File '{filename}' processed successfully!", "error_splitting_file": "Error: An error occurred while splitting or exporting file '{filename}'. {error}", "archiving_files": "\nProcessing complete, archiving source files...", "moved_file": "  > Moved: {filename}", "error_archiving": "Error: An error occurred while archiving files. {error}", "all_files_processed": "\n--- All files processed ---"}