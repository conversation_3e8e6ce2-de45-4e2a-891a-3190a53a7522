{"error_config_not_found": "错误: 配置文件 '{config_filename}' 未找到。", "error_config_create_prompt": "请确保您已创建 config.json 文件，或从默认模板复制一份。", "error_config_missing_keys": "错误: 配置文件 '{config_filename}' 缺失以下必需的键: {keys}", "loading_config": "  > 正在从 {config_filename} 加载配置...", "error_config_invalid": "错误: 配置文件 '{config_filename}' 格式无效。请检查JSON语法。", "details": "详细信息: {e}", "error_ffprobe_duration": "  错误: ffprobe 无法获取文件 '{filename}' 的时长。", "error_ffprobe_not_found": "  诊断: 找不到 ffprobe 程序。请确保 ffmpeg.exe 和 ffprobe.exe 已放置在脚本目录或系统 PATH 中。", "error_ffmpeg_general": "  FFmpeg 错误: {error}", "error_get_duration_unknown": "  获取文件 '{filename}' 时长时发生未知错误: {error}", "error_model_not_in_config": "错误：在配置的 models 字典中找不到 '{model_key}'。", "model_found_locally": "模型 '{model_key}' 已在本地找到。路径: {model_path}", "model_downloading": "模型 '{model_key}' 在本地未找到。开始从Hugging Face下载...", "using_mirror": "  > 使用镜像: {endpoint}", "no_mirror": "  > 不使用镜像，直接从官方 Hugging Face Hub 下载。", "model_download_success": "模型 '{model_key}' 下载成功！", "error_model_download": "错误：下载模型时发生网络错误或文件写入错误。 {error}", "checking_cover_art": "  > 正在检查内嵌封面...", "cover_art_found": "  > 已找到封面并提取到临时文件: {cover_path}", "no_cover_art_found": "  > 未找到封面。", "error_extracting_cover_art": "  > 提取封面时出错: {error}", "checking_embedded_chapters": "  > 正在检查内嵌章节元数据...", "found_embedded_chapters": "  > 找到 {count} 个内嵌章节。将跳过语音识别。", "no_embedded_chapters": "  > 未找到内嵌章节。将执行语音识别流程。", "parsing_srt": "  > 正在解析 SRT 文件: {filename}", "error_parsing_srt": "  > 解析 SRT 文件时出错: {error}", "chapter_detected_with_title": "    > 检测到章节: Chapter {number} - '{title}' at {time:.2f}s", "chapter_detected_no_title": "    > 检测到章节: Chapter {number} at {time:.2f}s", "title_from_next_line": "    > 章节 {number} 标题为空，从下一行获取: '{title}'", "error_writing_srt": "  > 写入 SRT 文件时出错: {error}", "program_start": "--- 启动章节切割程序 ---", "ffmpeg_path_info": "脚本将自动在系统 PATH 或脚本所在目录中查找 ffmpeg.exe 和 ffprobe.exe。", "ffmpeg_path_ensure": "请确保这些文件已放置在正确的位置。", "processing_start": "\n--- 开始处理音频文件 ---", "scanning_folder": "扫描 '{input_dir}' 文件夹...", "no_audio_files_found": "在 'input' 文件夹中没有找到支持的音频文件。脚本结束。", "processing_file": "\n--- 正在处理文件: {filename} ---", "output_will_be_saved_to": "输出将保存到: {output_sub_dir}", "error_getting_duration": "无法获取文件时长，跳过文件 '{filename}'。", "found_chapter_cache": "  > 找到章节缓存文件: {json_path_name}。正在加载...", "cache_format_mismatch": "  > 缓存格式与当前配置 (extract_chapter_title={extract_title}) 不匹配，将重新解析。", "cache_load_success": "  > 成功加载 {count} 个章节。", "cache_load_fail": "  > 加载JSON文件失败: {error}。将重新解析。", "srt_not_found": "  > 未找到SRT文件，开始进行语音识别...", "loading_model": "    > 正在准备并加载Whisper模型...", "model_prep_fail": "模型准备失败，脚本终止。", "model_load_success": "    > 模型加载成功！", "error_loading_model": "错误：无法加载模型。 {error}", "transcribing": "  [识别中]... {progress:5.1f}%", "transcription_chunk_export": "    > 正在导出区块 {current}/{total}...", "transcription_chunk_process": "    > 正在识别区块 {current}/{total}...", "transcription_memory_save": "  > 音频文件过大，为节省内存将分块处理...", "transcription_complete": "语音识别完成。", "saving_srt": "  > 将字幕保存到 SRT 文件: {srt_path_name}", "error_transcribing": "\n错误: 识别文件 '{filename}' 时出错。 {error}", "found_existing_srt": "  > 找到已存在的 SRT 文件: {srt_path_name}", "parsing_chapters": "\n正在检测章节...", "saving_chapters_to_cache": "  > 将解析出的 {count} 个章节信息保存到缓存: {json_path_name}", "error_saving_json": "  > 保存JSON文件时出错: {error}", "no_chapters_found": "未找到章节信息，跳过切割。", "splitting_audio": "\n正在切割并导出音频...", "loading_full_audio": "  > 正在加载完整音频用于切割...", "first_chapter_from_start": "  > 将第一章的开始时间设置为 0，以包含引言部分。", "exporting_file": "  > 正在导出 {output_filename} (从 {start_sec:.2f}s 到 {end_sec:.2f}s)...", "file_process_complete": "文件 '{filename}' 处理完成！", "error_splitting_file": "错误: 切割或导出文件 '{filename}' 时出错。 {error}", "archiving_files": "\n处理完成，正在归档源文件...", "moved_file": "  > 已移动: {filename}", "error_archiving": "错误: 归档文件时出错。 {error}", "all_files_processed": "\n--- 所有文件处理完毕 ---"}