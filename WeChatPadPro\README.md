# WeChatPadPro

<div align="center">

![WeChatPadPro](https://img.shields.io/badge/WeChatPadPro-v2.0-blue.svg)
![Docker](https://img.shields.io/badge/Docker-Supported-brightgreen.svg)
![Go](https://img.shields.io/badge/Go-1.19+-00ADD8.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

**🚀 高性能微信机器人框架 | 企业级消息自动化解决方案**

[快速开始](#快速开始) • [API文档](#api文档) • [部署指南](#部署指南) • [开发文档](#开发指南)

</div>

## 📖 项目简介

WeChatPadPro 是一个基于 Go 语言开发的高性能微信机器人框架，专为企业级应用场景设计。它提供了完整的微信消息接收、处理和发送能力，支持多种消息类型，具备强大的扩展性和稳定性。

### ✨ 核心特性

- 🔥 **高性能架构** - Go语言编写，支持高并发消息处理
- 🔌 **插件化设计** - 与Bncr框架无缝集成，支持丰富的插件生态
- 📱 **全消息类型** - 支持文本、图片、语音、视频、文件等所有微信消息类型
- 🔄 **智能重连** - 自动重连机制，确保服务稳定性
- 🛡️ **安全可靠** - 完整的认证体系和签名验证机制
- 🐳 **容器化部署** - Docker一键部署，开箱即用
- 📊 **实时监控** - 完整的日志系统和健康检查
- 🌐 **RESTful API** - 标准化API接口，支持多语言集成

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WeChatPadPro  │    │   Webhook       │    │     Bncr        │
│   (Core Service)│◄──►│   Client        │◄──►│   Framework     │
│   Port: 1238    │    │   Port: 1244    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     MySQL       │    │     Redis       │    │   Static Files  │
│   Port: 3306    │    │   Port: 6379    │    │   (Swagger UI)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 🧩 组件说明

| 组件 | 功能 | 技术栈 |
|------|------|--------|
| **WeChatPadPro Core** | 核心服务，处理微信消息收发 | Go + WebSocket |
| **MySQL** | 数据持久化存储 | MySQL 8.0 |
| **Redis** | 消息缓存和会话管理 | Redis 7.0 |
| **Webhook Client** | 消息回调处理 | Python Flask |
| **Swagger UI** | API文档和测试界面 | HTML/JS |

## 🚀 快速开始

### 前置要求

- Docker & Docker Compose
- 2GB+ 可用内存
- 开放端口：1238, 1244, 3306, 6379

### 一键部署

```bash
# 1. 克隆项目
git clone https://github.com/your-repo/WeChatPadPro.git
cd WeChatPadPro

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置必要参数

# 3. 启动服务
docker-compose up -d

# 4. 查看服务状态
docker-compose ps
```

### 验证部署

```bash
# 检查服务健康状态
curl http://localhost:1238/health

# 访问API文档
open http://localhost:1238/swagger/

# 查看日志
docker-compose logs -f wechatpadpro
```

## ⚙️ 配置说明

### 环境变量配置 (.env)

```bash
# 数据库配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=12345678
MYSQL_DATABASE=ipad861

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=12345678

# 服务配置
SERVER_PORT=1238
ADMIN_KEY=your_admin_key_here
LOG_LEVEL=info

# Webhook配置
WEBHOOK_URL=http://webhook-client:1244/webhook
WEBHOOK_SECRET=wh_sk_2024_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0
```

### Webhook配置 (webhook_config.json)

```json
{
    "url": "http://webhook-client:1244/webhook",
    "secret": "wh_sk_2024_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
    "enabled": true,
    "timeout": 8,
    "retryCount": 2,
    "messageTypes": ["*"],
    "includeSelfMessage": true,
    "useDirectStream": true,
    "useRedisSync": false,
    "independentMode": true
}
```

### 安全配置

#### SSL/TLS配置

```bash
# 启用HTTPS
ENABLE_HTTPS=true
SSL_CERT_PATH=/app/certs/server.crt
SSL_KEY_PATH=/app/certs/server.key

# 证书文件挂载
volumes:
  - ./certs:/app/certs:ro
```

#### 访问控制

```bash
# IP白名单
ALLOWED_IPS=***********/24,10.0.0.0/8

# API限流配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
```

#### 密钥管理

```bash
# 强密钥生成示例
ADMIN_KEY=$(openssl rand -hex 32)
WEBHOOK_SECRET=$(openssl rand -hex 32)
JWT_SECRET=$(openssl rand -hex 32)
```

### 监控配置

#### Prometheus指标

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'wechatpadpro'
    static_configs:
      - targets: ['localhost:1238']
    metrics_path: '/metrics'
```

#### 健康检查端点

```bash
# 基础健康检查
GET /health

# 详细状态检查
GET /status

# 指标数据
GET /metrics
```

#### 日志配置

```bash
# 日志级别配置
LOG_LEVEL=info          # debug, info, warn, error
LOG_FORMAT=json         # json, text
LOG_OUTPUT=file         # file, stdout, both

# 日志轮转配置
LOG_MAX_SIZE=100MB
LOG_MAX_BACKUPS=7
LOG_MAX_AGE=30
```

## 📡 API文档

### 认证方式

所有API请求需要在Header中包含管理密钥：

```bash
curl -H "Authorization: Bearer YOUR_ADMIN_KEY" \
     -H "Content-Type: application/json" \
     http://localhost:1238/api/endpoint
```

### 核心API接口

#### 发送文本消息

```bash
POST /message/SendTextMessage
Content-Type: application/json

{
    "ToUserName": "wxid_xxxxxxxxx",
    "Content": "Hello, World!"
}
```

#### 发送图片消息

```bash
POST /message/SendImageNewMessage
Content-Type: multipart/form-data

{
    "ToUserName": "wxid_xxxxxxxxx",
    "file": [图片文件]
}
```

#### 获取登录状态

```bash
GET /user/GetLoginStatus
```

#### 获取用户信息

```bash
GET /user/GetUserInfo
```

#### 发送语音消息

```bash
POST /message/SendVoice
Content-Type: multipart/form-data

{
    "ToUserName": "wxid_xxxxxxxxx",
    "file": [语音文件]
}
```

#### 发送视频消息

```bash
POST /message/SendVideoMsg
Content-Type: multipart/form-data

{
    "ToUserName": "wxid_xxxxxxxxx",
    "file": [视频文件]
}
```

#### 撤回消息

```bash
POST /message/RevokeMsg
Content-Type: application/json

{
    "MsgId": "message_id_here"
}
```

#### 获取联系人列表

```bash
GET /contact/GetContactList
```

#### 获取群聊成员

```bash
POST /chatroom/GetChatroomMemberList
Content-Type: application/json

{
    "ChatRoomWxId": "chatroom_wxid_here"
}
```

### 完整API文档

访问 `http://localhost:1238/swagger/` 查看完整的交互式API文档。

### WebSocket连接

WeChatPadPro支持WebSocket实时消息推送：

```javascript
const ws = new WebSocket('ws://localhost:1238/ws');

ws.onopen = function() {
    console.log('WebSocket连接已建立');
    // 发送认证信息
    ws.send(JSON.stringify({
        type: 'auth',
        token: 'YOUR_AUTH_KEY'
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到消息:', data);

    // 处理不同类型的消息
    switch(data.msgType) {
        case 1: // 文本消息
            handleTextMessage(data);
            break;
        case 3: // 图片消息
            handleImageMessage(data);
            break;
        // ... 其他消息类型
    }
};

ws.onerror = function(error) {
    console.error('WebSocket错误:', error);
};

ws.onclose = function() {
    console.log('WebSocket连接已关闭');
    // 实现重连逻辑
    setTimeout(connectWebSocket, 5000);
};
```

## 🔧 Bncr框架集成

### 安装适配器

1. 将 `wechatpadpro.js` 复制到 Bncr 的 `Adapter` 目录
2. 在 Bncr 管理界面中配置适配器参数

### 配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `weChatPadProUrl` | WeChatPadPro服务地址 | `192.168.123.188:1239` |
| `weChatPadProAdminKey` | 管理密钥 | `12345` |
| `activeMessagePoll` | 是否开启主动消息轮询 | `false` |
| `activeMessagePollInterval` | 消息轮询间隔(秒) | `5` |
| `max_text_cache` | 文本消息缓存数量 | `100` |
| `logLevel` | 日志级别 | `info` |

### 适配器特性

- **智能缓存管理** - 自动管理消息缓存，防止重复处理
- **断线重连** - 支持最多10次自动重连，确保服务稳定
- **模块化日志** - 分模块日志记录，便于调试和监控
- **错误处理** - 完善的错误处理机制，提供详细的错误信息
- **性能优化** - 优化的消息处理流程，支持高并发场景

### 消息类型支持

| 类型码 | 消息类型 | 处理方式 |
|--------|----------|----------|
| 1 | 文本消息 | ✅ 完全支持 |
| 3 | 图片消息 | ✅ 完全支持 |
| 34 | 语音消息 | ✅ 完全支持 |
| 43 | 视频消息 | ✅ 完全支持 |
| 47 | 表情/贴纸 | ✅ 完全支持 |
| 48 | 位置消息 | ✅ 转文本处理 |
| 49 | 多媒体/引用消息 | ✅ 完全支持 |
| 50 | 通话通知 | ✅ 完全支持 |
| 10000 | 系统消息 | ✅ 完全支持 |

## 🚀 性能优化

### 系统调优

#### 内存优化

```bash
# Docker内存限制
services:
  wechatpadpro:
    mem_limit: 2g
    mem_reservation: 1g

# Go运行时优化
GOGC=100                # GC触发百分比
GOMEMLIMIT=1GiB        # 内存限制
GOMAXPROCS=4           # 最大CPU核心数
```

#### 数据库优化

```sql
-- MySQL配置优化
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
max_connections = 200
query_cache_size = 64M

-- 索引优化
CREATE INDEX idx_msg_time ON messages(create_time);
CREATE INDEX idx_user_wxid ON users(wxid);
CREATE INDEX idx_room_wxid ON chatrooms(wxid);
```

#### Redis优化

```bash
# Redis配置
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000

# 连接池配置
REDIS_POOL_SIZE=20
REDIS_POOL_TIMEOUT=30s
REDIS_IDLE_TIMEOUT=300s
```

### 高可用部署

#### 负载均衡配置

```nginx
upstream wechatpadpro {
    server wechatpadpro1:1238 weight=3;
    server wechatpadpro2:1238 weight=2;
    server wechatpadpro3:1238 weight=1 backup;
}

server {
    listen 80;
    server_name api.wechatpadpro.com;

    location / {
        proxy_pass http://wechatpadpro;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

#### 集群部署

```yaml
# docker-compose.cluster.yml
version: '3.8'

services:
  wechatpadpro-1:
    image: wechatpadpro:latest
    environment:
      - NODE_ID=1
      - CLUSTER_MODE=true
    ports:
      - "1238:1238"

  wechatpadpro-2:
    image: wechatpadpro:latest
    environment:
      - NODE_ID=2
      - CLUSTER_MODE=true
    ports:
      - "1239:1238"

  redis-cluster:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes

  mysql-master:
    image: mysql:8.0
    environment:
      - MYSQL_REPLICATION_MODE=master

  mysql-slave:
    image: mysql:8.0
    environment:
      - MYSQL_REPLICATION_MODE=slave
```

### 最佳实践

#### 消息处理优化

```javascript
// 批量消息处理
const batchProcessor = {
    messages: [],
    batchSize: 100,
    flushInterval: 1000,

    addMessage(message) {
        this.messages.push(message);
        if (this.messages.length >= this.batchSize) {
            this.flush();
        }
    },

    flush() {
        if (this.messages.length > 0) {
            this.processBatch(this.messages);
            this.messages = [];
        }
    }
};

// 消息去重
const messageDeduplicator = {
    cache: new Map(),
    ttl: 300000, // 5分钟

    isDuplicate(messageId) {
        const now = Date.now();
        if (this.cache.has(messageId)) {
            return true;
        }

        this.cache.set(messageId, now);
        this.cleanup(now);
        return false;
    },

    cleanup(now) {
        for (const [id, timestamp] of this.cache.entries()) {
            if (now - timestamp > this.ttl) {
                this.cache.delete(id);
            }
        }
    }
};
```

#### 错误处理策略

```javascript
// 指数退避重试
class RetryHandler {
    constructor(maxRetries = 3, baseDelay = 1000) {
        this.maxRetries = maxRetries;
        this.baseDelay = baseDelay;
    }

    async execute(fn, context = '') {
        let lastError;

        for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error;

                if (attempt === this.maxRetries) {
                    logger.error(`${context} 最终失败:`, error);
                    throw error;
                }

                const delay = this.baseDelay * Math.pow(2, attempt);
                logger.warn(`${context} 重试 ${attempt + 1}/${this.maxRetries}, ${delay}ms后重试`);
                await this.sleep(delay);
            }
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
```

## 🛠️ 开发指南

### 本地开发环境

```bash
# 1. 启动依赖服务
docker-compose up -d mysql redis

# 2. 配置环境变量
export MYSQL_HOST=localhost
export REDIS_HOST=localhost
# ... 其他环境变量

# 3. 运行主服务
./wechatpadpro
```

### 自定义Webhook处理

创建自定义webhook处理器：

```python
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/webhook', methods=['POST'])
def custom_webhook():
    data = request.get_json()
    
    # 自定义消息处理逻辑
    if data.get('msgType') == 1:  # 文本消息
        content = data.get('content', '')
        # 处理文本消息
        
    return jsonify({"status": "ok"})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=1244)
```

### 扩展消息处理器

在Bncr适配器中添加自定义消息处理：

```javascript
// 在 messageProcessor 中添加新的处理器
messageProcessor.typeHandlers[999] = 'handleCustomMessage';

messageProcessor.handleCustomMessage = function(processedMessage, rawMessage, messageContent) {
    // 自定义消息处理逻辑
    processedMessage.message_str = '自定义处理: ' + messageContent;
    return processedMessage;
};
```

## 📁 项目结构

```
WeChatPadPro/
├── wechatpadpro              # 主服务可执行文件
├── webhook-client.py         # Webhook客户端
├── docker-compose.yml        # Docker编排文件
├── Dockerfile.wechatpadpro   # 主服务镜像
├── Dockerfile.webhook        # Webhook镜像
├── .env                      # 环境变量配置
├── webhook_config.json       # Webhook配置
├── assets/                   # 资源文件
│   ├── ca-cert/             # 证书文件
│   ├── meta-inf.yaml        # 元信息配置
│   └── sae.dat              # 数据文件
├── static/                   # 静态文件
│   ├── swagger/             # API文档
│   ├── templates/           # 模板文件
│   └── doc/                 # 文档图片
├── mysql/                    # MySQL配置
└── redis/                    # Redis配置
```

## 📋 使用案例

### 企业客服机器人

```javascript
// 智能客服示例
class CustomerServiceBot {
    constructor() {
        this.knowledgeBase = new Map();
        this.sessionManager = new Map();
    }

    async handleMessage(message) {
        const session = this.getSession(message.fromUser);

        // 意图识别
        const intent = await this.recognizeIntent(message.content);

        switch (intent) {
            case 'greeting':
                return this.handleGreeting(session);
            case 'product_inquiry':
                return this.handleProductInquiry(message, session);
            case 'complaint':
                return this.handleComplaint(message, session);
            case 'human_transfer':
                return this.transferToHuman(session);
            default:
                return this.handleDefault(message, session);
        }
    }

    async handleProductInquiry(message, session) {
        const products = await this.searchProducts(message.content);
        if (products.length > 0) {
            return this.formatProductResponse(products);
        }
        return "抱歉，没有找到相关产品信息，请联系人工客服。";
    }
}
```

### 群聊管理机器人

```javascript
// 群聊管理示例
class GroupManagerBot {
    constructor() {
        this.groupRules = new Map();
        this.userWarnings = new Map();
    }

    async handleGroupMessage(message) {
        if (message.isAtBot) {
            return this.handleAtMessage(message);
        }

        // 内容审核
        if (await this.isInappropriateContent(message.content)) {
            await this.handleInappropriateContent(message);
            return;
        }

        // 广告检测
        if (await this.isAdvertisement(message.content)) {
            await this.handleAdvertisement(message);
            return;
        }

        // 活跃度统计
        this.updateUserActivity(message.fromUser, message.chatroom);
    }

    async handleAtMessage(message) {
        const command = this.parseCommand(message.content);

        switch (command.action) {
            case 'kick':
                return this.kickUser(command.target, message.chatroom);
            case 'mute':
                return this.muteUser(command.target, command.duration);
            case 'rules':
                return this.showGroupRules(message.chatroom);
            case 'stats':
                return this.showGroupStats(message.chatroom);
        }
    }
}
```

### 自动化工作流

```javascript
// 工作流自动化示例
class WorkflowBot {
    constructor() {
        this.workflows = new Map();
        this.triggers = new Map();
    }

    // 审批流程
    async handleApprovalWorkflow(message) {
        const workflow = {
            id: this.generateId(),
            type: 'approval',
            initiator: message.fromUser,
            content: message.content,
            status: 'pending',
            approvers: ['manager1', 'manager2'],
            currentStep: 0
        };

        this.workflows.set(workflow.id, workflow);
        await this.notifyNextApprover(workflow);
    }

    // 定时任务
    async scheduleTask(task) {
        const schedule = {
            id: this.generateId(),
            task: task,
            schedule: task.cron,
            nextRun: this.calculateNextRun(task.cron),
            enabled: true
        };

        this.scheduleJob(schedule);
    }

    // 数据同步
    async syncData(source, target) {
        const syncJob = {
            source: source,
            target: target,
            lastSync: new Date(),
            status: 'running'
        };

        try {
            const data = await this.fetchData(source);
            await this.pushData(target, data);
            syncJob.status = 'completed';
        } catch (error) {
            syncJob.status = 'failed';
            syncJob.error = error.message;
        }

        return syncJob;
    }
}
```

### 多平台消息同步

```javascript
// 多平台同步示例
class MultiPlatformSync {
    constructor() {
        this.platforms = new Map();
        this.messageQueue = [];
        this.syncRules = new Map();
    }

    addPlatform(name, adapter) {
        this.platforms.set(name, adapter);
    }

    async syncMessage(message, sourcePlatform) {
        const rules = this.syncRules.get(sourcePlatform) || [];

        for (const rule of rules) {
            if (this.matchesRule(message, rule)) {
                const targetPlatforms = rule.targets;

                for (const platform of targetPlatforms) {
                    const adapter = this.platforms.get(platform);
                    if (adapter) {
                        await this.transformAndSend(message, adapter, rule);
                    }
                }
            }
        }
    }

    async transformAndSend(message, adapter, rule) {
        // 消息格式转换
        const transformedMessage = await this.transformMessage(message, rule);

        // 发送到目标平台
        await adapter.sendMessage(transformedMessage);

        // 记录同步日志
        this.logSync(message, adapter.platform, transformedMessage);
    }
}
```

### 日志分析

```bash
# 查看主服务日志
docker-compose logs -f wechatpadpro

# 查看webhook日志
docker-compose logs -f webhook-client

# 查看数据库日志
docker-compose logs -f mysql

# 查看Redis日志
docker-compose logs -f redis

# 实时监控所有服务
docker-compose logs -f

# 查看特定时间段的日志
docker-compose logs --since="2024-01-01T00:00:00" --until="2024-01-01T23:59:59" wechatpadpro

# 搜索错误日志
docker-compose logs wechatpadpro 2>&1 | grep -i error

# 统计日志级别
docker-compose logs wechatpadpro 2>&1 | grep -E "(ERROR|WARN|INFO|DEBUG)" | sort | uniq -c
```

### 性能监控

#### 系统资源监控

```bash
# 容器资源使用情况
docker stats

# 详细的容器信息
docker-compose exec wechatpadpro top

# 内存使用分析
docker-compose exec wechatpadpro cat /proc/meminfo

# 磁盘使用情况
docker-compose exec wechatpadpro df -h

# 网络连接状态
docker-compose exec wechatpadpro netstat -tulpn
```

#### 应用性能监控

```bash
# API响应时间测试
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:1238/health

# 并发测试
ab -n 1000 -c 10 http://localhost:1238/health

# WebSocket连接测试
wscat -c ws://localhost:1238/ws
```

#### 数据库性能监控

```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看连接数
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';

-- 查看缓存命中率
SHOW STATUS LIKE 'Qcache_hits';
SHOW STATUS LIKE 'Qcache_inserts';

-- 查看表锁状态
SHOW STATUS LIKE 'Table_locks_waited';
SHOW STATUS LIKE 'Table_locks_immediate';
```

### 调试技巧

#### 开启调试模式

```bash
# 设置调试级别
LOG_LEVEL=debug

# 开启详细日志
VERBOSE_LOGGING=true

# 开启性能分析
ENABLE_PROFILING=true
PROFILE_PORT=6060
```

#### 消息追踪

```javascript
// 消息追踪示例
class MessageTracer {
    constructor() {
        this.traces = new Map();
    }

    startTrace(messageId) {
        this.traces.set(messageId, {
            id: messageId,
            startTime: Date.now(),
            steps: [],
            status: 'processing'
        });
    }

    addStep(messageId, step, data = {}) {
        const trace = this.traces.get(messageId);
        if (trace) {
            trace.steps.push({
                step: step,
                timestamp: Date.now(),
                data: data
            });
        }
    }

    endTrace(messageId, status = 'completed') {
        const trace = this.traces.get(messageId);
        if (trace) {
            trace.endTime = Date.now();
            trace.duration = trace.endTime - trace.startTime;
            trace.status = status;

            // 输出追踪信息
            console.log(`Message ${messageId} trace:`, trace);
        }
    }
}
```

#### 网络诊断

```bash
# 检查端口连通性
telnet localhost 1238
nc -zv localhost 1238

# 检查DNS解析
nslookup mysql
dig mysql

# 检查网络延迟
ping mysql
ping redis

# 检查防火墙规则
iptables -L
ufw status

# 检查网络接口
ip addr show
ifconfig
```

### 备份与恢复

#### 数据备份

```bash
# MySQL数据备份
docker-compose exec mysql mysqldump -u root -p12345678 ipad861 > backup_$(date +%Y%m%d_%H%M%S).sql

# Redis数据备份
docker-compose exec redis redis-cli --rdb backup_$(date +%Y%m%d_%H%M%S).rdb

# 配置文件备份
tar -czf config_backup_$(date +%Y%m%d_%H%M%S).tar.gz .env webhook_config.json docker-compose.yml

# 完整备份脚本
#!/bin/bash
BACKUP_DIR="/backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# 备份数据库
docker-compose exec mysql mysqldump -u root -p12345678 ipad861 > $BACKUP_DIR/mysql.sql

# 备份Redis
docker-compose exec redis redis-cli BGSAVE
docker cp $(docker-compose ps -q redis):/data/dump.rdb $BACKUP_DIR/

# 备份配置
cp .env webhook_config.json docker-compose.yml $BACKUP_DIR/

# 备份日志
docker-compose logs > $BACKUP_DIR/logs.txt

echo "备份完成: $BACKUP_DIR"
```

#### 数据恢复

```bash
# MySQL数据恢复
docker-compose exec -T mysql mysql -u root -p12345678 ipad861 < backup.sql

# Redis数据恢复
docker-compose stop redis
docker cp backup.rdb $(docker-compose ps -q redis):/data/dump.rdb
docker-compose start redis

# 配置恢复
cp backup/.env .
cp backup/webhook_config.json .
cp backup/docker-compose.yml .
docker-compose up -d
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 开发规范

- 遵循 Go 语言编码规范
- 添加适当的单元测试
- 更新相关文档
- 确保所有测试通过

### 代码提交规范

```bash
# 提交消息格式
<type>(<scope>): <subject>

# 类型说明
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
test:     测试相关
chore:    构建过程或辅助工具的变动

# 示例
feat(api): 添加批量发送消息接口
fix(websocket): 修复连接断开重连问题
docs(readme): 更新API文档说明
```

### 测试指南

```bash
# 运行单元测试
go test ./...

# 运行集成测试
go test -tags=integration ./...

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out

# 性能测试
go test -bench=. ./...

# 压力测试
go test -race ./...
```

### 代码审查清单

- [ ] 代码符合项目编码规范
- [ ] 添加了必要的单元测试
- [ ] 测试覆盖率达到80%以上
- [ ] 更新了相关文档
- [ ] 没有引入安全漏洞
- [ ] 性能没有明显下降
- [ ] 向后兼容性良好
- [ ] 错误处理完善
- [ ] 日志记录适当
- [ ] 配置项文档化

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

本项目仅供学习和研究使用，请遵守相关法律法规和微信使用条款。使用本项目所产生的任何法律责任由使用者自行承担。

### 第三方工具

- [WeChatPadPro CLI](https://github.com/wechatpadpro/cli) - 命令行管理工具
- [监控面板](https://github.com/wechatpadpro/dashboard) - Web监控界面
- [配置生成器](https://config.wechatpadpro.com) - 在线配置生成工具
- [性能测试工具](https://github.com/wechatpadpro/benchmark) - 压力测试套件

## 🔗 相关项目

- [Bncr](https://github.com/Bncr-team/Bncr) - 无界聊天机器人框架
- [OpenWechat](https://github.com/eatmoreapple/openwechat) - 微信机器人SDK
- [WechatBot](https://github.com/guyueyingmu/wechat-bot) - 另一个微信机器人实现
- [ChatBot-API](https://github.com/zhayujie/chatgpt-on-wechat) - ChatGPT微信机器人


### 开源项目

- [智能客服机器人](https://github.com/example/smart-customer-service) - 基于AI的智能客服
- [群聊管理助手](https://github.com/example/group-manager) - 全功能群聊管理工具
- [消息同步工具](https://github.com/example/message-sync) - 多平台消息同步

## 📊 项目统计

![GitHub stars](https://img.shields.io/github/stars/your-repo/WeChatPadPro?style=social)
![GitHub forks](https://img.shields.io/github/forks/your-repo/WeChatPadPro?style=social)
![GitHub issues](https://img.shields.io/github/issues/your-repo/WeChatPadPro)
![GitHub pull requests](https://img.shields.io/github/issues-pr/your-repo/WeChatPadPro)
![GitHub contributors](https://img.shields.io/github/contributors/your-repo/WeChatPadPro)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

### 核心贡献者

- [@contributor1](https://github.com/contributor1) - 项目创始人
- [@contributor2](https://github.com/contributor2) - 核心开发者
- [@contributor3](https://github.com/contributor3) - 文档维护者

### 特别感谢

- [Bncr](https://github.com/Bncr-team/Bncr) - 优秀的聊天机器人框架
- [Go](https://golang.org/) - 高性能编程语言
- [Docker](https://www.docker.com/) - 容器化技术
- [MySQL](https://www.mysql.com/) - 可靠的数据库系统
- [Redis](https://redis.io/) - 高性能缓存系统
---

<div align="center">

**🌟 如果这个项目对你有帮助，请给它一个 Star！🌟**

[![Star History Chart](https://api.star-history.com/svg?repos=your-repo/WeChatPadPro&type=Date)](https://star-history.com/#your-repo/WeChatPadPro&Date)

[🐛 报告问题](https://github.com/your-repo/WeChatPadPro/issues) • [💡 功能请求](https://github.com/your-repo/WeChatPadPro/issues) • [💬 讨论交流](https://github.com/your-repo/WeChatPadPro/discussions) • [📖 Wiki](https://github.com/your-repo/WeChatPadPro/wiki)

**让我们一起构建更好的微信机器人生态！**

</div>
