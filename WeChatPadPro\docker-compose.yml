services:
  redis:
    build:
      context: ./redis
      dockerfile: Dockerfile
    container_name: redis
    environment:
      - TZ=Asia/Shanghai
    networks:
      - wechatpadpro-network
    ports:
      - "6379:6379"
    volumes:
      - /root/wxchat/redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "12345678", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 10s

  mysql:
    build:
      context: ./mysql
      dockerfile: Dockerfile
    container_name: mysql
    environment:
      - MYSQL_ROOT_PASSWORD=12345678
      - MYSQL_DATABASE=ipad861
      - TZ=Asia/Shanghai
    networks:
      - wechatpadpro-network
    ports:
      - "3306:3306"
    volumes:
      - /root/wxchat/mysql_data:/var/lib/mysql
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-p12345678"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 10s

  webhook-client:
    build:
      context: .
      dockerfile: Dockerfile.webhook
    container_name: webhook-client
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - wechatpadpro-network
    ports:
      - "1244:1244"
    volumes:
      - /root/wxchat/webhook:/app
    restart: unless-stopped

  wechatpadpro:
    build:
      context: .
      dockerfile: Dockerfile.wechatpadpro
    container_name: wechatpadpro
    env_file:
      - .env
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - wechatpadpro-network
    volumes:
      - /root/wxchat/logs:/app/logs
    ports:
      - "1238:1238"
    restart: unless-stopped
    
networks:
  wechatpadpro-network:
    driver: bridge
