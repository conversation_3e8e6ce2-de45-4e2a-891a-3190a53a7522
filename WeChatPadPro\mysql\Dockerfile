# 使用官方的 MariaDB Alpine 镜像作为基础，它已经非常小了
# FROM mariadb:11
FROM yobasystems/alpine-mariadb:latest

# 1. 设置环境变量
# 设置时区以确保容器内时间正确
ENV TZ=Asia/Shanghai

# 2. 安装时区数据并配置
# 在一个 RUN 指令中完成，以减少镜像层数
RUN apk update && \
    apk add --no-cache tzdata && \
    # 将时区文件链接到系统位置
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone && \
    # 清理 apk 缓存
    rm -rf /var/cache/apk/*

# 3. 声明数据卷
# 官方基础镜像已经包含了这一步，但再次声明可以增加可读性
VOLUME /var/lib/mysql

# 4. 暴露端口
# 官方基础镜像也已包含，此处为清晰起见再次声明
EXPOSE 3306

# 注意：
# - 我们不需要覆盖 CMD 或 ENTRYPOINT，因为官方镜像的入口脚本已经非常完善，
#   它会自动处理数据库的初始化（基于您在 docker run 命令中传入的环境变量）。
# - 官方镜像已经包含了优化的 HEALTHCHECK，我们直接使用即可，无需重新定义。