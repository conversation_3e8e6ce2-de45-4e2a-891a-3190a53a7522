# 使用官方的 Redis Alpine 镜像作为基础
FROM redis:7.2-alpine

# 1. 设置时区环境变量
ENV TZ=Asia/Shanghai

# 2. 安装时区数据
RUN apk update && \
    apk add --no-cache tzdata && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone && \
    rm -rf /var/cache/apk/*

# 3. 拷贝自定义的配置文件
# 将我们的 redis.conf 文件拷贝到镜像中 Redis 默认会加载的位置
COPY redis.conf /usr/local/etc/redis/redis.conf

# 4. 声明数据卷
# 官方镜像已包含，此处为清晰起见再次声明
VOLUME /data

# 5. 暴露端口
EXPOSE 6379

# 6. 添加健康检查
# 完全复刻您在 docker-compose.yml 中的健康检查配置
HEALTHCHECK --interval=5s --timeout=3s --retries=5 --start-period=10s \
  CMD ["redis-cli", "-a", "12345678", "ping"]

# 7. 设置启动命令
# 让 Redis 服务器使用我们的配置文件启动
CMD ["redis-server", "/usr/local/etc/redis/redis.conf"]