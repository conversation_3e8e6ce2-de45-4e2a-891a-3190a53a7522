/* WeChatPadPro-861 Custom Swagger UI Theme */

/* 全局样式 */
body {
  font-family: 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f8f9fa;
  color: #212529;
}

/* Swagger UI 容器 */
#swagger-ui {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 标题区域 */
.swagger-ui .topbar {
  background-color: #2c3e50;
  padding: 15px 0;
}

.swagger-ui .info {
  margin: 20px 0;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.swagger-ui .info .title {
  font-size: 32px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 15px;
}

.swagger-ui .info .description {
  font-size: 16px;
  line-height: 1.6;
}

/* 标签分组 */
.swagger-ui .opblock-tag {
  font-size: 18px;
  font-weight: 600;
  margin: 15px 0;
  padding: 12px 15px;
  background-color: #f1f8ff;
  border-radius: 6px;
  border-left: 4px solid #3498db;
  transition: all 0.3s ease;
}

.swagger-ui .opblock-tag:hover {
  background-color: #e3f2fd;
  cursor: pointer;
}

/* API 操作块 */
.swagger-ui .opblock {
  margin: 0 0 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.swagger-ui .opblock:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* HTTP 方法颜色 */
.swagger-ui .opblock-get {
  border-color: #61affe;
  background: rgba(97, 175, 254, 0.1);
}

.swagger-ui .opblock-post {
  border-color: #49cc90;
  background: rgba(73, 204, 144, 0.1);
}

.swagger-ui .opblock-put {
  border-color: #fca130;
  background: rgba(252, 161, 48, 0.1);
}

.swagger-ui .opblock-delete {
  border-color: #f93e3e;
  background: rgba(249, 62, 62, 0.1);
}

/* 操作摘要 */
.swagger-ui .opblock .opblock-summary {
  padding: 12px;
}

.swagger-ui .opblock .opblock-summary-method {
  font-weight: 600;
  border-radius: 4px;
  padding: 6px 12px;
  min-width: 80px;
  text-align: center;
}

.swagger-ui .opblock-summary-path {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  font-weight: 500;
}

.swagger-ui .opblock-summary-description {
  font-size: 14px;
  color: #555;
}

/* 参数区域 */
.swagger-ui .parameters-container {
  padding: 15px;
  background-color: #fafafa;
  border-radius: 6px;
}

.swagger-ui .parameters-col_description {
  font-size: 14px;
  color: #444;
}

/* 请求体编辑器 */
.swagger-ui .opblock-body pre {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: #282c34;
  color: #abb2bf;
  border-radius: 6px;
  padding: 12px;
}

/* 响应区域 */
.swagger-ui .responses-wrapper {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.swagger-ui .response-col_status {
  font-weight: 600;
}

/* 按钮样式 */
.swagger-ui .btn {
  border-radius: 4px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.swagger-ui .execute {
  background-color: #3498db;
  color: white;
  border-color: #3498db;
}

.swagger-ui .execute:hover {
  background-color: #2980b9;
  border-color: #2980b9;
}

.swagger-ui .btn-clear {
  background-color: #e74c3c;
  color: white;
  border-color: #e74c3c;
}

.swagger-ui .btn-clear:hover {
  background-color: #c0392b;
  border-color: #c0392b;
}

/* 模型定义区域 */
.swagger-ui .model {
  font-size: 14px;
}

.swagger-ui .model-title {
  font-size: 16px;
  font-weight: 600;
}

/* 自定义复制按钮 */
button[copy="true"] {
  background-color: #3498db !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 6px 12px !important;
  font-size: 13px !important;
  cursor: pointer !important;
  transition: background-color 0.3s ease !important;
}

button[copy="true"]:hover {
  background-color: #2980b9 !important;
}

/* 自定义表单区域 */
#xxcaibi {
  background-color: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin: 20px auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  width: 90%;
}

.xxcaibi-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.xxcaibi-label {
  color: #495057;
  font-size: 14px;
  width: 120px;
  font-weight: 500;
}

.xxcaibi-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.xxcaibi-input:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* WebSocket 客户端区域 */
#messages {
  margin-top: 15px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
  background-color: #f8f9fa;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 13px;
}

.message {
  color: #333;
  margin: 5px 0;
  line-height: 1.5;
}

.success {
  color: #2ecc71;
  font-weight: bold;
}

.warning {
  color: #f39c12;
  font-weight: bold;
}

.error {
  color: #e74c3c;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .swagger-ui .opblock-summary-method {
    min-width: 60px;
    padding: 4px 8px;
  }
  
  .xxcaibi-group {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .xxcaibi-label {
    width: 100%;
    margin-bottom: 5px;
  }
} 