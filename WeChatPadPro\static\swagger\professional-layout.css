/* WeChatPadPro-861 专业布局增强 CSS */

/* 全局样式调整 */
body {
  background-color: #f9fafc;
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.swagger-ui {
  --primary-color: #4e54c8;
  --secondary-color: #8f94fb;
  --success-color: #4CAF50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --gray-50: #f9fafc;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --mono-font: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, monospace;
  --border-radius: 8px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 顶部导航栏隐藏 */
.swagger-ui .topbar {
  display: none !important;
}

/* 信息区域美化 */
.swagger-ui .information-container {
  background-color: white;
  margin: 0 !important;
  padding: 0 !important;
  box-shadow: var(--shadow);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.swagger-ui .info {
  margin: 0 !important;
  padding: 30px 5% !important;
}

.swagger-ui .info hgroup.main {
  margin-bottom: 20px;
}

.swagger-ui .info .title {
  color: var(--gray-900);
  font-weight: 700;
  font-size: 28px;
  line-height: 1.2;
  margin: 0 0 10px 0;
}

.swagger-ui .info .title small.version-stamp {
  background-color: var(--primary-color);
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin-left: 10px;
  vertical-align: middle;
  box-shadow: var(--shadow-sm);
}

.swagger-ui .info .description {
  color: var(--gray-700);
  font-size: 16px;
  line-height: 1.6;
  margin-top: 15px;
}

/* 美化 API 接口区块 */
.swagger-ui .opblock-tag {
  margin: 0 0 15px;
  padding: 15px 20px;
  border-radius: var(--border-radius);
  background-color: white;
  box-shadow: var(--shadow);
  border: none !important;
  transition: all 0.3s ease;
}

.swagger-ui .opblock-tag:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.swagger-ui .opblock-tag:active {
  transform: translateY(0px);
}

.swagger-ui .opblock {
  margin: 0 0 15px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  border: none !important;
  background: white;
  transition: all 0.3s ease;
}

.swagger-ui .opblock:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* 美化接口方法标签 */
.swagger-ui .opblock .opblock-summary-method {
  border-radius: 4px;
  padding: 6px 12px;
  min-width: 80px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  box-shadow: var(--shadow-sm);
}

.swagger-ui .opblock-summary-operation-id,
.swagger-ui .opblock-summary-path,
.swagger-ui .opblock-summary-path__deprecated,
.swagger-ui .opblock-summary-description {
  font-size: 15px;
  line-height: 1.4;
  color: var(--gray-700);
}

.swagger-ui .opblock-summary-path {
  font-family: var(--mono-font);
  font-weight: 500;
  color: var(--gray-800);
}

/* 美化表单元素 */
.swagger-ui input,
.swagger-ui select,
.swagger-ui textarea {
  border-radius: 4px;
  border: 1px solid var(--gray-300);
  padding: 8px 12px;
  font-size: 14px;
  font-family: var(--font-family);
  color: var(--gray-800);
  background-color: white;
  transition: all 0.2s ease;
}

.swagger-ui input:focus,
.swagger-ui select:focus,
.swagger-ui textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(78, 84, 200, 0.1);
  outline: none;
}

/* 按钮样式美化 */
.swagger-ui .btn {
  border-radius: 4px;
  border: none;
  font-weight: 500;
  font-size: 14px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.swagger-ui .btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.swagger-ui .btn:active {
  transform: translateY(0);
}

.swagger-ui .btn.try-out__btn {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

.swagger-ui .btn.try-out__btn:hover {
  background-color: var(--gray-300);
}

.swagger-ui .btn.execute {
  background-color: var(--primary-color);
  color: white;
}

.swagger-ui .btn.execute:hover {
  background-color: #3c42b0;
}

.swagger-ui .btn.cancel {
  background-color: var(--gray-500);
  color: white;
}

.swagger-ui .btn.cancel:hover {
  background-color: var(--gray-600);
}

/* 响应区域美化 */
.swagger-ui .responses-inner {
  background-color: var(--gray-50);
  border-radius: var(--border-radius);
  padding: 15px;
}

.swagger-ui .response-col_status {
  font-weight: 600;
  font-size: 15px;
}

.swagger-ui .response-col_description__inner p {
  font-size: 14px;
  color: var(--gray-700);
  line-height: 1.5;
}

/* 表格样式增强 */
.swagger-ui table {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin: 15px 0;
}

.swagger-ui table thead tr {
  background-color: var(--gray-100);
}

.swagger-ui table thead tr th {
  color: var(--gray-700);
  font-weight: 600;
  font-size: 14px;
  padding: 12px 15px;
  border-bottom: 1px solid var(--gray-200);
}

.swagger-ui table tbody tr td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-700);
  font-size: 14px;
}

.swagger-ui table tbody tr:last-child td {
  border-bottom: none;
}

/* 自定义页脚样式 */
.swagger-ui .footer {
  text-align: center;
  margin: 30px 0;
  padding: 20px 0;
  color: var(--gray-500);
  font-size: 14px;
  border-top: 1px solid var(--gray-200);
}

.swagger-ui .footer a {
  color: var(--primary-color);
  text-decoration: none;
}

.swagger-ui .footer a:hover {
  text-decoration: underline;
}

/* 增强颜色一致性 */
.swagger-ui .opblock-get .opblock-summary-method {
  background-color: #61affe;
}

.swagger-ui .opblock-post .opblock-summary-method {
  background-color: #49cc90;
}

.swagger-ui .opblock-put .opblock-summary-method {
  background-color: #fca130;
}

.swagger-ui .opblock-delete .opblock-summary-method {
  background-color: #f93e3e;
}

.swagger-ui .opblock-patch .opblock-summary-method {
  background-color: #50e3c2;
}

/* 修复复制按钮样式 */
button[copy="true"] {
  background: linear-gradient(to right, var(--primary-color), #3c42b0) !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 6px 15px !important;
  font-size: 13px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: var(--shadow-sm) !important;
}

button[copy="true"]:hover {
  background: linear-gradient(to right, #3c42b0, var(--primary-color)) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow) !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .swagger-ui .info .title {
    font-size: 24px;
  }
  
  .swagger-ui .info .title small.version-stamp {
    font-size: 12px;
    padding: 3px 8px;
  }
  
  .swagger-ui .opblock-tag {
    padding: 12px 15px;
  }
  
  .swagger-ui .opblock .opblock-summary-method {
    min-width: 60px;
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .swagger-ui .opblock-summary-path {
    font-size: 13px;
  }
}

/* 隐藏 Models 区域 */
.swagger-ui section.models {
  display: none !important;
}

/* 隐藏 schemes-wrapper */
.swagger-ui .scheme-container .schemes-wrapper {
  display: none !important;
}

/* 修复 url 显示 */
.swagger-ui .base-url {
  display: none !important;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* 美化授权按钮 */
.swagger-ui .auth-wrapper .authorize {
  background-color: var(--primary-color);
  border: none;
  color: white;
  box-shadow: var(--shadow-sm);
  padding: 6px 15px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.swagger-ui .auth-wrapper .authorize:hover {
  background-color: #3c42b0;
  box-shadow: var(--shadow);
} 