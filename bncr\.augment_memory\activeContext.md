# BNCR项目 - 活跃上下文记忆

## 📋 当前任务状态
**任务**: 执行augment_init命令初始化BNCR项目记忆系统
**状态**: 进行中
**时间**: 2025/07/13 10:17:33 (UTC+8)

## 🎯 项目概览
**项目名称**: BNCR (Bot Node.js Chat Robot)
**项目类型**: 多平台聊天机器人框架
**技术栈**: Node.js + TypeScript + Express + WebSocket
**主要功能**: 支持多平台适配器的聊天机器人系统

## 🏗️ 核心架构
- **适配器系统**: 支持QQ、微信、Telegram、SSH、Web等多平台
- **插件系统**: 模块化插件架构，支持动态加载
- **配置管理**: JSON Schema配置验证系统
- **数据库**: 支持Level、NeDB、SQLite、MySQL
- **Web管理**: Express + WebSocket实时管理界面

## 📁 关键目录结构
```
bncr/
├── Adapter/           # 平台适配器
│   ├── qq.js         # QQ适配器
│   ├── tgBot.js      # Telegram适配器
│   ├── wechatpadpro.js # 微信适配器
│   └── ...
├── config/           # 配置文件
├── db/              # 数据库模块
├── plugins/         # 插件目录
├── public/          # 静态资源
└── shared/          # 共享资源
```

## 🔧 技术特点
- TypeScript支持，类型安全
- 模块化适配器架构
- 插件热加载机制
- 多数据库支持
- WebSocket实时通信
- JWT身份验证

## 📝 当前工作重点
1. 建立完整的项目记忆系统
2. 分析项目架构和设计模式
3. 记录最佳实践和开发规范
4. 创建技术决策文档

## 🎯 下一步计划
- 完成记忆系统初始化
- 深入分析适配器架构
- 记录插件开发规范
- 建立代码质量标准

---
*最后更新: 2025/07/13 10:17:33 (UTC+8)*
