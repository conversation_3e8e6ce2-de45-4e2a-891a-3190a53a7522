# BNCR项目架构设计

## 🏛️ 整体架构

### 分层架构
```
┌─────────────────────────────────────┐
│           Web管理界面                │
├─────────────────────────────────────┤
│           API接口层                  │
├─────────────────────────────────────┤
│           核心业务层                 │
├─────────────────────────────────────┤
│           适配器层                   │
├─────────────────────────────────────┤
│           数据持久层                 │
└─────────────────────────────────────┘
```

## 🔌 适配器架构

### 适配器接口设计
- **统一消息格式**: 标准化的消息对象结构
- **平台抽象**: 屏蔽不同平台的API差异
- **事件驱动**: 基于事件的消息处理机制

### 支持的适配器
1. **QQ适配器** (`qq.js`)
2. **Telegram适配器** (`tgBot.js`)
3. **微信适配器** (`wechatpadpro.js`, `wxXyo.js`)
4. **SSH适配器** (`ssh.js`)
5. **Web适配器** (`web.js`)

### 适配器生命周期
```javascript
// 适配器初始化流程
1. 配置验证 (JSON Schema)
2. 依赖检查和安装
3. 平台连接建立
4. 消息监听启动
5. 事件处理注册
```

## 🧩 插件系统

### 插件架构
- **热加载**: 支持插件动态加载/卸载
- **沙箱隔离**: 插件运行环境隔离
- **API注入**: 统一的插件API接口

### 插件分类
- **官方插件**: 核心功能插件
- **第三方插件**: 社区贡献插件
- **自定义插件**: 用户开发插件

## 🗄️ 数据架构

### 多数据库支持
```javascript
// 数据库适配器
├── Level.ts      // 高性能键值存储
├── Nedb.ts       // 嵌入式文档数据库
├── SQLite        // 关系型数据库
└── MySQL         // 企业级数据库
```

### 数据分层
- **配置数据**: 系统和插件配置
- **用户数据**: 用户信息和权限
- **消息数据**: 聊天记录和日志
- **缓存数据**: 临时数据和会话

## 🌐 网络架构

### 通信协议
- **HTTP/HTTPS**: RESTful API接口
- **WebSocket**: 实时双向通信
- **平台API**: 各平台原生API调用

### 安全机制
- **JWT认证**: 无状态身份验证
- **CORS处理**: 跨域请求控制
- **数据加密**: 敏感信息加密存储

## 📊 监控架构

### 日志系统
- **分级日志**: ERROR/WARN/INFO/DEBUG
- **日志轮转**: 自动日志文件管理
- **实时监控**: WebSocket日志推送

### 性能监控
- **系统信息**: CPU/内存/磁盘监控
- **消息统计**: 消息处理性能统计
- **错误追踪**: 异常信息收集

## 🔧 配置架构

### 配置管理
```javascript
// 配置层次结构
├── 系统配置 (config.json)
├── 适配器配置 (各适配器独立)
├── 插件配置 (pluginConfig/)
└── 用户配置 (user/)
```

### 配置验证
- **JSON Schema**: 配置结构验证
- **类型检查**: TypeScript类型安全
- **默认值**: 配置项默认值管理

---
*创建时间: 2025/07/13 10:17:33 (UTC+8)*
