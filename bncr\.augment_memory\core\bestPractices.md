# BNCR项目最佳实践

## 🚀 开发最佳实践

### 1. 适配器开发规范

#### 适配器文件结构
```javascript
/**
 * 适配器头部注释规范
 * <AUTHOR>
 * @name 适配器名称
 * @team Bncr团队
 * @version 版本号
 * @description 适配器描述
 * @adapter true
 * @public false/true
 * @disable false/true
 * @priority 优先级数字
 * @classification ["分类标签"]
 */

// 1. 配置构造器
const jsonSchema = BncrCreateSchema.object({
  // 配置项定义
});

// 2. 配置管理器
const ConfigDB = new BncrPluginConfig(jsonSchema);

// 3. 主模块导出
module.exports = async () => {
  // 适配器实现逻辑
};
```

#### 配置验证最佳实践
```javascript
// ✅ 推荐：完整的配置定义
const jsonSchema = BncrCreateSchema.object({
  enable: BncrCreateSchema.boolean()
    .setTitle('是否开启适配器')
    .setDescription('设置为关则不加载该适配器')
    .setDefault(false),
  token: BncrCreateSchema.string()
    .setTitle('访问令牌')
    .setDescription('平台提供的API令牌')
    .setDefault(''),
  timeout: BncrCreateSchema.number()
    .setTitle('超时时间')
    .setDescription('请求超时时间(秒)')
    .setDefault(30)
    .setMinimum(1)
    .setMaximum(300)
});

// ❌ 避免：缺少描述和默认值
const badSchema = BncrCreateSchema.object({
  token: BncrCreateSchema.string()
});
```

#### 依赖管理最佳实践
```javascript
// ✅ 推荐：动态依赖检查和安装
await sysMethod.testModule(['node-telegram-bot-api'], { 
  install: true 
});

// ✅ 推荐：错误处理
try {
  const TelegramBot = require('node-telegram-bot-api');
} catch (error) {
  sysMethod.startOutLogs('依赖模块加载失败:', error.message);
  return;
}
```

### 2. 插件开发规范

#### 插件文件结构
```javascript
/**
 * 插件头部注释
 * @name 插件名称
 * <AUTHOR>
 * @version 版本
 * @description 功能描述
 * @rule 触发规则
 * @admin 是否需要管理员权限
 * @priority 优先级
 */

// 插件配置
const jsonSchema = BncrCreateSchema.object({
  // 配置定义
});

// 插件主逻辑
module.exports = async (s) => {
  // 插件实现
};
```

#### 消息处理最佳实践
```javascript
// ✅ 推荐：统一的消息处理流程
module.exports = async (s) => {
  try {
    // 1. 参数验证
    if (!s.getMsg()) {
      return s.reply('消息内容为空');
    }
    
    // 2. 权限检查
    if (needAdmin && !s.isAdmin()) {
      return s.reply('需要管理员权限');
    }
    
    // 3. 业务逻辑处理
    const result = await processMessage(s.getMsg());
    
    // 4. 结果返回
    return s.reply(result);
    
  } catch (error) {
    sysMethod.log('插件执行错误:', error);
    return s.reply('处理失败，请稍后重试');
  }
};
```

### 3. 数据库使用规范

#### 数据库选择指南
```javascript
// Level - 高性能键值存储
// 适用场景：缓存、计数器、简单配置
const levelDB = new BncrDB('level');
await levelDB.set('key', 'value');

// NeDB - 文档数据库
// 适用场景：用户数据、复杂查询
const neDB = new BncrDB('nedb');
await neDB.insert({ userId: '123', name: 'user' });

// SQLite - 关系数据库
// 适用场景：复杂关系、事务处理
const sqlite = new BncrDB('sqlite');
await sqlite.query('SELECT * FROM users WHERE id = ?', [userId]);
```

#### 数据操作最佳实践
```javascript
// ✅ 推荐：错误处理和事务
try {
  await db.beginTransaction();
  
  await db.insert('users', userData);
  await db.update('stats', { count: count + 1 });
  
  await db.commit();
} catch (error) {
  await db.rollback();
  throw error;
}

// ✅ 推荐：数据验证
function validateUserData(data) {
  if (!data.userId || !data.name) {
    throw new Error('用户数据不完整');
  }
  return true;
}
```

### 4. 错误处理规范

#### 分层错误处理
```javascript
// 适配器层错误处理
try {
  const response = await platformAPI.sendMessage(message);
} catch (error) {
  if (error.code === 'RATE_LIMIT') {
    // 限流处理
    await sleep(error.retryAfter * 1000);
    return retry();
  }
  throw new AdapterError('发送失败', error);
}

// 业务层错误处理
try {
  await processUserCommand(command);
} catch (error) {
  if (error instanceof ValidationError) {
    return reply('输入格式错误');
  }
  if (error instanceof PermissionError) {
    return reply('权限不足');
  }
  throw error; // 向上抛出未知错误
}

// 系统层错误处理
process.on('uncaughtException', (error) => {
  sysMethod.log('未捕获异常:', error);
  // 优雅关闭
});
```

### 5. 性能优化实践

#### 内存管理
```javascript
// ✅ 推荐：限制缓存大小
class MessageCache {
  constructor(maxSize = 1000) {
    this.cache = new Map();
    this.maxSize = maxSize;
  }
  
  set(key, value) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }
}

// ✅ 推荐：及时清理资源
const cleanup = () => {
  if (connection) {
    connection.close();
  }
  if (timer) {
    clearInterval(timer);
  }
};

process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);
```

#### 异步处理优化
```javascript
// ✅ 推荐：并发处理
const results = await Promise.all([
  processMessage1(msg1),
  processMessage2(msg2),
  processMessage3(msg3)
]);

// ✅ 推荐：流式处理大数据
const stream = fs.createReadStream('large-file.txt');
stream.on('data', (chunk) => {
  processChunk(chunk);
});
```

### 6. 安全最佳实践

#### 输入验证
```javascript
// ✅ 推荐：严格的输入验证
function validateInput(input) {
  // 长度检查
  if (input.length > 1000) {
    throw new Error('输入过长');
  }
  
  // 特殊字符过滤
  const sanitized = input.replace(/<script.*?>.*?<\/script>/gi, '');
  
  // SQL注入防护
  const escaped = mysql.escape(sanitized);
  
  return escaped;
}
```

#### 权限控制
```javascript
// ✅ 推荐：细粒度权限检查
function checkPermission(userId, action) {
  const user = getUserById(userId);
  const permissions = user.permissions || [];
  
  return permissions.includes(action) || user.isAdmin;
}

// 使用示例
if (!checkPermission(s.getUserId(), 'SEND_MESSAGE')) {
  return s.reply('权限不足');
}
```

### 7. 日志记录规范

#### 日志级别使用
```javascript
// ERROR: 系统错误，需要立即处理
sysMethod.log('数据库连接失败', 'ERROR');

// WARN: 警告信息，可能影响功能
sysMethod.log('API调用频率过高', 'WARN');

// INFO: 一般信息，记录重要操作
sysMethod.log('用户登录成功', 'INFO');

// DEBUG: 调试信息，开发阶段使用
sysMethod.log('处理消息详情', 'DEBUG');
```

#### 结构化日志
```javascript
// ✅ 推荐：结构化日志记录
const logData = {
  timestamp: new Date().toISOString(),
  level: 'INFO',
  module: 'tgBot',
  action: 'sendMessage',
  userId: s.getUserId(),
  messageId: messageId,
  success: true,
  duration: Date.now() - startTime
};

sysMethod.log(JSON.stringify(logData));
```

### 8. 测试最佳实践

#### 单元测试
```javascript
// 测试适配器功能
describe('TelegramAdapter', () => {
  let adapter;
  
  beforeEach(() => {
    adapter = new TelegramAdapter(mockConfig);
  });
  
  test('should send message successfully', async () => {
    const result = await adapter.sendMessage(mockMessage);
    expect(result.success).toBe(true);
  });
  
  test('should handle rate limit', async () => {
    // 模拟限流场景
    mockAPI.sendMessage.mockRejectedValue(new RateLimitError());
    
    const result = await adapter.sendMessage(mockMessage);
    expect(result.retried).toBe(true);
  });
});
```

---
*创建时间: 2025/07/13 10:17:33 (UTC+8)*
