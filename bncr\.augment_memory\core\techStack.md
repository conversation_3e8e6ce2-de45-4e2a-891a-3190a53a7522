# BNCR项目技术栈分析

## 🚀 核心技术栈

### 运行时环境
- **Node.js**: JavaScript运行时环境
- **TypeScript**: 类型安全的JavaScript超集
- **ts-node**: TypeScript直接执行工具

### Web框架
- **Express.js**: Web应用框架
- **express-ws**: WebSocket支持
- **express-jwt**: JWT身份验证中间件

### 数据库技术
- **Level**: 高性能键值数据库
- **NeDB**: 嵌入式JavaScript数据库
- **SQLite3**: 轻量级关系数据库
- **MySQL**: 关系型数据库

### 网络通信
- **axios**: HTTP客户端
- **got**: 现代HTTP请求库
- **request**: HTTP请求库(已废弃但仍在使用)
- **ssh2**: SSH客户端

### 数据处理
- **cheerio**: 服务端jQuery实现
- **xml2js**: XML解析器
- **sharp**: 图像处理库
- **archiver**: 文件压缩工具

### 实时通信
- **WebSocket**: 实时双向通信
- **node-cron**: 定时任务调度

### 安全认证
- **jsonwebtoken**: JWT令牌处理
- **crypto**: 加密功能

### 开发工具
- **log4js**: 日志管理
- **colors**: 终端颜色输出
- **chokidar**: 文件监控
- **multer**: 文件上传处理

### AI集成
- **openai**: OpenAI API集成

### 系统信息
- **systeminformation**: 系统信息获取
- **node-machine-id**: 机器唯一标识

## 🏗️ 架构特点

### 适配器模式
- 统一的消息处理接口
- 多平台支持(QQ、微信、Telegram等)
- 插件化扩展机制

### 配置管理
- JSON Schema验证
- 动态配置加载
- 类型安全的配置系统

### 数据持久化
- 多数据库支持
- 灵活的存储策略
- 数据迁移能力

### 实时通信
- WebSocket长连接
- 事件驱动架构
- 消息队列处理

---
*创建时间: 2025/07/13 10:17:33 (UTC+8)*
