/**
 * <AUTHOR>
 * @name Bncr开发提示依赖
 * @description Bncr开发声明文件，开发者需要安装此文件来提供编辑器的代码提示
 * @team Bncr团队
 * @version v1.0.0
 * @priority 100
 * @disable false
 * @service true
 * @public true
 *
 */

import type express from 'express';
import type node<PERSON>ron from 'node-cron';
import type { Logger } from 'log4js';

declare global {
  interface sendPluginsInfo {
    type: 'sub' | 'github';
    id: string;
    name: string;
    description: string;
    author: string;
    team: string;
    version: string;
    classification: string | string[];
    systemVersionRange: string;
    filename: string;
    fileDir: string;
    downloadUrl?: string;
    htmlLiks?: string;
    isMod?: boolean;
    isCron: boolean;
    isService: boolean;
    isAdapter: boolean;
    isChatPlugin?: boolean;
    isEncPlugin: boolean;
    isAuthentication: boolean;
  }
  /**
   * Bncr自带的轻量化K&V数据库解决方案
   */
  interface BncrDBInterface {
    /**
     * 获取数据,返回数据库中对应的值，未读取到传递的key对应的value时返回undefined
     * @param key 必选 需要读取的键值key
     * @param returnVal 可选 未读取到value时返回的值
     * @param boolean 可选 返回原始数据
     */
    /** 获取数据 */
    get(key: string): Promise<unknown | undefined>;
    get<T>(key: string): Promise<T | undefined>;
    get<T, K>(key: string, def: K, bool?: boolean): Promise<T | K>;
    get(key: string, def?: unknown, bool?: boolean): Promise<unknown>;
    /**
     * 设置值到数据库
     * @param key 要设置的key
     * @param value 要设置的值
     * @param opt -def 可选，设置成功后返回的默认值
     * @param opt -password 可选，设置密码
     */
    set(key: string, value: unknown): Promise<boolean>;
    set(key: string, value: unknown, opt?: { password: string }): Promise<boolean>;
    set(key: string, value: unknown, opt?: { def: unknown; password?: string }): Promise<boolean | unknown>;
    set<T>(key: string, value: T, opt?: { password?: string }): Promise<boolean>;
    set<T>(key: string, value: T, opt?: { def: unknown; password?: string }): Promise<boolean | unknown>;
    set<T, K>(key: string, value: T, opt: { def: K; password?: string }): Promise<K | boolean>;
    set(key: string, value: unknown, opt?: { def?: unknown; password?: string }): Promise<boolean>;
    /**
     * 删除数据
     * @param key 要删除的key
     * @returns boolean布尔值
     */
    del(key: string): Promise<boolean>;
    del<T>(key: string, def: T): Promise<boolean | T>;
    del(key: string, def?: unknown): Promise<boolean | unknown>;
    /**
     * 读取数据表下所有的key
     * @returns string[] 字符串数组
     */
    keys(): Promise<string[]>;
    /**
     * 读取数据库中所有数据表名
     * @returns string[] 字符串数组
     */
    getAllForm(): Promise<string[]>;

    /**
     * 监听一个key的修改，事件类型有del和 set 两种
     * 当是set时，newValue会传递将要修改的值 del时为空。
     * 执行method.stop()时，会拦截接下来的操作。
     * @param watchInfo
     */
    watch(watchInfo: {
      /* 给监听器赋值一个唯一id */
      id: string;
      /* 给监听器赋值一个回调 */
      callback: (
        /* 触发回调时会传递method对象，这个值包含以下方法 */
        method: {
          /* 拦截到的值 */
          newValue: any;
          /* 监听到的事件类型 */
          eventType: 'del' | 'set';
          /* 阻止修改 */
          stop: () => undefined;
          /* 篡改拦截到的值 */
          changeValue: (newValue: any) => undefined;
        }
      ) => any;
      /* 监听的key */
      key: string;
      /* 可选一个密码，后续需要通过密码才可更改监听器，否则任何人都能覆盖相同id的监听器 */
      password?: string | undefined;
    }): boolean;

    /**
     * 取消对应id和key的监听器
     * @param watchInfo
     */
    unWatch(watchInfo: { id: string; key: string; password?: string }): boolean;
  }
  /**
   * Bncr适配器构造器，通过该class，可以完美自定义任何平台接入
   */
  interface AdapterInterface {
    /**
     *消息接收器，
     * @param msgInfo
     */
    receive(msgInfo: msgInter): Promise<void>;
    /**
     * 回复消息给对方
     * @param replyInfo 要发送的消息体
     * @returns 一个字符串，如果没读取到发送消息的id，则返回一个空字符串
     */
    reply(replyInfo: replyInfo): Promise<string>;
    /**
     * 要撤回的消息
     * @param msgIdArr 要撤回消息的数组
     * @returns 一个字符串，如果没读取到发送消息的id，则返回一个空字符串
     *
     */
    delMsg(msgIdArr: string[]): Promise<void>;
    /**
     * 推送消息到指定人或群
     * @param replyInfo 要发送的消息体
     * @returns 一个字符串，如果没读取到发送消息的id，则返回一个空字符串
     */
    push(replyInfo: replyInfo): Promise<string>;
  }

  var BncrDB: {
    /**
     * @param form 必选 数据表名称
     * @param opt 可选 传入数据库实例化对象，通过该构造函数来控制nedb自定义数据库
     *
     * ```ts
     * 示例
     * //1.使用默认user数据库
     * const db = new BncrDB('system')
     * //2.演示如何单独开辟一个数据库
     * import { Level } from 'level';
     * import path from 'path';
     * //创建一个自己的数据库实例
     * const MyDB = new Level<string, any>(path.join(process.cwd(), `BncrData/db/mydb`), { valueEncoding: 'json' });
     * const db = new BncrDB('system',{
     *      //注册到全局的名字
     *      registerName:'mydb',
     *      //使用哪个数据库中间件处理
     *      useMiddlewarePath:'db/Level.ts',
     *      //传入你的数据库实例
     *      db:MyDB
     * })
     * ```
     *
     */
    new (
      name: string,
      opt?: {
        registerName: string;
        useMiddlewarePath: string;
        db?: { [key: string]: any } | undefined;
      }
    ): BncrDBInterface;
  };
  var Adapter: {
    /**
     * 适配器构造器，new 时应传递一个string字符串作为平台名称
     * @param AdapterName 适配器名称，
     */
    new (AdapterName: string): AdapterInterface;
  };

  interface Sender {
    msgInfo: msgInter;
    reply(msg: string | replyInfo): Promise<string>;

    /* 代替用户发送消息 */
    inlineSugar(msg: string): Promise<boolean>;
    //删除撤回消息
    delMsg(...msgidArr: any[]): Promise<any>;
    /**
     * 是否等待撤回消息
     * 如果最后一个参数为对象且有等待时间参数，则阻塞指定秒,否则立即执行
     */
    isWaitDel(argsArr: any[]): Promise<any[]>;
    /**
     * 监听某个用户发送的任何消息
     */
    waitInput(callback: (sender: any) => any, time: number): Promise<Sender | null>;

    again(replyInfo: any): Promise<string>;

    /** 获取匹配的正则参数 */
    param(k: number): string | undefined;
    //获取消息
    getMsg(): string;
    //设置消息
    setMsg(msg: any): any;
    //获取用户id
    getUserId(): any;
    //获取用户名
    getUserName(): any;
    //获取群消息id
    getGroupId(): any;
    //获取群消息id
    getGroupName(): any;
    //获取平台
    getFrom(): any;
    //获取消息id
    getMsgId(): any;
    //是否管理员消息
    isAdmin(): Promise<boolean>;
  }
  interface String {
    strip: string;
    stripColors: string;
    black: string;
    red: string;
    green: string;
    yellow: string;
    blue: string;
    magenta: string;
    cyan: string;
    white: string;
    gray: string;
    grey: string;

    bgBlack: string;
    bgRed: string;
    bgGreen: string;
    bgYellow: string;
    bgBlue: string;
    bgMagenta: string;
    bgCyan: string;
    bgWhite: string;

    reset: string;
    // @ts-ignore
    bold: string;
    dim: string;
    italic: string;
    underline: string;
    inverse: string;
    hidden: string;
    strikethrough: string;

    rainbow: string;
    zebra: string;
    america: string;
    trap: string;
    random: string;
    zalgo: string;
  }
  /**
   * 消息体
   */
  interface msgInter {
    /**用户的消息id，必传项 */
    userId: string;
    /**用户名 必传项目，没有也要传空字符串 */
    userName: string;
    /**群聊id，如果不是群请传递"0" */
    groupId: string | '0';
    /**群聊名称，没有传递空字符串 */
    groupName: string;
    /**接受到的消息 字符串*/
    msg: string;
    /**消息id，用作撤销消息使用，没有传递空字符串 */
    msgId: string;
    /**平台类型 ，一般不用指定，用于区分系统适配器*/
    fromType?: string;
    friendId?: string;
  }
  type replyInfo =
    | {
        type: string;
        path?: string;
        msg?: string;
        userId?: string;
        groupId?: string;
        toMsgId?: string;
      }
    | {
        type: 'text';
        msg: string;
        path?: undefined;
      }
    | {
        type: 'image' | 'video' | 'audio';
        path: string;
        msg?: string;
      };

  interface replyInfos {
    type: 'text' | 'image' | 'video' | 'audio';
    msg?: string;
    path?: string;
    userId?: string;
    groupId?: string;
    toMsgId?: string;
  }
  var sysMethod: {
    SystemStorage: {
      authorizationStatus: {
        [key: string]: boolean;
      };
    };
    sysOutLogs: (...params: any[]) => void;
    msgOutLogs: (mark: number, ...params: any[]) => Promise<void>;
    startOutLogs: (...params: any[]) => void;
    npmInstallInfo: { [key: string]: any };
    lock: boolean;
    systemStatus: {
      adapter: boolean;
      plugins: boolean;
      systemAdapter: boolean;
    };
    /**运行模式 */
    WorkMod: 'develop' | 'alpha' | 'production';
    /** 系统版本 */
    Version: string;
    /** MachineId */
    MachineId: string;
    /**
     * 存着config.js中的所有配置
     */
    config: object;

    /**
     * 查看bncr运行的平台
     */
    osPlatform(): string;
    /**
     * 判断用户是否dev用户
     */
    isDev(): boolean;
    /**
     * 异步睡眠，等待指定时间
     * @param second 等待多少秒
     * @returns void 0
     */
    sleep: (second: number) => Promise<void>;
    /**
     * 获取时间
     * @param format 格式
     * @returns 时间
     */
    getTime: (format: 'hh:mm:ss' | 'yyyy-MM-dd' | 'yyyy-MM-dd hh:mm:ss' | 'yyyy-MM-dd-hh-mm-ss') => string | number;
    /**
     * 系统定时器方法
     */
    cron: {
      /**
       * 添加一个新的定时器
       */
      newCron: typeof nodeCron.schedule;
      /**
       * 检测给定的字符串是否是定时表达式
       */
      isCron: typeof nodeCron.validate;
    };

    /**
     * 安装npm包的方法，传递一个npm包名
     * @returns string | null
     * 当opts.outConsole为true时，会在控制台实时输出执行情况 返回值为null
     * 否则将以字符串的方式抛出结果
     * ```js
     * await sysMethod.npmInstall('request'); //会返回执行信息String
     * await sysMethod.npmInstall('request', { outConsole: true }); // 将会在控制台实时打印安装情况，返回结果为null
     * ```
     */
    npmInstall: (
      str: string,
      opts?: { outConsole: boolean }
    ) => Promise<{
      status: boolean;
      data: string;
    }>;
    /**
     * createStartupCompletionHook为2.0新增的系统方法，
     * 它可以向系统重启完成后执行列表中增加执行函数
     * 现在有了这个钩子，你可以为所欲为的控制启动完成后的行为
     * @returns viod
     * 第一个参数为注册名，必须为字符串且请勿重复，否则不会被添加到列表中,
     * 第二个参数为函数，当系统启动完成后，会并发执行所有钩子的第二个参数的函数，它可以是异步的,
     * 注意：
     * 系统启动完成后，是并行(并发)执行所有的启动完成钩子，并不是依次执行，
     * 所以不要在不同的钩子中做数据交互，这并不可行.
     * ```js
     *
     * //示例一，启动完成后向控制台输出文本
     * sysMethod.createStartupCompletionHook('outStartOK', async () => {
     *   console.log('-来自启动钩子: 无界启动完成啦~');
     * });
     * ```
     */
    createStartupCompletionHook: (name: string, callBack: Function) => void;

    /**
     * 测试npm包是否已安装
     * @param strArr 字符串数组
     * @param opt 字段install 发现不存在是否立即安装？
     * @returns 结果对象
     *
     * ```js
     * await sysMethod.testModule(['telegram', 'input']); //将只测试，返回结果
     * await sysMethod.testModule(['telegram', 'input'], { install: true }); //发现少模块自动安装
     * ```
     */
    testModule: (
      strArr: string[],
      opt?: { install?: boolean }
    ) => {
      [key: string]: any;
    };
    /**
     * 内联 以系统管理员的身份向平台内部发送消息
     * @param msg 要内联的消息
     * @param name from名，可选，不传递时默认为'system@Admin'
     * @returns 没有任何返回值
     * ```js
     * //例子 9点整以系统管理员身份 触发重启命令 from类型为system
     * sysMethod.cron.newCron('0 0 9 * * *', () => {
     *     sysMethod.inline('重启');
     * });
     * ````
     */
    inline(msg: string, name?: string): Promise<undefined>;
    /**
     * 推送信息
     * @param pushInfo 要推送的信息
     */
    push(pushInfo: { platform: string; msg: string; path?: string; userId?: string; groupId?: string; type?: string; toMsgId?: string }): Promise<string>;
    /**
     * 推送各个平台的管理员
     * @param pushInfo 要推送的信息
     */
    pushAdmin(pushInfo: { platform: string[]; msg: string }): Promise<string | boolean>;
    /**
     * 系统配置
     */
    systemConfig: {
      AlphaToken?: string;
      ToverifyUrl: string;
      token: string;
      sysLogOpen: boolean;
      msgLogOpen: number;
      developerMode: boolean;
      DisableRouterPathLogs?: boolean;
      pluginsPublishingMode?: 'sub' | 'github';
    };
    /**顶级路径 */
    systemDir: string;
    /**运行工作路径 */
    runWorkDir: string;
    getSubscriptionUrl: (key: string, opt: { url: string; author: string; team: string }) => string;
    getDecSubscriptionUrl: (key: string, data: string) => { url?: string; author?: string; team?: string };
    [key: string]: any;
  };
  /* 系统路由 */
  var router: express.Router;
  /**经过new BncrDB加载过的数据库实例均会存储在此 */
  var DatabaseInstantiationObject: {
    [key: string]: {
      registerName: string;
      useMiddlewarePath: string;
      db?: object;
    };
  };
  /* 系统运行情况 */
  var RunningInformation: {
    AdapterTriggerRecord?: {
      /** 适配器 */
      [key: string]: {
        /** 名字 */
        name: string;
        /** 接收总量 */
        receive: number;
        /** 处理 */
        handle: number;
        /** 拦截 */
        intercept: number;
        /** 发送 */
        sending: number;
      };
    };
    getAdapterInfo?: (key: string) => { [key: string]: object };
    getLoadingPlugInfo?: (key: string) => { [path: string]: sendPluginsInfo };
    BncrCache: {
      [key: string]: any;
    };
    AllPluginConfigStorage: {
      [path: string]: {
        jsonSchema: { [key: string]: any };
        userConfig: { [key: string]: any };
      };
    };
  };
  var BncrJSLogger: Logger;
}

export {};
