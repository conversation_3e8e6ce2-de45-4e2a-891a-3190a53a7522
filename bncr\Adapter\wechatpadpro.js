/**
 * <AUTHOR>
 * @name wechatpadpro
 * @origin hhgg
 * @team hhgg
 * @version 1.0.1
 * @description wechatpadpro适配器 - 优化版本
 * @adapter true
 * @public true
 * @disable false
 * @priority 1
 * @Copyright ©2025 hhgg. All rights reserved
 * @classification ["adapter"]
 */

// Web界面配置适配器
const BCS = BncrCreateSchema;
const jsonSchema = BCS.object({
    enable: BCS.boolean().setTitle('是否开启适配器').setDescription(`设置为关则不加载该适配器`).setDefault(false),
    weChatPadProUrl: BCS.string().setTitle('weChatPadPro地址').setDescription(`Bncr将调用该地址发送消息(需包含端口如127.0.0.1:2345)`).setDefault('***************:1239'),
    weChatPadProAdminKey: BCS.string().setTitle('weChatPadPro管理秘钥').setDescription(`请输入weChatPadPro的管理密钥`).setDefault('12345'),
    activeMessagePoll: BCS.boolean().setTitle('是否开启主动消息轮询').setDescription(`只有当你发现微信消息没有按时同步到Bncr时，才需要启用，一般不会用到`).setDefault(false),
    activeMessagePollInterval: BCS.number().setTitle('消息轮询间隔').setDescription('主动消息轮询间隔，单位为秒').setDefault(5),
    max_text_cache: BCS.number().setTitle('文本消息缓存数量').setDescription('内存中缓存的最大文本消息数量').setDefault(100),
    logLevel: BCS.string().setTitle('日志级别').setDescription('设置控制台日志级别：info仅显示重要信息，debug显示所有日志').setEnum(['info', 'debug']).setDefault('info'),
});
const ConfigDB = new BncrPluginConfig(jsonSchema);

// 常量配置
const CONFIG = {
    DIRECTORIES: {
        IMAGE: '/bncr/BncrData/shared/image',
        AUDIO: '/bncr/BncrData/shared/mp3'
    },
    CONNECTION: {
        MAX_RECONNECT_ATTEMPTS: 10,
        RECONNECT_INTERVAL: 2000,
        WEBSOCKET_TIMEOUT: 30000,
        REQUEST_TIMEOUT: 10000
    },
    CACHE: {
        DEFAULT_TEXT_CACHE_SIZE: 100,
        CACHE_CLEANUP_INTERVAL: 3000000 // 5分钟
    },
    MESSAGE_TYPES: {
        TEXT: 1,
        IMAGE: 3,
        AUDIO: 34,
        VIDEO: 43,
        EMOJI: 47,
        LOCATION: 48,
        SYSTEM: 10000
    },
    API_ENDPOINTS: {
        SEND_TEXT: 'message/SendTextMessage',
        SEND_IMAGE: 'message/SendImageNewMessage',
        SEND_VOICE: 'message/SendVoice',
        SEND_VIDEO: 'message/SendVideoMsg',
        SEND_FILE: 'message/SendFileMsg',
        REVOKE_MSG: 'message/RevokeMsg',
        GET_LOGIN_STATUS: 'user/GetLoginStatus',
        GET_USER_INFO: 'user/GetUserInfo'
    }
};

// 兼容性别名
const image_dir = CONFIG.DIRECTORIES.IMAGE;
const audio_dir = CONFIG.DIRECTORIES.AUDIO;

module.exports = async () => {
    const axios = require('axios');
    const WebSocket = require('ws');
    const xml2js = require('xml2js');
    const fs = require('fs');
    const fsPromises = fs.promises;
    const xmlParserSingleton = (() => {
        let instance = null;
        return {
            getInstance: () => {
                if (!instance) {
                    instance = new xml2js.Parser({
                        explicitArray: false,
                        ignoreAttrs: false,
                        mergeAttrs: true
                    });
                }
                return instance;
            }
        };
    })();
    const xmlParser = xmlParserSingleton.getInstance();
    // 配置初始化和验证
    await ConfigDB.get();

    if (!Object.keys(ConfigDB.userConfig).length) {
        return sysMethod.startOutLogs('未对weChatPadPro适配器进行配置,退出');
    }

    if (!ConfigDB.userConfig.enable) {
        return sysMethod.startOutLogs('未启用weChatPadPro适配器,退出');
    }

    // 提取和验证配置
    const adapterConfig = {
        url: ConfigDB.userConfig.weChatPadProUrl,
        adminKey: ConfigDB.userConfig.weChatPadProAdminKey,
        activeMessagePoll: ConfigDB.userConfig.activeMessagePoll || false,
        pollInterval: ConfigDB.userConfig.activeMessagePollInterval || 5,
        maxTextCache: ConfigDB.userConfig.max_text_cache || CONFIG.CACHE.DEFAULT_TEXT_CACHE_SIZE,
        logLevel: ConfigDB.userConfig.logLevel || 'info',
        maxReconnectAttempts: CONFIG.CONNECTION.MAX_RECONNECT_ATTEMPTS,
        reconnectInterval: CONFIG.CONNECTION.RECONNECT_INTERVAL
    };
    // 配置验证
    if (!adapterConfig.url) {
        return sysMethod.startOutLogs('WeChatPadPro地址未配置,退出');
    }
    if (!adapterConfig.adminKey) {
        return sysMethod.startOutLogs('WeChatPadPro管理密钥未配置,退出');
    }
    // 标准化日志系统
    const logger = {
        // 日志级别
        LEVELS: {
            DEBUG: 0,
            INFO: 1,
            WARN: 2,
            ERROR: 3
        },
        // 获取当前日志级别
        getCurrentLevel: () => {
            return adapterConfig.logLevel === 'debug' ? logger.LEVELS.DEBUG : logger.LEVELS.INFO;
        },
        getTimestamp: () => {
            const now = new Date();
            const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
            return utc8Time.toISOString().replace('T', ' ').slice(0, 19);
        },
        // 格式化日志消息
        formatMessage: (level, module, message, ...args) => {
            const timestamp = logger.getTimestamp();
            const moduleStr = module ? `[${module}]` : '';
            const argsStr = args.length > 0 ? ` ${args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ')}` : '';
            return `${timestamp} [${level}]${moduleStr} ${message}${argsStr}`;
        },
        // 日志输出方法
        debug: (message, ...args) => {
            if (logger.getCurrentLevel() <= logger.LEVELS.DEBUG) {
                console.debug(logger.formatMessage('DEBUG', 'WeChatPadPro', message, ...args));
            }
        },
        info: (message, ...args) => {
            if (logger.getCurrentLevel() <= logger.LEVELS.INFO) {
                console.log(logger.formatMessage('INFO', 'WeChatPadPro', message, ...args));
            }
        },
        warn: (message, ...args) => {
            if (logger.getCurrentLevel() <= logger.LEVELS.WARN) {
                console.warn(logger.formatMessage('WARN', 'WeChatPadPro', message, ...args));
            }
        },
        error: (message, ...args) => {
            if (logger.getCurrentLevel() <= logger.LEVELS.ERROR) {
                console.error(logger.formatMessage('ERROR', 'WeChatPadPro', message, ...args));
            }
        },
        // 模块化日志方法
        module: (moduleName) => ({
            debug: (message, ...args) => {
                if (logger.getCurrentLevel() <= logger.LEVELS.DEBUG) {
                    console.debug(logger.formatMessage('DEBUG', moduleName, message, ...args));
                }
            },
            info: (message, ...args) => {
                if (logger.getCurrentLevel() <= logger.LEVELS.INFO) {
                    console.log(logger.formatMessage('INFO', moduleName, message, ...args));
                }
            },
            warn: (message, ...args) => {
                if (logger.getCurrentLevel() <= logger.LEVELS.WARN) {
                    console.warn(logger.formatMessage('WARN', moduleName, message, ...args));
                }
            },
            error: (message, ...args) => {
                if (logger.getCurrentLevel() <= logger.LEVELS.ERROR) {
                    console.error(logger.formatMessage('ERROR', moduleName, message, ...args));
                }
            }
        })
    };
    // 统一错误处理工具函数
    const errorHandler = {
        // 错误类型常量
        ERROR_TYPES: {
            CONNECTION_FAILED: 'CONNECTION_FAILED',
            API_ERROR: 'API_ERROR',
            VALIDATION_ERROR: 'VALIDATION_ERROR',
            PROCESSING_ERROR: 'PROCESSING_ERROR'
        },
        // 处理 axios 请求错误
        handleAxiosError: (error, operation) => {
            if (error.isAxiosError && error.code === 'ECONNREFUSED') {
                return {
                    type: errorHandler.ERROR_TYPES.CONNECTION_FAILED,
                    message: `连接WeChatPadPro服务失败: ${error.message}`,
                    operation
                };
            }
            return {
                type: errorHandler.ERROR_TYPES.PROCESSING_ERROR,
                message: `${operation}时出错: ${error.message}`,
                operation
            };
        },
        // 处理 API 响应错误
        handleApiResponse: (response, operation) => {
            if (response.status !== 200) {
                return {
                    type: errorHandler.ERROR_TYPES.API_ERROR,
                    message: `${operation}失败: HTTP ${response.status}`,
                    operation,
                    statusCode: response.status
                };
            }
            if (response.data.Code !== 200) {
                return {
                    type: errorHandler.ERROR_TYPES.API_ERROR,
                    message: `${operation}失败: API Code ${response.data.Code}`,
                    operation,
                    apiCode: response.data.Code,
                    data: response.data
                };
            }
            return null; // 无错误
        },
        // 参数验证错误
        createValidationError: (operation, message) => {
            return {
                type: errorHandler.ERROR_TYPES.VALIDATION_ERROR,
                message: `${operation}: ${message}`,
                operation
            };
        },
        // 统一的错误日志记录
        logError: (operation, error) => {
            let errorInfo;
            if (typeof error === 'string') {
                errorInfo = {
                    type: errorHandler.ERROR_TYPES.PROCESSING_ERROR,
                    message: error,
                    operation
                };
            } else if (error.isAxiosError) {
                errorInfo = errorHandler.handleAxiosError(error, operation);
            } else if (error.type && error.message) {
                errorInfo = error; // 已经是格式化的错误对象
            } else {
                errorInfo = {
                    type: errorHandler.ERROR_TYPES.PROCESSING_ERROR,
                    message: `${operation}时出错: ${error.message || error}`,
                    operation
                };
            }
            logger.error(`[${errorInfo.type}] ${errorInfo.message}`);
            return errorInfo.message;
        },
        // 检查并处理API响应
        validateApiResponse: (response, operation) => {
            const error = errorHandler.handleApiResponse(response, operation);
            if (error) {
                logger.error(`[${error.type}] ${error.message}`);
                return { success: false, error };
            }
            return { success: true, data: response.data };
        }
    };
    if (!adapterConfig.url) return logger.error('未设置WeChatPadPro地址');
    const wechatpadpro = new Adapter('wechatpadpro');
    const wxDB = new BncrDB('wechatpadpro');
    // 状态变量
    let authKey = await wxDB.get('wx_authKey', '');
    let wxid = await wxDB.get('wx_wxid', '');
    let ws = null;
    let isTerminating = false;
    let reconnectTimeoutId = null;
    let reconnectAttempts = 0;
    // 增强的缓存管理系统
    const cacheManager = {
        textCache: {},
        maxSize: adapterConfig.maxTextCache,

        // 添加文本到缓存
        addText: function(key, value) {
            if (Object.keys(this.textCache).length >= this.maxSize) {
                // 删除最旧的缓存项
                const oldestKey = Object.keys(this.textCache)[0];
                delete this.textCache[oldestKey];
            }
            this.textCache[key] = {
                value: value,
                timestamp: Date.now()
            };
        },
        // 获取缓存文本
        getText: function(key) {
            const cached = this.textCache[key];
            return cached ? cached.value : null;
        },
        // 清理过期缓存
        cleanup: function() {
            const now = Date.now();
            const expireTime = CONFIG.CACHE.CACHE_CLEANUP_INTERVAL;

            Object.keys(this.textCache).forEach(key => {
                if (now - this.textCache[key].timestamp > expireTime) {
                    delete this.textCache[key];
                }
            });
        },
        // 获取缓存统计
        getStats: function() {
            return {
                size: Object.keys(this.textCache).length,
                maxSize: this.maxSize
            };
        }
    };
    // 兼容性别名
    const cached_texts = cacheManager.textCache;
    const max_text_cache = cacheManager.maxSize;
    // 增强的消息处理器模块
    const messageProcessor = {
        // 消息类型处理器映射 (使用常量配置)
        typeHandlers: {
            [CONFIG.MESSAGE_TYPES.TEXT]: 'handleTextMessage',      // 文本消息
            [CONFIG.MESSAGE_TYPES.IMAGE]: 'handleImageMessage',     // 图片消息
            [CONFIG.MESSAGE_TYPES.AUDIO]: 'handleAudioMessage',    // 语音消息
            [CONFIG.MESSAGE_TYPES.VIDEO]: 'handleVideoMessage',    // 视频消息
            [CONFIG.MESSAGE_TYPES.EMOJI]: 'handleEmojiMessage',    // 表情/贴纸
            [CONFIG.MESSAGE_TYPES.LOCATION]: 'handleTextMessage',  // 位置消息(当作文本处理)
            49: 'handleMultimediaMessage', // 多媒体消息/引用消息
            50: 'handleCallMessage',       // 语音/视频通话通知
            [CONFIG.MESSAGE_TYPES.SYSTEM]: 'handleSystemMessage'   // 系统消息
        },
        // 性能监控
        stats: {
            processedCount: 0,
            errorCount: 0,
            lastProcessTime: 0
        },
        // 处理消息内容的主入口 (增强版)
        async processMessageContent(processedMsg, rawMessage, msgType, contentStr) {
            const startTime = Date.now();

            try {
                if (!processedMsg || !rawMessage) {
                    logger.warn('processMessageContent收到无效参数');
                    this.stats.errorCount++;
                    return;
                }
                const handlerName = this.typeHandlers[msgType];
                if (handlerName && typeof this[handlerName] === 'function') {
                    await this[handlerName](processedMsg, rawMessage, contentStr);
                    this.stats.processedCount++;
                } else {
                    logger.debug(`收到未处理的消息类型: ${msgType}. 内容: ${contentStr}`);
                    processedMsg.message_str = `[未处理的消息类型: ${msgType}]`;
                    processedMsg.message_components.push(createPlainComponent(processedMsg.message_str));
                }
                this.stats.lastProcessTime = Date.now() - startTime;
                    const duration = Date.now() - startTime;
                    logger.debug(`性能监控: 消息处理 耗时 ${duration}ms`);
            } catch (error) {
                this.stats.errorCount++;
                logger.error('处理消息内容时发生错误:', {
                    message: error.message,
                    stack: error.stack,
                    msgType: msgType,
                    contentStr: contentStr ? contentStr.substring(0, 100) + '...' : 'null'
                });
                processedMsg.message_str = '[消息处理失败]';
                processedMsg.message_components.push(createPlainComponent(processedMsg.message_str));
            }
        },
        // 获取处理统计
        getStats: function() {
            return { ...this.stats };
        },
        // 文本消息处理器
        async handleTextMessage(processedMsg, rawMessage, contentStr) {
            let textContent = contentStr;
            if (processedMsg.type === "GROUP_MESSAGE") {
                const actualSenderId = processedMsg.sender?.user_id;
                if (actualSenderId && contentStr.startsWith(actualSenderId + ':')) {
                    textContent = contentStr.substring(actualSenderId.length + 1).trimStart();
                }
            }
            processedMsg.message_str = textContent;
            processedMsg.message_components.push(createPlainComponent(textContent));
            // 缓存文本消息
            const newMsgId = rawMessage.new_msg_id;
            if (newMsgId) {
                this.cacheTextMessage(newMsgId, textContent);
            }
        },
        // 图片消息处理器
        async handleImageMessage(processedMsg, rawMessage) {
            const toUserNameStr = rawMessage.to_user_name?.str || "";
            const newMsgIdNum = rawMessage.new_msg_id;
            const fromUserNameStr = rawMessage.from_user_name?.str || "";
            const msgIdNum = rawMessage.msg_id || 0;
            const imageResp = await _downloadMedia('image', {
                from_user_name: fromUserNameStr,
                to_user_name: toUserNameStr,
                msg_id: msgIdNum
            });
            const imageB64Data = imageResp?.Data?.Data?.Buffer;
            if (imageB64Data) {
                const result = await saveFile(imageB64Data, newMsgIdNum, 'image');
                processedMsg.message_components.push(createPlainComponent(`[图片]`)); 
                processedMsg.message_str = `[图片]-${result.filename}`;
            } else {
                processedMsg.message_str = "[图片下载失败]";
                processedMsg.message_components.push(createPlainComponent("[图片下载失败]"));
            }
        },
        // 语音消息处理器
        async handleAudioMessage(processedMsg, rawMessage) {
            const toUserNameStr = rawMessage.to_user_name?.str || "";
            const newMsgIdNum = rawMessage.new_msg_id;
            const length = rawMessage.content?.str?.match(/\blength="(\d+)"/)?.[1];
            const audioResp = await _downloadMedia('audio', {
                length: length,
                to_user_name: toUserNameStr,
                new_msg_id: newMsgIdNum
            });
            const audioB64Data = audioResp?.Data?.Base64;
            if (audioB64Data) {
                const result = await saveFile(audioB64Data, newMsgIdNum, 'audio');
                processedMsg.message_components.push(createPlainComponent(`[语音]`));
                processedMsg.message_str = `[语音]-${result.filename}`;
            } else {
                processedMsg.message_str = "[语音下载失败]";
                processedMsg.message_components.push(createPlainComponent("[语音下载失败]"));
            }
        },
        // 视频消息处理器
        async handleVideoMessage(processedMsg, rawMessage, contentStr) {
            if (contentStr.includes("videomsg")) {
                const length = contentStr.match(/\bplaylength="(\d+)"/)?.[1];
                logger.debug("收到普通视频消息.");
                processedMsg.message_str = `[普通视频消息]-时长${length}秒`;
                processedMsg.message_components.push(createPlainComponent(`[普通视频消息]-时长${length}秒`));
            } else {
                logger.debug("收到未知视频消息.");
                processedMsg.message_str = "[未知视频消息]";
                processedMsg.message_components.push(createPlainComponent("[未知视频消息]"));
            }
        },
        // 表情消息处理器
        async handleEmojiMessage(processedMsg, rawMessage, contentStr) {
            try {
                const emojiComponent = await parseEmojiXml(contentStr, (processedMsg.type !== "GROUP_MESSAGE"), xmlParser);
                if (emojiComponent) {
                    processedMsg.message_components.push(emojiComponent);
                    processedMsg.message_str = "[贴纸/表情]";
                } else {
                    processedMsg.message_str = "[贴纸/表情 - 解析失败]";
                    processedMsg.message_components.push(createPlainComponent("[贴纸/表情 - 解析失败]"));
                }
            } catch (e) {
                logger.warn(`msg_type 47 (表情) 处理失败: ${e.message}`, e);
                processedMsg.message_components.push(createPlainComponent("[表情XML消息解析失败]"));
                processedMsg.message_str = "[表情XML消息解析失败]";
            }
        },
        // 多媒体消息处理器
        async handleMultimediaMessage(processedMsg, rawMessage, contentStr) {
            try {
                let components;
                try {
                    const isPrivateChat = (processedMsg.type !== "GROUP_MESSAGE");
                    const xmlDoc = await formatContentToXml(contentStr, isPrivateChat);
                    if (!xmlDoc || !xmlDoc.msg || !xmlDoc.msg.appmsg) {
                        logger.warn("无效的XML结构或缺少appmsg. XML文档:", JSON.stringify(xmlDoc));
                        components = [createPlainComponent("[XML消息结构错误]")];
                    } else {
                        const appmsgType = xmlDoc.msg.appmsg.type;
                        if (appmsgType === "57") {
                            components = await parseReplyXml(xmlDoc, rawMessage, cached_texts);
                        } else if (appmsgType === "51") {
                            // 优先从finderFeed中提取描述，如果没有则从appmsg.desc中提取
                            let description = "未知描述";
                            if (xmlDoc.msg.appmsg.finderFeed && xmlDoc.msg.appmsg.finderFeed.desc) {
                                description = xmlDoc.msg.appmsg.finderFeed.desc;
                            } else if (xmlDoc.msg.appmsg.desc) {
                                description = xmlDoc.msg.appmsg.desc;
                            }
                            components = [createPlainComponent(`[视频公众号视频]-${description}`)];
                        } else {
                            components = [createPlainComponent(`[XML消息:${appmsgType}] ${xmlDoc.msg.appmsg.title || "未知内容"}`)];
                        }
                    }
                } catch (e) {
                    logger.warn(`[内联XML解析] 处理失败: ${e.message}`, e);
                    components = [createPlainComponent("[XML解析异常]")];
                }
                if (components && components.length > 0) {
                    processedMsg.message_components = [...processedMsg.message_components, ...components].reverse();
                    processedMsg.message_str = processedMsg.message_components.slice(0, 2).map(c => {
                        if (c.type === 'Plain') return c.text;
                        if (c.type === 'Image') return '[图片]';
                        if (c.type === 'WechatEmoji') return '[表情]';
                        return `[${c.type || '未知组件'}]`;
                    }).join(' ');
                } else {
                    processedMsg.message_components.push(createPlainComponent("[XML消息已处理 - 无输出]"));
                    processedMsg.message_str = "[XML消息已处理 - 无输出]";
                }
            } catch (e) {
                logger.warn(`msg_type 49 主处理失败: ${e.message}`, e);
                processedMsg.message_components.push(createPlainComponent("[XML消息处理异常]"));
                processedMsg.message_str = "[XML消息处理异常]";
            }
        },
        // 通话消息处理器
        async handleCallMessage(processedMsg, rawMessage, contentStr) {
            logger.debug("收到语音/视频通话通知 (类型 50), 内容通常不显示为聊天消息.");
            processedMsg.message_str = "[语音/视频通话通知]";
            if (contentStr) {
                processedMsg.message_components.push(createPlainComponent(contentStr));
            } else {
                processedMsg.message_components.push(createPlainComponent("[语音/视频通话通知]"));
            }
        },
        // 系统消息处理器
        async handleSystemMessage(processedMsg, rawMessage, contentStr) {
            if (contentStr.includes("<secmsg>")) {
                logger.debug("收到群聊内折叠的消息.");
                processedMsg.message_str = "[群聊内折叠的消息]";
                processedMsg.message_components.push(createPlainComponent("[群聊内折叠的消息]"));
            } else if (contentStr.includes("<revokemsg>")) {
                logger.debug("撤回了一条消息.");
                processedMsg.message_str = `[撤回了一条消息]`;
                processedMsg.message_components.push(createPlainComponent(`[撤回了一条消息]`));
            } else if (contentStr.includes("tmpl_type_profile") || contentStr.includes("$adder$")) {
                const output = parseGroupInviteMessage(contentStr);
                logger.debug(`收到 ${output} 的消息.`);
                processedMsg.message_str = `[收到 ${output} 的消息]`;
                processedMsg.message_components.push(createPlainComponent(`[收到 ${output} 的消息]`));
            } else if (contentStr.includes("patsuffix")) {
                const output = parsePatMessage(contentStr);
                logger.debug(`收到 ${output} 的消息.`);
                processedMsg.message_str = `[收到 ${output} 的消息]`;
                processedMsg.message_components.push(createPlainComponent(`[收到 ${output} 的消息]`));
            } else {
                logger.debug("收到未处理的消息类型: 10002. 内容: " + contentStr);
                processedMsg.message_str = `[未处理的消息类型: 10002]`;
                processedMsg.message_components.push(createPlainComponent(`[未处理的消息类型: 10002]`));
            }
        },
        // 缓存文本消息 (使用增强的缓存管理器)
        cacheTextMessage(newMsgId, textContent) {
            try {
                const stats = cacheManager.getStats();
                if (stats.size >= stats.maxSize) {
                    logger.debug(`文本缓存达到上限(${stats.maxSize}), 自动清理旧条目`);
                }

                cacheManager.addText(String(newMsgId), textContent);
                logger.debug(`缓存文本消息, new_msg_id=${newMsgId}, 当前缓存数量: ${cacheManager.getStats().size}`);

            } catch (error) {
                logger.error('缓存文本消息时发生错误:', error);
            }
        }
    };
    // 创建模块化日志记录器
    const connectionLogger = logger.module('Connection');
    const authLogger = logger.module('Authentication');
    const messageLogger = logger.module('MessageProcessor');
    // 初始化连接
    let isLoginIn = await checkOnlineStatus();
    if (authKey && isLoginIn) {
        connectionLogger.info("设备已登录, 使用现有认证密钥进行连接");
        ws = connectWebsocket();
    } else {
        if (!authKey) {
            authLogger.info("没有可用认证密钥, 将生成新的认证密钥");
            authKey = await generateAuthKey();
        }
        if (!isLoginIn) {
            authLogger.info("设备已离线, 开始二维码登录流程");
            const qrCodeUrl = await getLoginQrCode();
            if (qrCodeUrl) {
                authLogger.info("请复制以下二维码链接，粘贴到浏览器中后，扫码登录", { qrCodeUrl });
            } else {
                authLogger.error("获取二维码链接失败");
                await terminate();
                return;
            }
            const loginSuccessful = await checkLoginStatus();
            if (loginSuccessful) {
                authLogger.info("登录成功, 适配器已连接");
            } else {
                authLogger.warn("登录失败或超时, 适配器将关闭");
                await terminate();
                return;
            }
        }
        ws = connectWebsocket();
    }
    // 启动定期缓存清理
    const cacheCleanupInterval = setInterval(() => {
        try {
            cacheManager.cleanup();
            const stats = cacheManager.getStats();
            logger.debug(`缓存清理完成, 当前缓存数量: ${stats.size}/${stats.maxSize}`);
        } catch (error) {
            logger.error('缓存清理时发生错误:', error);
        }
    }, CONFIG.CACHE.CACHE_CLEANUP_INTERVAL);
    // 适配器终止时清理定时器
    const originalTerminate = terminate;
    terminate = async function() {
        if (cacheCleanupInterval) {
            clearInterval(cacheCleanupInterval);
        }
        return await originalTerminate();
    };
    async function generateAuthKey() {
        const url = `http://${adapterConfig.url}/admin/GenAuthKey1`;
        const params = { key: adapterConfig.adminKey };
        const payload = { Count: 1, Days: 365 };
        try {
            const response = await axios.post(url, payload, { params });
            const validation = errorHandler.validateApiResponse(response, '认证密钥生成');
            if (!validation.success) {
                return null;
            }
            const responseData = validation.data;
            if (responseData.Data && Array.isArray(responseData.Data) && responseData.Data.length > 0) {
                const newKey = responseData.Data[0];
                if (newKey && typeof newKey === 'string' && newKey.length > 0) {
                    await wxDB.set('wx_authKey', newKey);
                    logger.info("成功获取新的认证密钥");
                    return newKey;
                } else {
                    const error = errorHandler.createValidationError('认证密钥生成', `密钥数据无效或为空: ${newKey}`);
                    errorHandler.logError('认证密钥生成', error);
                }
            } else {
                const error = errorHandler.createValidationError('认证密钥生成', `Data数组中未找到密钥: ${JSON.stringify(responseData)}`);
                errorHandler.logError('认证密钥生成', error);
            }
        } catch (error) {
            errorHandler.logError('生成认证密钥', error);
        }
        return null;
    }
    async function getLoginQrCode() {
        const url = `http://${adapterConfig.url}/login/GetLoginQrCodeNewX`;
        const params = { key: authKey };
        const payload = { "Check": false, "Proxy": "" };

        try {
            const response = await axios.post(url, payload, {
                params,
                timeout: CONFIG.CONNECTION.REQUEST_TIMEOUT
            });

            const apiError = errorHandler.handleApiResponse(response, '获取登录二维码');
            if (apiError) {
                logger.error(`[${apiError.type}] ${apiError.message}`);
                return null;
            }

            const responseData = response.data;
            if (responseData.Data) {
                logger.info("获取登录二维码成功", responseData.Data.qrcodeUrl);
                return responseData.Data.qrcodeUrl || null;
            }

            return null;
        } catch (error) {
            const handledError = errorHandler.handleAxiosError(error, '获取登录二维码');
            logger.error(`[${handledError.type}] ${handledError.message}`);
            return null;
        }
    }
    async function checkLoginStatus() {
        const url = `http://${adapterConfig.url}/login/CheckLoginStatus`;
        const params = { key: authKey };
        let attempts = 0;
        const maxAttempts = 36;
        const countdown = 180; // seconds
        logger.info(`请在 ${countdown} 秒内扫描二维码.`);
        while (attempts < maxAttempts) {
            if (isTerminating) {
                logger.info("适配器正在终止. 停止登录检查.");
                return false;
            }
            try {
                const response = await axios.get(url, { params });
                const responseData = response.data;
                if (response.status === 200 && responseData.Code === 200) {
                    if (responseData.Data && responseData.Data.state !== undefined) {
                        const status = responseData.Data.state;
                        logger.info(`尝试 ${attempts + 1}, 当前登录状态: ${status}, 剩余 ${countdown - attempts * 5}秒`);
                        if (status === 2) { // 登录成功
                            wxid = responseData.Data.wxid;
                            const newPass = responseData.Data.wxnewpass;
                            if (newPass && typeof newPass === 'string' && newPass.length > 0) {
                                logger.info(`登录成功, wxid: ${wxid}, 认证密钥已更新.`);
                                await wxDB.set('wx_authKey', newPass);
                                await wxDB.set('wx_wxid', wxid);
                                return true;
                            } else {
                                logger.error(`登录状态正常 (状态 2), 但wxnewpass无效或为空: '${newPass}'. 无法可靠建立会话.`);
                                return false;
                            }
                        } else if (status === -2) { // 二维码过期
                            logger.error("二维码已过期, 请重试.");
                            return false;
                        }
                    }
                } else if (responseData.Code === 300) {
                    logger.info(`尝试 ${attempts + 1}, 等待扫描... 剩余时间: ${countdown - attempts * 5}秒`);
                } else {
                    logger.warn(`检查登录状态失败: ${response.status}, ${JSON.stringify(responseData)}`);
                }
            } catch (error) {
                const errorMsg = error.isAxiosError && error.code === 'ECONNREFUSED'
                    ? `连接WeChatPadPro服务失败: ${error.message}`
                    : `检查登录状态时出错: ${error.message}`;
                logger.error(errorMsg);
            }
            if (isTerminating) {
                logger.info("适配器正在终止. 中止登录检查.");
                return false;
            }
            attempts++;
            if (attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 5000));
                if (isTerminating) {
                    logger.info("适配器正在终止. 中止登录检查.");
                    return false;
                }
            }
        }
        logger.warn("登录检查超过最大尝试次数, 退出检查.");
        return false;
    }
    async function checkOnlineStatus() {
        if (!authKey) {
            logger.info("无认证密钥, 设备视为离线.");
            return false;
        }
        const url = `http://${adapterConfig.url}/login/GetLoginStatus`;
        const params = { key: authKey };
        try {
            const response = await axios.get(url, { params });
            const responseData = response.data;
            if (response.status === 200 && responseData.Code === 200) {
                const loginState = responseData.Data && responseData.Data.loginState;
                if (loginState === 1) {
                    logger.info("WeChatPadPro 设备当前在线.");
                    return true;
                } else if (loginState === 3) {
                    logger.info("WeChatPadPro 设备不在线.");
                    return false;
                } else {
                    logger.error(`未知的在线状态: ${loginState}`);
                    return false;
                }
            } else if (response.status === 200 && responseData.Code === 300) {
                logger.info("WeChatPadPro 设备已登出.");
                return false;
            } else {
                logger.error(`检查在线状态失败: ${response.status}, ${JSON.stringify(responseData)}`);
                return false;
            }
        } catch (error) {
            const errorMsg = error.isAxiosError && error.code === 'ECONNREFUSED'
                ? `连接WeChatPadPro服务失败: ${error.message}`
                : `检查在线状态时出错: ${error.message}`;
            logger.error(errorMsg);
            return false;
        }
    }
    function connectWebsocket() {
        if (isTerminating) {
            logger.info("适配器正在终止, WebSocket连接在connectWebsocket入口处终止.");
            return null;
        }
        if (reconnectAttempts >= adapterConfig.maxReconnectAttempts) {
            logger.error(`已达到最大重连次数(${adapterConfig.maxReconnectAttempts}). 停止尝试连接.`);
            return null;
        }
        const wsUrl = `ws://${adapterConfig.url}/ws/GetSyncMsg?key=${authKey}`;
        const newWsInstance = new WebSocket(wsUrl);
        let pingTimeout;
        const heartbeat = () => {
            if (pingTimeout) {
                clearTimeout(pingTimeout);
                pingTimeout = null;
            }
            const timeoutDuration = (adapterConfig.activeMessagePoll ? adapterConfig.pollInterval * 1000 : 60 * 1000);
            pingTimeout = setTimeout(() => {
                logger.debug(`WebSocket PING 超时 (时长: ${timeoutDuration}ms), 终止连接.`);
                if (newWsInstance.readyState === WebSocket.OPEN) {
                    newWsInstance.terminate();
                }
            }, timeoutDuration);
        };
        newWsInstance.on('open', () => {
            logger.debug("WebSocket 连接成功.");
            reconnectAttempts = 0; // 重置重连计数
            if (reconnectTimeoutId) {
                clearTimeout(reconnectTimeoutId);
                reconnectTimeoutId = null;
            }
            heartbeat(); // 连接时启动心跳
        });
        newWsInstance.on('ping', () => {
            heartbeat(); // ping时重置超时
        });
        newWsInstance.on('message', async (message) => {
            heartbeat(); // 任何消息时重置超时
            // 在JSON解析前，将大整数字段处理为字符串
            const jsonStr = message.toString().replace(/"new_msg_id":\s*(\d+)/g, '"new_msg_id":"$1"');
            const data = JSON.parse(jsonStr, (key, value) => {
                if (key === 'new_msg_id' && typeof value === 'string') {
                    return BigInt(value); // 从字符串创建BigInt，避免精度损失
                }
                return value;
            });
            data.new_msg_id = data.new_msg_id.toString();
            await handleWebsocketMessage(data);
        });
        newWsInstance.on('close', (code, reason) => {
            if (pingTimeout) {
                clearTimeout(pingTimeout);
                pingTimeout = null;
            }
            logger.debug(`WebSocket 连接关闭. 代码: ${code}, 原因: ${reason ? reason.toString() : 'N/A'}`);
            if (ws === newWsInstance) ws = null;
            if (code !== 1000 && !isTerminating) { // 仅当此连接意外关闭且未终止时才安排重连
                reconnectAttempts++;
                logger.debug(`WebSocket 意外关闭, ${adapterConfig.reconnectInterval/1000}秒后尝试重连... (尝试 ${reconnectAttempts}/${adapterConfig.maxReconnectAttempts})`);
                if (reconnectTimeoutId) {
                    clearTimeout(reconnectTimeoutId);
                }
                reconnectTimeoutId = setTimeout(() => {
                    if (!isTerminating && !ws) {
                        const reconnectedWs = connectWebsocket();
                        if (reconnectedWs) {
                            ws = reconnectedWs;
                        }
                    } else if (isTerminating) {
                        logger.info("跳过计划的重连: 适配器正在终止.");
                    } else if (ws) {
                        logger.info("跳过计划的重连: WebSocket似乎已重新建立.");
                    }
                }, adapterConfig.reconnectInterval);
            } else if (isTerminating && reconnectTimeoutId) {
                clearTimeout(reconnectTimeoutId);
                reconnectTimeoutId = null;
            }
        });
        newWsInstance.on('error', (error) => {
            if (pingTimeout) {
                clearTimeout(pingTimeout);
                pingTimeout = null;
            }
            logger.error(`WebSocket 连接错误: ${error.message}. 请检查WeChatPadPro服务状态或尝试重启适配器.`);
        });
        return newWsInstance;
    }
    async function handleWebsocketMessage(message_str) {
        try {
            if (!message_str || typeof message_str !== 'object') { // 验证必要字段
                logger.warn(`WebSocket消息解析后不是对象: ${JSON.stringify(message_str)}`);
                return;
            }
            if (message_str.msg_id != null && message_str.from_user_name != null) {
                const processedMessage = await convertMessage(message_str);
                if (processedMessage) {
                    processedMessage.message_components = processedMessage.message_components.reverse();
                    logger.debug("处理后的消息对象:", JSON.stringify(processedMessage, null, 2));
                    const bncrMessage = {
                        userId: processedMessage.sender?.user_id || '',
                        userName: processedMessage.sender?.nickname || '',
                        groupId: processedMessage.type === "GROUP_MESSAGE" ? processedMessage.group_id.replace('@chatroom', '') : '0',
                        groupName: '',  // 可能需要获取实际群名
                        msg: processedMessage.message_str || '',
                        msgId: processedMessage.message_id || '',
                        fromType: `Social`,
                    };
                    wechatpadpro.receive(bncrMessage);
                }
            } else {
                logger.warn(`收到结构未知的WebSocket消息: ${JSON.stringify(message_str)}`);
            }
        } catch (e) {
            logger.error(`处理WebSocket消息时出错: ${e.message}`, e);
        }
    }
    async function convertMessage(rawMessage) {
        // 验证输入
        if (!rawMessage || typeof rawMessage !== 'object') {
            logger.warn('convertMessage收到无效的原始消息格式');
            return null;
        }
        let processedMessage = { raw_message: rawMessage };
        processedMessage.message_id = String(rawMessage.msg_id || '');
        processedMessage.timestamp = rawMessage.create_time || Math.floor((Date.now() + (8 * 60 * 60 * 1000)) / 1000);
        processedMessage.self_id = wxid;
        const fromUserName = rawMessage.from_user_name?.str || "";
        const toUserName = rawMessage.to_user_name?.str || "";
        const messageContent = rawMessage.content?.str || "";
        const pushContent = rawMessage.push_content || "";
        const messageType = rawMessage.msg_type;
        processedMessage.message_str = "";
        processedMessage.message_components = [];
        // 过滤自己发送的消息和系统消息
        if (fromUserName === wxid) {
            logger.debug("忽略自己发送的消息.");
            return null;
        }
        if (["weixin", "newsapp", "newsapp_wechat"].includes(fromUserName)) {
            logger.debug("忽略来自微信团队或新闻应用的消息.");
            return null;
        }
        // 处理聊天类型
        const chatTypeProcessed = await _processChatType(processedMessage, rawMessage, fromUserName, toUserName, messageContent, pushContent);
        if (!chatTypeProcessed) return null;
        // 处理消息内容 (添加类型验证)
        if (messageType !== undefined && messageType !== null) {
            await messageProcessor.processMessageContent(processedMessage, rawMessage, messageType, messageContent);
        } else {
            logger.warn('消息类型为空，跳过处理', {
                msgId: rawMessage.msg_id,
                fromUser: fromUserName,
                content: messageContent ? messageContent.substring(0, 50) + '...' : 'null'
            });
            processedMessage.message_str = '[消息类型未知]';
            processedMessage.message_components.push(createPlainComponent('[消息类型未知]'));
        }
        return processedMessage;
    }
    async function _processChatType(processedMsg, raw_message, from_user_name, to_user_name, content, push_content) {
        if (from_user_name === "weixin") return false;
        processedMsg.at_me = false;
        if (from_user_name.includes("@chatroom")) {
            processedMsg.type = "GROUP_MESSAGE";
            processedMsg.group_id = from_user_name;
            let sender_wxid_in_group = "";
            const colonIndex = content.indexOf(':');
            if (colonIndex > 0 && colonIndex < content.length - 1) {
                const potentialSenderId = content.substring(0, colonIndex);
                // 验证发送者ID
                if (!potentialSenderId.includes(' ') && !potentialSenderId.startsWith('<') &&
                    potentialSenderId.length > 0 && potentialSenderId.length < 64) {
                    sender_wxid_in_group = potentialSenderId;
                }
            }
            processedMsg.sender = { user_id: sender_wxid_in_group, nickname: "" };
            if (sender_wxid_in_group) {
                const accurate_nickname = await _getGroupMemberNickname(processedMsg.group_id, sender_wxid_in_group);
                if (accurate_nickname) {
                    processedMsg.sender.nickname = accurate_nickname;
                }
            }
            processedMsg.session_id = from_user_name;
            const msg_source = raw_message.msg_source || "";
            if (typeof msg_source === 'string' && msg_source.includes(wxid)) {
                processedMsg.at_me = true;
            }
            if (typeof raw_message.push_content === 'string' && raw_message.push_content.includes("在群聊中@了你")) {
                processedMsg.at_me = true;
            }
        } else {
            processedMsg.type = "FRIEND_MESSAGE";
            processedMsg.group_id = "";
            let nick_name = "";
            if (push_content && push_content.includes(" : ")) {
                nick_name = push_content.split(" : ")[0];
            }
            processedMsg.sender = { user_id: from_user_name, nickname: nick_name };
            processedMsg.session_id = from_user_name;
        }
        return true;
    }
    async function _getGroupMemberNickname(group_id, member_wxid) {
        if (member_wxid.includes('@chatroom')) {
            logger.debug(`member_wxid 包含 @chatroom，跳过查询: ${member_wxid}`);
            return null;
        }
        if (!group_id || !member_wxid) {
            logger.warn(`获取群成员昵称时参数无效: group_id=${group_id}, member_wxid=${member_wxid}`);
            return null;
        }
        const url = `http://${adapterConfig.url}/group/GetChatroomMemberDetail`;
        const params = { key: authKey };
        const payload = { ChatRoomName: group_id };
        try {
            const response = await axios.post(url, payload, { params });
            const response_data = response.data;
            if (response.status === 200 && response_data.Code === 200) {
                const member_list = response_data.Data?.member_data?.chatroom_member_list || [];
                for (const member of member_list) {
                    if (member.user_name === member_wxid) {
                        return member.nick_name || member_wxid;
                    }
                }
                logger.warn(`群 ${group_id} 中未找到成员 ${member_wxid} 的昵称`);
            } else {
                logger.error(`获取群成员详情失败: ${response.status}, ${JSON.stringify(response_data)}`);
            }
            return null;
        } catch (e) {
            logger.error(`获取群成员详情时出错: ${e.message}`);
            return null;
        }
    }
    async function _downloadMedia(mediaType, options) {
        if (!options || typeof options !== 'object') return logger.warn(`下载${mediaType === 'image' ? '图片' : '语音'}时参数无效: options=${JSON.stringify(options)}`);
        let url, payload;
        if (mediaType === 'image') {
            const { from_user_name, to_user_name, msg_id } = options;
            if (!from_user_name || !to_user_name || !msg_id) return logger.warn(`下载图片时参数无效: from=${from_user_name}, to=${to_user_name}, msgId=${msg_id}`);
            url = `http://${adapterConfig.url}/message/GetMsgBigImg`;
            payload = {
                CompressType: 0,
                FromUserName: from_user_name,
                MsgId: msg_id,
                Section: { DataLen: 61440, StartPos: 0 },
                ToUserName: to_user_name,
                TotalLen: 0,
            };
        } else if (mediaType === 'audio') {
            const { length, to_user_name, new_msg_id } = options;
            if (!length || !to_user_name || !new_msg_id) return logger.warn(`下载语音时参数无效: length=${length}, to=${to_user_name}, NewMsgId=${new_msg_id}`);
            url = `http://${adapterConfig.url}/message/GetMsgVoice`;
            payload = {
                Bufid: "0",
                Length: Number(length),
                NewMsgId: new_msg_id,
                ToUserName: to_user_name,
            };
        } else {
            return logger.warn(`不支持的媒体类型: ${mediaType}`);
        }
        const params = { key: authKey };
        try {
            const response = await axios.post(url, payload, { params });
            if (response.status === 200) {
                if (mediaType === 'image' && response.data?.Data?.Data?.Buffer) {
                    return response.data;
                } else if (mediaType === 'audio' && response.data?.Data?.Base64) {
                    return response.data;
                } else {
                    logger.error(`${mediaType === 'image' ? '图片' : '语音'}下载响应缺少数据: ${JSON.stringify(response.data)}`);
                    return null;
                }
            } else {
                return logger.error(`下载${mediaType === 'image' ? '图片' : '语音'}失败: ${response.status}`);
            }
        } catch (e) {
            return logger.error(`下载${mediaType === 'image' ? '图片' : '语音'}时出错: ${e.message}`);
        }
    }
    function parseGroupInviteMessage(contentStr) {
        try {
            // 提取template内容
            const templateMatch = contentStr.match(/<template><!\[CDATA\[(.*?)\]\]><\/template>/);
            if (!templateMatch) {
                return null;
            }
            const template = templateMatch[1];
            // 处理邀请加入群聊消息："$username$"邀请"$names$"加入了群聊
            if (template.includes('"$username$"邀请"$names$"加入了群聊')) {
                let result = template;
                const usernameMatch = contentStr.match(/<link name="username"[^>]*>([\s\S]*?)<\/link>/);
                if (usernameMatch) {
                    const userMatch = usernameMatch[1].match(/<username><!\[CDATA\[(.*?)\]\]><\/username>[\s\S]*?<nickname><!\[CDATA\[(.*?)\]\]><\/nickname>/);
                    if (userMatch) {
                        const displayName = userMatch[2] || userMatch[1]; // 优先nickname，否则username
                        result = result.replace(/\$username\$/g, displayName);
                    }
                }
                const namesMatch = contentStr.match(/<link name="names"[^>]*>([\s\S]*?)<\/link>/);
                if (namesMatch) {
                    const memberMatches = [...namesMatch[1].matchAll(/<member>[\s\S]*?<username><!\[CDATA\[(.*?)\]\]><\/username>[\s\S]*?<nickname><!\[CDATA\[(.*?)\]\]><\/nickname>[\s\S]*?<\/member>/g)];
                    const names = memberMatches.map(match => match[2] || match[1]).join('、'); // 优先nickname，否则username
                    result = result.replace(/\$names\$/g, names);
                }
                return result;
            }
            // 处理扫码加入群聊消息：包含$adder$的情况
            if (contentStr.includes('$adder$')) {
                const adderMatch = contentStr.match(/<link name="adder"[^>]*>[\s\S]*?<nickname><!\[CDATA\[(.*?)\]\]>[\s\S]*?<\/link>/);
                const fromMatch = contentStr.match(/<link name="from"[^>]*>[\s\S]*?<nickname><!\[CDATA\[(.*?)\]\]>[\s\S]*?<\/link>/);
                if (adderMatch && fromMatch) {
                    const adderName = adderMatch[1];
                    const fromName = fromMatch[1];
                    return `"${adderName}"通过扫描"${fromName}"分享的二维码加入群聊`;
                }
            }
            return null;
        } catch (error) {
            console.error('解析群系统消息时出错:', error);
            return null;
        }
    }
    function parsePatMessage(contentStr) {
        try {
            // 提取template内容
            const templateMatch = contentStr.match(/<template><!\[CDATA\[(.*?)\]\]><\/template>/);
            if (!templateMatch) {
                return null;
            }
            const template = templateMatch[1];
            // 提取用户信息
            const fromUserMatch = contentStr.match(/<fromusername>(.*?)<\/fromusername>/);
            const pattedUserMatch = contentStr.match(/<pattedusername>(.*?)<\/pattedusername>/);
            const patSuffixMatch = contentStr.match(/<patsuffix><!\[CDATA\[(.*?)\]\]><\/patsuffix>/);
            if (!fromUserMatch || !pattedUserMatch) {
                return template; // 如果无法提取用户信息，返回原始模板
            }
            const fromUser = fromUserMatch[1];
            const pattedUser = pattedUserMatch[1];
            const patSuffix = patSuffixMatch ? patSuffixMatch[1] : '';
            // 替换模板中的占位符
            let result = template;
            result = result.replace(/\$\{[^}]*\}/g, (match) => {
                const wxid = match.slice(2, -1); // 去掉 ${ 和 }
                if (wxid === fromUser) {
                    return fromUser; // 这里可以替换为昵称，目前使用原始wxid
                } else if (wxid === pattedUser) {
                    return pattedUser; // 这里可以替换为昵称，目前使用原始wxid
                }
                return match;
            });
            return result;
        } catch (error) {
            console.error('解析拍一拍消息时出错:', error);
            return null;
        }
    }
    async function terminate() {
        logger.info("终止 WeChatPadPro 适配器.");
        isTerminating = true; // 通知整个模块终止
        // 明确清除任何待定的重连超时
        if (reconnectTimeoutId) {
            clearTimeout(reconnectTimeoutId);
            reconnectTimeoutId = null;
            logger.info("清除待定的WebSocket重连尝试.");
        }
        if (ws) {
            logger.info("关闭活动的WebSocket连接.");
            ws.removeAllListeners(); // 关键: 防止'close'处理程序重新触发重连
            if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
                ws.terminate(); // 优雅地关闭WebSocket
            }
            ws = null; // 清除全局引用
        } else {
            logger.info("没有要终止的活动WebSocket连接.");
        }
        logger.info("WeChatPadPro 适配器终止序列完成.");
    }
    wechatpadpro.reply = async function (replyInfo) {
        logger.debug('wechatpadpro发送消息', replyInfo);
        try {
            if (!authKey) {
                logger.error("WeChatPadPro: 发送消息失败，认证密钥不存在");
                return '';
            }
            if (!replyInfo || typeof replyInfo !== 'object') {
                logger.error("WeChatPadPro: 发送消息失败，参数无效", replyInfo);
                return '';
            }
            // 确定接收者ID (群聊或私聊)
            const to_wxid = replyInfo.groupId && replyInfo.groupId !== '0' ?
                replyInfo.groupId : replyInfo.userId;
            if (!to_wxid) {
                logger.error("WeChatPadPro: 发送消息失败，接收者ID为空");
                return '';
            }
            const url = `http://${adapterConfig.url}/`;
            const params = { key: authKey };
            let endpoint = '';
            let payload = {};
            // 根据消息类型构造请求 (使用常量配置)
            switch (replyInfo.type) {
                case 'text':
                    // 处理换行符，确保适配微信客户端
                    endpoint = CONFIG.API_ENDPOINTS.SEND_TEXT;
                    payload = {
                        "MsgItem": [
                            {
                                "AtWxIDList": [""],
                                "ImageContent": "",
                                "MsgType": CONFIG.MESSAGE_TYPES.TEXT,
                                "TextContent": replyInfo.msg,
                                "ToUserName": to_wxid
                            }
                        ]
                    };
                    break;
                case 'image':
                    endpoint = CONFIG.API_ENDPOINTS.SEND_IMAGE;
                    payload = {
                        "MsgItem": [
                            {
                              "AtWxIDList": [""],
                              "ImageContent": replyInfo.path,
                              "MsgType": 0,
                              "TextContent": "",
                              "ToUserName": to_wxid
                            }
                          ]
                    };
                    break;
                case 'audio':
                    endpoint = CONFIG.API_ENDPOINTS.SEND_VOICE;
                    payload = {
                        "ToUserName": to_wxid,
                        "VoiceData": replyInfo.msg.base64,
                        "VoiceFormat": 4,
                        "VoiceSecond": replyInfo.msg.duration // 修复了逗号错误
                    };
                    break;
                case 'video':
                    endpoint = CONFIG.API_ENDPOINTS.SEND_VIDEO;
                    payload = {
                        ToUserName: to_wxid,
                        VideoUrl: replyInfo.path
                    };
                    break;
                case 'file':
                    endpoint = CONFIG.API_ENDPOINTS.SEND_FILE;
                    payload = {
                        ToUserName: to_wxid,
                        FilePath: replyInfo.path
                    };
                    break;
                default:
                    logger.warn(`WeChatPadPro: 不支持的消息类型: ${replyInfo.type}`);
                    return;
            }
            payload && await requestpad(url, endpoint, payload, params);
            return '';
        } catch (e) {
            logger.error('WeChatPadPro: 发送消息过程中发生异常', e);
            return '';
        }
    };
    wechatpadpro.push = async function (replyInfo) {
        return await this.reply(replyInfo);
    };
    wechatpadpro.delMsg = async function (msgId) {
        try {
            if (!authKey || !msgId) {
                logger.error("WeChatPadPro: 撤回消息失败，认证密钥不存在或消息ID为空");
                return false;
            }
            const url = `http://${adapterConfig.url}/message/RevokeMsg`;
            const params = { key: authKey };
            const payload = { MsgId: msgId };
            try {
                const response = await axios.post(url, payload, { params });
                const responseData = response.data;
                if (response.status === 200 && responseData.Code === 200) {
                    logger.info(`WeChatPadPro: 消息撤回成功, msgId=${msgId}`);
                    return true;
                } else {
                    logger.error(`WeChatPadPro: 消息撤回失败: ${response.status}, ${JSON.stringify(responseData)}`);
                    return false;
                }
            } catch (error) {
                const errorMsg = error.isAxiosError && error.code === 'ECONNREFUSED'
                    ? `连接WeChatPadPro服务失败: ${error.message}`
                    : `撤回消息时出错: ${error.message}`;
                logger.error(errorMsg);
                return false;
            }
        } catch (e) {
            logger.error('WeChatPadPro: 撤回消息过程中发生异常', e);
            return false;
        }
    };
    /**
     * 增强的请求函数
     * @param {string} url - 基础URL
     * @param {string} endpoint - API端点
     * @param {Object} payload - 请求载荷
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 响应数据
     */
    async function requestpad(url, endpoint, payload, params) {
        const startTime = Date.now();

        try {
            const requestConfig = {
                params,
                timeout: CONFIG.CONNECTION.REQUEST_TIMEOUT,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'BNCR-WeChatPadPro-Adapter/1.0.1'
                }
            };
            const response = await axios.post(url + endpoint, payload, requestConfig);
            // 性能监控
            const duration = Date.now() - startTime;
            logger.debug(`性能监控: API请求 ${endpoint} 耗时 ${duration}ms`);
            // 验证响应
            const apiError = errorHandler.handleApiResponse(response, `API请求 ${endpoint}`);
            if (apiError) {
                logger.error(`[${apiError.type}] ${apiError.message}`);
                return null;
            }
            logger.debug(`API请求成功: ${endpoint}`, response.data);
            return response.data;
        } catch (error) {
            const handledError = errorHandler.handleAxiosError(error, `API请求 ${endpoint}`);
            logger.error(`[${handledError.type}] ${handledError.message}`);
            return null;
        }
    }
    async function getContactList() {
        const url = `http://${adapterConfig.url}/friend/GetContactList`;
        const params = { key: authKey };
        const payload = { CurrentChatRoomContactSeq: 0, CurrentWxcontactSeq: 0 };
        try {
            const response = await axios.post(url, payload, { params });
            if (response.status !== 200) {
                logger.error(`获取联系人列表失败: ${response.status}`);
                return null;
            }
            const result = response.data;
            if (result.Code === 200 && result.Data) {
                return result.Data.ContactList?.contactUsernameList || [];
            } else {
                logger.error(`获取联系人列表失败: ${JSON.stringify(result)}`);
                return null;
            }
        } catch (e) {
            logger.error(`获取联系人列表时出错: ${e.message}`);
            return null;
        }
    }
    async function getContactDetailsList(room_wx_id_list = [], user_names = []) {
        const url = `http://${adapterConfig.url}/friend/GetContactDetailsList`;
        const params = { key: authKey };
        const payload = { RoomWxIDList: room_wx_id_list, UserNames: user_names };
        try {
            const response = await axios.post(url, payload, { params });
            if (response.status !== 200) {
                logger.error(`获取联系人详情列表失败: ${response.status}`);
                return null;
            }
            const result = response.data;
            if (result.Code === 200 && result.Data) {
                return result.Data.contactList || {};
            } else {
                logger.error(`获取联系人详情列表失败: ${JSON.stringify(result)}`);
                return null;
            }
        } catch (e) {
            logger.error(`获取联系人详情列表时出错: ${e.message}`);
            return null;
        }
    }
    function createPlainComponent(text) {
        return { type: 'Plain', text: text || '' };
    }
    function createImageComponent(base64Str) {
        return { type: 'Image', base64: base64Str || '' };
    }
    function createAudioComponent(base64Str) {
        return { type: 'Audio', base64: base64Str || '' };
    }
    function createWechatEmojiComponent({ md5, md5_len, cdnurl }) {
        return {
            type: 'WechatEmoji',
            md5: md5 || '',
            md5_len: md5_len || 0,
            cdnurl: cdnurl || ''
        };
    }
    async function formatContentToXml(content, isPrivateChat) {
        try {
            let msgStr = content;
            if (!isPrivateChat) {
                const separator = ":\n"; // 转义冒号和换行符
                const separatorIndex = msgStr.indexOf(separator);
                if (separatorIndex !== -1) {
                    const prefix = msgStr.substring(0, separatorIndex);
                    // 验证前缀是否是有效的发送者ID
                    if (!prefix.includes("<") && !prefix.includes(" ") &&
                        prefix.length > 0 && prefix.length < 64) {
                        msgStr = msgStr.substring(separatorIndex + separator.length);
                    }
                }
            }
            if (typeof msgStr !== 'string' || !msgStr.trim()) {
                logger.warn("[XML解析] 前缀处理后XML解析的消息字符串为空或无效.");
                return null;
            }
            return new Promise((resolve, reject) => {
                const parser = xmlParserSingleton.getInstance();
                parser.parseString(msgStr, (err, result) => {
                    if (err) {
                        logger.error(`[XML实际解析失败] 正在解析的内容: "${msgStr}", 错误: ${err.message}`, err);
                        reject(err);
                    } else {
                        resolve(result);
                    }
                });
            });
        } catch (e) {
            logger.error(`[XML解析失败] 内容: "${content}", 错误: ${e.message}`, e);
            throw e;
        }
    }
    async function parseReplyXml(xmlDoc, rawMessage, cachedTexts) {
        const components = [];
        const fromUserName = rawMessage.from_user_name?.str || "";
        const toUserName = rawMessage.to_user_name?.str || "";
        const msgId = rawMessage.msg_id || "";
        try {
            // 使用单例XML解析器
            if (!xmlDoc || !xmlDoc.msg) {
                components.push(createPlainComponent("[引用消息解析失败 - 无效XML结构]"));
                return components;
            }
            const appmsg = xmlDoc.msg.appmsg;
            if (!appmsg) {
                components.push(createPlainComponent("[引用消息解析失败 - 无appmsg]"));
                return components;
            }
            const refermsg = appmsg.refermsg;
            if (!refermsg) {
                const titleAsNewMsg = appmsg.title || "";
                if (titleAsNewMsg) {
                    components.push(createPlainComponent(titleAsNewMsg));
                } else {
                    components.push(createPlainComponent("[引用消息解析失败 - 无refermsg且无title]"));
                }
                return components;
            }
            const quoteType = parseInt(refermsg.type || "0", 10);
            const nickname = refermsg.displayname || "未知发送者";
            const quoteContent = refermsg.content || "";
            const svrid = refermsg.svrid;
            // 定义解析引用字符串的函数
            const parseQuoteString = async (qc) => {
                return new Promise((resolve, reject) => {
                    const parser = xmlParserSingleton.getInstance();
                    parser.parseString(qc, (err, result) => {
                        if (err) {
                            logger.error(`[parseQuoteString实际解析失败] 内容: "${qc}", 错误: ${err.message}`, err);
                            reject(err);
                        } else {
                            resolve(result);
                        }
                    });
                });
            };
            // 根据引用类型处理
            switch (quoteType) {
                case 1: // 文本引用
                    const quotedText = cachedTexts[String(svrid)] || quoteContent;
                    components.push(createPlainComponent(`[引用] ${nickname}: ${quotedText}`));
                    break;
                case 3: // 图片引用
                    const svrid_img = rawMessage.content?.str?.match(/<svrid>(.*?)<\/svrid>/)?.[1] ?? '没有找到 svrid';
                    const quotedImageResult = await findFileBySvrid(svrid_img, 'image');
                    if (quotedImageResult) {
                        components.push(createPlainComponent(`[引用] ${nickname}: [图片]-${quotedImageResult.filename}`));
                    } else {
                        logger.debug(`引用的图片(svrid: ${svrid_img})未找到`);
                        components.push(createPlainComponent(`[引用] ${nickname}: [图片 - 未能获取]`));
                    }
                    break;
                case 34: // 语音引用
                    const svrid_audio = rawMessage.content?.str?.match(/<svrid>(.*?)<\/svrid>/)?.[1] ?? '没有找到 svrid';
                    const quotedAudioResult = await findFileBySvrid(svrid_audio, 'audio');
                    if (quotedAudioResult) {
                        components.push(createPlainComponent(`[引用] ${nickname}: [语音]-${quotedAudioResult.filename}`));
                    } else {
                        logger.debug(`引用的语音(svrid: ${svrid_audio})未找到`);
                        components.push(createPlainComponent(`[引用] ${nickname}: [语音 - 未能获取]`));
                    }
                    break;
                case 43: // 普通视频引用
                    const match = quoteContent.match(/playlength="(\d+)"|^(\d+):/);
                    const videolength = match ? (match[1] || match[2]) : null;
                    if (videolength) {
                        components.push(createPlainComponent(`[引用] ${nickname}: [普通视频消息]-时长 ${videolength} 秒`));
                    } else {
                        logger.debug(`引用的普通视频消息(quoteContent: ${quoteContent})未找到`);
                        components.push(createPlainComponent(`[引用] ${nickname}: [普通视频消息]-时长未获取到`));
                    }
                    break;
                case 49: // 复杂引用类型
                    try {
                        if (quoteContent.trim().startsWith("<")) {
                            const nestedRoot = await parseQuoteString(quoteContent);
                            const nestedTitle = nestedRoot?.msg?.appmsg?.title || nestedRoot?.appmsg?.title || "";
                            if (nestedTitle) {
                                components.push(createPlainComponent(`[引用] ${nickname}: ${nestedTitle}`));
                            } else {
                                components.push(createPlainComponent(`[引用] ${nickname}: [复杂引用类型 - 查看原文]`));
                            }
                        } else {
                            components.push(createPlainComponent(`[引用] ${nickname}: ${quoteContent}`));
                        }
                    } catch (e) {
                        logger.warn(`[嵌套引用解析失败] svrid=${svrid} 内容: "${quoteContent}", err=${e.message}`);
                        components.push(createPlainComponent(`[引用] ${nickname}: [嵌套引用消息解析错误]`));
                    }
                    break;
                default:
                    logger.info(`[未知引用类型] quote_type=${quoteType}, svrid=${svrid}, content: ${quoteContent}`);
                    components.push(createPlainComponent(`[引用] ${nickname}: [${quoteContent || '不支持的引用类型'}]`));
                    break;
            }
            // 添加主要内容
            const title = appmsg.title || "";
            if (title) {
                components.push(createPlainComponent(title));
            } else if (components.length === 0) {
                components.push(createPlainComponent("[回复消息无主要内容]"));
            }
        } catch (e) {
            logger.error(`[parseReplyXml] 整体解析失败: ${e.message}`, e);
            components.length = 0;
            components.push(createPlainComponent("[引用消息整体解析失败]"));
        }
        return components;
    }
    async function parseEmojiXml(content, isPrivateChat) {
        try {
            const xmlDoc = await formatContentToXml(content, isPrivateChat);
            if (!xmlDoc || !xmlDoc.msg || !xmlDoc.msg.emoji) return null;
            const emojiElement = xmlDoc.msg.emoji;
            const md5 = emojiElement.md5;
            const len = emojiElement.len || emojiElement.md5_len;
            const cdnurl = emojiElement.cdnurl;
            if (md5 && len && cdnurl) {
                return createWechatEmojiComponent({
                    md5: md5,
                    md5_len: len,
                    cdnurl: cdnurl
                });
            } else {
                logger.warn("[parseEmojiXml] XML中缺少一些表情属性:", emojiElement);
                return null;
            }
        } catch (e) {
            logger.error(`[parseEmojiXml] 失败: ${e.message}`, e);
        }
        return null;
    }
    async function findFileBySvrid(svrid, type) {
        try {
            const dir = type === 'image' ? image_dir : audio_dir;
            const ext = type === 'image' ? 'png' : 'mp3';
            const fileType = type === 'image' ? '图片' : '语音';
            // 异步检查目录是否存在
            try {
                await fsPromises.access(dir);
            } catch {
                logger.warn(`${fileType}目录不存在: ${dir}`);
                return null;
            }
            // 异步读取目录中的所有文件
            const files = await fsPromises.readdir(dir);
            const matchingFile = files.find(file => file.includes(`${svrid}.${ext}`));
            if (!matchingFile) {
                logger.warn(`未找到svrid为${svrid}的${fileType}`);
                return null;
            }
            logger.debug(`找到${type === 'image' ? '图片' : '语音'}文件: ${matchingFile}`);
            return { filename: matchingFile }; // 只返回包含文件名的对象
        } catch (error) {
            logger.error(`查找${type === 'image' ? '图片' : '语音'}失败: ${error.message}`);
            return null;
        }
    }
    async function saveFile(base64Data, outputName, type = 'image') {
        if (!base64Data) {
            logger.warn(`保存${type === 'image' ? '图片' : '语音'}失败: base64数据为空`);
            return { success: false, error: 'base64数据为空' };
        }
        try {
            const dir = type === 'image' ? image_dir : audio_dir;
            const ext = type === 'image' ? 'png' : 'mp3';
            const filename = `${outputName}.${ext}`;
            const filePath = `${dir}/${filename}`;
            // 异步确保目录存在
            try {
                await fsPromises.access(dir);
            } catch {
                await fsPromises.mkdir(dir, { recursive: true });
            }
            if (type === 'image') {
                await fsPromises.writeFile(filePath, Buffer.from(base64Data, 'base64'));
                logger.debug(`图片已保存到: ${filePath}`);
            } else {
                const response = await axios.post(
                    'http://***************:8321/convert',
                    { base64_data: base64Data, format: 'mp3' },
                    { headers: {'Content-Type': 'application/json'}, responseType: 'arraybuffer' }
                );
                await fsPromises.writeFile(filePath, response.data);
                logger.debug(`语音已保存到: ${filePath}`);
            }
            return { success: true, filePath, filename };
        } catch (error) {
            const errorMsg = `保存${type === 'image' ? '图片' : '语音'}失败: ${error.message}`;
            logger.error(errorMsg);
            return { success: false, error: errorMsg };
        }
    }
    return wechatpadpro;
}