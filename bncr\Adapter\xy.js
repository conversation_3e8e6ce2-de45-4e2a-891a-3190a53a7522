/**
 * This file is part of the Bncr project.
 * <AUTHOR> Agent
 * @name xy
 * @team Bncr团队
 * @version 2.0.0
 * @description 闲鱼适配器 - 支持闲鱼平台消息收发和AI自动回复
 * @adapter true
 * @public false
 * @disable false
 * @priority 5
 * @classification ["官方适配器"]
 * @Copyright ©2025 Bncr团队. All rights reserved
 */

const fs = require('fs').promises;
const { existsSync } = require('fs');
const path = require('path');
const crypto = require('crypto');
const WebSocket = require('ws');
const axios = require('axios');
const sqlite3 = require('better-sqlite3');
const yaml = require('js-yaml');
const sharp = require('sharp');
const { OpenAI } = require('openai');
const FormData = require('form-data');
const { decode } = require("@msgpack/msgpack");

/* 配置构造器 */
const jsonSchema = BncrCreateSchema.object({
  enable: BncrCreateSchema.boolean()
    .setTitle('是否开启适配器')
    .setDescription('设置为关则不加载该适配器')
    .setDefault(false),
  cookiesStr: BncrCreateSchema.string()
    .setTitle('闲鱼Cookie字符串')
    .setDescription('从浏览器开发者工具中获取完整的Cookie字符串，用于身份认证')
    .setDefault(''),
  apiKey: BncrCreateSchema.string()
    .setTitle('AI服务API密钥')
    .setDescription('用于AI自动回复功能的API密钥')
    .setDefault(''),
  modelBaseUrl: BncrCreateSchema.string()
    .setTitle('AI模型服务地址')
    .setDescription('AI模型API的基础URL地址')
    .setDefault('https://dashscope.aliyuncs.com/compatible-mode/v1'),
  modelName: BncrCreateSchema.string()
    .setTitle('AI模型名称')
    .setDescription('使用的AI模型名称')
    .setDefault('qwen-max'),
  logLevel: BncrCreateSchema.string()
    .setTitle('日志级别')
    .setDescription('日志输出级别：DEBUG, INFO, WARN, ERROR')
    .setDefault('INFO'),
  heartbeatInterval: BncrCreateSchema.number()
    .setTitle('心跳间隔(秒)')
    .setDescription('WebSocket心跳检测间隔时间')
    .setDefault(15),
  heartbeatTimeout: BncrCreateSchema.number()
    .setTitle('心跳超时(秒)')
    .setDescription('心跳响应超时时间')
    .setDefault(10),
  tokenRefreshInterval: BncrCreateSchema.number()
    .setTitle('Token刷新间隔(秒)')
    .setDescription('访问令牌自动刷新间隔')
    .setDefault(3600),
  messageExpireTime: BncrCreateSchema.number()
    .setTitle('消息过期时间(毫秒)')
    .setDescription('忽略超过此时间的历史消息')
    .setDefault(300000),
  manualModeTimeout: BncrCreateSchema.number()
    .setTitle('人工接管超时(秒)')
    .setDescription('人工接管模式的超时时间')
    .setDefault(3600),
  toggleKeywords: BncrCreateSchema.string()
    .setTitle('人工接管切换关键词')
    .setDescription('触发人工接管模式的关键词')
    .setDefault('。'),
  agentsConfigPath: BncrCreateSchema.string()
    .setTitle('AI代理配置文件路径')
    .setDescription('agents_config.yaml文件的相对路径')
    .setDefault('./Adapter/agents_config.yaml'),
  enableAI: BncrCreateSchema.boolean()
    .setTitle('是否启用AI自动回复')
    .setDescription('开启后将根据配置自动回复消息')
    .setDefault(true)
});

/* 配置管理器 */
const ConfigDB = new BncrPluginConfig(jsonSchema);

module.exports = async () => {
  const fs = require('fs').promises;
  const { existsSync } = require('fs');
  const path = require('path');
  const crypto = require('crypto');
  const WebSocket = require('ws');
  const axios = require('axios');
  const sqlite3 = require('better-sqlite3');
  const yaml = require('js-yaml');
  const sharp = require('sharp');
  const { OpenAI } = require('openai');
  const FormData = require('form-data');
  const { decode } = require("@msgpack/msgpack");
  
  await ConfigDB.get();
  /* 如果用户未配置,userConfig则为空对象{} */
  if (!Object.keys(ConfigDB.userConfig).length) {
    sysMethod.startOutLogs('未配置闲鱼适配器,退出.');
    return;
  }
  if (!ConfigDB?.userConfig?.enable) return sysMethod.startOutLogs('未启用闲鱼适配器 退出.');

  // 检查必要配置
  if (!ConfigDB.userConfig.cookiesStr) {
    sysMethod.startOutLogs('未配置闲鱼Cookie字符串,退出.');
    return;
  }

  if (ConfigDB.userConfig.enableAI && !ConfigDB.userConfig.apiKey) {
    sysMethod.startOutLogs('启用AI功能但未配置API密钥,退出.');
    return;
  }

  const xy = new Adapter('xy');

  try {
    // 初始化闲鱼适配器
    const xianyuAdapter = new XianyuAdapter(ConfigDB);
    await xianyuAdapter.init();

    // 注入标准适配器方法
    xy.receive = xianyuAdapter.receive.bind(xianyuAdapter);
    xy.reply = xianyuAdapter.reply.bind(xianyuAdapter);
    xy.push = xianyuAdapter.push.bind(xianyuAdapter);
    xy.delMsg = xianyuAdapter.delMsg.bind(xianyuAdapter);

    // 保存适配器实例引用，用于清理
    xy._xianyuAdapter = xianyuAdapter;

    sysMethod.startOutLogs('闲鱼适配器初始化成功.');
    return xy;
  } catch (error) {
    sysMethod.startOutLogs(`闲鱼适配器初始化失败: ${error.message}`);
    return;
  }
};

// ==================== 工具函数模块 ====================

const sleep = ms => new Promise(res => setTimeout(res, ms));

function transCookies(cookiesStr) {
    const cookies = {};
    if (!cookiesStr) return cookies;

    cookiesStr.split('; ').forEach(cookie => {
        try {
            const parts = cookie.split('=');
            if (parts.length === 2) {
                cookies[parts[0]] = parts[1];
            }
        } catch (error) {
            // 忽略解析错误的cookie
        }
    });
    return cookies;
}

/**
 * 生成mid
 * @returns {string} mid字符串
 */
function generateMid() {
    const randomPart = Math.floor(1000 * Math.random());
    const timestamp = Date.now();
    return `${randomPart}${timestamp} 0`;
}

/**
 * 生成uuid
 * @returns {string} uuid字符串
 */
function generateUuid() {
    const timestamp = Date.now();
    return `-${timestamp}1`;
}

/**
 * 生成设备ID
 * @param {string} userId - 用户ID
 * @returns {string} 设备ID
 */
function generateDeviceId(userId) {
    const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    const result = [];

    for (let i = 0; i < 36; i++) {
        if ([8, 13, 18, 23].includes(i)) {
            result.push("-");
        } else if (i === 14) {
            result.push("4");
        } else if (i === 19) {
            const randVal = Math.floor(16 * Math.random());
            result.push(chars[(randVal & 0x3) | 0x8]);
        } else {
            const randVal = Math.floor(16 * Math.random());
            result.push(chars[randVal]);
        }
    }
    return result.join('') + "-" + userId;
}

/**
 * 生成签名
 * @param {string} t - 时间戳
 * @param {string} token - Token
 * @param {string} data - 数据
 * @returns {string} MD5签名
 */
function generateSign(t, token, data) {
    const appKey = "34839810";
    const msg = `${token}&${t}&${appKey}&${data}`;
    return crypto.createHash('md5').update(msg, 'utf8').digest('hex');
}

/**
 * 解密函数 - 使用 @msgpack/msgpack 库
 * @param {string} data - Base64编码的加密数据
 * @returns {string} 解密后的JSON字符串
 */
function decrypt(data) {
    try {
        // 1. Base64解码为Buffer
        const decodedBytes = Buffer.from(data, 'base64');
        // 2. 使用 @msgpack/msgpack 的 decode 方法解码Buffer
        const result = decode(decodedBytes, {
            // 可选：启用对大整数的支持，以防万一
            int64: "string",
        });
        // 3. 将解码后的JavaScript对象/值转换为JSON字符串
        //    BigInt 类型需要特殊处理
        return JSON.stringify(result, (key, value) =>
            typeof value === 'bigint' ? value.toString() : value
        );
    } catch (error) {
        logger.warn(`❌ 消息解码失败 (可能是非MessagePack格式): ${error.message}`);
        // 保留回退逻辑，以防数据不是有效的 MessagePack
        try {
            const decodedBytes = Buffer.from(data, 'base64');
            const textResult = decodedBytes.toString('utf-8');
            // 检查是否是看起来像JSON的文本
            if (textResult.startsWith('{') || textResult.startsWith('[')) {
                return JSON.stringify({ text: textResult });
            }
            const hexResult = decodedBytes.toString('hex');
            return JSON.stringify({ hex: hexResult, error: `Decode failed: ${error.message}` });
        } catch (fallbackError) {
             // 如果连Base64解码都失败，返回原始错误
            return JSON.stringify({ error: `Decrypt failed: ${fallbackError.message}`, raw_data: data });
        }
    }
}

// ==================== 日志系统配置 ====================

class Logger {
    constructor(level = 'INFO') {
        this.level = level.toUpperCase();
        this.levels = { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 };
    }
    setLevel(level) {
        this.level = level.toUpperCase();
    }
    _shouldLog(level) {
        return this.levels[level] >= this.levels[this.level];
    }
    _formatMessage(level, message, meta = {}) {
        const timestamp = new Date().toISOString().replace('T', ' ').slice(0, -5);
        const metaStr = Object.keys(meta).length ? JSON.stringify(meta) : '';
        return `${timestamp} | [闲鱼] ${level.padEnd(5)} | ${message} ${metaStr}`;
    }
    debug(message, meta) {
        if (this._shouldLog('DEBUG')) {
            console.log(this._formatMessage('DEBUG', message, meta));
        }
    }
    info(message, meta) {
        if (this._shouldLog('INFO')) {
            console.log(this._formatMessage('INFO', message, meta));
        }
    }
    warn(message, meta) {
        if (this._shouldLog('WARN')) {
            console.warn(this._formatMessage('WARN', message, meta));
        }
    }
    error(message, meta) {
        if (this._shouldLog('ERROR')) {
            console.error(this._formatMessage('ERROR', message, meta));
        }
    }
}
const logger = new Logger();
// ==================== 配置管理模块 ====================

class ConfigManager {
    constructor(configDB, agentsConfigPath = './Adapter/agents_config.yaml') {
        this.configDB = configDB;
        this.agentsConfigPath = this.validateAndNormalizePath(agentsConfigPath, ['.yaml', '.yml']);
        this.agentsCache = {};
        this.itemsCache = {};
        this.lastModified = 0;
        // 设置日志级别
        logger.setLevel(this.getLogLevel());
    }

    async init() {
        await this.loadAgentsConfig();
        logger.info('配置管理器初始化完成');
    }

    validateAndNormalizePath(filePath, allowedExtensions) {
        if (!filePath) {
            throw new Error('配置文件路径不能为空');
        }
        // 规范化路径
        const normalizedPath = path.normalize(filePath);
        // 检查路径遍历攻击
        if (normalizedPath.includes('..')) {
            throw new Error(`检测到路径遍历攻击模式: ${filePath}`);
        }
        // 检查绝对路径（只允许相对路径）
        if (path.isAbsolute(normalizedPath)) {
            throw new Error(`不允许使用绝对路径: ${filePath}`);
        }
        // 检查危险字符
        const dangerousChars = ['<', '>', '|', '*', '?', '"'];
        if (dangerousChars.some(char => normalizedPath.includes(char))) {
            throw new Error(`路径包含危险字符: ${filePath}`);
        }
        // 检查文件扩展名
        const fileExt = path.extname(normalizedPath).toLowerCase();
        if (normalizedPath.endsWith('.env') || allowedExtensions.includes(fileExt)) {
            logger.debug(`路径验证通过: ${filePath} -> ${normalizedPath}`);
            return normalizedPath;
        } else {
            throw new Error(`不支持的文件扩展名: ${fileExt}，允许的扩展名: ${allowedExtensions.join(', ')}`);
        }
    }



    async loadAgentsConfig() {
        try {
            if (existsSync(this.agentsConfigPath)) {
                const stats = await fs.stat(this.agentsConfigPath);
                const currentModified = stats.mtime.getTime();

                if (currentModified > this.lastModified) {
                    const configData = await fs.readFile(this.agentsConfigPath, 'utf8');
                    const parsedConfig = yaml.load(configData);

                    this.lastModified = currentModified;
                    this.validateAgentsConfig(parsedConfig);
                    this.agentsCache = parsedConfig.agents || {};
                    this.itemsCache = parsedConfig.items || {};

                    logger.info(`配置文件已重新加载 - Agents: ${Object.keys(this.agentsCache).length}, Items: ${Object.keys(this.itemsCache).length}`);
                }
            } else {
                logger.warn(`agents配置文件不存在: ${this.agentsConfigPath}，使用默认配置`);
                this.loadDefaultAgentsConfig();
            }
        } catch (error) {
            logger.error(`加载agents配置文件失败: ${error.message}`);
            this.loadDefaultAgentsConfig();
        }
    }

    validateAgentsConfig(configData) {
        try {
            if (typeof configData !== 'object' || configData === null) {
                throw new Error('agents配置文件格式错误：根节点必须是对象');
            }
            if (!configData.agents) {
                throw new Error('agents配置文件格式错误：缺少agents节点');
            }
            logger.debug('agents配置文件验证通过');
        } catch (error) {
            logger.error(`agents配置文件验证失败: ${error.message}`);
            this.loadDefaultAgentsConfig();
        }
    }

    loadDefaultAgentsConfig() {
        logger.warn('使用默认agents配置');
        this.agentsCache = {
            'default': {
                'enabled': true,
                'prompt': '默认回复提示词',
            },
            'image': {
                'enabled': true,
                'prompt': '默认图片识别提示词',
            }
        };
    }



    // 商品和Agent相关方法
    isAiEnabledForItem(itemId) {
        if (this.itemsCache[itemId]) {
            const itemConfig = this.itemsCache[itemId];
            return itemConfig.enabled || false;
        }
        return false;
    }

    getAgentsForItem(itemId) {
        if (this.itemsCache[itemId]) {
            const itemConfig = this.itemsCache[itemId];
            if (itemConfig.agents && Array.isArray(itemConfig.agents)) {
                return itemConfig.agents.filter(agent => agent.trim());
            }
        }
        return [];
    }

    getAgentConfig(agentName) {
        return this.agentsCache[agentName] || {};
    }

    getAgentType(agentName) {
        const agentConfig = this.getAgentConfig(agentName);
        return agentConfig.type || null;
    }

    getItemConfig(itemId) {
        return this.itemsCache[itemId] || {};
    }

    getAllConfiguredItems() {
        return Object.keys(this.itemsCache);
    }

    isAgentEnabled(agentName) {
        const agentConfig = this.getAgentConfig(agentName);
        return agentConfig.enabled || false;
    }

    getAgentPrompt(agentName) {
        const agentConfig = this.getAgentConfig(agentName);
        return agentConfig.prompt || '';
    }

    getEnabledAgents() {
        return Object.keys(this.agentsCache).filter(name =>
            this.agentsCache[name].enabled
        );
    }

    // 配置获取方法 - 从bncr配置中读取
    getCookiesStr() { return this.configDB.userConfig.cookiesStr || ''; }
    getApiKey() { return this.configDB.userConfig.apiKey || ''; }
    getModelBaseUrl() { return this.configDB.userConfig.modelBaseUrl || 'https://dashscope.aliyuncs.com/compatible-mode/v1'; }
    getModelName() { return this.configDB.userConfig.modelName || 'qwen-max'; }
    getLogLevel() { return this.configDB.userConfig.logLevel || 'INFO'; }
    getHeartbeatInterval() { return parseInt(this.configDB.userConfig.heartbeatInterval || 15); }
    getHeartbeatTimeout() { return parseInt(this.configDB.userConfig.heartbeatTimeout || 10); }
    getTokenRefreshInterval() { return parseInt(this.configDB.userConfig.tokenRefreshInterval || 3600); }
    getTokenRetryInterval() { return 300; } // 固定值，不需要配置
    getMessageExpireTime() { return parseInt(this.configDB.userConfig.messageExpireTime || 300000); }
    getManualModeTimeout() { return parseInt(this.configDB.userConfig.manualModeTimeout || 3600); }
    getToggleKeywords() { return this.configDB.userConfig.toggleKeywords || '。'; }
    getAgentsConfigPath() { return this.configDB.userConfig.agentsConfigPath || './Adapter/agents_config.yaml'; }
    getEnableAI() { return this.configDB.userConfig.enableAI !== false; }



    cleanup() {
        // 配置管理器清理工作（当前无需特殊清理）
    }
}

// ==================== 聊天上下文管理器 (直接数据库版本) ====================

class ChatContextManager {
    constructor(maxHistory = 100, dbPath = 'data/chat_history.db') {
        this.maxHistory = maxHistory;
        this.dbPath = dbPath;
        this.db = null;
        this.isInitialized = false;
    }

    async initDb() {
        try {
            if (this.isInitialized) return;

            // 确保数据目录存在
            const dbDir = path.dirname(this.dbPath);
            if (dbDir && !existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
            }

            // 初始化数据库
            this.db = new sqlite3(this.dbPath);

            // 设置WAL模式和同步模式
            this.db.pragma('journal_mode = WAL');
            this.db.pragma('synchronous = NORMAL');

            // 创建消息表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    item_id TEXT NOT NULL,
                    role TEXT NOT NULL,
                    content TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    chat_id TEXT
                )
            `);

            // 检查是否需要添加chat_id字段
            const columns = this.db.prepare("PRAGMA table_info(messages)").all();
            const hasChatId = columns.some(col => col.name === 'chat_id');

            if (!hasChatId) {
                this.db.exec('ALTER TABLE messages ADD COLUMN chat_id TEXT');
            }

            // 创建索引
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_chat_id ON messages (chat_id)');
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_timestamp ON messages (timestamp)');

            // 创建商品信息表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS items (
                    item_id TEXT PRIMARY KEY,
                    data TEXT NOT NULL,
                    price REAL,
                    description TEXT,
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

            this.isInitialized = true;
            logger.info(`聊天历史数据库初始化完成: ${this.dbPath}`);
        } catch (error) {
            logger.error(`数据库初始化失败: ${error.message}`);
            throw error;
        }
    }

    async saveItemInfo(itemId, itemData) {
        try {
            if (!this.isInitialized) await this.initDb();

            const priceValue = itemData.soldPrice || 0;
            const price = (typeof priceValue === 'string' && priceValue === '价格未知') ? 0.0 : parseFloat(priceValue);
            const description = itemData.desc || '';
            const dataJson = JSON.stringify(itemData);
            const timestamp = new Date().toISOString();

            const stmt = this.db.prepare(`
                INSERT INTO items (item_id, data, price, description, last_updated)
                VALUES (?, ?, ?, ?, ?)
                ON CONFLICT(item_id)
                DO UPDATE SET data = ?, price = ?, description = ?, last_updated = ?
            `);

            stmt.run(itemId, dataJson, price, description, timestamp, dataJson, price, description, timestamp);
            logger.debug(`商品信息已保存: ${itemId}`);
        } catch (error) {
            logger.error(`保存商品信息时出错: ${error.message}`);
            throw error;
        }
    }

    async getItemInfo(itemId) {
        try {
            if (!this.isInitialized) await this.initDb();

            const stmt = this.db.prepare('SELECT data FROM items WHERE item_id = ?');
            const result = stmt.get(itemId);
            return result ? JSON.parse(result.data) : null;
        } catch (error) {
            logger.error(`获取商品信息时出错: ${error.message}`);
            return null;
        }
    }

    async addMessageByChat(chatId, userId, itemId, role, content) {
        try {
            if (!this.isInitialized) await this.initDb();

            const timestamp = new Date().toISOString();

            // 插入新消息
            const insertStmt = this.db.prepare(`
                INSERT INTO messages (user_id, item_id, role, content, timestamp, chat_id)
                VALUES (?, ?, ?, ?, ?, ?)
            `);
            insertStmt.run(userId, itemId, role, content, timestamp, chatId);

            // 清理旧消息，保持最大历史记录数
            const oldestStmt = this.db.prepare(`
                SELECT id FROM messages WHERE chat_id = ?
                ORDER BY timestamp DESC LIMIT 1 OFFSET ?
            `);
            const oldestResult = oldestStmt.get(chatId, this.maxHistory);

            if (oldestResult) {
                const deleteStmt = this.db.prepare('DELETE FROM messages WHERE chat_id = ? AND id < ?');
                deleteStmt.run(chatId, oldestResult.id);
            }
        } catch (error) {
            logger.error(`添加消息到数据库时出错: ${error.message}`);
            throw error;
        }
    }

    async getContextByChat(chatId) {
        try {
            if (!this.isInitialized) await this.initDb();

            const stmt = this.db.prepare(`
                SELECT role, content FROM messages WHERE chat_id = ?
                ORDER BY timestamp ASC LIMIT ?
            `);
            const rows = stmt.all(chatId, this.maxHistory);
            return rows.map(row => ({ role: row.role, content: row.content }));
        } catch (error) {
            logger.error(`获取对话历史时出错: ${error.message}`);
            return [];
        }
    }

    close() {
        if (this.db) {
            try {
                this.db.close();
                this.db = null;
                this.isInitialized = false;
                logger.debug('数据库连接已关闭');
            } catch (error) {
                logger.error(`关闭数据库时出错: ${error.message}`);
            }
        }
    }
}

// ==================== AI回复机器人模块 ====================

class BaseAgent {
    constructor(client, configManager, agentName) {
        this.client = client;
        this.configManager = configManager;
        this.agentName = agentName;
        this.loadConfig();
    }

    loadConfig() {
        this.systemPrompt = this.configManager.getAgentPrompt(this.agentName);
    }

    reloadConfig() {
        this.loadConfig();
        logger.info(`Agent ${this.agentName} 配置已重新加载`);
    }

    buildMessages(userMsg, itemDesc, context) {
        const systemPromptContent = `【商品信息】${itemDesc}\n${this.systemPrompt}`;
        const messages = [{ role: 'system', content: systemPromptContent }];
        messages.push(...context);
        messages.push({ role: 'user', content: userMsg });
        return messages;
    }

    async callLlm(messages) {
        const response = await this.client.chat.completions.create({
            model: this.configManager.getModelName(),
            messages: messages,
        });
        return response.choices[0].message.content;
    }

    async generate(userMsg, itemDesc, context) {
        const messages = this.buildMessages(userMsg, itemDesc, context);
        const response = await this.callLlm(messages);
        return response;
    }
}

class ImageAgent extends BaseAgent {
    constructor(client, configManager, agentName) {
        super(client, configManager, agentName);
    }

    async analyzeImage(imagePath, itemId = null) {
        try {
            const imageBase64 = await this.imageToBase64(imagePath);
            if (!imageBase64) {
                return '抱歉，图片处理失败，请重新发送';
            }
            const prompt = this.systemPrompt;
            const messages = [
                {
                    role: 'user',
                    content: [
                        { type: 'text', text: prompt },
                        { type: 'image_url', image_url: { url: `data:image/jpeg;base64,${imageBase64}` } }
                    ]
                }
            ];
            const response = await this.callLlm(messages);
            return response;
        } catch (error) {
            logger.error(`图片分析失败: ${error.message}`);
            return '抱歉，图片分析遇到问题，请稍后再试';
        }
    }

    async imageToBase64(imagePath) {
        try {
            const imageBuffer = await fs.readFile(imagePath);

            // 使用sharp处理图片，确保格式正确并压缩大小
            const processedImage = await sharp(imageBuffer)
                .resize(1024, 1024, { fit: 'inside', withoutEnlargement: true })
                .jpeg({ quality: 85 })
                .toBuffer();
            return processedImage.toString('base64');
        } catch (error) {
            logger.error(`图片转换base64失败: ${error.message}`);
            return null;
        }
    }
}

class XianyuReplyBot {
    constructor(configManager) {
        this.configManager = configManager;
        this.reinitClient();
        this.initAgents();
    }

    reinitClient() {
        this.client = new OpenAI({
            apiKey: this.configManager.getApiKey(),
            baseURL: this.configManager.getModelBaseUrl(),
        });
        logger.info('✅ OpenAI客户端已使用最新配置重新初始化。');
    }

    createAgent(agentName) {
        const agentConfig = this.configManager.getAgentConfig(agentName);
        const agentType = agentConfig.type;
        if (agentType === 'image') {
            logger.info(`创建图片Agent: ${agentName}`);
            return new ImageAgent(this.client, this.configManager, agentName);
        } else if (agentType === 'text') {
            logger.info(`创建文本Agent: ${agentName} (BaseAgent)`);
            return new BaseAgent(this.client, this.configManager, agentName);
        } else {
            logger.warn(`Agent '${agentName}' 的类型 '${agentType}' 未知或未定义，将作为通用Agent创建。`);
            return new BaseAgent(this.client, this.configManager, agentName);
        }
    }

    initAgents() {
        this.agents = {};
        const enabledAgents = this.configManager.getEnabledAgents();
        logger.info(`开始初始化Agent，发现 ${enabledAgents.length} 个启用的Agent: ${enabledAgents.join(', ')}`);
        for (const agentName of enabledAgents) {
            try {
                this.agents[agentName] = this.createAgent(agentName);
                logger.info(`✅ 成功初始化Agent: ${agentName}`);
            } catch (error) {
                logger.error(`❌ 初始化Agent失败: ${agentName}, 错误: ${error.message}`);
                this.agents[agentName] = new BaseAgent(this.client, this.configManager, agentName);
                logger.warn(`⚠️  ${agentName} 回退到BaseAgent`);
            }
        }
        // 确保至少有一个默认Agent
        if (Object.keys(this.agents).length === 0) {
            logger.warn('没有启用的Agent，创建默认Agent');
            this.agents['default'] = new BaseAgent(this.client, this.configManager, 'default');
        }
        logger.info(`已初始化Agent: ${Object.keys(this.agents).join(', ')}`);
    }



    async generateReply(userMsg, itemDesc, context, itemId = null) {
        const agentNamesForItem = this.configManager.getAgentsForItem(itemId);
        if (agentNamesForItem.length === 0) {
            logger.debug(`商品 ${itemId} 未配置agents，不使用AI回复`);
            return null;
        }
        // 寻找第一个启用的、类型为'text'的agent
        for (const agentName of agentNamesForItem) {
            if (this.agents[agentName] && this.configManager.getAgentType(agentName) === 'text') {
                const selectedAgent = this.agents[agentName];
                logger.info(`为商品 ${itemId} 的文本消息选择了Agent: '${agentName}'`);
                return await selectedAgent.generate(userMsg, itemDesc, context);
            }
        }
        logger.info(`商品 ${itemId} 的配置中未找到可用的文本Agent，不回复。`);
        return null;
    }

    async generateImageAnalysis(imagePath, itemId) {
        const agentNamesForItem = this.configManager.getAgentsForItem(itemId);
        if (agentNamesForItem.length === 0) {
            logger.debug(`商品 ${itemId} 未配置agents，不分析图片`);
            return null;
        }
        // 寻找第一个启用的、类型为'image'的agent
        for (const agentName of agentNamesForItem) {
            if (this.agents[agentName] && this.configManager.getAgentType(agentName) === 'image') {
                const selectedAgent = this.agents[agentName];
                if (selectedAgent instanceof ImageAgent) {
                    logger.info(`为商品 ${itemId} 的图片消息选择了Agent: '${agentName}'`);
                    return await selectedAgent.analyzeImage(imagePath);
                } else {
                    logger.warn(`Agent '${agentName}' 类型为 'image' 但不是ImageAgent实例，跳过。`);
                }
            }
        }
        logger.info(`商品 ${itemId} 的配置中未找到可用的图片Agent，不分析。`);
        return null;
    }
}

// ==================== 风控管理器 ====================

class RateLimitManager {
    constructor() {
        this.blockedUntil = 0;
        this.consecutiveFailures = 0;
        this.lastRequestTime = 0;
        this.minInterval = 1.0; // 最小请求间隔（秒）
    }

    isBlocked() {
        return Date.now() < this.blockedUntil;
    }

    getWaitTime() {
        if (this.isBlocked()) {
            return (this.blockedUntil - Date.now()) / 1000;
        }
        // 计算基于上次请求的最小间隔
        const timeSinceLast = (Date.now() - this.lastRequestTime) / 1000;
        if (timeSinceLast < this.minInterval) {
            return this.minInterval - timeSinceLast;
        }
        return 0;
    }

    checkRateLimitError(errorMessages) {
        const rateLimitKeywords = [
            '被挤爆啦',
            '请稍后重试',
            'FAIL_SYS_USER_VALIDATE',
            'RGV587_ERROR',
            '系统繁忙',
            '访问频繁',
            '请求过于频繁'
        ];
        const errorText = errorMessages.join(' ');
        return rateLimitKeywords.some(keyword => errorText.includes(keyword));
    }

    recordSuccess() {
        this.consecutiveFailures = 0;
        this.lastRequestTime = Date.now();
        // 成功后可以适当减少阻塞时间
        if (this.blockedUntil > Date.now()) {
            this.blockedUntil = Math.max(Date.now(), this.blockedUntil - 30000);
        }
    }

    recordFailure(errorMessages = []) {
        this.consecutiveFailures += 1;
        this.lastRequestTime = Date.now();
        // 如果是风控错误，实施更严格的退避策略
        if (errorMessages.length > 0 && this.checkRateLimitError(errorMessages)) {
            // 指数退避：基础时间 * 2^失败次数，最大不超过1小时
            const backoffTime = Math.min(60 * Math.pow(2, Math.min(this.consecutiveFailures - 1, 6)), 3600) * 1000;
            this.blockedUntil = Date.now() + backoffTime;
            logger.warn(`🚫 检测到API风控，实施退避策略，阻塞 ${backoffTime / 1000} 秒`);
        } else {
            // 普通错误，较短的退避时间
            const backoffTime = Math.min(5 * this.consecutiveFailures, 60) * 1000;
            this.blockedUntil = Date.now() + backoffTime;
            logger.info(`⏳ API请求失败，退避 ${backoffTime / 1000} 秒`);
        }
    }

    async waitIfNeeded() {
        const waitTime = this.getWaitTime();
        if (waitTime > 0) {
            logger.info(`⏰ 风控管理器：等待 ${waitTime.toFixed(1)} 秒后继续请求`);
            await sleep(1000);
        }
    }
}

// ==================== 闲鱼API类 ====================

class XianyuApis {
    constructor(configManager = null) {
        this.configManager = configManager;
        this.rateLimitManager = new RateLimitManager();
        // 创建简单的axios实例，手动管理Cookie
        this.axiosInstance = axios.create({
            timeout: 30000,
            headers: {
                'accept': 'application/json',
                'accept-language': 'zh-CN,zh;q=0.9',
                'cache-control': 'no-cache',
                'origin': 'https://www.goofish.com',
                'pragma': 'no-cache',
                'priority': 'u=1, i',
                'referer': 'https://www.goofish.com/',
                'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }
        });
        // 添加请求拦截器来自动添加Cookie
        this.axiosInstance.interceptors.request.use((config) => {
            config.headers.Cookie = this.getCookieString();
            return config;
        });
        // 添加响应拦截器来自动更新Cookie
        this.axiosInstance.interceptors.response.use((response) => {
            this.updateCookiesFromResponse(response);
            return response;
        }, (error) => {
            if (error.response) {
                this.updateCookiesFromResponse(error.response);
            }
            return Promise.reject(error);
        });
        this.cookies = {};
    }

    updateCookies(cookiesStr) {
        this.cookies = transCookies(cookiesStr);
        logger.debug(`已更新 ${Object.keys(this.cookies).length} 个Cookie`);
    }

    updateCookiesFromResponse(response) {
        try {
            const setCookieHeaders = response.headers['set-cookie'];
            if (setCookieHeaders && Array.isArray(setCookieHeaders)) {
                setCookieHeaders.forEach(cookieStr => {
                    // 解析Set-Cookie头
                    const parts = cookieStr.split(';')[0].split('=');
                    if (parts.length === 2) {
                        const name = parts[0].trim();
                        const value = parts[1].trim();
                        if (value && value !== this.cookies[name]) {
                            this.cookies[name] = value;
                            // 对_m_h5_tk字段提供详细日志
                            if (name === '_m_h5_tk') logger.debug(`🔑 从响应中获取到新的_m_h5_tk: ${value.substring(0, 20)}...`);
                        }
                    }
                });
            }
        } catch (error) {
            logger.warn(`从响应更新Cookie失败: ${error.message}`);
        }
    }

    checkCookieStatus() {
        // 检查关键Cookie字段的状态
        const criticalFields = ['_m_h5_tk', 'unb', 'cookie2', 'XSRF-TOKEN', 'cna'];
        criticalFields.forEach(field => {
            if (this.cookies[field]) {
                if (field === '_m_h5_tk') {
                    logger.debug(`🔑 关键字段 ${field} 当前值: ${this.cookies[field].substring(0, 20)}...`);
                } else {
                    logger.debug(`关键Cookie字段 ${field} 存在`);
                }
            } else if (field === '_m_h5_tk') {
                logger.warn(`⚠️ 关键字段 ${field} 缺失！`);
            }
        });
    }

    getCookieString() {
        return Object.entries(this.cookies)
            .filter(([key, value]) => value) // 过滤空值
            .map(([key, value]) => `${key}=${value}`)
            .join('; ');
    }

    async updateEnvCookies() {
        try {
            const cookieStr = this.getCookieString();
            if (!this.configManager) {
                logger.warn('ConfigManager未初始化，无法更新cookies配置');
                return;
            }
            const envPath = this.configManager.envPath;
            if (!existsSync(envPath)) {
                logger.warn(`.env文件不存在: ${envPath}，无法更新COOKIES_STR`);
                return;
            }
            // 读取当前.env文件
            let envContent = await fs.readFile(envPath, 'utf8');
            // 更新COOKIES_STR
            if (envContent.includes('COOKIES_STR=')) {
                envContent = envContent.replace(/COOKIES_STR=.*/, `COOKIES_STR=${cookieStr}`);
            } else {
                envContent = envContent.trim() + `\nCOOKIES_STR=${cookieStr}\n`;
            }
            // 写回.env文件
            await fs.writeFile(envPath, envContent, 'utf8');
            logger.info('已更新.env文件中的COOKIES_STR');
            // 重新加载.env文件
            this.configManager.loadEnvConfig();
        } catch (error) {
            logger.warn(`更新.env文件失败: ${error.message}`);
        }
    }

    async hasLogin(retryCount = 0) {
        if (retryCount >= 2) {
            logger.error('Login检查失败，重试次数过多');
            return false;
        }
        try {
            const url = 'https://passport.goofish.com/newlogin/hasLogin.do';
            const params = new URLSearchParams({
                appName: 'xianyu',
                fromSite: '77'
            });
            const data = new URLSearchParams({
                hid: this.cookies.unb || '',
                ltl: 'true',
                appName: 'xianyu',
                appEntrance: 'web',
                _csrf_token: this.cookies['XSRF-TOKEN'] || '',
                umidToken: '',
                hsiz: this.cookies.cookie2 || '',
                bizParams: 'taobaoBizLoginFrom=web',
                mainPage: 'false',
                isMobile: 'false',
                lang: 'zh_CN',
                returnUrl: '',
                fromSite: '77',
                isIframe: 'true',
                documentReferer: 'https://www.goofish.com/',
                defaultView: 'hasLogin',
                umidTag: 'SERVER',
                deviceId: this.cookies.cna || ''
            });
            const response = await this.axiosInstance.post(`${url}?${params}`, data, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });
            const resJson = response.data;
            if (resJson?.content?.success) {
                logger.debug('Login成功');
                this.checkCookieStatus();
                return true;
            } else {
                logger.warn(`Login失败: ${JSON.stringify(resJson)}`);
                await sleep(500);
                return await this.hasLogin(retryCount + 1);
            }
        } catch (error) {
            logger.error(`Login请求异常: ${error.message}`);
            await sleep(500);
            return await this.hasLogin(retryCount + 1);
        }
    }

    async getNewH5Token() {
        try {
            logger.debug('正在获取新的_m_h5_tk...');
            // 保存当前的_m_h5_tk用于比较
            const currentH5tk = this.cookies._m_h5_tk;
            // 清空当前的_m_h5_tk，强制获取新的
            delete this.cookies._m_h5_tk;
            // 访问一个会生成新_m_h5_tk的API端点
            const response = await this.axiosInstance.get('https://h5api.m.goofish.com/h5/mtop.common.getTimestamp/1.0/', {
                params: {
                    jsv: '2.7.2',
                    appKey: '34839810',
                    t: Date.now().toString(),
                    sign: '',
                    v: '1.0',
                    type: 'originaljson',
                    dataType: 'json'
                }
            });
            // 检查响应中是否有新的_m_h5_tk
            logger.debug(`API响应状态: ${response.status}`);
            // 检查是否获取到了新的_m_h5_tk
            if (this.cookies._m_h5_tk && this.cookies._m_h5_tk !== currentH5tk) {
                logger.debug(`✅ 成功获取新的_m_h5_tk: ${this.cookies._m_h5_tk.substring(0, 20)}...`);
                return true;
            } else {
                logger.warn('⚠️ 未能获取到新的_m_h5_tk');
                // 如果没有获取到，尝试恢复原来的值
                if (currentH5tk) {
                    this.cookies._m_h5_tk = currentH5tk;
                    logger.debug('已恢复原来的_m_h5_tk');
                }
                return false;
            }
        } catch (error) {
            logger.error(`获取新_m_h5_tk失败: ${error.message}`);
            return false;
        }
    }

    async refreshSession() {
        try {
            logger.debug('正在刷新session...');
            // 首先尝试获取新的_m_h5_tk
            const h5tkSuccess = await this.getNewH5Token();
            if (h5tkSuccess) return true;
            // 如果获取_m_h5_tk失败，尝试访问主页
            const response = await this.axiosInstance.get('https://www.goofish.com/', {
                headers: {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Cache-Control': 'max-age=0'
                }
            });
            logger.debug('主页访问成功，session已刷新');
            this.checkCookieStatus();
            return true;
        } catch (error) {
            logger.error(`所有session刷新方法都失败: ${error.message}`);
            return false;
        }
    }

    async _makeApiRequest({ apiName, apiVersion, data, retries = 3 }) {
        if (this.rateLimitManager.isBlocked()) {
            const waitTime = this.rateLimitManager.getWaitTime();
            logger.warn(`🚫 API被风控阻塞中，还需等待 ${waitTime.toFixed(1)} 秒`);
            return { error: `API被风控阻塞，请等待 ${Math.ceil(waitTime)} 秒` };
        }
        for (let attempt = 0; attempt < retries; attempt++) {
            await this.rateLimitManager.waitIfNeeded();
            const t = Date.now().toString();
            const dataVal = JSON.stringify(data);
            const token = (this.cookies._m_h5_tk || '').split('_')[0];
            const params = new URLSearchParams({
                jsv: '2.7.2',
                appKey: '34839810',
                t: t,
                sign: generateSign(t, token, dataVal),
                api: apiName,
                v: apiVersion,
                type: 'originaljson',
                dataType: 'json',
            });
            try {
                const url = `https://h5api.m.goofish.com/h5/${apiName}/${apiVersion}/`;
                const response = await this.axiosInstance.post(url, `data=${encodeURIComponent(dataVal)}`, {
                    params: Object.fromEntries(params),
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
                });
                const resJson = response.data;
                if (resJson?.ret?.some(ret => ret.includes('SUCCESS'))) {
                    this.rateLimitManager.recordSuccess();
                    return resJson; // 成功，返回结果
                }
                const errorMessages = resJson?.ret || [];
                logger.warn(`API ${apiName} 调用失败 (尝试 ${attempt + 1}): ${JSON.stringify(errorMessages)}`);
                this.rateLimitManager.recordFailure(errorMessages);

                if (this.rateLimitManager.checkRateLimitError(errorMessages)) {
                     const waitTime = this.rateLimitManager.getWaitTime();
                    return { error: `API被风控，请等待 ${Math.ceil(waitTime)} 秒后重试` };
                }
            } catch (error) {
                logger.error(`API ${apiName} 请求异常 (尝试 ${attempt + 1}): ${error.message}`);
                this.rateLimitManager.recordFailure();
            }
            // 等待一小段时间再重试
            if (attempt < retries - 1) {
                await sleep(2000 * (attempt + 1));
            }
        }
        logger.error(`获取 ${apiName} 失败，已达最大重试次数`);
        return { error: `获取 ${apiName} 失败，重试次数过多` };
    }

    async getToken(deviceId) {
        // 在获取token之前检查并刷新session
        logger.debug(`刷新session`);
        await this.refreshSession();
        if (!(await this.hasLogin())) {
            logger.error('🔴 登录失败，请更新Cookie后重启');
            process.exit(1);
        }
        const result = await this._makeApiRequest({
            apiName: 'mtop.taobao.idlemessage.pc.login.token',
            apiVersion: '1.0',
            data: {
                appKey: '444e9908a51d1cb236a27862abc769c9',
                deviceId: deviceId
            }
        });
        if (result && !result.error) {
            logger.info('Token获取成功');
        } else {
            logger.warn(`Token API调用失败: ${JSON.stringify(result)}`);
        }
        return result;
    }

    // --- 重构：getItemInfo 方法 ---
    async getItemInfo(itemId) {
        const result = await this._makeApiRequest({
            apiName: 'mtop.taobao.idle.pc.detail',
            apiVersion: '1.0',
            data: { itemId: itemId }
        });
        if (result && !result.error) {
            logger.debug(`商品信息获取成功: ${itemId}`);
        } else {
            logger.warn(`商品信息API调用失败: ${JSON.stringify(result)}`);
        }
        return result;
    }

    async downloadImage(imageUrl, saveDir = 'images') {
        try {
            // 确保保存目录存在
            await fs.mkdir(saveDir, { recursive: true });
            // 生成唯一文件名
            const timestamp = Date.now();
            const hash = crypto.createHash('md5').update(imageUrl).digest('hex').substring(0, 8);
            const fileName = `${timestamp}_${hash}.jpg`;
            const filePath = path.join(saveDir, fileName);
            // 下载图片
            const response = await axios.get(imageUrl, {
                responseType: 'arraybuffer',
                timeout: 30000
            });
            // 保存图片
            await fs.writeFile(filePath, response.data);
            logger.info(`图片下载成功: ${filePath}`);
            return filePath;
        } catch (error) {
            logger.error(`图片下载失败: ${error.message}`);
            return null;
        }
    }

    async uploadImageDirect(filePath) {
        try {
            if (!existsSync(filePath)) {
                logger.error(`图片文件不存在: ${filePath}`);
                return null;
            }
            const uploadUrl = 'https://stream-upload.goofish.com/api/upload.api';
            const form = new FormData();
            form.append('uploadImg', fs.createReadStream(filePath), {
                filename: path.basename(filePath),
                contentType: 'image/jpeg'
            });
            const response = await axios.post(uploadUrl, form, {
                params: {
                    floderId: '0',
                    appkey: 'xy_chat',
                    _input_charset: 'utf-8'
                },
                headers: {
                    ...form.getHeaders(),
                    'Cookie': this.getCookieString()
                },
                timeout: 30000
            });
            const resJson = response.data;
            if (resJson?.success) {
                const imgData = resJson.object;
                if (!imgData || !imgData.url) {
                    logger.error(`图片上传成功，但服务器返回的数据无效: ${JSON.stringify(resJson)}`);
                    return null;
                }
                const pixStr = imgData.pix || '0x0';
                let width = 0, height = 0;
                try {
                    [width, height] = pixStr.split('x').map(p => parseInt(p));
                } catch {
                    width = height = 0;
                }
                const result = {
                    url: imgData.url,
                    height: height,
                    width: width,
                    size: parseInt(imgData.size || 0)
                };
                logger.info(`图片直传成功: ${result.url}`);
                return result;
            } else {
                logger.error(`图片上传API返回失败状态: ${JSON.stringify(resJson)}`);
                return null;
            }
        } catch (error) {
            logger.error(`图片上传失败: ${error.message}`);
            return null;
        }
    }
}

// ==================== 主要的闲鱼Live类 ====================

class XianyuLive {
    constructor(bot, configManager, contextManager) {
        this.configManager = configManager;
        this.xianyu = new XianyuApis(configManager);
        this.bot = bot;
        // 从 configManager 实时获取 cookies 并更新
        const cookiesStr = this.configManager.getCookiesStr();
        this.cookies = transCookies(cookiesStr);
        this.xianyu.updateCookies(cookiesStr);
        this.myid = this.cookies.unb || 'unknown_user';
        this.deviceId = generateDeviceId(this.myid);
        this.contextManager = contextManager;
        this.baseUrl = 'wss://wss-goofish.dingtalk.com/';
        // 状态管理
        this.lastTokenRefreshTime = 0;
        this.currentToken = null;
        this.ws = null;
        this.heartbeatTimer = null;
        this.tokenRefreshTimer = null;
        this.connectionRestartFlag = false;
        this.lastHeartbeatTime = 0;
        this.lastHeartbeatResponse = 0;
        this.lastActivityTime = 0;
        // 人工接管相关 - 注意：移除下面这行
        this.manualModeConversations = new Set();
        this.manualModeTimestamps = new Map(); // 添加这行来存储时间戳
        // 关闭事件
        this.shutdownFlag = false;
    }

    async refreshToken() {
        try {
            logger.info('开始刷新token...');
            const tokenResult = await this.xianyu.getToken(this.deviceId);

            if (tokenResult && tokenResult.data && tokenResult.data.accessToken) {
                this.currentToken = tokenResult.data.accessToken;
                this.lastTokenRefreshTime = Date.now();
                logger.info('Token刷新成功');
                // logger.info('正在将最新的Cookie状态回写到.env文件...');
                // await this.xianyu.updateEnvCookies();
                return this.currentToken;
            } else {
                logger.error(`Token刷新失败: ${JSON.stringify(tokenResult)}`);
                return null;
            }
        } catch (error) {
            logger.error(`Token刷新异常: ${error.message}`);
            return null;
        }
    }

    async tokenRefreshLoop() {
        const scheduleNext = async (delay = 60000) => {
            if (this.shutdownFlag) return;
            this.tokenRefreshTimer = setTimeout(async () => {
                try {
                    const currentTime = Date.now();
                    const tokenRefreshInterval = this.configManager.getTokenRefreshInterval();
                    if (currentTime - this.lastTokenRefreshTime >= tokenRefreshInterval * 1000) {
                        logger.info('Token即将过期，准备刷新...');
                        const newToken = await this.refreshToken();

                        if (newToken) {
                            logger.info('Token刷新成功，准备重新建立连接...');
                            this.connectionRestartFlag = true;
                            if (this.ws) {
                                this.ws.close();
                            }
                            return; // 退出循环
                        } else {
                            const tokenRetryInterval = this.configManager.getTokenRetryInterval();
                            logger.error(`Token刷新失败，将在${tokenRetryInterval / 60}分钟后重试`);
                            await scheduleNext(tokenRetryInterval * 1000);
                            return;
                        }
                    }
                    await scheduleNext(); // 继续下一次检查
                } catch (error) {
                    logger.error(`Token刷新循环出错: ${error.message}`);
                    await scheduleNext(); // 出错后继续
                }
            }, delay);
        };
        await scheduleNext();
    }

    async sendMsg(ws, cid, toid, text) {
        const textContent = {
            contentType: 1,
            text: { text: text }
        };
        const textBase64 = Buffer.from(JSON.stringify(textContent)).toString('base64');
        const msg = {
            lwp: '/r/MessageSend/sendByReceiverScope',
            headers: { mid: generateMid() },
            body: [
                {
                    uuid: generateUuid(),
                    cid: `${cid}@goofish`,
                    conversationType: 1,
                    content: {
                        contentType: 101,
                        custom: {
                            type: 1,
                            data: textBase64
                        }
                    },
                    redPointPolicy: 0,
                    extension: { extJson: '{}' },
                    ctx: { appVersion: '1.0', platform: 'web' },
                    mtags: {},
                    msgReadStatusSetting: 1
                },
                {
                    actualReceivers: [
                        `${toid}@goofish`,
                        `${this.myid}@goofish`
                    ]
                }
            ]
        };
        ws.send(JSON.stringify(msg));
        this.lastActivityTime = Date.now();
        logger.info(`发送消息到会话 ${cid}: ${text}`);
    }

    async sendImage(ws, cid, toid, imagePath) {
        const uploadResult = await this.xianyu.uploadImageDirect(imagePath);
        if (!uploadResult) {
            logger.error('图片上传失败，无法发送。');
            await this.sendMsg(ws, cid, toid, '[图片发送失败]');
            return;
        }
        try {
            const dataInnerJson = {
                contentType: 2,
                image: {
                    pics: [
                        {
                            height: uploadResult.height,
                            width: uploadResult.width,
                            type: 0,
                            url: uploadResult.url
                        }
                    ]
                }
            };
            const imageInfoJsonStr = JSON.stringify(dataInnerJson);
            const imageInfoBase64 = Buffer.from(imageInfoJsonStr).toString('base64');
            const msg = {
                lwp: '/r/MessageSend/sendByReceiverScope',
                headers: { mid: generateMid() },
                body: [
                    {
                        uuid: generateUuid(),
                        cid: `${cid}@goofish`,
                        conversationType: 1,
                        content: {
                            contentType: 101,
                            custom: {
                                type: 2,
                                data: imageInfoBase64
                            }
                        },
                        redPointPolicy: 0,
                        extension: { extJson: '{}' },
                        ctx: { appVersion: '1.0', platform: 'web' },
                        mtags: {},
                        msgReadStatusSetting: 1
                    },
                    {
                        actualReceivers: [`${toid}@goofish`, `${this.myid}@goofish`]
                    }
                ]
            };
            ws.send(JSON.stringify(msg));
            this.lastActivityTime = Date.now();
            logger.info(`已向会话 ${cid} 发送图片，URL: ${uploadResult.url}`);
        } catch (error) {
            logger.error(`构建或发送图片消息时出错: ${error.message}`);
        }
    }

    async init(ws) {
        // 如果没有token或者token过期，获取新token
        if (!this.currentToken || (Date.now() - this.lastTokenRefreshTime) >= this.tokenRefreshInterval * 1000) {
            logger.info('获取初始token...');
            await this.refreshToken();
        }

        if (!this.currentToken) {
            logger.error('无法获取有效token，初始化失败');
            throw new Error('Token获取失败');
        }
        const msg = {
            lwp: '/reg',
            headers: {
                'cache-header': 'app-key token ua wv',
                'app-key': '444e9908a51d1cb236a27862abc769c9',
                'token': this.currentToken,
                'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 DingTalk(2.1.5) OS(Windows/10) Browser(Chrome/*********) DingWeb/2.1.5 IMPaaS DingWeb/2.1.5',
                'dt': 'j',
                'wv': 'im:3,au:3,sy:6',
                'sync': '0,0;0;0;',
                'did': this.deviceId,
                'mid': generateMid()
            }
        };
        ws.send(JSON.stringify(msg));
        this.lastActivityTime = Date.now();
        await sleep(1000);
        const currentTime = Date.now();
        const syncMsg = {
            lwp: '/r/SyncStatus/ackDiff',
            headers: { mid: '5701741704675979 0' },
            body: [
                {
                    pipeline: 'sync',
                    tooLong2Tag: 'PNM,1',
                    channel: 'sync',
                    topic: 'sync',
                    highPts: 0,
                    pts: currentTime * 1000,  // 修复：应该是毫秒时间戳 * 1000，不是 * 1000000
                    seq: 0,
                    timestamp: currentTime
                }
            ]
        };
        ws.send(JSON.stringify(syncMsg));
        logger.info('✅ 连接注册完成');
    }

    // 消息类型判断方法
    isChatMessage(message) {
        try {
            const contentType = message?.['1']?.['6']?.['3']?.['4'];
            // 支持字符串和数字类型
            return contentType === 1 || contentType === '1';
        } catch {
            return false;
        }
    }

    isImageMessage(message) {
        try {
            const contentType = message?.['1']?.['6']?.['3']?.['4'];
            // 支持字符串和数字类型
            return contentType === 2 || contentType === '2';
        } catch {
            return false;
        }
    }

    isTradeCardMessage(message) {
        try {
            const contentType = message?.['1']?.['6']?.['3']?.['4'];
            // 支持字符串和数字类型
            return contentType === 26 || contentType === '26' ||
                   contentType === 14 || contentType === '14';
        } catch {
            return false;
        }
    }

    isSyncPackage(messageData) {
        try {
            return (
                messageData &&
                typeof messageData === 'object' &&
                messageData.body &&
                messageData.body.syncPushPackage &&
                messageData.body.syncPushPackage.data &&
                messageData.body.syncPushPackage.data.length > 0
            );
        } catch {
            return false;
        }
    }

    isTypingStatus(message) {
        try {
            return (
                message &&
                typeof message === 'object' &&
                message['1'] &&
                Array.isArray(message['1']) &&
                message['1'].length > 0 &&
                typeof message['1'][0] === 'object' &&
                message['1'][0]['1'] &&
                typeof message['1'][0]['1'] === 'string' &&
                message['1'][0]['1'].includes('@goofish')
            );
        } catch {
            return false;
        }
    }

    isSystemMessage(message) {
        try {
            return (
                message &&
                typeof message === 'object' &&
                message['3'] &&
                typeof message['3'] === 'object' &&
                message['3'].needPush === 'false'
            );
        } catch {
            return false;
        }
    }

    isSessionArousalMessage(message) {
        try {
            // 这类消息最可靠的特征是 operation.content 中包含 sessionArouse 对象
            return !!(message?.operation?.content?.sessionArouse);
        } catch {
            return false;
        }
    }

    // 人工接管模式相关方法
    checkToggleKeywords(message) {
        const messageStripped = message.trim();
        // 直接从 configManager 获取最新配置
        const toggleKeywords = this.configManager.getToggleKeywords();
        return toggleKeywords.includes(messageStripped);
    }

    isManualMode(chatId) {
        if (!this.manualModeConversations.has(chatId)) return false;
        const currentTime = Date.now();
        if (this.manualModeTimestamps.has(chatId)) {
            const timestamp = this.manualModeTimestamps.get(chatId);
            // 直接从 configManager 获取最新配置
            const manualModeTimeout = this.configManager.getManualModeTimeout(); // 实时获取
            if (currentTime - timestamp > manualModeTimeout * 1000) {
                this.exitManualMode(chatId);
                return false;
            }
        }
        return true;
    }

    enterManualMode(chatId) {
        this.manualModeConversations.add(chatId);
        this.manualModeTimestamps.set(chatId, Date.now());
    }

    exitManualMode(chatId) {
        this.manualModeConversations.delete(chatId);
        this.manualModeTimestamps.delete(chatId);
    }

    toggleManualMode(chatId) {
        if (this.isManualMode(chatId)) {
            this.exitManualMode(chatId);
            return 'auto';
        } else {
            this.enterManualMode(chatId);
            return 'manual';
        }
    }

    async handleChatMessage(websocket, chatId, sendUserId, itemId, sendMessage) {
        try {
            // 检查是否处于人工接管模式
            if (this.isManualMode(chatId)) {
                logger.debug(`🔴 会话 ${chatId} 处于人工接管模式，跳过自动回复`);
                return;
            }
            // 检查该商品是否启用AI回复
            if (!this.configManager.isAiEnabledForItem(itemId)) {
                logger.debug(`💬 商品 ${itemId} 未启用AI，消息已记录但不进行自动回复`);
                return;
            }
            // 获取商品信息
            let itemInfo = await this.contextManager.getItemInfo(itemId);
            if (!itemInfo) {
                logger.info(`从API获取商品信息: ${itemId}`);
                try {
                    const apiResult = await this.xianyu.getItemInfo(itemId);
                    if (apiResult && typeof apiResult === 'object' && apiResult.data) {
                        const itemData = apiResult.data;
                        itemInfo = {
                            desc: itemData.desc || '商品描述未知',
                            soldPrice: itemData.soldPrice || '价格未知'
                        };
                        // 保存到数据库
                        await this.contextManager.saveItemInfo(itemId, itemInfo);
                        logger.info(`商品信息获取并保存成功: ${itemId}`);
                    } else {
                        logger.warn(`API返回数据格式异常: ${JSON.stringify(apiResult)}`);
                        itemInfo = {
                            desc: '商品信息获取失败',
                            soldPrice: '价格未知'
                        };
                    }
                } catch (error) {
                    logger.error(`获取商品信息API调用异常: ${error.message}`);
                    itemInfo = {
                        desc: '商品信息获取失败',
                        soldPrice: '价格未知'
                    };
                }
            } else {
                logger.info(`从数据库获取商品信息: ${itemId}`);
            }
            // 构建商品描述
            const itemDescription = `${itemInfo.desc};当前商品售卖价格为:${String(itemInfo.soldPrice)}`;

            // 获取完整的对话上下文
            const context = await this.contextManager.getContextByChat(chatId);
            // 生成AI回复
            const botReply = await this.bot.generateReply(sendMessage, itemDescription, context, itemId);
            if (botReply) {
                await this.contextManager.addMessageByChat(chatId, this.myid, itemId, 'assistant', botReply);
                await this.sendMsg(websocket, chatId, sendUserId, botReply);
            }
        } catch (error) {
            logger.error(`处理聊天消息时发生错误: ${error.message}`);
            // 发生错误时发送友好的错误消息
            await this.sendMsg(websocket, chatId, sendUserId, '抱歉，处理您的消息时遇到问题，请稍后再试');
        }
    }

    async handleImageMessage(websocket, chatId, sendUserId, itemId, imageUrl) {
        try {
            if (!this.configManager.isAiEnabledForItem(itemId)) {
                logger.info(`💬 商品 ${itemId} 未启用AI，图片消息已记录但不进行识别`);
                return;
            }
            logger.info(`🖼️ 商品 ${itemId} 已启用AI，开始分析图片...`);
            const imagePath = await this.xianyu.downloadImage(imageUrl);
            if (!imagePath) {
                await this.sendMsg(websocket, chatId, sendUserId, '抱歉，图片处理失败，请重新发送。');
                return;
            }
            // 使用新的路由逻辑分析图片
            const response = await this.bot.generateImageAnalysis(imagePath, itemId);
            // 清理临时图片
            try {
                await fs.promises.unlink(imagePath);
                logger.debug(`已删除临时图片文件: ${imagePath}`);
            } catch (error) {
                logger.warn(`删除临时图片文件失败: ${error.message}`);
            }
            if (response) {
                await this.sendMsg(websocket, chatId, sendUserId, response);
                await this.contextManager.addMessageByChat(chatId, sendUserId, itemId, 'assistant', `[图片分析结果] ${response}`);
            } else {
                logger.info(`商品 ${itemId} 未找到合适的图片Agent或不需回复。`);
            }
        } catch (error) {
            logger.error(`处理图片消息时发生错误: ${error.message}`);
            await this.sendMsg(websocket, chatId, sendUserId, '抱歉，图片处理遇到问题，请稍后再试。');
        }
    }

    async sendHeartbeat(ws) {
        try {
            const heartbeatMid = generateMid();
            const heartbeatMsg = {
                lwp: '/!',
                headers: { mid: heartbeatMid }
            };
            ws.send(JSON.stringify(heartbeatMsg));
            const sendTime = Date.now();
            this.lastHeartbeatTime = sendTime;
            this.lastActivityTime = sendTime;
            logger.debug(`💓 心跳包已发送 (${new Date(sendTime).toLocaleTimeString()}) - MID: ${heartbeatMid}`);
            return heartbeatMid;
        } catch (error) {
            logger.error(`❌ 发送心跳包失败: ${error.message}`);
            throw error;
        }
    }

    async heartbeatLoop(ws) {
        const scheduleNext = async (delay = 1000) => {
            if (this.shutdownFlag) return;
            this.heartbeatTimer = setTimeout(async () => {
                try {
                    const currentTime = Date.now();
                    // 直接从 configManager 获取最新配置
                    const heartbeatInterval = this.configManager.getHeartbeatInterval();
                    // 发送心跳
                    if (currentTime - this.lastActivityTime >= heartbeatInterval * 1000) {
                        await this.sendHeartbeat(ws);
                    }
                    // 直接从 configManager 获取最新配置
                    const heartbeatTimeout = this.configManager.getHeartbeatTimeout();
                    const responseTimeout = currentTime - this.lastHeartbeatResponse;
                    const maxTimeout = (heartbeatInterval + heartbeatTimeout) * 1000;
                    if (responseTimeout > maxTimeout) {
                        logger.warn('心跳响应超时，可能连接已断开');
                        logger.debug(`响应超时时间: ${responseTimeout / 1000}s, 最大允许: ${maxTimeout / 1000}s`);
                        // 在原代码中，这里也缺少 return，我们加上以确保逻辑正确性
                        if (this.ws) this.ws.close(1006, 'Heartbeat timeout');
                        return; // 退出循环
                    }
                    await scheduleNext(); // 继续下一次检查
                } catch (error) {
                    logger.error(`心跳循环出错: ${error.message}`);
                    if (this.ws) this.ws.close(1011, 'Heartbeat loop error');
                    return; // 出错时退出循环
                }
            }, delay);
        };
        await scheduleNext();
    }

    handleHeartbeatResponse(messageData) {
        try {
            if (
                messageData &&
                typeof messageData === 'object' &&
                messageData.headers &&
                messageData.headers.mid &&
                messageData.code === 200
            ) {
                if (!messageData.body || !messageData.body.syncPushPackage) {
                    const responseTime = Date.now();
                    const latency = responseTime - this.lastHeartbeatTime;
                    this.lastHeartbeatResponse = responseTime;
                    logger.debug(`💓 收到心跳响应 (${new Date(responseTime).toLocaleTimeString()}) - MID: ${messageData.headers.mid}, 延迟: ${latency}ms`);
                    return true;
                }
            }
        } catch (error) {
            logger.error(`处理心跳响应出错: ${error.message}`);
            logger.debug(`心跳响应消息格式: ${JSON.stringify(messageData)}`);
        }
        return false;
    }

    _parseSyncMessage(syncData) {
        if (!syncData?.data) {
            logger.debug('❌ 同步数据中没有data字段');
            return null;
        }
        try {
            const data = syncData.data;
            // 优先尝试JSON解析，因为它更常见且快速
            const utf8Data = Buffer.from(data, 'base64').toString('utf-8');
            const message = JSON.parse(utf8Data);
            // 简单验证一下是否是我们期望的聊天消息结构
            if (message && (message.chatType || message.operation || message['1'])) {
                return message;
            }
            throw new Error("Not a standard chat message, try msgpack");
        } catch (e) {
            try {
                // 如果JSON解析失败或结构不符，再尝试MessagePack
                const decryptedData = decrypt(syncData.data);
                return JSON.parse(decryptedData);
            } catch (error) {
                logger.error(`❌ 消息解密/解析彻底失败: ${error.message}`);
                logger.debug(`原始数据: ${syncData.data.substring(0, 100)}...`);
                return null;
            }
        }
    }

    // +++ 新增：消息路由私有方法 +++
    _routeMessage(message, websocket) {
        // 使用可选链让代码更安全简洁
        const isChat = this.isChatMessage(message);
        const isImage = this.isImageMessage(message);
        const isTradeCard = this.isTradeCardMessage(message);
        if (isChat || isImage || isTradeCard) {
            this._handleUserMessage(message, websocket, { isChat, isImage, isTradeCard });
            return;
        }
        const isSystem = this.isSystemMessage(message);
        const isTyping = this.isTypingStatus(message);
        const isSessionArousal = this.isSessionArousalMessage(message);
        if (isSystem || isTyping || isSessionArousal) {
            logger.debug(`🔧 忽略系统事件 (System: ${isSystem}, Typing: ${isTyping}, Arousal: ${isSessionArousal})`);
            return;
        }
        // 可以增加对订单状态等其他消息的处理
        if (message?.['3']?.redReminder) {
            const userId = message['1']?.split('@')[0] || '未知用户ID';
            logger.info(`订单状态 for ${userId}: ${message['3'].redReminder}`);
        }
    }

    // +++ 新增：处理用户消息的私有方法 +++
    async _handleUserMessage(message, websocket, { isChat, isImage, isTradeCard }) {
        const contentBlock = message?.['1']?.['10'];
        const createTime = parseInt(message?.['1']?.['5']);
        const chatId = message?.['1']?.['2']?.split('@')[0];
        // 早期返回，减少嵌套
        if (!contentBlock || !chatId) return;
        // 检查消息是否过期
        if (Date.now() - createTime > this.configManager.getMessageExpireTime()) {
            logger.debug(`⏳ 过期消息丢弃，会话: ${chatId}`);
            return;
        }
        const sendUserName = contentBlock.reminderTitle;
        const sendUserId = contentBlock.senderUserId;
        const urlInfo = contentBlock.reminderUrl;
        const itemId = urlInfo?.split('itemId=')[1]?.split('&')[0];
        if (!itemId) {
            logger.warn('❌ 无法从URL中提取商品ID', { url: urlInfo });
            return;
        }
        // 处理自己发送的消息（用于切换模式）
        if (sendUserId === this.myid) {
            if (isChat && this.checkToggleKeywords(contentBlock.reminderContent)) {
                 const mode = this.toggleManualMode(chatId);
                 const replyText = mode === 'manual' ? '已切换到人工客服模式' : '已恢复自动回复模式';
                 logger.info(`🎛️ 会话 ${chatId} 切换到 ${mode} 模式`);
                 await this.sendMsg(websocket, chatId, sendUserId, replyText);
            }
            return;
        }
        // 处理收到的消息
        if (isChat) {
            const sendMessage = contentBlock.reminderContent;
            logger.info(`用户: ${sendUserName}, 商品: ${itemId}, 会话: ${chatId}, 消息: ${sendMessage}`);
            await this.contextManager.addMessageByChat(chatId, sendUserId, itemId, 'user', sendMessage);
            await this.handleChatMessage(websocket, chatId, sendUserId, itemId, sendMessage);
        } else if (isImage) {
            const extData = JSON.parse(message['1']['6']['3']['5'] || '{}');
            const imageUrl = extData?.image?.pics?.[0]?.url;
            if (imageUrl) {
                logger.info(`用户: ${sendUserName}, 商品: ${itemId}, 会话: ${chatId}, 收到图片: ${imageUrl}`);
                await this.handleImageMessage(websocket, chatId, sendUserId, itemId, imageUrl);
            } else {
                logger.warn('❌ 图片消息缺少URL');
            }
        } else if (isTradeCard) {
            logger.info(`📦 收到交易卡片 - 用户: ${sendUserName}, 商品: ${itemId}, 会话: ${chatId}`);
        }
    }

    async handleMessage(messageData, websocket) {
        try {
            // 1. 发送ACK
            const ack = { code: 200, headers: { mid: messageData.headers?.mid, sid: messageData.headers?.sid }};
            websocket.send(JSON.stringify(ack));
            // 2. 检查是否为同步包
            if (!this.isSyncPackage(messageData)) return;
            // 3. 解析消息
            const message = this._parseSyncMessage(messageData.body.syncPushPackage.data[0]);
            if (!message) return;
            // 4. 路由消息
            this._routeMessage(message, websocket);
        } catch (error) {
            logger.error(`处理顶层消息时发生错误: ${error.message}`);
            logger.debug(`原始消息: ${JSON.stringify(messageData)}`);
        }
    }

    async shutdown() {
        if (this.shutdownFlag) return;
        logger.info('🔴 开始关闭程序...');
        this.shutdownFlag = true;
        // 清理定时器
        if (this.heartbeatTimer) {
            clearTimeout(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
        if (this.tokenRefreshTimer) {
            clearTimeout(this.tokenRefreshTimer);
            this.tokenRefreshTimer = null;
        }
        // 关闭WebSocket连接
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            logger.info('正在关闭WebSocket连接...');
            this.ws.close(1000, 'Client shutdown');
        }
        // 清理配置管理器
        if (this.configManager) {
            this.configManager.cleanup();
        }
        // 关闭数据库连接
        if (this.contextManager) {
            this.contextManager.close();
        }
        logger.info('✅ 程序已成功关闭。');
    }

    async main() {
        while (!this.shutdownFlag) {
            try {
                this.connectionRestartFlag = false;
                const currentCookiesStr = this.configManager.getCookiesStr();
                const headers = {
                    'Cookie': currentCookiesStr,
                    'Host': 'wss-goofish.dingtalk.com',
                    'Connection': 'Upgrade',
                    'Pragma': 'no-cache',
                    'Cache-Control': 'no-cache',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Origin': 'https://www.goofish.com',
                    'Accept-Encoding': 'gzip, deflate, br, zstd',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                };
                logger.info('正在连接WebSocket...');
                this.ws = new WebSocket(this.baseUrl, { headers });
                // 设置WebSocket事件处理
                this.ws.on('open', async () => {
                    try {
                        logger.info('WebSocket连接已建立');
                        await this.init(this.ws);
                        this.lastHeartbeatTime = Date.now();
                        this.lastHeartbeatResponse = Date.now();
                        this.lastActivityTime = Date.now();
                        // 启动心跳循环
                        this.heartbeatLoop(this.ws).catch(error => {
                            logger.error(`心跳循环异常: ${error.message}`);
                        });
                        // 启动token刷新循环
                        this.tokenRefreshLoop().catch(error => {
                            logger.error(`Token刷新循环异常: ${error.message}`);
                        });
                        logger.info('❤ 连接成功，开始监听消息...');
                    } catch (error) {
                        logger.error(`WebSocket初始化失败: ${error.message}`);
                        this.ws.close();
                    }
                });
                this.ws.on('message', async (data) => {
                    // 更新最后响应时间
                    this.lastHeartbeatResponse = Date.now();
                    this.lastActivityTime = Date.now();
                    if (this.shutdownFlag) return;
                    try {
                        if (this.connectionRestartFlag) {
                            logger.info('检测到连接重启标志，准备重新建立连接...');
                            this.ws.close();
                            return;
                        }
                        const messageData = JSON.parse(data.toString());
                        // 处理心跳响应
                        if (this.handleHeartbeatResponse(messageData)) return;
                        await this.handleMessage(messageData, this.ws);
                    } catch (error) {
                        if (error instanceof SyntaxError) {
                            logger.error('❌ 消息解析失败 - JSON格式错误');
                            logger.debug(`原始数据: ${data.toString().substring(0, 500)}...`);
                        } else {
                            logger.error(`❌ 处理消息时发生错误: ${error.message}`);
                            logger.debug(`原始消息: ${data.toString().substring(0, 500)}...`);
                        }
                    }
                });
                this.ws.on('close', (code, reason) => {
                    if (!this.shutdownFlag) {
                        logger.warn(`WebSocket连接已关闭: ${code} - ${reason}`);
                    }
                    // 清理定时器
                    if (this.heartbeatTimer) {
                        clearTimeout(this.heartbeatTimer);
                        this.heartbeatTimer = null;
                    }
                    if (this.tokenRefreshTimer) {
                        clearTimeout(this.tokenRefreshTimer);
                        this.tokenRefreshTimer = null;
                    }
                });
                this.ws.on('error', (error) => {
                    if (!this.shutdownFlag) {
                        logger.error(`WebSocket连接发生错误: ${error.message}`);
                    }
                });
                // 等待WebSocket连接关闭
                await new Promise((resolve) => {
                    this.ws.on('close', resolve);
                });
                if (this.connectionRestartFlag && !this.shutdownFlag) {
                    logger.info('主动重启连接，立即重连...');
                    continue;
                }
                if (!this.shutdownFlag) {
                    logger.info('等待5秒后重连...');
                    await sleep(5000);
                }
            } catch (error) {
                if (!this.shutdownFlag) {
                    logger.error(`连接发生错误: ${error.message}`);
                    logger.info('等待5秒后重连...');
                    await sleep(5000);
                }
            }
        }
    }
}

// ==================== 闲鱼适配器类 ====================

class XianyuAdapter {
    constructor(configDB) {
        this.configDB = configDB;
        this.configManager = null;
        this.contextManager = null;
        this.bot = null;
        this.xianyuApis = null;
        this.xianyuLive = null;
        this.isRunning = false;
        this.sessionMap = new Map(); // 用于映射bncr消息ID和闲鱼会话
    }

    async init() {
        try {
            logger.info('🚀 正在初始化闲鱼适配器...');

            // 初始化配置管理器
            this.configManager = new ConfigManager(this.configDB);
            await this.configManager.init();

            // 检查必要的配置
            const cookiesStr = this.configManager.getCookiesStr();
            if (!cookiesStr) {
                throw new Error('未配置闲鱼Cookie字符串');
            }

            if (this.configManager.getEnableAI() && !this.configManager.getApiKey()) {
                throw new Error('启用AI功能但未配置API密钥');
            }

            // 初始化聊天上下文管理器
            logger.info('正在初始化数据库...');
            this.contextManager = new ChatContextManager();
            await this.contextManager.initDb();
            logger.info('✅ 数据库初始化完成');

            // 初始化AI回复机器人（如果启用）
            if (this.configManager.getEnableAI()) {
                logger.info('正在初始化AI回复机器人...');
                this.bot = new XianyuReplyBot(this.configManager);
                logger.info('✅ AI回复机器人初始化完成');
            }

            // 初始化闲鱼API客户端
            logger.info('正在初始化闲鱼API客户端...');
            this.xianyuApis = new XianyuApis(this.configManager);
            this.xianyuApis.updateCookies(cookiesStr);
            logger.info('✅ 闲鱼API客户端初始化完成');

            // 测试登录状态
            logger.info('正在检查登录状态...');
            const loginStatus = await this.xianyuApis.hasLogin();
            if (loginStatus) {
                logger.info('✅ 登录状态正常');
            } else {
                logger.warn('⚠️ 登录状态异常，请检查Cookie配置');
            }

            // 初始化闲鱼Live主程序
            logger.info('正在启动闲鱼Live连接...');
            this.xianyuLive = new XianyuLive(this.bot, this.configManager, this.contextManager);

            // 设置消息回调，将闲鱼消息转发到bncr
            this.xianyuLive.setMessageCallback((xianyuMsg) => {
                this.handleXianyuMessage(xianyuMsg);
            });

            // 启动后台连接
            this.startBackgroundConnection();

            logger.info('🎉 闲鱼适配器初始化完成！');
            this.isRunning = true;

        } catch (error) {
            logger.error(`❌ 闲鱼适配器初始化失败: ${error.message}`);
            throw error;
        }
    }

    startBackgroundConnection() {
        // 在后台启动闲鱼Live连接
        if (this.xianyuLive) {
            setTimeout(() => {
                this.xianyuLive.main().catch(error => {
                    logger.error(`闲鱼Live连接异常: ${error.message}`);
                    // 可以在这里实现重连逻辑
                });
            }, 1000);
        }
    }

    handleXianyuMessage(xianyuMsg) {
        try {
            // 将闲鱼消息转换为bncr格式
            const bncrMsg = this.convertXianyuToBncr(xianyuMsg);
            if (bncrMsg) {
                // 存储会话映射
                this.sessionMap.set(bncrMsg.msgId, {
                    chatId: xianyuMsg.chatId,
                    userId: xianyuMsg.userId,
                    itemId: xianyuMsg.itemId
                });

                // 调用bncr的receive方法
                this.receive(bncrMsg);
            }
        } catch (error) {
            logger.error(`处理闲鱼消息时出错: ${error.message}`);
        }
    }

    convertXianyuToBncr(xianyuMsg) {
        if (!xianyuMsg) return null;

        return {
            userId: xianyuMsg.userId || '',
            userName: xianyuMsg.userName || '',
            groupId: '0', // 闲鱼主要是私聊
            groupName: '',
            msg: xianyuMsg.message || '',
            msgId: `xy_${xianyuMsg.chatId}_${Date.now()}`,
            fromType: 'Social'
        };
    }

    convertBncrToXianyu(replyInfo) {
        // 从会话映射中获取闲鱼相关信息
        const sessionInfo = this.sessionMap.get(replyInfo.msgId);
        if (!sessionInfo) {
            // 如果没有找到会话信息，尝试从userId中解析
            const chatId = replyInfo.groupId !== '0' ? replyInfo.groupId : replyInfo.userId;
            return {
                chatId: chatId,
                userId: replyInfo.userId,
                message: replyInfo.msg,
                type: replyInfo.type || 'text'
            };
        }

        return {
            chatId: sessionInfo.chatId,
            userId: sessionInfo.userId,
            itemId: sessionInfo.itemId,
            message: replyInfo.msg,
            type: replyInfo.type || 'text'
        };
    }