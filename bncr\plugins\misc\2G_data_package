/**作者
 * <AUTHOR>
 * @name 2G_data_package
 * @team hhgg
 * @version 1.0.0
 * @description 京东plus会员领取2G日报
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule ^(尊敬的中国移动用户.*|(\d{6}))
 * @rule ^(移动京东.*)
 * @admin false
 * @disable false
 * @public false
 */
sysMethod.testModule(['crypto-js', 'axios'], { install: true });
const {again, sendMessage} = require('./mod/utils');
const CryptoJS = require('crypto-js');
const axios = require('axios');

const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'coc-en-version': '1486234121417629696',
    'referer': 'https://dev.coc.10086.cn/coc/web1/plusCoupon/?pageId=1706215671194681344&channelId=P00000066751',
    'origin': 'https://dev.coc.10086.cn'
}

const axiosInstance = axios.create({
    timeout: 10000 // 设置超时时间为10秒
});

// 添加请求拦截器
axiosInstance.interceptors.request.use((config) => {
    config.headers = { ...config.headers, ...headers };
    return config;
});

module.exports = async s => {
    let phone;
    let userId = s.getUserId();
    let number = s.param(1)
    if (number.startsWith('移动京东')) {
        phone = processString(number)
        if (!phone) {
            phone = await again(s, '手机号有误，请在60秒内重新输入，仅输入11位数字即可！')
        }
        const tmp = await sendSMScode(phone)
        if (!tmp.includes('已发送')) {
            await s.reply(tmp)
            return
        }
        number = await again(s, tmp)
    }
    console.log(number)
    const regex = /(\d{6})/;
    const code = number.match(regex)[0];
    const cookie = await loginCheck(phone, code);
    delete headers['coc-en-version'];
    headers['cookie'] = cookie;
    console.log(headers)
    const [remainTimes, flag] = await queryTimes();
    if (flag) {
        await sendMessage(userId, '本周已领取！');
        return;
    } else if (remainTimes === 4) {
        await sendMessage(userId, '本月已领取4次，请下月再领取！');
        return;
    }
    headers['referer'] = 'https://dev.coc.10086.cn/coc/web1/plusCoupon/redirectPgae';
    const param = await extractJsFilenames()
    const jsonParam = param.replace(/(\w+):/g, '"$1":').replace(/'/g, '"')
    const packInfo = await getPackage(jsonParam)
    await sendMessage(userId, packInfo);
}

function processString(str) {
    const matches = str.match(/\d+/g);
    if (!matches) return '13662222943'; // 如果没有匹配到任何数字，返回默认号码
    const number = matches[0];
    if (number.length === 11) return number; // 如果第一个匹配的数字是11位
}

async function sendSMScode(t) {
    const param = {
        "mobile": t,
        "smsCodeTypeEnum": "100"
    }
    try {
        const response = await axiosInstance.post('https://dev.coc.10086.cn/coc/user/smsCode', body_encrypt(param));
        if (response.data.success) {
            return '验证码已发送，请复制短信全部内容，在60秒内回复给我！'
        } else {
            return response.data.message
        }
    } catch (error) {
        console.error('发送短信验证码失败:', error);
        return '发送短信验证码失败，请稍后重试。';
    }
}

async function loginCheck(phone, code) {
    const param ={
        "mobile": phone,
        "smsCode": code.toString(),
        "userId": "",
        "smsCodeTypeEnum": 100,
        "needCheck": false,
        "verifyType": 0
    }
    try {
        const response = await axiosInstance.post('https://dev.coc.10086.cn/coc/user/loginCheck', body_encrypt(param));
        if (response.data.success) {
            return 'User-Token=' + response.data.data.token
        }
    } catch (error) {
        console.error('登录检查失败:', error);
        return null;
    }
}

async function queryTimes() {
    try {
        const response = await axiosInstance.post('https://dev.coc.10086.cn/coc/grocer/jd/timesQuery', {});
        console.log(`timesquery-body:\n${JSON.stringify(response.data, null, 2)}`);
        if (response.data.success) {
            return [response.data.data.monthReceive, response.data.data.isWeekFinish]
        }
    } catch (error) {
        console.error('查询次数失败:', error);
        return [null, null];
    }
}

async function getPackage(t) {
    const param = JSON.parse(t)
    delete param.pageId
    try {
        const response = await axiosInstance.post('https://dev.coc.10086.cn/coc/grocer/jd/register', param);
        console.log(JSON.stringify(response.data, null, 2));
        if (response.data.success) {
            return `本周的京东-移动2G日包领取成功！\n本月已领${response.data.data.monthReceive}次`
        }
    } catch (error) {
        console.error('获取套餐失败:', error);
        return '获取套餐失败，请稍后重试。';
    }
}

async function extractJsFilenames() {
    const baseUrl = 'https://dev.coc.10086.cn/coc/web1/plusCoupon/js/';
    const { data: htmlContent } = await axios.get('https://dev.coc.10086.cn/coc/web1/plusCoupon/?pageId=1706215671194681344&channelId=P00000066751');
    const chunkJsFilenames = [...new Set(htmlContent.match(/chunk-[^"]+\.js/g) || [])]
        .filter(filename => !filename.includes('vendor'));
    const regex = /{activityId:"[^"]+",sourceGoodsId:"[^"]+",pageId:1}/;
    const requests = chunkJsFilenames.map(filename => 
        axios.get(baseUrl + filename)
            .then(({ data }) => {
                const match = data.match(regex);
                if (match) {
                    console.log(`Found in ${filename}:`, match[0]);
                    return match[0];
                }
                return null;
            })
            .catch(error => {
                console.error(`Error fetching ${filename}:`, error.message);
                return null;
            })
    );
    const results = await Promise.all(requests);
    const validResult = results.find(result => result !== null);
    return validResult || null;
}

function body_encrypt(i) {
    const encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(JSON.stringify(i)), CryptoJS.enc.Utf8.parse("0226942d11914188"), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
    }).toString();
    return {cocEnContent: encrypted}
}
