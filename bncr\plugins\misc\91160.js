/**作者
 * <AUTHOR>
 * @name 91160
 * @team hhgg
 * @version 1.0.0
 * @description 根据提供的就诊信息，自动开启青龙上91160的抢号程序
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule ^(挂号)(.*)$
 * @admin true
 * @disable false
 */

sysMethod.testModule(['cheerio','axios'], { install: true });
const {extract_info_normal, again, get_ql_token, write_ql_task, sendMessage} = require('./mod/utils');
const cheerio = require('cheerio');
const axios = require('axios');
const fs = require('fs');

const pre_context =
`\n'''\n1.请分析上面的信息并按照如下json模板进行回复，不需要回复多余文字和字符。
{
   "hosp_name": "<医院名称，默认'儿童医院'>",
   "dep_name": "<科室名称，例如 '内科'>",
   "doctors":  "<医生中文姓名>",
   "date": "<就诊日期，年份默认为${new Date().getFullYear()}年>",
   "duration": <持续天数>",
   "usr": <账号，一般是11位的手机号>",
   "pwd": "<密码，一般紧随账号名后>",
   "patient": "<就诊人姓名>",
   "priority_times": "<优先挂号时段，默认为空>",
   "excluded_times": "<需要排除的挂号时段，默认为空>",
   "task_name": "<任务名称>",
   "type": "<挂号类型，默认为'抢号模式'>"
}'
2.'hosp_name'字段以字符串形式回应，提取信息中包含的医院名称的信息，严格按照已提供的信息原文回应，如无法找到该医院名称的信息，则默认回复'儿童医院'。
3.'dep_name'字段以字符串形式回应，提取信息中包含的医院科室名称信息。
4.4.'doctors'字段以字符串形式回应，仅保留医生中文姓名，不用保留'医生'字样，如有多个名字请用','连接，严格按照已提供的信息原文回应，如果信息中包含'所有'或者'全部'，则回复空字符串。
5.'date'字段以数组形式回应，数组中每个元素的格式均为'yyyy-mm-dd'，如果信息中包含'长期'，则回复空数组。
6.'duration'字段需以数字形式回应，一般紧随就诊日期后，如果信息中包含'长期'，则回复999，如无法找到该字段的信息，则默认回复0。
7.'usr'字段以字符串形式回应，一般是11位的手机号，严格按照该字段的信息原文回应。
8.'pwd'字段以字符串形式回应，一般紧随账号名后，严格按照该字段的信息原文回应。
9.'patient'字段以字符串形式回应，严格按照该字段的信息原文回应。
10.上午时段对应["08:00-08:30", "08:30-09:00", "09:00-09:30", "09:30-10:00", "10:00-10:30", "10:30-11:00", "11:00-11:30"]，下午时段对应["14:00-14:30", "14:30-15:00", "15:00-15:30", "15:30-16:00", "16:00-16:30"]。
11.'priority_times'字段以数组形式回应，表示需要优先考虑的时段，按照已提供的信息原文分析，从上午或者下午时段中选择符合要求的元素，例如优先考虑上午9-10点，则回复上午时段中包含9点和10点的所有元素，如果优先考虑全部上午的号段，则回复['am']，如果优先考虑全部下午的号段，则回复['pm']，如无法找到该字段的信息，则默认为[]。
12.'excluded_times'字段以数组形式回应，表示需要排除的时段，按照已提供的信息原文分析，从上午或者下午时段中选择符合要求的元素，例如不要下午2-3点的号，则回复下午时段中2-3点间的所有元素，如果需要排除全部上午的号段，则回复['am']，如果需要排除全部下午的号段，则回复['pm']如无法找到该字段的信息，则默认为[]。
13.'task_name'字段以字符串形式回应，格式为'dg_'加就诊人姓名的拼音，例如就诊人姓名为张三，则任务名称为'dg_zhangsan'。
14.'type'字段以字符串形式回应，如果信息中包含'常规'或者'普通'或者'正常'，则回复'常规模式'，如无法找到该字段的信息，则默认回复'抢号模式'。\n'''`

module.exports = async s => {
    const info = s.param(2) || await again(s, `请在30秒内输入需要挂号信息,科室-医生-开始日期-天数-用户名-密码-就诊人`) || null;
    userId = s.getUserId();
    if (!info) return;
    const prompt = info + pre_context;
    let data = await extract_info_normal(prompt)
    const unitData = JSON.parse(fs.readFileSync('/bncr/BncrData/plugins/misc/unit.json', 'utf8'));
    let filteredUnits = unitData.filter(unit => 
        unit.name.includes(data.hosp_name) || (unit.alias && unit.alias.includes(data.hosp_name))
    );
    if (filteredUnits.length === 0) {
        console.log('未查找到医院，请重新输入');
        await sendMessage(userId, '未查找到医院，请重新输入')
        return;
    }
    if (filteredUnits.length > 1) {
        const show_result = filteredUnits.map((item, index) => `${index + 1}： ${item.name}--${item.unit_id}`).join('\n\n');
        console.log(`查询到多个医院，请选择：\n${show_result}\n`);
        const chosen_num = await again(s, `查询到多个医院，请选择：\n${show_result}`)
        if (!chosen_num) return
        filteredUnits = [filteredUnits[chosen_num-1]]
    }
    let dep_info = await extractAndFindDepartment(filteredUnits[0]['url'], data.dep_name)
    if (dep_info.length === 0) {
        console.log('未查找到你需要就诊的科室，请重新输入');
        await sendMessage(userId, '未查找到就诊科室，请重新输入')
        return;
    }
    if (dep_info.length > 1) {
        const show_result = dep_info.map((item, index) => `${index + 1}： ${item.title}--${item.id}`).join('\n\n');
        console.log(`查询到多个就诊科室，请选择：\n${show_result}\n`);
        const chosen_num = await again(s, `查询到多个就诊科室，请选择：\n${show_result}`)
        if (!chosen_num) return
        dep_info = [dep_info[chosen_num-1]]
    }
    data['unit_id'] = filteredUnits[0]['unit_id']
    data['dep_id'] = dep_info[0]['id']
    const new_file = `160_${data.type === '抢号模式' ? 'multi' : 'normal'}.py`;
    delete data.type
    await sendMessage(userId, JSON.stringify(data, null, 2))
    const ql_token = await get_ql_token('http://***************:5700')
    const num = await getMaxNumbers(ql_token)
    if (num.maxTaskNum > num.maxFileNum) {
        const message = '代挂号任务数大于脚本数，请检查原因！';
        console.log(message);
        await sendMessage(userId, message);
        return;
    }
    await updateENV(data.task_name, JSON.stringify(data), ql_token);
    await write_ql_task('http://***************:5700', ql_token, data.task_name, new_file);
    await sendMessage(userId, '代挂号任务创建成功！');
}

async function extractAndFindDepartment(url, targetTitle) {
    try {
        const response = await axios.get(url, {
            headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36 Edg/111.0.1661.62' }
        });
        const $ = cheerio.load(response.data);
        const departments = [];
        $('.dep_item.layout a[id][title]').each((_, a) => {
            const title = $(a).attr('title');
            if (!title.includes('特诊') && (title === targetTitle || title.includes(targetTitle))) {
                departments.push({
                    id: $(a).attr('id').split('_').pop(),
                    title: title
                });
            }
        });
        return departments;
    } catch (error) {
        console.error('Error:', error);
        return [];
    }
}

async function updateENV(name, value, token) {
    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    try {
        const response = await axios.get('http://***************:5700/api/envs', { headers });
        if (response.data.code !== 200) {
            console.log('updateENV请求失败!');
            return;
        }
        const extractedData = response.data.data.map(item => ({
            id: item.id,
            name: item.name,
            remarks: item.remarks,
            value: item.value
        }));
        const existingVar = extractedData.find(item => item.name === name);
        let payload, method, successMsg;
        if (existingVar) {
            payload = { ...existingVar, value };
            method = 'put';
            successMsg = `${name}环境变量修改成功`;
        } else {
            payload = [{ name, value }];
            method = 'post';
            successMsg = `${name}环境变量新增成功`;
        }
        // // 更新或新增环境变量
        const updateResponse = await axios({
            method: method,
            url: 'http://***************:5700/api/envs',
            headers: headers,
            data: payload
        });
        if (updateResponse.data.code === 200) {
            console.log(successMsg);
        }
    } catch (error) {
        console.error('updateENV-Error:', error.message);
    }
}

function getMaxNumbers(token) {
    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    const urls = [
        'http://***************:5700/api/crons?searchValue=&page=1&size=200&filters={}&queryString={%22filters%22:null,%22sorts%22:null,%22filterRelation%22:%22and%22}',
        'http://***************:5700/api/scripts'
    ];
    return axios.all(urls.map(url => axios.get(url, { headers })))
        .then(([taskResponse, fileResponse]) => {
            const taskData = taskResponse.data.data?.data?.filter(item => !item.isDisabled) || [];
            const fileData = fileResponse.data.data || [];
            const taskNumbers = taskData
                .map(item => parseInt(item.command.match(/daigua_(\d+)/)?.[1], 10))
                .filter(Boolean)
                .sort((a, b) => a - b);
            let maxTaskNum = 1;
            for (const num of taskNumbers) {
                if (num > maxTaskNum || num === taskNumbers[taskNumbers.length - 1]) {
                    maxTaskNum = num > maxTaskNum ? maxTaskNum - 1 : num;
                    break;
                }
                maxTaskNum++;
            }
            const maxFileNum = Math.max(...fileData
                .filter(item => item.title.startsWith('daigua_') && item.title.endsWith('.py'))
                .map(item => parseInt(item.title.match(/daigua_(\d+)\.py/)?.[1], 10) || 0), 0);
            return { maxTaskNum, maxFileNum };
        })
        .catch(error => {
            console.error('getMaxNumbers-Error:', error.message);
            return { maxTaskNum: 0, maxFileNum: 0 };
        });
}

async function write_code(name, token) {
    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    try {
        let content
        const response1 = await axios({
            method: 'GET',
            url: 'http://***************:5700/api/scripts/detail?file=daigua_1.py&path=',
            headers: headers,
        });
        if (response1.data.code === 200) {
            console.log(`获取daigua代码成功！`);
            content = {'content':response1.data.data, 'filename':name, 'path': ""}
        }
        const response2 = await axios({
            method: 'PUT',
            url: 'http://***************:5700/api/scripts',
            headers: headers,
            data: content
        });
        if (response2.data.code === 200) {
            console.log(`挂号代码写入${name}成功！`);
        }
    } catch (error) {
        console.error('write_code-Error:', error.message);
    }
}