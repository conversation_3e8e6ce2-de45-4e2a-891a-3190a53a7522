/**作者
 * <AUTHOR>
 * @name audio2text
 * @team hhgg
 * @version 1.0.0
 * @description 提取需要提醒的信息
 * @platform tgBot qq ssh HumanTG wechatpadpro wxXyo
 * @rule ([^\\\/]+\.mp3)
 * @admin false
 * @disable false
 * @public false
 */

sysMethod.testModule(['crypto'], { install: true });
const fs = require('fs');
const crypto = require('crypto');
const {requestN, sleep} = require('./mod/utils');

module.exports = async s => {
    if (s.param(1).includes('.mp3')){
      await sleep(2000)
      const voicefile = '/bncr/BncrData/shared/mp3/'+ s.param(1).replace("[语音]-", "")
      const content = fs.readFileSync(voicefile).toString('base64');
      let tmp_body = {
          "ChannelNum": 1,
          "EngineModelType": "16k_zh",
          "ResTextFormat": 0,
          "Data": content,
          "SourceType": 1
      }
      let payload = JSON.stringify(tmp_body);
      let taskid  = await get_text(payload)
      console.log(taskid)
      if (taskid && taskid != 1) {
          await sleep(5000)
          let tmp_body = taskid
          let payload = JSON.stringify(tmp_body);
          let prompt_data  = await get_text(payload, "DescribeTaskStatus")
          console.log(prompt_data)
          const regex1 = /(提醒|记(?:录|一)?(?:下|以下)?)/;
          const regex2 = /^(?!.*停止煮.*蛋)|.*煮.*蛋.*|.*蛋.*煮.*$/;
          if (regex1.test(prompt_data) || regex2.test(prompt_data)) {
            // const updated_data = regex2.test(prompt_data) ? prompt_data.replace(regex1, '') : prompt_data;
            console.log(prompt_data + '-' + s.getUserId())
            await sysMethod.inline(prompt_data + '-' + s.getUserId());
          } else {
            return;
          }
      }
    }
}
async function get_text(payload, action="CreateRecTask") {
  const SECRET_ID = 'AKIDzEUn8qVqDxgK21kHQnAdoyhRAD5sgSKC';
  const SECRET_KEY = '7WBUFK93BdBlkmvkLC9ACLYgiott9mVL';
  const host = "asr.tencentcloudapi.com"
  const service = "asr"
  const region = "ap-guangzhou"
  const version = "2019-06-14"
  const timestamp = parseInt(String(new Date().getTime() / 1000))
  const date = getDate(timestamp)
  // ************* 步骤 1：拼接规范请求串 *************
  const signedHeaders = "content-type;host"
  const hashedRequestPayload = getHash(payload)
  const httpRequestMethod = "POST"
  const canonicalUri = "/"
  const canonicalQueryString = ""
  const canonicalHeaders ="content-type:application/json; charset=utf-8\n" + "host:" + host + "\n"
  const canonicalRequest = httpRequestMethod + "\n" + canonicalUri + "\n" + canonicalQueryString + "\n" + canonicalHeaders + "\n" + signedHeaders + "\n" + hashedRequestPayload
  // ************* 步骤 2：拼接待签名字符串 *************
  const algorithm = "TC3-HMAC-SHA256"
  const hashedCanonicalRequest = getHash(canonicalRequest)
  const credentialScope = date + "/" + service + "/" + "tc3_request"
  const stringToSign = algorithm + "\n" + timestamp + "\n" + credentialScope + "\n" + hashedCanonicalRequest
  // ************* 步骤 3：计算签名 *************
  const kDate = sha256(date, "TC3" + SECRET_KEY)
  const kService = sha256(service, kDate)
  const kSigning = sha256("tc3_request", kService)
  const signature = sha256(stringToSign, kSigning, "hex")
  // ************* 步骤 4：拼接 Authorization *************
  const authorization = algorithm + " " + "Credential=" + SECRET_ID + "/" + credentialScope + ", " + "SignedHeaders=" + signedHeaders + ", " + "Signature=" + signature
  // ************* 步骤 5：构造并发起请求 *************
  const headers = {
    Authorization: authorization,
    "Content-Type": "application/json; charset=utf-8",
    Host: host,
    "X-TC-Action": action,
    "X-TC-Timestamp": timestamp,
    "X-TC-Version": version,
  }
  if (region) {
    headers["X-TC-Region"] = region
  }
  const requestOptions = {
      url: `https://${host}/`,
      method: httpRequestMethod,
      headers: headers,
      data: JSON.parse(payload),
      json: true
    };
  try {
      const [response,body] = await requestN(requestOptions);
      if (response.status === 200 && action == "DescribeTaskStatus") {
        let raw_data = body.Response.Data.Result
        const regex = /\[[^\]]*\]/g;
        const result = raw_data.replace(regex, '').replace(/\s+/g, '');
        return result
      }else if (response.status === 200 && action == "CreateRecTask") {
        // console.log(body)
        return body.Response.Data
      }else {
        // console.log(body)
        return 1
      }
  } catch (error) {
      console.log('get_text error:', error);
  }
}
function sha256(message, secret = "", encoding) {
  const hmac = crypto.createHmac("sha256", secret)
  return hmac.update(message).digest(encoding)
}
function getHash(message, encoding = "hex") {
  const hash = crypto.createHash("sha256")
  return hash.update(message).digest(encoding)
}
function getDate(timestamp) {
  const date = new Date(timestamp * 1000)
  const year = date.getUTCFullYear()
  const month = ("0" + (date.getUTCMonth() + 1)).slice(-2)
  const day = ("0" + date.getUTCDate()).slice(-2)
  return `${year}-${month}-${day}`
}

