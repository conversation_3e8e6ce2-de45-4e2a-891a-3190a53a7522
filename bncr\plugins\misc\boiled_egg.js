/**
 * <AUTHOR>
 * @name boiled_egg
 * @team hhgg
 * @version 1.0.1
 * @description 设定时间后自动通电煮鸡蛋，煮到设定事件后自动断电
 * @platform tgBot qq ssh HumanTG wechatpadpro wxXyo
 * @rule (.*(煮.*蛋|蛋.*煮).*)
 * @admin false
 * @disable false
 * @public false
 */

// 依赖模块检查和加载
sysMethod.testModule(['crypto'], { install: true });
const crypto = require('crypto');
const fs = require('fs');
const { requestN, sleep, get_cronAndContent, sendMessage } = require('./mod/utils');
// 常量配置
const CONFIG = {
    REMINDER_DIR: '/bncr/BncrData/plugins/reminder/',
    COOK_DURATION_MINUTES: 35,
    RESTART_DELAY: 1000,
    API_BASE_URL: 'https://api.skyworthiot.com',
    DEVICE_CONFIG: {
        AK: '2.dcd9b07e79cc45e9be5f5bacf7772596',
        UID: '29dd582856d747389d9ae35c35eceac3',
        DEVICE_ID: '6ab6b27854504a0cac0f3afd1eba661',
        APPKEY: 'ca90d3d2265d4e14acac571a8f15f1cf',
        SECRET: '7567c43746f347b092c3fd806c84a6b7'
    }
};
// 通用请求头
const COMMON_HEADERS = {
    'Host': 'api.skyworthiot.com',
    'Content-Type': 'application/x-www-form-urlencoded',
    'User-Agent': 'iPhone13,2(iOS/14.2.1) EEUI(EEUI/4.4.3) Weex/0.24.0 ExternalUA 1170x2532',
};
// API端点
const API_ENDPOINTS = {
    DEVICE_STATUS: '/ai_home/defined/get_device_list_with_desc_and_status',
    DEVICE_CONTROL: '/devicemgr/v1/device/shadow/modify'
};
// 用户提示消息
const MESSAGES = {
    EGG_COMPLETE: '煮蛋完成啦~请尽快食用',
    POWER_OFF_ERROR: '发生错误，请检查（关闭电源动作）！',
    POWER_ON_ERROR: '发生错误，请检查（打开电源动作）！',
    TIME_EXPIRED: '您设置的提醒时间已经过去啦~~~~~请重新设置',
    DEVICE_BUSY: '开关状态已经为开启，请前往查看是否有其他电器正在运行',
    COOK_START: '开始煮蛋啦~~',
    COOK_TIME_NOTICE: '提醒内容：\n鸡蛋预计煮熟时间\n\n提醒时间：\n',
    START_TIME_NOTICE: '提醒内容：\n煮鸡蛋预计开始时间\n\n提醒时间：\n'
};
/**
 * 主函数 - 处理煮鸡蛋流程
 * @param {Object} s - 消息对象
 */
module.exports = async (s) => {
    let userId;
    try {
        let input = s.param(1);
        userId = input.split('-')[1] || s.getUserId();
        const immediateRegex = /(现在)|(马上)|(^煮蛋)|(^煮鸡蛋)/;
        if (input.includes('停止煮鸡蛋')) {
            // 处理停止煮鸡蛋命令
            await handleStopCooking(userId);
        } else if (immediateRegex.test(input)) {
            // 处理立即开始煮鸡蛋命令
            await handleImmediateCooking(userId, immediateRegex.test(input));
        } else {
            // 处理定时煮鸡蛋命令
            input = input.split('-')[0];
            await handleScheduledCooking(input, userId, immediateRegex.test(input));
        }
        // 重启系统
        await sleep(CONFIG.RESTART_DELAY);
        await sysMethod.inline('重启');
    } catch (error) {
        console.error('煮鸡蛋流程发生错误:', error);
        if (userId) {
            await sendMessage(userId, '系统发生错误，请稍后重试');
        }
    }
};
/**
 * 处理停止煮鸡蛋命令
 * @param {string} userId - 用户ID
 */
async function handleStopCooking(userId) {
    try {
        const switchMsg = await switchOnAndOff(0);

        if (switchMsg.msg === '成功') {
            await sendMessage(userId, MESSAGES.EGG_COMPLETE);
        } else {
            await sendMessage(userId, MESSAGES.POWER_OFF_ERROR);
        }
    } catch (error) {
        console.error('停止煮鸡蛋时发生错误:', error);
        await sendMessage(userId, MESSAGES.POWER_OFF_ERROR);
    }
}
/**
 * 处理立即开始煮鸡蛋命令
 * @param {string} userId - 用户ID
 * @param {boolean} isImmediate - 是否立即开始
 */
async function handleImmediateCooking(userId, isImmediate) {
    try {
        // 获取定时任务信息
        const [content, cronExp, filename] = await get_cronAndContent('现在立刻开始煮鸡蛋', CONFIG.COOK_DURATION_MINUTES);
        if (cronExp === 0) {
            await sendMessage(userId, MESSAGES.TIME_EXPIRED);
            return;
        }
        // 检查设备状态
        const deviceStatus = await checkDeviceStatus();
        if (deviceStatus.includes('1')) {
            console.log(MESSAGES.DEVICE_BUSY);
            await sendMessage(userId, MESSAGES.DEVICE_BUSY);
            return;
        }
        // 开启设备
        const switchMsg = await switchOnAndOff(1);
        if (switchMsg.msg !== '成功') {
            console.log(MESSAGES.POWER_ON_ERROR);
            await sendMessage(userId, MESSAGES.POWER_ON_ERROR);
            return;
        }
        console.log(MESSAGES.COOK_START);
        // 创建定时任务
        const scheduleTime = await writeCronJs(cronExp, filename, userId, isImmediate);
        await sendMessage(userId, `${MESSAGES.COOK_TIME_NOTICE}${scheduleTime}`);
    } catch (error) {
        console.error('立即煮鸡蛋时发生错误:', error);
        await sendMessage(userId, MESSAGES.POWER_ON_ERROR);
    }
}
/**
 * 处理定时煮鸡蛋命令
 * @param {string} input - 用户输入
 * @param {string} userId - 用户ID
 * @param {boolean} isImmediate - 是否立即开始
 */
async function handleScheduledCooking(input, userId, isImmediate) {
    try {
        const [content, cronExp, filename] = await get_cronAndContent(input);
        if (cronExp === 0) {
            await sendMessage(userId, MESSAGES.TIME_EXPIRED);
            return;
        }
        const scheduleTime = await writeCronJs(cronExp, filename, userId, isImmediate);
        await sendMessage(userId, `${MESSAGES.START_TIME_NOTICE}${scheduleTime}`);
    } catch (error) {
        console.error('定时煮鸡蛋时发生错误:', error);
        await sendMessage(userId, '设置定时任务失败，请重试');
    }
}
/**
 * 生成带签名的查询URL
 * @param {string} endpoint - API端点
 * @returns {string} 完整的查询URL
 */
function getQueryUrl(endpoint) {
    try {
        const baseUrl = endpoint.startsWith('http') ? endpoint : `${CONFIG.API_BASE_URL}${endpoint}`;
        const params = {
            appkey: CONFIG.DEVICE_CONFIG.APPKEY,
            time: Math.floor(Date.now() / 1000).toString(),
            uid: CONFIG.DEVICE_CONFIG.UID,
            ak: CONFIG.DEVICE_CONFIG.AK,
            device_id: CONFIG.DEVICE_CONFIG.DEVICE_ID
        };
        // 按键名排序
        const sortedKeys = Object.keys(params).sort();
        // 生成签名字符串
        const concatenatedParams = sortedKeys.map(key => key + params[key]).join('') + CONFIG.DEVICE_CONFIG.SECRET;
        const hash = crypto.createHash('md5').update(concatenatedParams).digest('hex');
        // 生成查询参数
        const queryParams = sortedKeys.map(key => `${key}=${params[key]}`).join('&');
        const queryUrl = `${baseUrl}?${queryParams}&sign=${hash.toLowerCase()}`;
        return queryUrl;
    } catch (error) {
        console.error('生成查询URL时发生错误:', error);
        throw new Error('URL生成失败');
    }
}
/**
 * 控制设备开关
 * @param {number} status - 开关状态 (0: 关闭, 1: 开启)
 * @returns {Object} 操作结果
 */
async function switchOnAndOff(status) {
    try {
        const switchUrl = getQueryUrl(API_ENDPOINTS.DEVICE_CONTROL);
        const formData = `{
  "SWITCH" : ${status}
}`;
        const option = {
            url: switchUrl,
            method: "post",
            headers: COMMON_HEADERS,
            json: true,
            form: formData,
        };
        const [response, body] = await requestN(option);

        if (response.statusCode !== 200) {
            throw new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`);
        }
        return body;
    } catch (error) {
        console.error('控制设备开关时发生错误:', error);
        return { msg: '失败', error: error.message };
    }
}
/**
 * 检查设备状态
 * @returns {string} 设备状态
 */
async function checkDeviceStatus() {
    try {
        const queryStatusUrl = getQueryUrl(API_ENDPOINTS.DEVICE_STATUS);
        const option = {
            url: queryStatusUrl,
            method: "GET",
            headers: COMMON_HEADERS,
            json: true
        };
        const [response, body] = await requestN(option);
        if (response.statusCode !== 200) {
            throw new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`);
        }
        if (!body.data || !body.data[0]) {
            throw new Error('设备状态数据格式错误');
        }
        return body.data[0].report_status;
    } catch (error) {
        console.error('检查设备状态时发生错误:', error);
        return '0'; // 默认返回关闭状态
    }
}

/**
 * 创建定时任务文件
 * @param {string} cronExp - cron表达式
 * @param {string} filename - 文件名
 * @param {string} userId - 用户ID
 * @param {boolean} writedata - 是否为立即执行模式
 * @returns {Promise<string>} 格式化的时间字符串
 */
async function writeCronJs(cronExp, filename, userId, writedata) {
    return new Promise((resolve, reject) => {
        try {
            const templatePath = CONFIG.REMINDER_DIR + 'egg';
            const newFilePath = CONFIG.REMINDER_DIR + filename;
            fs.readFile(templatePath, 'utf8', (err, data) => {
                if (err) {
                    console.error('读取模板文件失败:', err);
                    reject(err);
                    return;
                }
                try {
                    const fileLines = data.split('\n');

                    // 更新文件内容
                    fileLines[2] = ` * @name ${filename.split('.')[0]}`;
                    fileLines[9] = ` * @cron ${cronExp}`;
                    fileLines[15] = writedata
                        ? `  await sysMethod.inline('停止煮鸡蛋-${userId}');`
                        : `  await sysMethod.inline('煮蛋-${userId}');`;
                    const updatedData = fileLines.join('\n');
                    // 写入新文件
                    fs.writeFile(newFilePath, updatedData, 'utf8', (writeErr) => {
                        if (writeErr) {
                            console.error('写入定时任务文件失败:', writeErr);
                            reject(writeErr);
                            return;
                        }
                        console.log(`定时任务文件创建成功: ${filename}`);
                    });
                } catch (processError) {
                    console.error('处理文件内容时发生错误:', processError);
                    reject(processError);
                }
            });
            // 解析时间并返回格式化字符串
            const scheduleTime = formatCronTime(cronExp);
            resolve(scheduleTime);
        } catch (error) {
            console.error('创建定时任务时发生错误:', error);
            reject(error);
        }
    });
}
/**
 * 格式化cron时间为可读字符串
 * @param {string} cronExp - cron表达式
 * @returns {string} 格式化的时间字符串
 */
function formatCronTime(cronExp) {
    try {
        const cronParts = cronExp.split(' ');
        // 支持6字段格式：秒 分 时 日 月 周
        const [second, minute, hour, day, month] = cronParts;
        return `${month}月${day}日 ${hour}点${minute}分${second}秒`;
    } catch (error) {
        console.error('格式化时间时发生错误:', error);
        return '时间格式错误';
    }
}