/**作者
 * <AUTHOR>
 * @name csair-news-push
 * @team hhgg
 * @version 1.0.0
 * @description 南航最新公告推送
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule dkskfdh反对恢复的是23
 * @cron * 11,14-17,23 * * *
 * @admin true
 * @disable false
 * @public false
 */

const {requestN,notifyAdmin}= require('./mod/utils');
const bncrDB = new BncrDB('csair_news');

module.exports = async s => {
    try {
        const data = await get_data();
        if (!data) {
            // console.log('暂无新公告');
            return;
        }
        const savedData = JSON.parse(await bncrDB.get('csair'));
        if (savedData && savedData[0].url === data[0].url) {
            // console.log('当日暂无其他新公告');
            return;
        }
        const report = data.map(item => `${item.title}\n${item.url}\n${item.time}\n\n`).join('\n');
        await bncrDB.set('csair', JSON.stringify(data));
        console.log('发现南航新公告,已存入数据库');
        console.log(report)
        await notifyAdmin(report);
    } catch (error) {
        console.error('发生错误:', error);
        await notifyAdmin(report);
    }
}

async function get_data() {
    const option = {
        url: `https://m.csair.com/prod-api/page/site/openApi/getNewsRedis?lid=23&aid=14&localpath=about%2Fnews%2Fnews_notice`,
        method: "get",
    };
    const [response,body] = await requestN(option);
    const regex = /getContentList\((\{[\s\S]*?\})\)/;
    const matches = body.match(regex);
    if (matches && matches[1]) {
        const obj = JSON.parse(matches[1]);
        const filteredNews = obj.news.filter(item => item.pushTime > Math.floor(new Date().setHours(0, 0, 0, 0)));
        if (filteredNews.length > 0) {
          filteredNews.sort((a, b) => b.pushTime - a.pushTime);
          return filteredNews.map(item => ({
              title: item.lname,
              url: `https://www.csair.com/mcms/mcmsNewSite/zh/cn/build/index.html?page=${item.localpath.match(/\/([^/]+)$/)[1]}`,
              time: formatPushTime(item.pushTime)
          }));
        }
    }
}

function formatPushTime(timestamp) {
    const date = new Date(timestamp);
    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:${date.getMinutes()}`
}
