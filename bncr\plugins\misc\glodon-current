/**作者
 * <AUTHOR>
 * @name glodon-current
 * @team hhgg
 * @version 1.0.0
 * @description 广联达查询当天过磅数量总重
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule ^总重$
 * @admin false
 * @disable false
 * @public false
 */


const {requestN,readCookie}= require('./mod/utils');

module.exports = async s => {
    cookie = await readCookie('goldon');
    date  = getCurrentDate();
    time = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
    report = ''
    txt = []
    tmp = []
    today_sum = await get_data(cookie,date)
    txt.push(...today_sum.map(({ carNumber: car, materials: [{ materialName: type }], code: number, tareWeightTime: intime, completionTime: outtime, realWeightSum: weight }) => ({ car, type, number, intime, outtime, weight })));
    tmp.push(...today_sum.map(({ carNumber: car, materials: [{ materialName: type }], code: number, tareWeightTime: intime, completionTime: outtime, realWeightSum: weight }) => ({ car, type, number, intime, outtime, weight })));
    let sum_shi = 0
    let sum_fen = 0
    let count_shi = 0
    let count_fen = 0
    tmp.forEach(item => {
        if (item.type === '碎石') {
            sum_shi += item.weight;
            count_shi++;
        }
        if (item.type === '普通石粉') {
            sum_fen += item.weight;
            count_fen++;
        }
    });
    report += `今日截至${time}销售情况汇报：共运输${count_shi+count_fen}车次，其中${count_shi}车为碎石，总重量${Number(sum_shi.toFixed(2))}吨，${count_fen}车为石粉，总重量为${Number(sum_fen.toFixed(2))}吨`;
    console.log(report)
    s.reply(report)
}

async function get_data(t,d) {
    const headers = {
        'Host': 'xmgl.glodon.com',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/113.0.1774.42',
        'Cookie': '.GYS.CLOUD.TOKEN=' + t,
    }
    data = {
        "beginDate": d+' 00:00:00',
	// "beginDate": '2023-05-19',
        "billType": null,
        "billTypeIds": [0],
        "contractId": "",
        "djly": ["称重","补录","移动发料"],
	"endDate": d+' 23:59:59',
        // "endDate": '2023-05-19',
        "materialQueryIds": "",
        "materialQueryType": 0,
        "orgId": 748289350027264,
        "pageIndex": 0,
        "pageSize": 1000,
        "pcjg": [1,0,-1],
        "projectId": 748289350164480,
        "vendorIds": ""
    }
    const option = {
        url: "https://xmgl.glodon.com/gys/inspection-service/analyze/payout/new-list",
        method: "post",
        headers: headers,
        body: data,
        json: true,
    };
    const [response,body] = await requestN(option);
    if (response.statusCode === 200) {
        data_list = body.data.data.bills
        tt = data_list.map(element => element.providerId === 764461850125824 ? element : null).filter(Boolean)
        return tt
    }
}

function getCurrentDate() {
  const date = new Date();
  const hoursToAdd = 7.5 * 60 * 60 * 1000;
  date.setTime(date.getTime() + hoursToAdd);
  const formattedDate = date.toISOString().slice(0, 10);
  return formattedDate;
}
