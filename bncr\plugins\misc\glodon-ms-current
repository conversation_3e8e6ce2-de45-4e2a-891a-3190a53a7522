/**作者
 * <AUTHOR>
 * @name glodon-ms-current
 * @team hhgg
 * @version 1.0.0
 * @description 广联达查询当天过磅毛石数量总重
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule ^毛石$
 * @admin false
 * @disable false
 * @public false
 */


const {requestN,readCookie}= require('./mod/utils');
const fs = require('fs');

module.exports = async s => {
    cookie = await readCookie('goldon');
    date = getCurrentDate();
    time = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
    report = ''
    company = ['深圳志骋建设有限公司','能迩（深圳）供应链管理有限公司','深圳市易杰洲建筑工程有限公司','深圳市旭风环保科技有限公司']
    today_sum = await get_data(cookie,date)
    report = `今日0点至${time}销售情况汇报：`
    for (i in today_sum) {
        if (today_sum[i].length > 0) {
            sum_shi = 0
            tmp = []
            tmp.push(...today_sum[i].map(({ carNumber: car, materials: [{ materialName: type }], code: number, tareWeightTime: intime, completionTime: outtime, realWeightSum: weight }) => ({ car, type, number, intime, outtime, weight })));
            tmp.reverse()
            filePath = '/bncr/BncrData/plugins/misc/ms_data/' + company[i] +'.txt';
            if (fs.existsSync(filePath)) {
                txt = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                filteredElements = txt.filter(item => item.outtime.includes(date));
		if (filteredElements.length != tmp.length) {
                    tmp.forEach((element) => {
                        const found = filteredElements.some((item) => item.number === element.number);
                        if (!found) {
                            filteredElements.push(element);
                            txt.push(element);
                        }
                    });
                }
                sum_shi = TotalWeight(filteredElements);
            }else{
                sum_shi = TotalWeight(tmp);
                txt = tmp
            }
            report += `${company[i]}共运输毛石${tmp.length}车次，总重量${sum_shi}吨\n`;
            fs.writeFileSync(filePath, JSON.stringify(txt), 'utf8')
        }
    }
    console.log(report)
    s.reply(report)
}

async function get_data(t,d) {
    const headers = {
        'Host': 'xmgl.glodon.com',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/113.0.1774.42',
        'Cookie': '.GYS.CLOUD.TOKEN=' + t,
    }
    data = {
        "beginDate": d+' 00:00:00',
        // "beginDate": '2023-05-19',
        "billType": null,
        "billTypeIds": [0],
        "contractId": "",
        "djly": ["称重","补录","移动发料"],
        "endDate": d+' 23:59:59',
        // "endDate": '2023-05-19',
        "materialQueryIds": "",
        "materialQueryType": 0,
        "orgId": 748289350027264,
        "pageIndex": 0,
        "pageSize": 1000,
        "pcjg": [1,0,-1],
        "projectId": 748289350164480,
        "vendorIds": ""
    }
    const option = {
        url: "https://xmgl.glodon.com/gys/inspection-service/analyze/payout/new-list",
        method: "post",
        headers: headers,
        body: data,
        json: true,
    };
    const [response,body] = await requestN(option);
    if (response.statusCode === 200) {
        data_list = body.data.data.bills
        tt = []
        tt[0] = data_list.map(element => element.providerId === 826485441444352 ? element : null).filter(Boolean)
        tt[1] = data_list.map(element => element.providerId === 826483976792576 ? element : null).filter(Boolean)
        tt[2] = data_list.map(element => element.providerId === 826499484423168 ? element : null).filter(Boolean)
        tt[3] = data_list.map(element => element.providerId === 838176111308800 ? element : null).filter(Boolean)
        return tt
    }
}

function TotalWeight(elements) {
    let sum = 0;
    elements.forEach(item => {
        if (item.type === '毛石') {
            sum += item.weight;
        }
    });
    return Number(sum.toFixed(2))
}

function getCurrentDate() {
  const date = new Date();
  const hoursToAdd = 7.5 * 60 * 60 * 1000;
  date.setTime(date.getTime() + hoursToAdd);
  const formattedDate = date.toISOString().slice(0, 10);
  return formattedDate;
}

