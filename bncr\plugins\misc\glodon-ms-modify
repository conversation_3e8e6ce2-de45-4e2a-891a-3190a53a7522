/**作者
 * <AUTHOR>
 * @name glodon-ms-modify
 * @team hhgg
 * @version 1.0.0
 * @description 修改某个买家的毛石数据
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule ^修改(志骋|旭风)(\d+-\d+-\d+-(\d+|\d+\.\d+))$
 * @rule ^旭风(增加)(\d+|\d+\.\d+)$
 * @admin true
 * @disable false
 * @public false
 */


const {readCookie, saveCookie}= require('./mod/utils');
const fs = require('fs');

module.exports = async s => {
    if (s.param(1).indexOf('增加') != -1) {
        new_money = s.param(2)
        old_money = await readCookie('money')
        if (old_money != undefined){
            await saveCookie('money', new_money+old_money)
            s.reply('已更新旭风货款数据！')
        }else {
            await saveCookie('money', new_money)
            s.reply('新增旭风货款数据成功！')
        }
        await saveCookie('money', money)
    } else {
        company = s.param(1)
        data = s.param(2)
        year = new Date().toISOString().slice(0, 4)
        parts = data.split('-'); // 用'-'分割aa字符串
        for (i=0;i<2;i++){
            parts[i] = parts[i].padStart(2, '0')
        }
        desiredNumber = year + parts[0] + parts[1] + '-YTYZCYKJ-01-' + parts[2].padStart(4, '0'); // 在bb字符串前加上年份
        desiredWeight = parseFloat(parts[3])
        cookie = await readCookie('goldon');
        if ('深圳志骋建设有限公司'.indexOf(company) != -1) {
            company = '深圳志骋建设有限公司'
        }else {
            company = '深圳市旭风环保科技有限公司'
        }
        filePath = '/bncr/BncrData/plugins/misc/ms_data/' + company +'.txt';
        if (fs.existsSync(filePath)) {
            txt = JSON.parse(fs.readFileSync(filePath, 'utf8'));
            elementToUpdate = txt.find(item => item.number === desiredNumber);
            if (elementToUpdate && desiredWeight !=0) {
                elementToUpdate.weight = desiredWeight;
                report = company + '磅单' + desiredNumber + '的重量已修改为：' + desiredWeight
            } else if (desiredWeight ==0) {
                report = '即将删除' + company + '磅单' + desiredNumber
                txt = txt.filter(item => item.number == desiredNumber);
            } else {
                report = '未找到' + company + '磅单' + desiredNumber
            }
            fs.writeFileSync(filePath, JSON.stringify(txt), 'utf8')
        }else{
            report = '未找到' + company + '的数据'
        }
        s.reply(report)
    }
}

