/**作者
 * <AUTHOR>
 * @name glodon-ms-surplus
 * @team hhgg
 * @version 1.0.0
 * @description 查询旭风剩余可拉的毛石数量
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule ^旭风剩余$
 * @admin false
 * @disable false
 * @public false
 */

const {readCookie, saveCookie}= require('./mod/utils');
const fs = require('fs');

module.exports = async s => {
    money = await readCookie('money');
    if (money != undefined) {
        filePath = '/bncr/BncrData/plugins/misc/ms_data/深圳市旭风环保科技有限公司.txt';
        total = money*10000/43.5
        if (fs.existsSync(filePath)) {
            txt = JSON.parse(fs.readFileSync(filePath, 'utf8'));
            let sum = 0;
            txt.forEach(item => {
                if (item.type === '毛石') {
                    sum += item.weight;
                }
            })
            report = '深圳市旭风环保科技有限公司当前货款剩余可拉毛石：' + (total-sum).toFixed(2) + '吨'
            s.reply(report)
        }
    } else {
        s.reply('暂未设置旭风的货款金额')
    }
}

