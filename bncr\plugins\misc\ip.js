/**作者
 * <AUTHOR>
 * @name ip
 * @team hhgg
 * @version 1.0.0
 * @description 获取光猫当前ip地址
 * @platform tgBot qq ssh HumanTG wechatpadpro wxXyo
 * @rule ^(ip)$
 * @admin true
 * @disable false
 */

sysMethod.testModule(['dns'], { install: true });
const dns = require('dns');
const hostname = '183381.xyz';

module.exports = async s => {
    result = ''
    dns.resolve4(hostname, (err, addresses) => {
        if (err) {
          result = `无法解析 ${hostname} 的 IP 地址: ${err.message}`
          console.error(result);
          return;
        }
        if (addresses.length === 0) {
          result = `未找到 ${hostname} 的 IP 地址`
          console.error(result);
        } else {
          result = `光猫的 IP 地址是: ${addresses[0]}`
          console.error(result);
        }
        s.reply(result)
      });
}
