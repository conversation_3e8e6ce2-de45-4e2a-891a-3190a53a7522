/**作者
 * <AUTHOR>
 * @name kd
 * @team hhgg
 * @version 1.0.0
 * @description 查询快递
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule ^(快递|查快递)(.*)$
 * @rule ^(快递|查快递)$
 * @admin false
 * @disable false
 * @public false
 */

sysMethod.testModule(['cheerio'], { install: true });
const {requestN} = require('./mod/utils');
const cheerio = require('cheerio');

module.exports = async s => {
    number = s.param(2) || await again(s, `请在30秒内输入快递单号`) || null;
    if (!number) return;
    let rt =''
    if (number.includes('SF')) {
        let sf_num, phone = ''
        let match = number.match(/(SF\d+)|(\d+)/g);
        if (match) {
            [sf_num, phone] = match[0].startsWith('SF') ? match : match.reverse();
        }
        const token = await get_sf_token()
        const data = await get_sf_routes(token, sf_num, phone)
        let route_msg = data.msgData.routeResps[0].routes.map(route => `${route.acceptTime}\n${route.remark}\n`);
        rt = route_msg.reverse().join('\n')
    } else {
        let kuaidi_data = await get_data(number)
	console.log(kuaidi_data)
        rt = kuaidi_data.join("\n")
    }
    s.reply(rt)
}
async function get_data(t) {
    const headers = {
        'Host': 'm.kuaidi.com',
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Origin': 'https://m.kuaidi.com',
        'Referer': 'https://m.kuaidi.com/'
    }
    const option = {
        url: "https://m.kuaidi.com/queryresults.html",
        method: "post",
        headers: headers,
        form: {'ordernumber': t, 'sign': 'KDQUERY' + Date.now(), 'exname': ''},
        proxy: "http://***************:8718",
    };
    const [response,body] = await requestN(option);
    if (response.statusCode === 200) {
        const $ = cheerio.load(body);
        const aa = [];
        const status = $('span.float_right.js_zt').text().trim();
        if (status) {
          aa.push(`快递状态：${status}`);
          aa.push('');
        }
        $('.timeline li').each((i, elem) => {
            const time = $(elem).find('.time_s').text().trim();
            const text = $(elem).find('.kd_con').clone().children('#choseId').remove().end().text().trim();
            if (time && text) {
                aa.push(time, text);
                aa.push('');
            }
        });
        return aa
    }
}

async function again(s, tip, wait) {
    !wait && (wait = 30);
    first = await s.reply(tip);
    let content = await s.waitInput(() => { }, wait)
    if (content === null) return await s.reply('超时已退出')
    if (content.getMsg() === ('q' || 'Q')) return await s.reply('已退出')
    return content.getMsg()
}

async function get_sf_token() {
    const headers ={
        'Content-Type': 'application/x-www-form-urlencoded',
    }
    let formData = {"partnerID": "YX6FWAZO","secret": "kAcur7tsc0H71Lv4H6E0F8HDwDUGRfCt","grantType": "password"}
    let option = {
        url: 'https://sfapi.sf-express.com/oauth2/accessToken',
        method: "post",
        headers: headers,
        json: true,
        form: formData,
        proxy: "http://***************:8718",
    };
    const [response,body] = await requestN(option);
    return body.accessToken
}

async function get_sf_routes(token,number,phone) {
    const headers ={
        'Content-Type': 'application/x-www-form-urlencoded',
    }
    let body_json = {
        "trackingType": "1",
        "trackingNumber": number,
        "methodType": "1",
        "checkPhoneNo": phone
    }
    let formData = {
        "partnerID": "YX6FWAZO",
        "requestID": "f7eb631e-f51a-41b1-be9c-5002463f0f619b21e4a908a008",
        "serviceCode": "EXP_RECE_SEARCH_ROUTES",
        "timestamp": Math.round(new Date().getTime() / 1000),
        "accessToken": token,
        "msgData": JSON.stringify(body_json),
    }
    let option = {
        url: 'https://bspgw.sf-express.com/std/service',
        method: "post",
        headers: headers,
        json: true,
        form: formData,
    };
    const [response,body] = await requestN(option);
    return JSON.parse(body.apiResultData)
}
