/**作者
 * <AUTHOR>
 * @name kouling
 * @team hhgg
 * @version 1.0.0
 * @description 每周五、六、日提取深圳移动公众号内的周末口令 
 * @platform tgBot qq ssh HumanTG wechatpadpro wxXyo
 * @rule ([^\\\/]+\.png)
 * @admin false
 * @disable false
 * @public false
 */

const axios = require('axios');
const fs = require('fs');
const {extract_info_normal, sendMessage} = require('./mod/utils');

module.exports = async s => {
    if (s.getUserId() == 'gh_2a2070493c9d') {
        try {
            const png_file = s.param(1).replace("[图片]-", "");
            const imgfile = '/bncr/BncrData/shared/image/'+ png_file;
            // Check if file exists before proceeding
            if (!fs.existsSync(imgfile)) {
                console.error(`Image file not found: ${imgfile}`);
                return;
            }
            const token = await get_token();
            const text_array = await read_image(token, imgfile);
            // 提取信息并格式化输出
            const prefix = "请从下列'''包围的文字中提取出所需信息，并按照三行内容输出，其中第一行内容为'时间：xxxx'(如果文字内的时间格式有误请修改)，第二行为'口令：xxxx'，第三行为'奖励：xxxx'，不用回复多余文字";
            const prompt = prefix + '\n' + text_array.join('\n');
            data = await extract_info_normal(prompt, false);
            await sendMessage('wxid_iim9wf4t0s3b12',data)
            await sendMessage('wxid_pzu5dhkdw7di22',data)
        } catch (error) {
            console.error('Error processing image:', error);
        }
    }
}
// 获取访问令牌
async function get_token() {
    const ak = 'Y7GZGxaRMrgnSNUsOxe4235M'
    const sk = 'rG7XlsfuTvZ5ZvprM8GNd80ncxuCs9b9'
    try {
        const params = {
            grant_type: "client_credentials",
            client_id: ak,
            client_secret: sk
        };
        const response = await axios.get("https://aip.baidubce.com/oauth/2.0/token", { params });
        return response.data.access_token;
    } catch (error) {
        console.error('Error getting token:', error.message);
        throw new Error('Unable to retrieve access token');
    }
}
// 使用百度API读取图片
async function read_image(token, image_path) {
    try {
        const data = await fs.promises.readFile(image_path);
        const image = Buffer.from(data).toString('base64');
        const response = await axios.post(
            `https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic?access_token=${token}`,
            `image=${encodeURIComponent(image)}`,
            {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
            }
        );
        const tmp = [];
        tmp.push("'''");
        tmp.push('移动周末口令');
        response.data.words_result.forEach(item => {
            tmp.push(item.words);
        });
        tmp.push("'''");
        return tmp;
    } catch (error) {
        console.error(`Error processing image: ${error.message}`);
        throw new Error('Unable to read image');
    }
}
