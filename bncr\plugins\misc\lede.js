/**作者
 * <AUTHOR>
 * @name lede
 * @team hhgg
 * @version 1.0.0
 * @description Lede重拨
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule 路由重启
 * @rule 重启路由
 * @admin true
 * @disable false
 */

sysMethod.testModule(['ssh2'], { install: true });
const ssh2 = require('ssh2');
const sleep= require('./mod/utils');
const dns = require('dns');
const conn = new ssh2.Client();
// SSH参数
const tmp = 
`*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
const sshConfig = {
  host: 'nas.183381.xyz',
  port: 8770,
  username: 'root',
  privateKey: tmp
};

module.exports = async s => {
    ip = await get_ip()
    await s.reply(`重启前的ip为：${ip}\n光猫即将重新拨号，请稍后!!!`)
    await sshExecCommand(sshConfig)
}

function get_ip() {
    return new Promise((resolve) => {
      const hostname = 'nas.183381.xyz';
      dns.resolve4(hostname, (err, addresses) => {
          result = ''
          if (err) {
            result = `无法解析 ${hostname} 的 IP 地址: ${err.message}`
            console.error(result);
            return;
          }
          if (addresses.length === 0) {
            result = `未找到 ${hostname} 的 IP 地址`
            console.error(result);
          } else {
            result = `光猫的 IP 地址是: ${addresses[0]}`
            console.error(result);
            resolve(addresses[0])
          }
        });
    })
  }

function sshExecCommand(t) {
  return new Promise((resolve) => {
      conn.on('ready', () => {
          console.log('SSH 连接已建立，重启拨号中，请等待5分钟');
          conn.exec('/sbin/ifup wan', (err, stream) => {
              if (err) {
                  reject(err);
                  return;
              }
              stream.on('close', (code, signal) => {
                  conn.end();
                  resolve();
              }).on('data', (data) => {
                  console.log(data)
              }).stderr.on('data', (data) => {
                  console.error('STDERR: ' + data);
              });
          });
      }).connect(t);
  });
}
