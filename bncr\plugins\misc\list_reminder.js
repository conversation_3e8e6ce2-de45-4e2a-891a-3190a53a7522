/**
 * <AUTHOR>
 * @name list_reminder
 * @team hhgg
 * @version 1.0.1
 * @description 查看或删除reminder目录下的定时脚本
 * @platform tgBot qq ssh HumanTG wechatpadpro wxXyo
 * @rule ^(定时)$
 * @admin true
 * @disable false
 * @public false
 */

const fs = require('fs');
const path = require('path');

// 常量配置
const CONFIG = {
    REMINDER_DIR: '/bncr/BncrData/plugins/reminder/',
    WAIT_TIMEOUT: 30,
    DEFAULT_MESSAGE: '煮鸡蛋',
    MESSAGE_LINE_INDEX: 18,
    CRON_LINE_INDEX: 9,
    MIN_FILE_LINES: 10
};
// 用户提示消息 (保持原有内容不变)
const MESSAGES = {
    INPUT_PROMPT: [
        '请在30秒内输入数字，选择你要停止/启动的js脚本',
        '可多选，请使用中英文的逗号或句号或者'-'区隔',
        '超时默认不选择js脚本，按q或Q可直接退出'
    ],
    DELETE_SUCCESS: '上述提醒已删除!',
    INPUT_ERROR: '错误！输入的数字有误\n请重新输入',
    EXIT_MESSAGE: '已退出'
};
// 正则表达式
const REGEX = {
    CRON_PATTERN: /\*\s*@cron\s+([*\d,-]+)\s+([*\d,-]+)\s+([*\d,-]+)\s+([*\d,-]+)\s+([*\d,-]+)\s+([*\d,-]+)/,
    MESSAGE_PATTERN: /msg: '([^']+)'/,
    INPUT_SEPARATORS: /[.,，。]/
};

module.exports = async s => {
    const jsFiles = fs.readdirSync(CONFIG.REMINDER_DIR).filter(file => path.extname(file) === '.js');
    const [remain_jsFiles,js_file_array] = deleteOutDateAndTraverse(jsFiles)
    const reply_js_file_array = js_file_array.map((item, index) => { return `${index} --> ${item.key}:${item.value}` });
    reply_js_file_array.push('请在30秒内输入数字，选择你要停止/启动的js脚本', '可多选，请使用中英文的逗号或句号或者‘-’区隔', '超时默认不选择js脚本，按q或Q可直接退出');
    s.reply(reply_js_file_array.join('\n\n'));
    const newMsg = await s.waitInput(() => {}, 30);
    if (!newMsg) return;
    const input_num = newMsg.getMsg();
    if (input_num && input_num != 'q' && input_num != 'Q') {
        const filtered_jsfiles = get_input_num(input_num, remain_jsFiles);
        if (filtered_jsfiles.length > 0) {
            filtered_jsfiles.forEach((fileName) => {
                fs.unlinkSync(path.join(CONFIG.REMINDER_DIR, fileName));
            })
            await newMsg.reply(`${filtered_jsfiles.join('\n')}\n上述提醒已删除!`);
        }else {
            await newMsg.reply('错误！输入的数字有误\n'+'请重新输入');
        }
    }else if (input_num == 'q' || input_num == 'Q') {
        await newMsg.reply('已退出')
        return
    }
    sysMethod.inline('重启');
}
function isRepeatingPattern(value) {
    if (value === '*') return true;
    if (value.includes(',')) return true;
    if (value.includes('-')) return true;
    return false;
}
function formatCronForDisplay(value) {
    if (value === '*') return '每';
    if (value.includes(',')) {
        return value.split(',').join('、');
    }
    if (value.includes('-')) {
        const [start, end] = value.split('-');
        return `${start}-${end}`;
    }
    return value;
}
/**
 * 删除过期文件并遍历有效文件
 * @param {string[]} fileNames - 文件名数组
 * @returns {Array} [剩余文件数组, 文件信息数组]
 */
function deleteOutDateAndTraverse(fileNames) {
    const jsFileArray = [];
    const now = new Date();
    const currentYear = now.getFullYear();
    let remainFiles = [...fileNames];
    fileNames.forEach((fileName) => {
        try {
            const filePath = path.join(CONFIG.REMINDER_DIR, fileName);
            if (!fs.existsSync(filePath)) {
                console.log(`文件 ${fileName} 不存在，跳过处理`);
                return;
            }
            const fileContent = fs.readFileSync(filePath, 'utf8');
            const lines = fileContent.split('\n');

            if (lines.length < CONFIG.MIN_FILE_LINES) {
                console.log(`文件 ${fileName} 行数不足${CONFIG.MIN_FILE_LINES}行，跳过处理`);
                return;
            }
            const tenthLine = lines[CONFIG.CRON_LINE_INDEX];
            // 匹配6字段格式：秒 分 时 日 月 周
            const cronMatch = tenthLine.match(REGEX.CRON_PATTERN);
            if (!cronMatch) {
                console.log(`文件 ${fileName} 第十行不是预期的 crontab 格式，跳过处理`);
                return;
            }
            const [, second, minute, hour, day, month] = cronMatch;
            if (!isRepeatingPattern(second) && !isRepeatingPattern(minute) &&
                !isRepeatingPattern(hour) && !isRepeatingPattern(day) &&
                !isRepeatingPattern(month)) {
                const cronTime = new Date(currentYear, parseInt(month) - 1, parseInt(day),
                                        parseInt(hour), parseInt(minute), parseInt(second));
                if (cronTime < now) {
                    fs.unlinkSync(filePath);
                    remainFiles = remainFiles.filter(item => item !== fileName);
                    console.log(`删除过时文件: ${fileName} (时间: ${cronTime.toLocaleString()})`);
                    return;
                }
            }
            const scheduleTime = formatScheduleTime6Fields(second, minute, hour, day, month);
            // 提取消息内容
            let value = CONFIG.DEFAULT_MESSAGE;
            if (lines.length >= CONFIG.MESSAGE_LINE_INDEX + 1) {
                const matchResult = lines[CONFIG.MESSAGE_LINE_INDEX].match(REGEX.MESSAGE_PATTERN);
                if (matchResult && matchResult[1]) {
                    value = matchResult[1];
                }
            }
            jsFileArray.push({
                key: scheduleTime,
                value: value,
            });
        } catch (error) {
            console.error(`处理文件 ${fileName} 时出错:`, error.message);
        }
    });
    return [remainFiles, jsFileArray];
}
/**
 * 格式化6字段时间显示
 * @param {string} second - 秒
 * @param {string} minute - 分
 * @param {string} hour - 时
 * @param {string} day - 日
 * @param {string} month - 月
 * @returns {string} 格式化的时间字符串
 */
function formatScheduleTime6Fields(second, minute, hour, day, month) {
    if (second === '*' || minute === '*' || hour === '*' || day === '*' || month === '*') {
        const secondDisplay = second === '*' ? '每秒' : `${formatCronForDisplay(second)}秒`;
        const minuteDisplay = minute === '*' ? '每分钟' : `${formatCronForDisplay(minute)}分`;
        const hourDisplay = hour === '*' ? '每小时' : `${formatCronForDisplay(hour)}点`;
        const dayDisplay = day === '*' ? '每天' : `${formatCronForDisplay(day)}日`;
        const monthDisplay = month === '*' ? '每月' : `${formatCronForDisplay(month)}月`;
        if (day === '*' && month === '*') {
            return `${dayDisplay} ${hourDisplay}${minuteDisplay}${secondDisplay}`;
        } else {
            return `${monthDisplay}${dayDisplay} ${hourDisplay}${minuteDisplay}${secondDisplay}`;
        }
    } else {
        return `${month}月${day}日 ${hour}点${minute}分${second}秒`;
    }
}
/**
 * 解析用户输入的数字选择
 * @param {string} inputNumbers - 用户输入的数字字符串
 * @param {Array} array - 文件数组
 * @returns {Array} 选中的文件列表
 */
function get_input_num(inputNumbers, array) {
    try {
        let numbers = [];
        if (inputNumbers.includes('-')) {
            // 处理范围选择，如 "1-8"
            const range = inputNumbers.split('-').map(Number);
            if (range.length === 2 && !isNaN(range[0]) && !isNaN(range[1])) {
                for (let i = range[0]; i <= range[1]; i++) {
                    numbers.push(i);
                }
            }
        } else {
            // 处理逗号分隔的选择，如 "1,3,5"
            numbers = inputNumbers.split(REGEX.INPUT_SEPARATORS)
                .map(num => parseInt(num.trim(), 10))
                .filter(num => !isNaN(num));
        }
        // 验证数字范围并映射到文件
        const validNumbers = numbers.filter(num => num >= 0 && num < array.length);
        if (validNumbers.length !== numbers.length) {
            console.log('部分输入数字超出范围');
            return [];
        }
        // 去重并排序
        const uniqueNumbers = [...new Set(validNumbers)].sort((a, b) => a - b);
        const selectedFiles = uniqueNumbers.map(num => array[num]);
        console.log(`用户选择了 ${selectedFiles.length} 个文件`);
        return selectedFiles;
    } catch (error) {
        console.error('解析用户输入数字时发生错误:', error);
        return [];
    }
}

/**
 * 获取JavaScript文件列表
 * @returns {Promise<string[]>} JS文件列表
 */
async function getJavaScriptFiles() {
    try {
        if (!fs.existsSync(CONFIG.REMINDER_DIR)) {
            console.log('reminder目录不存在');
            return [];
        }
        const files = fs.readdirSync(CONFIG.REMINDER_DIR);
        const jsFiles = files.filter(file => path.extname(file) === '.js');
        console.log(`找到 ${jsFiles.length} 个JS文件`);
        return jsFiles;
    } catch (error) {
        console.error('获取JS文件列表时发生错误:', error);
        return [];
    }
}

/**
 * 构建回复数组
 * @param {Array} fileArray - 文件信息数组
 * @returns {string[]} 回复数组
 */
function buildReplyArray(fileArray) {
    const replyArray = fileArray.map((item, index) =>
        `${index} --> ${item.key}:${item.value}`
    );
    replyArray.push(...MESSAGES.INPUT_PROMPT);
    return replyArray;
}
/**
 * 等待用户输入
 * @param {Object} s - 消息对象
 * @returns {Promise<Object|null>} 用户输入消息对象
 */
async function waitForUserInput(s) {
    try {
        const newMsg = await s.waitInput(() => {}, CONFIG.WAIT_TIMEOUT);
        return newMsg;
    } catch (error) {
        console.error('等待用户输入时发生错误:', error);
        return null;
    }
}

/**
 * 处理用户选择
 * @param {Object} userInput - 用户输入消息对象
 * @param {string[]} remainFiles - 剩余文件列表
 * @param {Object} s - 消息对象
 */
async function handleUserSelection(userInput, remainFiles, s) {
    try {
        const inputText = userInput.getMsg();
        if (!inputText) return;
        if (inputText === 'q' || inputText === 'Q') {
            await userInput.reply(MESSAGES.EXIT_MESSAGE);
            return;
        }
        const selectedFiles = parseUserInput(inputText, remainFiles);
        if (selectedFiles.length > 0) {
            await deleteSelectedFiles(selectedFiles);
            await userInput.reply(`${selectedFiles.join('\n')}\n${MESSAGES.DELETE_SUCCESS}`);
        } else {
            await userInput.reply(MESSAGES.INPUT_ERROR);
        }
    } catch (error) {
        console.error('处理用户选择时发生错误:', error);
        await userInput.reply('处理选择时发生错误，请重试');
    }
}

/**
 * 解析用户输入
 * @param {string} inputText - 用户输入文本
 * @param {string[]} fileArray - 文件数组
 * @returns {string[]} 选中的文件列表
 */
function parseUserInput(inputText, fileArray) {
    try {
        return get_input_num(inputText, fileArray);
    } catch (error) {
        console.error('解析用户输入时发生错误:', error);
        return [];
    }
}
/**
 * 删除选中的文件
 * @param {string[]} selectedFiles - 选中的文件列表
 */
async function deleteSelectedFiles(selectedFiles) {
    try {
        for (const fileName of selectedFiles) {
            const filePath = path.join(CONFIG.REMINDER_DIR, fileName);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                console.log(`已删除文件: ${fileName}`);
            }
        }
    } catch (error) {
        console.error('删除文件时发生错误:', error);
        throw error;
    }
}