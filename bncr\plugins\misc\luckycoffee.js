/**
 * <AUTHOR>
 * @name luckycoffee
 * @team hhgg
 * @version 1.0.1
 * @description 通过券码，实现瑞幸一键自动下单
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule ^(瑞幸)(.*)$
 * @admin false
 * @disable false
 * @public false
 */

// 依赖模块检查和加载
sysMethod.testModule(['axios'], { install: true });
const axios = require('axios');
const { requestN, sleep, extract_info_normal, again, sendMessage } = require('./mod/utils');

// 数据库实例
const bncrDB = new BncrDB('luckycoffee_coupon');

// 常量配置
const CONFIG = {
    BRAND_ID: 30001,
    MERCHANT_ID: 10000,
    APP_PLATFORM_TYPE: 1,
    BUSINESS_MERCHANT_APP_CONFIG_ID: '26cd3c230d884b408658f49f926ee47b',
    DEFAULT_CONTACT_MOBILE: '***********',
    DEFAULT_LATITUDE: 22.533327,
    DEFAULT_LONGITUDE: 113.993415,
    QR_SERVICE_URL: 'http://***************:3211/qrimg=',
    SLEEP_INTERVAL: {
        ORDER_SUBMIT: 1000,
        ORDER_CHECK: 2000
    }
};

// 通用请求头
const COMMON_HEADERS = {
    'appPlatformType': CONFIG.APP_PLATFORM_TYPE,
    'merchantId': CONFIG.MERCHANT_ID,
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090b09)XWEB/11065'
};

// API端点
const API_ENDPOINTS = {
    CITY_LIST: 'https://tsc-locallife-mall-api.open.fulu.com/api/Common/GetCityList',
    STORE_LIST: 'https://tsc-locallife-mall-api.open.fulu.com/api/BrandStore/GetList',
    STORE_PRODUCTS: 'https://tsc-locallife-mall-api.open.fulu.com/api/Product/GetStoreProductsCoupon',
    PRODUCT_DETAIL: 'https://tsc-locallife-mall-api.open.fulu.com/api/Product/GetStoreProductDetail',
    CREATE_ORDER: 'https://tsc-locallife-mall-api.open.fulu.com/api/Order/CouponCreateOrderForApi',
    ORDER_DETAIL: 'https://tsc-locallife-mall-api.open.fulu.com/api/Order/GetOrderDetailByCoupon'
};

// AI提示词模板
const AI_PROMPT_TEMPLATE = `
'''
1.请分析上面的信息并按照如下模板进行回复，不需要回复多余文字和字符。
{
   "city": "<城市名，例如 '北京'>",
   "store": "<店铺名称，例如 '西直门'>",
   "coffee": "<咖啡名称，例如 '标准美式'>",
   "quantity": <咖啡的数量，例如 '1'>,
   "takeaway": <是否需要打包，'1'或'2'二者选一，'2'代表需要，'1'代表不需要>,
   "temp": "<温度偏好，严格按'冰'或'热'二者选一>",
   "preference": "<口味偏好，例如含糖量多少、奶量、是否加奶油、奶的种类、茶的类型等描述>",
   "comment": <特殊需求，例如"少冰"或"去冰">
}

2.'coffee'字段严格按照已提供的信息原文回应。
3.'quantity'字段代表需要下单的咖啡数量，如无法找到该字段的信息，则回复默认值'1'。
4.'takeaway'字段代表是否需要打包，如无法找到该字段的信息，则回复默认值'1'。
5.'temp'字段需要以数组形式([])回应，如无法找到该字段的信息，则回复默认值'冰'。
6.'preference'字段需要以数组形式([])回应，严格按照已提供的信息原文回应，如无法找到该字段的信息，则回复默认值[]。
7.'comment'字段需要以数组形式([])回应，只能从关键字("少冰", "去冰")中二选一回答，如无法找到该字段的信息或与给定的关键字不符合，则回复默认值[]。
'''`;
/**
 * 主函数 - 处理瑞幸咖啡下单流程
 * @param {Object} s - 消息对象
 */
module.exports = async (s) => {
    let userId;
    try {
        // 获取用户输入信息
        const info = s.param(2) || await again(s, '请在30秒内输入订单信息') || null;
        userId = s.getUserId();
        if (!info) {
            await sendMessage(userId, '未获取到订单信息，操作已取消');
            return;
        }
        // 使用AI提取订单信息
        const orderData = await extractOrderInfo(info, userId);
        if (!orderData) return;
        // 处理订单流程
        await processOrder(orderData, userId, s);
    } catch (error) {
        console.error('瑞幸下单流程发生错误:', error);
        if (userId) {
            await sendMessage(userId, `处理订单时发生错误: ${error.message || '未知错误'}`);
        }
    }
};

/**
 * 提取订单信息
 * @param {string} info - 用户输入信息
 * @param {string} userId - 用户ID
 * @returns {Object|null} 订单数据
 */
async function extractOrderInfo(info, userId) {
    try {
        const prompt = info + AI_PROMPT_TEMPLATE;
        const data = await extract_info_normal(prompt);
        // 验证必要字段
        if (!data.city || !data.store || !data.coffee) {
            await sendMessage(userId, '订单信息不完整，请提供城市、店铺和咖啡信息');
            return null;
        }
        await sendMessage(userId, `解析的订单信息:\n${JSON.stringify(data, null, 2)}`);
        return data;
    } catch (error) {
        console.error('提取订单信息失败:', error);
        await sendMessage(userId, '解析订单信息失败，请重新输入');
        return null;
    }
}
/**
 * 处理订单流程
 * @param {Object} orderData - 订单数据
 * @param {string} userId - 用户ID
 * @param {Object} s - 消息对象
 */
async function processOrder(orderData, userId, s) {
    const { city, store: address, coffee, quantity, takeaway: taketype, comment } = orderData;
    const order = {
        temperature: orderData.temp,
        preference: orderData.preference,
    };
    // 1. 获取城市代码
    const cityCode = await getCityCode(city);
    if (!cityCode) {
        await sendMessage(userId, `未找到城市 "${city}" 的信息，请检查城市名称`);
        return;
    }
    // 2. 查找店铺
    const selectedStore = await selectStore(cityCode, address, userId, s);
    if (!selectedStore) return;
    // 3. 获取券码
    const [selectedCoupons, remainingCoupons] = await getCoupons('coupon', quantity, '券码暂缺，请尽快购买！', userId);
    if (!selectedCoupons) return;
    // 4. 选择咖啡产品
    const selectedProduct = await selectCoffeeProduct(selectedStore.id, selectedCoupons[0], coffee, userId, s);
    if (!selectedProduct) return;
    // 5. 获取产品规格
    const specs = await getOrderSpec(selectedProduct.id, selectedStore.id);
    if (!specs) {
        await sendMessage(userId, '获取产品规格失败，请稍后重试');
        return;
    }
    // 6. 处理规格选择
    const processedSpecs = processProductSpecs(specs, order);
    // 7. 提交订单
    await submitOrders(selectedCoupons, selectedProduct, selectedStore, processedSpecs, taketype, comment, userId);
    // 8. 更新券码数据库
    await bncrDB.set('coupon', remainingCoupons.length > 0 ? remainingCoupons.join(',') : '');
    // 9. 监控取餐码
    await monitorPickupCodes(selectedCoupons, selectedProduct, userId);
}

/**
 * 选择店铺
 * @param {string} cityCode - 城市代码
 * @param {string} address - 店铺地址关键词
 * @param {string} userId - 用户ID
 * @param {Object} s - 消息对象
 * @returns {Object|null} 选中的店铺
 */
async function selectStore(cityCode, address, userId, s) {
    try {
        let stores = await getStoreId(cityCode, address);
        if (!stores || stores.length === 0) {
            await sendMessage(userId, '未查找到店铺，请检查店铺名称或地址');
            return null;
        }
        if (stores.length === 1) {
            console.log(`找到店铺: ${stores[0].name}`);
            return stores[0];
        }
        // 多个店铺时让用户选择
        const storeList = stores.map((item, index) =>
            `${index + 1}. ${item.name}\n   地址: ${item.address}`
        ).join('\n\n');
        const chosenNum = await again(s, `查询到多个店铺，请选择：\n${storeList}`);
        if (!chosenNum || chosenNum < 1 || chosenNum > stores.length) {
            await sendMessage(userId, '选择无效，操作已取消');
            return null;
        }
        const selectedStore = stores[chosenNum - 1];
        console.log(`用户选择店铺: ${selectedStore.name}`);
        return selectedStore;

    } catch (error) {
        console.error('选择店铺时发生错误:', error);
        await sendMessage(userId, '查询店铺信息失败，请稍后重试');
        return null;
    }
}

/**
 * 选择咖啡产品
 * @param {string} storeId - 店铺ID
 * @param {string} coupon - 券码
 * @param {string} coffeeName - 咖啡名称
 * @param {string} userId - 用户ID
 * @param {Object} s - 消息对象
 * @returns {Object|null} 选中的咖啡产品
 */
async function selectCoffeeProduct(storeId, coupon, coffeeName, userId, s) {
    try {
        const availProducts = await getStoreAvailProduct(storeId, coupon);
        if (!availProducts || availProducts.length === 0) {
            await sendMessage(userId, '该店铺暂无可用产品');
            return null;
        }
        // 精确匹配
        let matchedCoffee = availProducts.find(product => product.name === coffeeName);
        if (matchedCoffee) {
            console.log(`精确匹配到咖啡: ${matchedCoffee.name}`);
            return matchedCoffee;
        }
        // 模糊匹配
        const fuzzyMatches = availProducts.filter(product =>
            product.name.includes(coffeeName)
        );
        if (fuzzyMatches.length === 0) {
            await sendMessage(userId, `未找到匹配的咖啡 "${coffeeName}"，请检查咖啡名称`);
            return null;
        }
        if (fuzzyMatches.length === 1) {
            console.log(`模糊匹配到咖啡: ${fuzzyMatches[0].name}`);
            return fuzzyMatches[0];
        }
        // 多个匹配时让用户选择
        const coffeeList = fuzzyMatches.map((item, index) =>
            `${index + 1}. ${item.name} ${item.issellout ? '(售罄)' : '(有库存)'} ¥${item.saleprice}`
        ).join('\n');
        const chosenNum = await again(s, `查询到多个咖啡，请选择：\n${coffeeList}`);
        if (!chosenNum || chosenNum < 1 || chosenNum > fuzzyMatches.length) {
            await sendMessage(userId, '选择无效，操作已取消');
            return null;
        }
        const selectedCoffee = fuzzyMatches[chosenNum - 1];
        console.log(`用户选择咖啡: ${selectedCoffee.name}`);
        return selectedCoffee;
    } catch (error) {
        console.error('选择咖啡产品时发生错误:', error);
        await sendMessage(userId, '查询咖啡信息失败，请稍后重试');
        return null;
    }
}

/**
 * 处理产品规格
 * @param {Object} specs - 产品规格
 * @param {Object} order - 订单偏好
 * @returns {Object} 处理后的规格
 */
function processProductSpecs(specs, order) {
    const valuesInOrder = Object.values(order).flat();
    specs.specs = specs.specs.map(item => {
        if (item.specItems && item.specItems.length >= 1) {
            // 查找精确匹配的规格项
            const exactMatch = item.specItems.find(specItem =>
                valuesInOrder.some(value => specItem.specItemName.includes(value))
            );
            if (exactMatch) {
                return { ...item, specItems: [exactMatch] };
            }
            // 使用默认规格项
            return { ...item, specItems: [item.specItems[0]] };
        }
        return item;
    });
    console.log('处理后的规格:', JSON.stringify(specs.specs, null, 2));
    return specs;
}

/**
 * 提交订单
 * @param {Array} coupons - 券码数组
 * @param {Object} product - 产品信息
 * @param {Object} store - 店铺信息
 * @param {Object} specs - 产品规格
 * @param {number} takeType - 取餐类型
 * @param {Array} comment - 备注信息
 * @param {string} userId - 用户ID
 */
async function submitOrders(coupons, product, store, specs, takeType, comment, userId) {
    const valuesInOrder = specs.specs.map(spec => spec.specItems[0].specItemName);
    for (const coupon of coupons) {
        try {
            const products = [{
                "attrText": valuesInOrder.join('/'),
                "buyNum": 1,
                "id": product.id,
                "memberPrice": 0,
                "name": product.name,
                "officialPrice": product.officialprice,
                "picUrl": product.picurl,
                "productId": product.id,
                "salePrice": product.saleprice,
                "specs": specs.specs,
                "totalPrice": product.officialprice
            }];
            // 第一次提交订单
            const firstOrder = {
                "activityTypeEnum": 0,
                "brandId": CONFIG.BRAND_ID,
                "contactMobile": null,
                "couponCode": coupon,
                "isCalcMemberPrice": true,
                "isCouponPrice": true,
                "orderCode": null,
                "storeId": store.id,
                "takeType": 1,
                "products": products
            };
            const firstResult = await postOrder(firstOrder, 1);
            if (!firstResult.flag) {
                const errorMsg = `券码 ${coupon} 第1次提交订单失败`;
                console.error(errorMsg, firstResult.body);
                await sendMessage(userId, `${errorMsg}: ${JSON.stringify(firstResult.body, null, 2)}`);
                continue; // 继续处理下一个券码
            }
            // 第二次提交订单
            products[0].totalPrice = product.saleprice;
            const secondOrder = {
                "contactMobile": CONFIG.DEFAULT_CONTACT_MOBILE,
                "couponCode": coupon,
                "storeId": store.id,
                "takeType": takeType,
                "products": products,
                "userComments": Array.isArray(comment) ? comment.join(',') : ''
            };
            const secondResult = await postOrder(secondOrder, 2);
            if (!secondResult.flag) {
                const errorMsg = `券码 ${coupon} 第2次提交订单失败`;
                console.error(errorMsg, secondResult.body);
                await sendMessage(userId, `${errorMsg}: ${JSON.stringify(secondResult.body, null, 2)}`);
                continue;
            }
            console.log(`券码 ${coupon} 订单提交成功: ${product.name} @ ${store.name}`);
            await sleep(CONFIG.SLEEP_INTERVAL.ORDER_SUBMIT);
        } catch (error) {
            console.error(`处理券码 ${coupon} 时发生错误:`, error);
            await sendMessage(userId, `处理券码 ${coupon} 时发生错误: ${error.message}`);
        }
    }
    // 发送成功消息
    await sendMessage(userId,
        `已成功提交券码:\n${coupons.join('\n')}\n` +
        `产品: ${product.name}\n` +
        `店铺: ${store.name}`
    );
}

/**
 * 监控取餐码
 * @param {Array} coupons - 券码数组
 * @param {Object} product - 产品信息
 * @param {string} userId - 用户ID
 */
async function monitorPickupCodes(coupons, product, userId) {
    const activeCoupons = [...coupons]; // 创建副本避免修改原数组
    while (activeCoupons.length > 0) {
        try {
            const codes = await getOrderCode(activeCoupons);
            for (let i = codes.length - 1; i >= 0; i--) {
                const code = codes[i];
                if (code && Object.keys(code).length > 0) {
                    console.log('取餐码已生成:', code);
                    // 生成二维码图片URL
                    const qrKey = code.qrUrl && code.qrUrl.includes('svg') ? 'qrCode' : 'qrUrl';
                    const qrInfo = `${code.storeName}---${product.name}---取餐码：${code.takeCode}---${code[qrKey]}`;
                    const qrImageUrl = `${CONFIG.QR_SERVICE_URL}${encodeURIComponent(qrInfo)}`;
                    // 发送取餐码信息
                    try {
                        await sysMethod.push({
                            platform: 'wxXyo',
                            userId: userId,
                            path: qrImageUrl,
                            type: 'image'
                        });
                        await sendMessage(userId,
                            `取餐码已生成！\n` +
                            `店铺: ${code.storeName}\n` +
                            `产品: ${product.name}\n` +
                            `取餐码: ${code.takeCode}`
                        );
                    } catch (pushError) {
                        console.error('发送取餐码图片失败:', pushError);
                        await sendMessage(userId,
                            `取餐码: ${code.takeCode}\n` +
                            `店铺: ${code.storeName}\n` +
                            `产品: ${product.name}`
                        );
                    }
                    // 从活跃券码列表中移除已处理的券码
                    activeCoupons.splice(i, 1);
                }
            }
            if (activeCoupons.length > 0) {
                await sleep(CONFIG.SLEEP_INTERVAL.ORDER_CHECK);
            }
        } catch (error) {
            console.error('监控取餐码时发生错误:', error);
            await sleep(CONFIG.SLEEP_INTERVAL.ORDER_CHECK);
        }
    }
    console.log('所有取餐码监控完成');
}

/**
 * 获取城市代码
 * @param {string} cityName - 城市名称
 * @returns {string|null} 城市代码
 */
async function getCityCode(cityName) {
    try {
        const option = {
            url: API_ENDPOINTS.CITY_LIST,
            method: "get",
            headers: COMMON_HEADERS,
            json: true,
        };
        const [response, body] = await requestN(option);
        if (response.statusCode === 200 && body.code === '0') {
            const city = body.data.list.find(item => item.city.includes(cityName));
            return city ? city.cityCode : null;
        }
        console.error('获取城市列表失败:', body);
        return null;
    } catch (error) {
        console.error('获取城市代码时发生错误:', error);
        return null;
    }
}

/**
 * 获取店铺列表
 * @param {string} cityCode - 城市代码
 * @param {string} keyword - 搜索关键词
 * @returns {Array|null} 店铺列表
 */
async function getStoreId(cityCode, keyword) {
    try {
        const url = `${API_ENDPOINTS.STORE_LIST}?brandId=${CONFIG.BRAND_ID}&cityCode=${cityCode}&searchType=0&latitude=${CONFIG.DEFAULT_LATITUDE}&longitude=${CONFIG.DEFAULT_LONGITUDE}&condition=${encodeURIComponent(keyword)}`;
        const option = {
            url: url,
            method: "get",
            headers: COMMON_HEADERS,
            json: true,
        };
        const [response, body] = await requestN(option);
        if (response.statusCode === 200 && body.code === '0') {
            return body.data.list.map(item => ({
                name: item.name,
                address: item.address,
                id: item.id,
            }));
        }
        console.error('获取店铺列表失败:', body);
        return null;
    } catch (error) {
        console.error('获取店铺ID时发生错误:', error);
        return null;
    }
}

/**
 * 获取店铺可用产品
 * @param {string} storeId - 店铺ID
 * @param {string} coupon - 券码
 * @returns {Array|null} 产品列表
 */
async function getStoreAvailProduct(storeId, coupon) {
    try {
        const url = `${API_ENDPOINTS.STORE_PRODUCTS}?brandId=${CONFIG.BRAND_ID}&storeId=${storeId}&takeType=2&BrandCategoryType=1&IsShowSellout=false&CouponCode=${coupon}`;
        const option = {
            url: url,
            method: "get",
            headers: COMMON_HEADERS,
            json: true,
        };
        const [response, body] = await requestN(option);
        if (response.statusCode === 200 && body.code === '0') {
            return body.data.list[0].products.map(item => ({
                name: item.name,
                id: item.id,
                issellout: item.isSellOut,
                officialprice: item.officialPrice,
                saleprice: item.salePrice,
                picurl: item.picUrl,
            }));
        }
        console.error('获取店铺产品失败:', body);
        return null;
    } catch (error) {
        console.error('获取店铺可用产品时发生错误:', error);
        return null;
    }
}

/**
 * 获取订单规格
 * @param {string} productId - 产品ID
 * @param {string} storeId - 店铺ID
 * @returns {Object|null} 产品规格
 */
async function getOrderSpec(productId, storeId) {
    try {
        const url = `${API_ENDPOINTS.PRODUCT_DETAIL}?productId=${productId}&storeId=${storeId}&searchSpecPriceFlag=true`;
        const option = {
            url: url,
            method: "get",
            headers: COMMON_HEADERS,
            json: true,
        };
        const [response, body] = await requestN(option);
        if (response.statusCode === 200 && body.code === '0') {
            // 处理规格数据，将默认项放在首位
            body.data.data = body.data.data.map(spec => {
                const defaultItem = spec.items.find(item => item.isDefault);
                if (defaultItem && spec.items[0] !== defaultItem) {
                    spec.items = [defaultItem, ...spec.items.filter(item => item !== defaultItem)];
                }
                return spec;
            });
            const specs = {
                "specs": body.data.data.map(spec => ({
                    "specId": spec.id,
                    "specName": spec.name,
                    "choicesCode": null,
                    "specItems": spec.items.map(item => ({
                        "specItemId": item.id,
                        "specItemName": item.name,
                        "buyNum": 1,
                        "price": item.price,
                        "specItemCode": null,
                        "choicesCode": null
                    }))
                }))
            };
            return specs;
        }
        console.error('获取产品规格失败:', body);
        return null;
    } catch (error) {
        console.error('获取订单规格时发生错误:', error);
        return null;
    }
}

/**
 * 提交订单
 * @param {Object} params - 订单参数
 * @param {number} attempt - 提交次数
 * @returns {Object} 提交结果
 */
async function postOrder(params, attempt = 1) {
    try {
        const headers = {
            ...COMMON_HEADERS,
            'businessMerchantAppConfigId': CONFIG.BUSINESS_MERCHANT_APP_CONFIG_ID,
            'content-type': 'application/json'
        };
        const option = {
            url: API_ENDPOINTS.CREATE_ORDER,
            method: "post",
            headers: headers,
            body: params,
            json: true,
        };
        const [response, body] = await requestN(option);
        const result = { body: body };
        if (response.statusCode === 200 && body.code === '0' && body.message === 'ok') {
            console.log(`第${attempt}次提交订单成功`);
            result.flag = true;
        } else {
            console.log(`第${attempt}次提交订单失败:`, body);
            result.flag = false;
        }
        return result;
    } catch (error) {
        console.error(`第${attempt}次提交订单时发生错误:`, error);
        return { flag: false, body: { error: error.message } };
    }
}

/**
 * 获取订单取餐码
 * @param {Array} coupons - 券码数组
 * @returns {Array} 取餐码信息数组
 */
async function getOrderCode(coupons) {
    try {
        const responses = await Promise.all(
            coupons.map(coupon =>
                axios.get(`${API_ENDPOINTS.ORDER_DETAIL}/${coupon}`, {
                    headers: COMMON_HEADERS
                })
            )
        );
        return responses.map(response => {
            try {
                if (response.data.data.orderStatusDesc.includes('成功')) {
                    return {
                        storeName: response.data.data.storeName,
                        qrUrl: response.data.data.takes[0].qrUrl,
                        takeCode: response.data.data.takes[0].takeCode,
                        qrCode: response.data.data.takes[0].qrCode,
                    };
                }
                return {};
            } catch (parseError) {
                console.error('解析订单详情失败:', parseError);
                return {};
            }
        });
    } catch (error) {
        console.error('获取订单取餐码时发生错误:', error);
        return coupons.map(() => ({})); // 返回空对象数组
    }
}

/**
 * 获取券码
 * @param {string} type - 券码类型
 * @param {number} quantity - 需要数量
 * @param {string} message - 缺少券码时的提示消息
 * @param {string} userId - 用户ID
 * @returns {Array} [选中的券码, 剩余的券码]
 */
async function getCoupons(type, quantity, message, userId) {
    try {
        const couponsString = await bncrDB.get(type);
        if (!couponsString) {
            await sendMessage(userId, message);
            return [null, null];
        }
        const couponsArray = couponsString.split(',').filter(coupon => coupon.trim());
        if (couponsArray.length < quantity) {
            await sendMessage(userId, `${type}券码还需${quantity - couponsArray.length}张，请补充！`);
            return [null, null];
        }
        const selected = couponsArray.slice(0, quantity);
        const remaining = couponsArray.slice(quantity);
        console.log(`获取到${selected.length}张券码，剩余${remaining.length}张`);
        return [selected, remaining];
    } catch (error) {
        console.error('获取券码时发生错误:', error);
        await sendMessage(userId, '获取券码失败，请稍后重试');
        return [null, null];
    }
}