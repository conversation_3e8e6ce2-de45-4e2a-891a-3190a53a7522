/**作者
 * <AUTHOR>
 * @name luckycoffee_coupon
 * @team hhgg
 * @version 1.0.0
 * @description 检查券码有效性，并存入可用券码
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule ^(券码)(.*)$
 * @admin false
 * @disable false
 * @public false
 */

const {requestN, sleep, sendMessage} = require('./mod/utils');

const bncrDB = new BncrDB('luckycoffee_coupon');

module.exports = async s => {
    let info = ''
    const userId = s.getUserId();
    const inputString = s.param(2) || null;
    const db = 'coupon'
    if (!inputString) {
        let coupons = await bncrDB.get(db);
        let filteredCoupons = [];
        if (coupons) {
            let couponsArray = coupons.split(',');
            for (let coupon of couponsArray) {
                const checkCoupon = await getOrderCode(coupon);
                await sleep(1000);
                if (Object.keys(checkCoupon).length > 0) {
                    console.log(`${coupon}：券码已用/作废，即将删除！`);
                } else {
                    console.log(`${coupon}：券码可用`);
                    info += `${coupon}\n`
                    filteredCoupons.push(coupon); // 将可用的券码添加到数组中
                }
            }
            await bncrDB.set(db, filteredCoupons.join(','));
        }
        info += `可用券码共：${filteredCoupons.length}张\n\n`
        await sendMessage(userId,info.replace(/\n\n$/, ''))
        return;
    }
    if (inputString.includes('淘宝网') && inputString.includes('瑞幸咖啡')) {
        info = await updateCoupons(inputString, db)
    } else {
        info = '无法提取瑞幸券码，请检查！'
    }
    await sendMessage(userId,info)
}

async function getOrderCode(coupon){
    const option = {
        url: `https://tsc-locallife-mall-api.open.fulu.com/api/Default/UseCoupon/${coupon}`,
        method: "post",
        headers: {'merchantid': 10000, 'appplatformtype': 3, 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0'},
        body: {"operateType":0},
        json: true,
    };
    const [response,body] = await requestN(option);
    // console.log(JSON.stringify(body.data, null, 2))
    console.log(body)
    if (response.statusCode === 200 && body.code === '0') {
        if (body.data.type === 2) return {brandCode: body.data.brandCode}
        return {};
    }
    if (body.code === '-1' && body.message.includes('作废')) return { takeCode: '券码已作废' };
    return {};
}

async function updateCoupons(inputString, dbKey) {
    let existingCoupons = await bncrDB.get(dbKey);
    const regex = /\b[A-Z0-9]{15,16}\b/g;
    const matches = inputString.match(regex) || [];
    let updatedCouponsArray;
    if (existingCoupons) {
        const existingCouponsArray = existingCoupons.split(',').map(coupon => coupon.trim());
        updatedCouponsArray = Array.from(new Set(existingCouponsArray.concat(matches)));
    } else {
        updatedCouponsArray = Array.from(new Set(matches));
    }
    const updatedCoupons = updatedCouponsArray.join(',');
    console.log(`${updatedCoupons}`);
    await bncrDB.set(dbKey, updatedCoupons);
    return `新增${matches.length}个券码，共计${updatedCouponsArray.length}个`
}
