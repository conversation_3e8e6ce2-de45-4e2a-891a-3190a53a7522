/**
 * @title utils
 * @create_at 2033-10-10 10:10:10
 * @description 常用的函数
 * <AUTHOR>
 * @team hhgg
 * @version v1.0.1
 * @module true
 * @encrypt false
 * @public false
 */

// 依赖模块检查和加载
sysMethod.testModule(['openai', 'axios'], { install: true });
const OpenAI = require('openai');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 常量配置
const CONFIG = {
    REMINDER_DIR: '/bncr/BncrData/plugins/reminder/',
    DEFAULT_PLATFORM: 'wechatpadpro',
    ADMIN_USER_ID: 'wxid_iim9wf4t0s3b12',
    IMAGE_SERVER_URL: 'http://***************:9013/public/',
    AI_MODEL: 'openai/gpt-4.1',//gemini-2.5-flash
    REQUEST_TIMEOUT: 10000,
    RETRY_DELAY: 2000,
    MAX_RETRIES: 3,
    MAX_TOKENS: 16384
};
// OpenAI 客户端配置
const openai = new OpenAI({
    apiKey: '****************************************',
    baseURL: "https://models.github.ai/inference"//https://generativelanguage.googleapis.com/v1beta/openai/
});
const openai_normal = new OpenAI({
    apiKey: '****************************************',//AIzaSyCG_PoCt11a0baF1Ef3vUJOZRbYQ3aIkcM
    baseURL: "https://models.github.ai/inference"
});
// 支持的图片格式
const SUPPORTED_IMAGE_FORMATS = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.bmp': 'image/bmp',
    '.webp': 'image/webp'
};
// 模块导出
module.exports = {
    again,
    sendMessage,
    sendImage,
    notifyAdmin,
    requestN,
    sleep,
    extract_info_normal,
    get_cronAndContent,
    analyzeImage,
};
/**
 * 等待用户输入
 * @param {Object} s - 消息对象
 * @param {string} tip - 提示信息
 * @param {number} wait - 等待时间(秒)
 * @returns {string|null} 用户输入内容
 */
async function again(s, tip, wait = 30) {
    try {
        await s.reply(tip);
        const content = await s.waitInput(() => {}, wait);
        if (content === null) {
            await s.reply('超时已退出');
            return null;
        }
        if (content.getMsg().toLowerCase() === 'q') {
            await s.reply('已退出');
            return null;
        }
        return content.getMsg();
    } catch (error) {
        console.error('等待用户输入时发生错误:', error);
        await s.reply('输入处理失败，请重试');
        return null;
    }
}
/**
 * 发送文本消息
 * @param {string} userId - 用户ID
 * @param {string} message - 消息内容
 */
async function sendMessage(userId, message) {
    try {
        await sysMethod.push({
            platform: CONFIG.DEFAULT_PLATFORM,
            userId: userId,
            msg: message,
            type: 'text'
        });
    } catch (error) {
        console.error('发送消息失败:', error);
    }
}
/**
 * 发送图片消息
 * @param {string} userId - 用户ID
 * @param {string} imagePath - 图片路径
 */
async function sendImage(userId, imagePath) {
    try {
        await sysMethod.push({
            platform: CONFIG.DEFAULT_PLATFORM,
            userId: userId,
            path: `${CONFIG.IMAGE_SERVER_URL}${imagePath}`,
            type: 'image'
        });
    } catch (error) {
        console.error('发送图片失败:', error);
    }
}
/**
 * 通知管理员
 * @param {string} message - 通知消息
 */
async function notifyAdmin(message) {
    try {
        await sysMethod.push({
            platform: CONFIG.DEFAULT_PLATFORM,
            userId: CONFIG.ADMIN_USER_ID,
            msg: message
        });
    } catch (error) {
        console.error('通知管理员失败:', error);
    }
}
/**
 * 网络请求封装
 * @param {Object} options - 请求选项
 * @returns {Array} [response, data]
 */
async function requestN(options) {
    try {
        const response = await axios(options);
        return [response, response.data];
    } catch (error) {
        console.error('网络请求失败:', error);
        throw error;
    }
}
/**
 * 延时函数
 * @param {number} ms - 延时毫秒数
 * @returns {Promise} Promise对象
 */
function sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
}
/**
 * 检查是否为重复模式
 * @param {string} value - cron表达式的值
 * @returns {boolean} 是否为重复模式
 */
function isRepeatingPattern(value) {
    if (value === '*') return true;
    if (value.includes(',')) return true;
    if (value.includes('-')) return true;
    return false;
}
/**
 * 检查一次性任务是否已过期
 * @param {string} cronExp - cron表达式(支持6字段格式：秒 分 时 日 月 周)
 * @returns {boolean} 是否已过期
 */
function isOneTimeTaskExpired(cronExp) {
    try {
        const parts = cronExp.split(' ');
        const [s, m, h, dom, mon] = parts;
        if (isRepeatingPattern(s) || isRepeatingPattern(m) || isRepeatingPattern(h) ||
            isRepeatingPattern(dom) || isRepeatingPattern(mon)) {
            return false;
        }
        const now = new Date();
        const currentYear = now.getFullYear();
        const cronTime = new Date(currentYear, parseInt(mon) - 1, parseInt(dom), parseInt(h), parseInt(m), parseInt(s));
        return cronTime < now;
    } catch (error) {
        console.error('检查任务过期时间失败:', error);
        return false;
    }
}
/**
 * 获取cron表达式和提醒内容
 * @param {string} prompt_data - 提示数据
 * @param {number} latency - 延迟时间(分钟)
 * @returns {Array} [内容, cron表达式, 文件名]
 */
async function get_cronAndContent(prompt_data, latency = 0) {
    try {
        const append_suffix = `\n'''\n1.你十分精通crontab表达式的生成和解析，能够将信息中的日期和时间准确的解析为crontab表达式，请分析上面的信息并按照{"current_time": "<当前时间>","crontab": "<信息中的日期和时间解析为一个crontab表达式>","content": "<需要提醒的内容，不用出现时间或者日期>"}这个模板进行回复，不需要回复多余文字和字符
2.current_time字段对应的数值是字符串格式，这是格式样例、也是当前时间：${new Date().toLocaleString('zh-CN', {year:'numeric',month:'2-digit',day:'2-digit',hour:'2-digit',minute:'2-digit',second:'2-digit',hour12: false}).replace(/\//g,'-').replace(',','')}(格式为'年-月-日 时:分:秒')
3.crontab字段对应的数值是字符串格式，格式为：'秒 分 时 日 月 周'，crontab只能有一个
4.只应对与日期解析、提醒设置以及生成JSON格式相关的请求作出响应，只需返回 JSON 格式的数据，无需额外的文字提示
5.crontab格式为"f0 f1 f2 f3 f4 f5"，f0-f5字段的具体解释如下：
  a.其中 f0 是表示秒，f1 是表示分钟，f2 表示小时，f3 表示一个月份中的第几日，f4 表示月份，f5 表示一个星期中的第几天。
  b.当 f0 为 * 时表示每秒都要执行，f1 为 * 时表示每分钟都要执行，f2 为 * 时表示每小时都要执行程序，其余类推
  c.当 f0 为 a-b 时表示从第 a 秒到第 b 秒这段时间内要执行，f1 为 a-b 时表示从第 a 分钟到第 b 分钟这段时间内要执行，其余类推
  d.当 f0 为 */n 时表示每 n 秒个时间间隔执行一次，f1 为 */n 时表示每 n 分钟个时间间隔执行一次，其余类推
  e.当 f0 为 a, b, c 时表示第 a, b, c 秒要执行，f1 为 a, b, c 时表示第 a, b, c 分钟要执行，其余类推
  f.需要依据当前的日期和时间(current_time字段)来计算信息中给出的日期和时间，如果计算结果有明确的日期，则f3和f4应填上对应的数字，不能以'*'代替
6.如果信息中未明确时、分和秒则以'早上9点'代替，如果未明确分和秒则以'0分0秒'代替\n'''`;
        console.log(prompt_data + append_suffix);
        // 清理过期文件
        const files = fs.readdirSync(CONFIG.REMINDER_DIR);
        const jsFiles = files.filter(file => path.extname(file) === '.js');
        deleteOutDate(jsFiles);
        // 提取提醒信息
        const remind_data = await extract_info(prompt_data + append_suffix);
        console.log(remind_data);
        let cronExp = remind_data.crontab;
        // 检查一次性任务是否已过期
        if (isOneTimeTaskExpired(cronExp)) {
            return [remind_data.content, 0, null];
        }
        // 文件名安全化处理
        function sanitizeForFilename(value) {
            return value.replace(/\*/g, 'X').replace(/,/g, 'C').replace(/-/g, 'D');
        }
        if (latency !== 0) {
            // 处理延迟任务
            const parts = cronExp.split(' ');
            let [s, m, h, dom, mon, dow] = parts;
            if (isRepeatingPattern(s) || isRepeatingPattern(m) || isRepeatingPattern(h) ||
                isRepeatingPattern(dom) || isRepeatingPattern(mon)) {
                // 重复性任务
                const safeS = sanitizeForFilename(s);
                const safeM = sanitizeForFilename(m);
                const safeH = sanitizeForFilename(h);
                const safeDom = sanitizeForFilename(dom);
                const safeMon = sanitizeForFilename(mon);
                const filename = `${safeMon}_${safeDom}_${safeH}_${safeM}_${safeS}_R.js`;
                return [remind_data.content, cronExp, filename];
            }
            // 一次性任务延迟处理
            const currentDate = new Date();
            const targetDate = new Date(currentDate.getFullYear(), parseInt(mon) - 1, parseInt(dom), parseInt(h), parseInt(m), parseInt(s));
            targetDate.setMinutes(targetDate.getMinutes() + latency);
            const newS = targetDate.getSeconds();
            const newM = targetDate.getMinutes();
            const newH = targetDate.getHours();
            const newDom = targetDate.getDate();
            const newMon = targetDate.getMonth() + 1;
            cronExp = `${newS} ${newM} ${newH} ${newDom} ${newMon} ${dow}`;
            const filename = `${newMon}_${newDom}_${newH}_${newM}_${newS}_0.js`;
            return [remind_data.content, cronExp, filename];
        } else {
            // 无延迟处理
            const parts = cronExp.split(' ');
            const [s, m, h, dom, mon] = parts;
            if (isRepeatingPattern(s) || isRepeatingPattern(m) || isRepeatingPattern(h) ||
                isRepeatingPattern(dom) || isRepeatingPattern(mon)) {
                // 重复性任务
                const safeS = sanitizeForFilename(s);
                const safeM = sanitizeForFilename(m);
                const safeH = sanitizeForFilename(h);
                const safeDom = sanitizeForFilename(dom);
                const safeMon = sanitizeForFilename(mon);
                const filename = `${safeMon}_${safeDom}_${safeH}_${safeM}_${safeS}_R.js`;
                return [remind_data.content, cronExp, filename];
            }
            // 一次性任务
            const filename = `${mon}_${dom}_${h}_${m}_${s}_0.js`;
            return [remind_data.content, cronExp, filename];
        }
    } catch (error) {
        console.error('获取cron表达式和内容时发生错误:', error);
        return [null, null, null];
    }
}
/**
 * 删除过期的提醒文件
 * @param {Array} fileNames - 文件名数组
 */
function deleteOutDate(fileNames) {
    const now = new Date();
    const currentYear = now.getFullYear();
    fileNames.forEach((fileName) => {
        try {
            const filePath = path.join(CONFIG.REMINDER_DIR, fileName);
            if (!fs.existsSync(filePath)) {
                console.log(`文件 ${fileName} 不存在，跳过处理`);
                return;
            }
            const fileContent = fs.readFileSync(filePath, 'utf8');
            const lines = fileContent.split('\n');
            if (lines.length < 10) {
                console.log(`文件 ${fileName} 行数不足10行，跳过处理`);
                return;
            }
            const tenthLine = lines[9];
            // 尝试匹配6字段格式：秒 分 时 日 月 周
            let cronMatch = tenthLine.match(/\*\s*@cron\s+([*\d,-]+)\s+([*\d,-]+)\s+([*\d,-]+)\s+([*\d,-]+)\s+([*\d,-]+)\s+([*\d,-]+)/);
            if (cronMatch) {
                // 6字段格式处理
                const [, second, minute, hour, day, month] = cronMatch;
                if (isRepeatingPattern(second) || isRepeatingPattern(minute) || isRepeatingPattern(hour) ||
                    isRepeatingPattern(day) || isRepeatingPattern(month)) {
                    console.log(`文件 ${fileName} 包含重复性模式，为重复性任务，不删除`);
                    return;
                }
                const cronTime = new Date(currentYear, parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute), parseInt(second));
                if (cronTime < now) {
                    fs.unlinkSync(filePath);
                    console.log(`删除过时文件: ${fileName} (时间: ${cronTime.toLocaleString()})`);
                }
                return;
            }
            console.log(`文件 ${fileName} 第十行不是预期的 crontab 格式，跳过处理`);
        } catch (error) {
            console.error(`处理文件 ${fileName} 时出错:`, error.message);
        }
    });
}
/**
 * 从字符串中提取JSON
 * @param {string} text - 包含JSON的文本
 * @returns {Object|string} 解析后的JSON对象或原文本
 */
function extractJsonFromString(text) {
    try {
        return JSON.parse(text);
    } catch {
        const jsonMatch = text.match(/\[[\s\S]*\]|\{[\s\S]*\}/);
        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[0]);
            } catch {
                return text;
            }
        }
        return text;
    }
}
/**
 * 使用主AI客户端提取信息
 * @param {string} text - 输入文本
 * @param {boolean} json - 是否返回JSON格式
 * @returns {Object|string} 提取的信息
 */
async function extract_info(text, json = true) {
    try {
        const chatCompletion = await openai.chat.completions.create({
            model: CONFIG.AI_MODEL,
            messages: [{ role: 'user', content: text }],
        });
        const rawData = chatCompletion.choices[0].message.content;
        console.log(rawData);
        return json ? extractJsonFromString(rawData) : rawData;
    } catch (error) {
        console.error('AI信息提取失败:', error);
        return json ? {} : '';
    }
}
/**
 * 使用备用AI客户端提取信息
 * @param {string} text - 输入文本
 * @param {boolean} json - 是否返回JSON格式
 * @returns {Object|string} 提取的信息
 */
async function extract_info_normal(text, json = true) {
    try {
        const chatCompletion = await openai_normal.chat.completions.create({
            model: CONFIG.AI_MODEL,
            messages: [{ role: 'user', content: text }],
        });
        const rawData = chatCompletion.choices[0].message.content;
        console.log(rawData);
        return json ? extractJsonFromString(rawData) : rawData;
    } catch (error) {
        console.error('AI信息提取失败:', error);
        return json ? {} : '';
    }
}
/**
 * 创建图片内容对象
 * @param {string} imagePath - 图片路径(本地或网络)
 * @returns {Object} 图片内容对象
 */
function createImageContent(imagePath) {
    let isNetworkImage;
    try {
        new URL(imagePath);
        isNetworkImage = true;
    } catch (_) {
        isNetworkImage = false;
    }
    if (isNetworkImage) {
        return {
            success: true,
            isNetworkImage: true,
            content: {
                type: 'image_url',
                image_url: { url: imagePath }
            }
        };
    } else {
        try {
            const ext = path.extname(imagePath).toLowerCase();
            const mimeType = SUPPORTED_IMAGE_FORMATS[ext] || 'image/jpeg';
            const base64Image = fs.readFileSync(imagePath).toString('base64');
            return {
                success: true,
                isNetworkImage: false,
                content: {
                    type: 'image_url',
                    image_url: { url: `data:${mimeType};base64,${base64Image}` }
                }
            };
        } catch (error) {
            return {
                success: false,
                isNetworkImage: false,
                error: `读取本地图片失败: ${error.message}`
            };
        }
    }
}
/**
 * 分析图片内容
 * @param {string} prompt - 分析提示词
 * @param {string} imagePath - 图片路径
 * @param {boolean} json - 是否返回JSON格式
 * @returns {Object|string|null} 分析结果
 */
async function analyzeImage(prompt, imagePath, json = true) {
    try {
        const imageResult = createImageContent(imagePath);
        if (!imageResult.success) {
            console.error('无法处理图片:', imageResult.error);
            return null;
        }
        console.log(imageResult.isNetworkImage ? '正在分析网络图片...' : '正在分析本地图片...');
        let chatCompletion = await openai.chat.completions.create({
            model: CONFIG.AI_MODEL,
            messages: [
                {
                    role: 'user',
                    content: [
                        { type: 'text', text: prompt },
                        imageResult.content
                    ]
                }
            ],
            max_tokens: CONFIG.MAX_TOKENS
        });
        if (typeof chatCompletion === 'string') {
            chatCompletion = JSON.parse(chatCompletion);
        }
        const content = chatCompletion.choices[0].message.content;
        return json ? extractJsonFromString(content) : content;
    } catch (error) {
        console.error('图片分析失败:', error.message);
        return null;
    }
}