/**
 * <AUTHOR>
 * @name oil
 * @team hhgg
 * @version 1.0.1
 * @description 油价查询
 * @platform tgBot qq ssh HumanTG wechatpadpro wxXyo
 * @rule ^(油价|查油价)([^ \n]+)$
 * @rule ^(油价|查油价)$
 * @admin false
 * @disable false
 * @public false
 */

// 依赖模块检查和加载
sysMethod.testModule(['sqlite3'], { install: true });
const sqlite3 = require('sqlite3');
const path = require('path');

// 常量配置
const CONFIG = {
    DEFAULT_REGION: '广东',
    DB_NAME: 'oil_price.db',
    DECIMAL_PLACES: 2,
    TABLE_NAME: 'oil_prices'
};
// 用户提示消息 (保持原有内容不变)
const MESSAGES = {
    NO_DATA: '{region} 地区暂无油价数据',
    DB_ERROR: '无法打开数据库: {error}',
    QUERY_ERROR: '查询失败: {error}',
    PRICE_HEADER: '{region} 地区油价为：',
    PRICE_FORMAT: {
        GASOLINE_92: '92#    {price}',
        GASOLINE_95: '95#    {price}',
        GASOLINE_98: '98#    {price}',
        DIESEL_0: '  0#    {price}'
    }
};
/**
 * 主函数 - 处理油价查询请求
 * @param {Object} s - 消息对象
 */
module.exports = async (s) => {
    try {
        console.log('开始处理油价查询请求...');
        // 1. 解析用户输入的地区
        const region = parseRegion(s);
        // 2. 查询油价数据
        const oilPriceResult = await queryOilPrice(region);
        // 3. 回复查询结果
        await s.reply(oilPriceResult);
        console.log(`油价查询完成: ${region}`);
    } catch (error) {
        console.error('油价查询失败:', error);
        await s.reply('油价查询失败，请稍后重试');
    }
};
/**
 * 解析用户输入的地区
 * @param {Object} s - 消息对象
 * @returns {string} 地区名称
 */
function parseRegion(s) {
    try {
        const userRegion = s.param(2);
        return userRegion || CONFIG.DEFAULT_REGION;
    } catch (error) {
        console.error('解析地区参数时发生错误:', error);
        return CONFIG.DEFAULT_REGION;
    }
}
/**
 * 查询油价数据
 * @param {string} region - 地区名称
 * @returns {Promise<string>} 格式化的油价信息
 */
async function queryOilPrice(region) {
    return new Promise((resolve, reject) => {
        try {
            const dbPath = path.join(__dirname, CONFIG.DB_NAME);
            const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
                if (err) {
                    const errorMsg = MESSAGES.DB_ERROR.replace('{error}', err.message);
                    console.error('数据库连接失败:', err);
                    return reject(errorMsg);
                }
            });
            const query = `SELECT * FROM ${CONFIG.TABLE_NAME} WHERE region = ? ORDER BY date DESC LIMIT 1`;
            db.get(query, [region], (err, row) => {
                if (err) {
                    db.close();
                    const errorMsg = MESSAGES.QUERY_ERROR.replace('{error}', err.message);
                    console.error('数据库查询失败:', err);
                    return reject(errorMsg);
                }
                if (!row) {
                    db.close();
                    const noDataMsg = MESSAGES.NO_DATA.replace('{region}', region);
                    console.log(`未找到地区数据: ${region}`);
                    return resolve(noDataMsg);
                }
                // 格式化输出油价信息
                const formattedResult = formatOilPriceResult(row);
                db.close();
                console.log(`成功查询到油价数据: ${region}`);
                resolve(formattedResult);
            });
        } catch (error) {
            console.error('查询油价时发生错误:', error);
            reject('查询油价时发生系统错误');
        }
    });
}
/**
 * 格式化油价结果
 * @param {Object} row - 数据库查询结果
 * @returns {string} 格式化的油价信息
 */
function formatOilPriceResult(row) {
    try {
        // 构建标题
        let result = MESSAGES.PRICE_HEADER.replace('{region}', row.region) + '\n';
        // 格式化各种油价，保留两位小数
        const gasoline92 = formatPrice(row.gasoline_92);
        const gasoline95 = formatPrice(row.gasoline_95);
        const gasoline98 = formatPrice(row.gasoline_98);
        const diesel0 = formatPrice(row.diesel_0);
        // 添加油价信息
        result += MESSAGES.PRICE_FORMAT.GASOLINE_92.replace('{price}', gasoline92) + '\n';
        result += MESSAGES.PRICE_FORMAT.GASOLINE_95.replace('{price}', gasoline95) + '\n';
        result += MESSAGES.PRICE_FORMAT.GASOLINE_98.replace('{price}', gasoline98) + '\n';
        result += MESSAGES.PRICE_FORMAT.DIESEL_0.replace('{price}', diesel0) + '\n';
        // 添加调价通知（如果有）
        if (row.notice) {
            result += row.notice;
        }
        return result;
    } catch (error) {
        console.error('格式化油价结果时发生错误:', error);
        return '格式化油价信息失败';
    }
}
/**
 * 格式化价格，保留指定小数位数
 * @param {number|string} price - 价格
 * @returns {string} 格式化后的价格
 */
function formatPrice(price) {
    try {
        if (price === null || price === undefined) {
            return '暂无';
        }
        const numPrice = parseFloat(price);
        if (isNaN(numPrice)) {
            return '暂无';
        }
        return numPrice.toFixed(CONFIG.DECIMAL_PLACES);
    } catch (error) {
        console.error('格式化价格时发生错误:', error);
        return '暂无';
    }
}