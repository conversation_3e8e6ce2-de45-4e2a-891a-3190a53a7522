/**
 * <AUTHOR>
 * @name oil_cron
 * @team hhgg
 * @version 1.0.1
 * @description 油价查询
 * @platform tgBot qq ssh HumanTG wechatpadpro wxXyo
 * @rule fhsd油价fhdhfd123
 * @cron 0 20 9 * * *
 * @admin true
 * @disable false
 * @public false
 */

// 依赖模块检查和加载
sysMethod.testModule(['sqlite3'], { install: true });
const { analyzeImage, extract_info_normal, requestN } = require('./mod/utils');
const sqlite3 = require('sqlite3');
const path = require('path');

// 常量配置
const CONFIG = {
    DATA_DIR: '/bncr/BncrData/plugins/misc/',
    DB_NAME: 'oil_price.db',
    OIL_PRICE_URL: 'http://m.qiyoujiage.com/',
    IMAGE_BASE_URL: 'http://www.qiyoujiage.com/images/youjia/',
    DATE_OFFSET_DAYS: 1,
    TABLE_NAME: 'oil_prices'
};
// AI提示词模板 (保持原有内容不变)
const AI_PROMPTS = {
    HTML_ANALYSIS: `请分析如下网页源代码，并从中提取两处重要信息：
1.油价调整的提示信息，一般放置在变量tishiContent内，且一般描述为"下次油价x月x日24时调整，预计上调油价xx元/吨(0.xx元/升-0.xx元/升)"，请去除html格式，只展示文字，并去除"大家相互转告油价上涨了"字样，仅保留油价将要调整的日期和油价上调/下降的描述
2.各省市的具体油价，一般以图片显示，具体路径为http://www.qiyoujiage.com/images/youjia/*.jpg
3.以json格式输出，不要回复多余文字,json格式如下
{"oil_price_notice":油价调整的提示信息, "oil_price_image":油价图片的url, "price_adjust_date":下一次油价调整的日期，格式为yyyy-mm-dd}
'''
{BODY_CONTENT}
'''`,

    IMAGE_ANALYSIS: `请提取图片中的各省市油价信息(忽略图片中的中文水印文字)，以json格式输出，不要输出多余文字，各省市的油价json格式如下所示
{
  "region": "北京",
  "gasoline_92": 6.91,
  "gasoline_95": 7.36,
  "gasoline_98": 8.86,
  "diesel_0": 6.59
}`
};
// 数据库表结构
const DB_SCHEMA = `CREATE TABLE IF NOT EXISTS ${CONFIG.TABLE_NAME} (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    region TEXT,
    gasoline_92 REAL,
    gasoline_95 REAL,
    gasoline_98 REAL,
    diesel_0 REAL,
    date TEXT,
    notice TEXT
)`;

/**
 * 主函数 - 定时获取和更新油价数据
 * @param {Object} s - 消息对象
 */
module.exports = async (s) => {
    const dbPath = path.join(CONFIG.DATA_DIR, CONFIG.DB_NAME);
    let db = null;
    try {
        console.log('开始执行油价数据更新任务...');
        // 1. 获取网页数据
        const webData = await fetchWebData();
        if (!webData) {
            throw new Error('获取网页数据失败');
        }
        // 2. 提取油价信息
        const oilInfo = await extractOilPriceInfo(webData);
        if (!oilInfo || !oilInfo.oil_price_image) {
            throw new Error('未能提取到油价图片URL');
        }
        // 3. 分析图片中的油价数据
        const priceData = await analyzeOilPriceImage(oilInfo.oil_price_image);
        if (!priceData) {
            throw new Error('图片分析失败');
        }
        // 4. 处理和验证数据
        const processedData = processOilPriceData(priceData, oilInfo);
        if (!processedData.priceArray.length) {
            throw new Error('未获取到有效的油价数据');
        }
        // 5. 更新数据库
        db = await initializeDatabase(dbPath);
        await updateOilPriceDatabase(db, processedData);
        console.log('油价数据更新任务完成');
    } catch (error) {
        console.error('油价数据更新失败:', error);
        // 可以在这里添加通知逻辑，比如发送邮件或机器人消息
        // await sendEmail('油价查询失败', error.message);
        // await tBot.sendMessage('油价查询失败: ' + error.message);
    } finally {
        if (db) {
            await closeDatabase(db);
        }
    }
};
/**
 * 获取油价网页数据
 * @returns {Promise<string>} 网页内容
 */
async function fetchWebData() {
    try {
        const requestOptions = {
            url: CONFIG.OIL_PRICE_URL,
            method: 'GET',
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        };
        const [response, body] = await requestN(requestOptions);
        if (response.status !== 200) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        if (!body) {
            throw new Error('网页内容为空');
        }
        console.log('成功获取油价网页数据');
        return body;
    } catch (error) {
        console.error('获取网页数据时发生错误:', error);
        throw error;
    }
}
/**
 * 提取油价信息
 * @param {string} webData - 网页数据
 * @returns {Promise<Object>} 油价信息
 */
async function extractOilPriceInfo(webData) {
    try {
        const htmlPrompt = AI_PROMPTS.HTML_ANALYSIS.replace('{BODY_CONTENT}', webData);
        const info = await extract_info_normal(htmlPrompt);
        if (!info) {
            throw new Error('AI分析返回空结果');
        }
        console.log('成功提取油价基本信息');
        return info;
    } catch (error) {
        console.error('提取油价信息时发生错误:', error);
        throw error;
    }
}
/**
 * 分析油价图片
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<Object>} 油价数据
 */
async function analyzeOilPriceImage(imageUrl) {
    try {
        const oilPrice = await analyzeImage(AI_PROMPTS.IMAGE_ANALYSIS, imageUrl);
        if (!oilPrice) {
            throw new Error('图片分析返回空结果');
        }
        console.log('成功分析油价图片数据');
        return oilPrice;
    } catch (error) {
        console.error('分析油价图片时发生错误:', error);
        throw error;
    }
}
/**
 * 处理油价数据
 * @param {Object} priceData - 原始价格数据
 * @param {Object} oilInfo - 油价基本信息
 * @returns {Object} 处理后的数据
 */
function processOilPriceData(priceData, oilInfo) {
    try {
        // 处理日期
        let priceDate = oilInfo.price_adjust_date ||
                       (priceData.date || new Date().toISOString().slice(0, 10));
        try {
            const dateObj = new Date(priceDate);
            if (!isNaN(dateObj)) {
                dateObj.setDate(dateObj.getDate() + CONFIG.DATE_OFFSET_DAYS);
                priceDate = dateObj.toISOString().slice(0, 10);
            }
        } catch (dateError) {
            console.error('日期处理错误:', dateError);
            priceDate = new Date().toISOString().slice(0, 10);
        }
        // 处理价格数据数组
        const priceArray = Array.isArray(priceData) ? priceData :
                          (priceData.data ? priceData.data :
                          (typeof priceData === 'object' ? [priceData] : []));
        // 验证数据格式
        const validPriceArray = priceArray.filter(item =>
            item &&
            typeof item === 'object' &&
            item.region &&
            (item.gasoline_92 !== undefined || item.gasoline_95 !== undefined)
        );
        console.log(`处理了 ${validPriceArray.length} 条有效油价数据`);
        return {
            priceArray: validPriceArray,
            date: priceDate,
            notice: oilInfo.oil_price_notice || '暂无调价通知'
        };
    } catch (error) {
        console.error('处理油价数据时发生错误:', error);
        throw error;
    }
}
/**
 * 初始化数据库
 * @param {string} dbPath - 数据库路径
 * @returns {Promise<sqlite3.Database>} 数据库实例
 */
async function initializeDatabase(dbPath) {
    return new Promise((resolve, reject) => {
        try {
            const db = new sqlite3.Database(dbPath, (err) => {
                if (err) {
                    console.error('数据库连接失败:', err);
                    reject(err);
                    return;
                }
                console.log('数据库连接成功');
                resolve(db);
            });
        } catch (error) {
            console.error('初始化数据库时发生错误:', error);
            reject(error);
        }
    });
}
/**
 * 更新油价数据库
 * @param {sqlite3.Database} db - 数据库实例
 * @param {Object} data - 处理后的数据
 */
async function updateOilPriceDatabase(db, data) {
    return new Promise((resolve, reject) => {
        try {
            db.serialize(() => {
                // 创建表
                db.run(DB_SCHEMA, (err) => {
                    if (err) {
                        console.error('创建表失败:', err);
                        reject(err);
                        return;
                    }
                });
                // 清空旧数据
                db.run(`DELETE FROM ${CONFIG.TABLE_NAME}`, (err) => {
                    if (err) {
                        console.error('清空旧数据失败:', err);
                        reject(err);
                        return;
                    }
                    console.log('已清空旧的油价数据');
                });
                // 准备插入语句
                const insertSQL = `INSERT INTO ${CONFIG.TABLE_NAME}
                    (region, gasoline_92, gasoline_95, gasoline_98, diesel_0, date, notice)
                    VALUES (?, ?, ?, ?, ?, ?, ?)`;
                const stmt = db.prepare(insertSQL);
                // 批量插入数据
                let insertCount = 0;
                for (const item of data.priceArray) {
                    stmt.run(
                        item.region || '未知地区',
                        item.gasoline_92 || null,
                        item.gasoline_95 || null,
                        item.gasoline_98 || null,
                        item.diesel_0 || null,
                        data.date,
                        data.notice,
                        (err) => {
                            if (err) {
                                console.error(`插入数据失败 (${item.region}):`, err);
                            } else {
                                insertCount++;
                            }
                        }
                    );
                }
                // 完成插入
                stmt.finalize((err) => {
                    if (err) {
                        console.error('完成数据插入时发生错误:', err);
                        reject(err);
                        return;
                    }
                    console.log(`油价数据已成功写入数据库，共插入 ${insertCount} 条记录`);
                    resolve();
                });
            });
        } catch (error) {
            console.error('更新数据库时发生错误:', error);
            reject(error);
        }
    });
}
/**
 * 关闭数据库连接
 * @param {sqlite3.Database} db - 数据库实例
 */
async function closeDatabase(db) {
    return new Promise((resolve) => {
        try {
            db.close((err) => {
                if (err) {
                    console.error('关闭数据库时发生错误:', err);
                } else {
                    console.log('数据库连接已关闭');
                }
                resolve();
            });
        } catch (error) {
            console.error('关闭数据库时发生异常:', error);
            resolve();
        }
    });
}