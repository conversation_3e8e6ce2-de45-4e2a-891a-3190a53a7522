/**
 * <AUTHOR>
 * @name reminder
 * @team hhgg
 * @version 1.0.1
 * @description 提取需要提醒的信息
 * @platform tgBot qq ssh HumanTG wechatpadpro wxXyo
 * @rule (.*(?:提醒|记一下|记录一下|记以下|记录以下).*)
 * @admin false
 * @disable false
 * @public false
 */

const fs = require('fs');
const path = require('path');
const { sleep, get_cronAndContent, sendMessage } = require('./mod/utils');

// 常量配置
const CONFIG = {
    REMINDER_DIR: '/bncr/BncrData/plugins/reminder/',
    TEMPLATE_FILE: 'sample',
    RESTART_DELAY: 1000,
    FILE_LINES: {
        NAME: 2,
        CRON: 9,
        MESSAGE: 18
    }
};
// 用户提示消息 (保持原有内容不变)
const MESSAGES = {
    NO_CONTENT: '您未设置任何提醒内容！',
    TIME_EXPIRED: '您设置的提醒时间已经过去啦~~~~~请重新设置',
    REMINDER_FORMAT: '提醒内容：\n{content}\n\n提醒时间：\n{time}'
};
// 正则表达式
const REGEX = {
    EMPTY_REMINDER: /^(提醒|记一下|记录一下|记以下|记录以下)$/
};
/**
 * 主函数 - 处理提醒设置流程
 * @param {Object} s - 消息对象
 */
module.exports = async (s) => {
    let userId;

    try {
        console.log('开始处理提醒设置请求...');
        // 1. 解析用户输入
        const { content: inputContent, userId: parsedUserId } = parseUserInput(s);
        userId = parsedUserId;
        // 2. 验证输入内容
        if (isEmptyReminder(inputContent)) {
            await sendMessage(userId, MESSAGES.NO_CONTENT);
            return;
        }
        // 3. 获取cron表达式和内容
        const [content, cronExp, filename] = await get_cronAndContent(inputContent);
        if (cronExp === 0) {
            await sendMessage(userId, MESSAGES.TIME_EXPIRED);
            return;
        }
        // 4. 创建提醒文件
        await createReminderFile(content, cronExp, filename);
        // 5. 格式化并发送确认消息
        const scheduleTime = formatScheduleTime(cronExp);
        const confirmMessage = MESSAGES.REMINDER_FORMAT
            .replace('{content}', content)
            .replace('{time}', scheduleTime);
        await sendMessage(userId, confirmMessage);
        // 6. 重启系统
        await sleep(CONFIG.RESTART_DELAY);
        await sysMethod.inline('重启');
        console.log('提醒设置完成');
    } catch (error) {
        console.error('处理提醒设置时发生错误:', error);
        if (userId) {
            await sendMessage(userId, '设置提醒时发生错误，请稍后重试');
        }
    }
};
/**
 * 解析用户输入
 * @param {Object} s - 消息对象
 * @returns {Object} 解析结果 {content, userId}
 */
function parseUserInput(s) {
    try {
        const promptData = s.param(1);

        if (promptData.includes('-')) {
            // 使用'-'分割字符串
            const [content, userId] = promptData.split('-');
            return { content, userId };
        } else {
            // 如果字符串中不包含'-'
            return {
                content: promptData,
                userId: s.getUserId()
            };
        }
    } catch (error) {
        console.error('解析用户输入时发生错误:', error);
        return {
            content: '',
            userId: s.getUserId()
        };
    }
}
/**
 * 检查是否为空提醒
 * @param {string} content - 输入内容
 * @returns {boolean} 是否为空提醒
 */
function isEmptyReminder(content) {
    return REGEX.EMPTY_REMINDER.test(content.trim());
}
/**
 * 创建提醒文件
 * @param {string} content - 提醒内容
 * @param {string} cronExp - cron表达式
 * @param {string} filename - 文件名
 */
async function createReminderFile(content, cronExp, filename) {
    return new Promise((resolve, reject) => {
        try {
            const templatePath = path.join(CONFIG.REMINDER_DIR, CONFIG.TEMPLATE_FILE);
            const newFilePath = path.join(CONFIG.REMINDER_DIR, filename);
            fs.readFile(templatePath, 'utf8', (err, data) => {
                if (err) {
                    console.error('读取模板文件失败:', err);
                    reject(err);
                    return;
                }
                try {
                    const fileLines = data.split('\n');
                    // 更新文件内容
                    fileLines[CONFIG.FILE_LINES.NAME] = ` * @name ${filename.split('.')[0]}`;
                    fileLines[CONFIG.FILE_LINES.CRON] = ` * @cron ${cronExp}`;
                    fileLines[CONFIG.FILE_LINES.MESSAGE] = `      msg: '${content}',`;
                    const updatedData = fileLines.join('\n');
                    // 写入新文件
                    fs.writeFile(newFilePath, updatedData, 'utf8', (writeErr) => {
                        if (writeErr) {
                            console.error('写入提醒文件失败:', writeErr);
                            reject(writeErr);
                            return;
                        }
                        console.log(`提醒文件创建成功: ${filename}`);
                        resolve();
                    });
                } catch (processError) {
                    console.error('处理文件内容时发生错误:', processError);
                    reject(processError);
                }
            });
        } catch (error) {
            console.error('创建提醒文件时发生错误:', error);
            reject(error);
        }
    });
}

/**
 * 格式化时间显示
 * @param {string} cronExp - cron表达式(6字段格式：秒 分 时 日 月 周)
 * @returns {string} 格式化的时间字符串
 */
function formatScheduleTime(cronExp) {
    try {
        const cronParts = cronExp.split(' ');
        const [second, minute, hour, day, month] = cronParts;
        return formatScheduleTime6Fields(second, minute, hour, day, month);

    } catch (error) {
        console.error('格式化时间时发生错误:', error);
        return '时间格式错误';
    }
}
/**
 * 格式化6字段时间显示
 * @param {string} second - 秒
 * @param {string} minute - 分
 * @param {string} hour - 时
 * @param {string} day - 日
 * @param {string} month - 月
 * @returns {string} 格式化的时间字符串
 */
function formatScheduleTime6Fields(second, minute, hour, day, month) {
    if (second === '*' || minute === '*' || hour === '*' || day === '*' || month === '*') {
        const secondDisplay = second === '*' ? '每秒' : `${formatCronForDisplay(second)}秒`;
        const minuteDisplay = minute === '*' ? '每分钟' : `${formatCronForDisplay(minute)}分`;
        const hourDisplay = hour === '*' ? '每小时' : `${formatCronForDisplay(hour)}点`;
        const dayDisplay = day === '*' ? '每天' : `${formatCronForDisplay(day)}日`;
        const monthDisplay = month === '*' ? '每月' : `${formatCronForDisplay(month)}月`;

        if (day === '*' && month === '*') {
            return `${dayDisplay} ${hourDisplay}${minuteDisplay}${secondDisplay}`;
        } else {
            return `${monthDisplay}${dayDisplay} ${hourDisplay}${minuteDisplay}${secondDisplay}`;
        }
    } else {
        return `${month}月${day}日 ${hour}点${minute}分${second}秒`;
    }
}
/**
 * 格式化cron字段显示
 * @param {string} value - cron字段值
 * @returns {string} 格式化的显示文本
 */
function formatCronForDisplay(value) {
    if (value === '*') return '每';
    if (value.includes(',')) {
        return value.split(',').join('、') + '时';
    }
    if (value.includes('-')) {
        const [start, end] = value.split('-');
        return `${start}-${end}时`;
    }
    return value;
}