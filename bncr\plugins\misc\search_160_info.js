/**作者
 * <AUTHOR>
 * @name search_160_info
 * @team hhgg
 * @version 1.0.0
 * @description 根据提供的医生名字，查找该医生的相关信息
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule ^(查找)(.*)$
 * @admin true
 * @disable false
 */

sysMethod.testModule(['axios','sharp','crypto','cheerio'], { install: true });
const {again, sendMessage, sendImage} = require('./mod/utils');
const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const sharp = require('sharp');
const cheerio = require('cheerio');
const img_dir = '/bncr/BncrData/public'

const PUBLIC_KEY = 
`-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDoUjtos8lXDiBGwpMU/2GeKBbJ
mKIpa8YpyuuvEvBTc2XCYG/Tm3QWfwj77NdlIATjt2lOKoCJaK7fK/T+AVzbfpG2
xZEkhnSzdelX8iSPiD3QKgtngn AOXliLnOf6iKZYpWd6maDhWP1JIQ6hE6WtlUio
2otY5+8sYjLuSbUoUwIDAQAB
-----END PUBLIC KEY-----`;
const pre_context =
`\n'''\n1.请分析上面的信息并按照如下json模板进行回复，不需要回复多余文字和字符。
{
   "hosp_name": "<医院名称，例如'儿童医院'>",
   "dep_name": "<科室名称，例如 '内科'>",
   "doctors":  "<医生的中文姓名>",
}'
2.'hosp_name'字段以字符串形式回应，提取信息中包含的医院名称的信息，如信息中未提及医院名称的信息，则回复空字符串。
3.'dep_name'字段以字符串形式回应，提取信息中包含的医院科室名称信息，仅保留科室名称，如信息中未提及医院科室名称的信息，则回复空字符串。
4.'doctors'字段以字符串形式回应，仅保留医生中文姓名，不用保留'医生'字样，如有多个名字请用','连接，如果信息中未提及医生姓名，则回复空字符串。\n'''`

module.exports = async s => {
    const info = s.param(2) || await again(s, `请在30秒内输入需要医院或科室或医生的名字`) || null;
    userId = s.getUserId();
    if (!info) return;
    const prompt = info + pre_context;
    let data = await extract_info_normal(prompt)
    console.log(data)
    if (data.hosp_name) {
        const unitData = JSON.parse(fs.readFileSync('/bncr/BncrData/plugins/misc/unit.json', 'utf8'));
        let filteredUnits = unitData.filter(unit => 
            unit.name.includes(data.hosp_name) || (unit.alias && unit.alias.includes(data.hosp_name))
        );
        if (filteredUnits.length === 0) {
            console.log('未查找到医院，请重新输入');
            await sendMessage(userId, '未查找到医院，请重新输入')
            return;
        }
        if (filteredUnits.length > 1) {
            const show_result = filteredUnits.map((item, index) => `${index + 1}： ${item.name}--${item.unit_id}`).join('\n\n');
            console.log(`查询到多个医院，请选择：\n${show_result}\n`);
            const chosen_num = await again(s, `查询到多个医院，请选择：\n${show_result}`)
            if (!chosen_num) return
            filteredUnits = [filteredUnits[chosen_num-1]]
        }
        data['unit_id'] = filteredUnits[0]['unit_id']
    }
    if (data.dep_name) {
        let dep_info = await extractAndFindDepartment(filteredUnits[0]['url'], data.dep_name)
        if (dep_info.length === 0) {
            console.log('未查找到你需要就诊的科室，请重新输入');
            await sendMessage(userId, '未查找到就诊科室，请重新输入')
            return;
        }
        if (dep_info.length > 1) {
            const show_result = dep_info.map((item, index) => `${index + 1}： ${item.title}--${item.id}`).join('\n\n');
            console.log(`查询到多个就诊科室，请选择：\n${show_result}\n`);
            const chosen_num = await again(s, `查询到多个就诊科室，请选择：\n${show_result}`)
            if (!chosen_num) return
            dep_info = [dep_info[chosen_num-1]]
        }
        data['dep_id'] = dep_info[0]['id']
    }
    const user_key = await log_in()
    console.log(user_key)
    const target_doctors = data['doctors'] ? data['doctors'].split(',') : []
    let unitInfo;
    if (target_doctors.length > 0) {
        let doctorsInfo = [];
        for (const doctor of target_doctors) {  
            const doc_info = await search_doc(doctor);
            if (doc_info && doc_info.length > 0) {
                doctorsInfo.push(...doc_info);
            } else {
                console.log(`未找到${doctor}的信息`);
                await sendMessage(userId, `未找到${doctor}的信息`);
            }
        }
        if (doctorsInfo.length === 0) {
            console.log('未找到任何医生信息');
            await sendMessage(userId, '未找到任何医生信息，请检查代码！');
            return;
        }
        let commonDeps = doctorsInfo.length > 1 ? compareDocsInfo(data, ...doctorsInfo) : doctorsInfo[0].unitDeps;
        if (commonDeps.length > 1) {
            const show_result = commonDeps.map((item, index) => `${index + 1}： ${item.depName}--${item.depId}`).join('\n\n');
            console.log(`查询到多个共同就诊科室，请选择：\n${show_result}\n`);
            const chosen_num = await again(s, `查询到多个共同就诊科室，请选择：\n${show_result}`);
            if (!chosen_num) return;
            unitInfo = commonDeps[chosen_num - 1];
        } else if (commonDeps.length === 1) {
            unitInfo = commonDeps[0];
        } else {
            console.log('未找到共同就诊科室');
            await sendMessage(userId, '未找到共同就诊科室');
            return;
        }
        console.log('选择的就诊科室:', unitInfo);
        const yuyueList = await getYuyueList(target_doctors, unitInfo.unitId, unitInfo.depId, user_key);
        console.log(yuyueList);
        await generateTableImage(yuyueList);
        await sendImage(userId, 'table_image.png');
        return
    }
    if (!data.unit_id) {
        console.log('未能从给定的信息中找到就诊医院，请检查输入信息！')
        await sendMessage(userId, '未能从给定的信息中找到就诊医院，请检查输入信息！')
        return;
    }
    if (!data.dep_id) {
        console.log('给定的信息中缺少就诊科室，请检查输入信息！')
        await sendMessage(userId, '给定的信息中缺少就诊科室，请检查输入信息！')
        return;
    }
    const yuyueList = await getYuyueList(target_doctors, data.unit_id, data.dep_id, user_key);
    console.log(yuyueList);
    await generateTableImage(yuyueList);
    await sendImage(userId, 'table_image.png');
}

async function extractAndFindDepartment(url, targetTitle) {
    try {
        const response = await axios.get(url, {
            headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/111.0.1661.62' }
        });
        const $ = cheerio.load(response.data);
        const departments = [];
        $('.dep_item.layout a[id][title]').each((_, a) => {
            const title = $(a).attr('title');
            if (!title.includes('特诊') && (title === targetTitle || title.includes(targetTitle))) {
                departments.push({
                    id: $(a).attr('id').split('_').pop(),
                    title: title
                });
            }
        });
        return departments;
    } catch (error) {
        console.error('Error:', error);
        return [];
    }
}

async function search_doc(name) {
    const baseUrl = 'https://soapi.91160.com/search/single.html';
    const params = new URLSearchParams({
        keyword: name,
        type: '1',
        search_city_id: '5'
    });
    const url = `${baseUrl}?${params.toString()}`;
    const headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 Edg/129.0.0.0'};
    try {
        const response = await axios.get(url, {headers});
        const data = response.data
        const extractedData = data.records.filter(record => record.result.cleanDoctorName.includes(name))
        .map(({ result }) => ({
          DoctorName: result.cleanDoctorName,
          zcName: result.zcName,
          doctorId: result.doctorId,
          unitId: result.unitId,
          unitName: result.unitName,
          unitDeps: result.unitDeps.map(({ unitId, depId, depName }) => ({ unitId, depId, depName }))
        }));
        if (extractedData.length === 0) {
            console.log(`未查找到 ${name} 的信息，请重新输入`);
            await sendMessage(userId, `未查找到 ${name} 的信息，请重新输入`)
            return;
        }
        if (extractedData.length > 1) {
            const show_result = extractedData.map((item, index) => `${index + 1}： ${item.DoctorName}--${item.unitName}`).join('\n\n');
            console.log(`查询到多个 ${name} 的信息，请选择：\n${show_result}\n`);
            const chosen_num = await again(s, `查询到多个 ${name} 的信息，请选择：\n${show_result}`)
            if (!chosen_num) return
            extractedData = [extractedData[chosen_num-1]]
        }
        console.log(JSON.stringify(extractedData, null, 2));
        return extractedData
    } catch (error) {
        console.error('搜索医生信息失败:', error.message);
        if (error.response) {
            console.error('服务器响应:', error.response.status, error.response.data);
        } else if (error.request) {
            console.error('未收到响应');
        }
        throw new Error('搜索医生信息时发生错误');
    }
}

function formatDate(date) {
    return date.toISOString().split('T')[0];
}

async function getYuyueList(targetDoctors, unitId, depId, accessHash) {
    const currentDate = formatDate(new Date());
    const endDate = new Date(currentDate);
    endDate.setDate(endDate.getDate() + 7);
    const urls = [
        `https://gate.91160.com/guahao/v1/pc/sch/dep?unit_id=${unitId}&dep_id=${depId}&date=${currentDate}&p=0&user_key=${accessHash}`,
        `https://gate.91160.com/guahao/v1/pc/sch/dep?unit_id=${unitId}&dep_id=${depId}&date=${formatDate(endDate)}&p=0&user_key=${accessHash}`
    ];
    const headers = {
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/111.0.1661.62',
        "Host": "gate.91160.com",
    };
    try {
        const responses = await Promise.all(urls.map(url => axios.get(url, { headers })));
        const combinedData = responses.reduce((acc, { data }) => {
            acc.doc.push(...data.data.doc);
            for (const [doctorId, schedules] of Object.entries(data.data.sch)) {
                if (!acc.sch[doctorId]) acc.sch[doctorId] = { am: {}, pm: {} };
                Object.assign(acc.sch[doctorId].am, schedules.am || {});
                Object.assign(acc.sch[doctorId].pm, schedules.pm || {});
            }
            return acc;
        }, { doc: [], sch: {} });
        const doctorNameMap = new Map(
            combinedData.doc.map(doc => [doc.doctor_id.toString(), doc.doctor_name])
        );
        const targetDoctorSet = new Set(targetDoctors);
        const yuyueList = [];
        for (const [doctorId, schedules] of Object.entries(combinedData.sch)) {
            const doctorName = doctorNameMap.get(doctorId);
            if (targetDoctors.length === 0 || targetDoctorSet.has(doctorName)) {
                for (const timeType of ['am', 'pm']) {
                    for (const schedule of Object.values(schedules[timeType])) {
                        yuyueList.push({
                            doctor_name: doctorName,
                            to_date: schedule.to_date,
                            time_type: schedule.time_type,
                            yuyue_num: schedule.yuyue_num,
                            left_num: schedule.left_num,
                            y_state_desc: schedule.y_state_desc
                        });
                    }
                }
            }
        }
        return yuyueList;
    } catch (error) {
        console.error(`Error in getYuyueList: ${error.message}`);
        return null;
    }
}

function compareDocsInfo(data, ...arrays) {
    const firstArray = arrays[0];
    const otherArrays = arrays.slice(1);
    return firstArray.flatMap(doctor => 
        doctor.unitDeps.filter(dep => {
            if ((data.unit_id && dep.unitId !== data.unit_id) || 
                (data.dep_id && dep.depId !== data.dep_id)) {
                return false;
            }
            return otherArrays.every(arr => 
                arr.some(otherDoctor => 
                    otherDoctor.unitDeps.some(otherDep => 
                        dep.unitId === otherDep.unitId && dep.depId === otherDep.depId
                    )
                )
            );
        })
    );
}

async function generateTableImage(data) {
    const finalFile = `${img_dir}/table_image.png`;
    const tableWidth = 900;
    const rowHeight = 40;
    const fontSize = 14;
    const headerHeight = 50;
    const sideColumnWidth = 100;
    const timeColumnWidth = 50;
    const separatorHeight = 2; // Height of the separator line
    const checkAndDeleteFile = (filename) => {
        if (fs.existsSync(filename)) {
            fs.unlinkSync(filename);
            console.log(`${filename} 文件已被删除`);
        }
    };
    checkAndDeleteFile(finalFile);
    const createSvgTable = (data, width, fontSize) => {
        const dates = [...new Set(data.map(item => item.to_date))];
        const doctors = [...new Set(data.map(item => item.doctor_name))];
        const svgHeight = (doctors.length * 2 + 1) * rowHeight + headerHeight + (doctors.length - 1) * separatorHeight;
        const dateColumnWidth = (width - sideColumnWidth - timeColumnWidth) / dates.length;

        const createSvgHeader = () => `
            <rect x="0" y="0" width="${width}" height="${headerHeight}" class="header" />
            <text x="${sideColumnWidth / 2}" y="${headerHeight / 2 + 5}" text-anchor="middle">医生列表</text>
            <text x="${sideColumnWidth + timeColumnWidth / 2}" y="${headerHeight / 2 + 5}" text-anchor="middle">时间</text>
            ${dates.map((date, index) => {
                const x = sideColumnWidth + timeColumnWidth + index * dateColumnWidth;
                return `
                    <text x="${x + dateColumnWidth / 2}" y="${headerHeight / 2 - 5}" text-anchor="middle" font-weight="bold">${date.slice(5)}</text>
                    <text x="${x + dateColumnWidth / 2}" y="${headerHeight / 2 + 15}" text-anchor="middle">周${['日', '一', '二', '三', '四', '五', '六'][new Date(date).getDay()]}</text>`;
            }).join('')}`;
        // 添加医生行
        const createDoctorRow = (doctor, doctorIndex, y) => `
            <rect x="0" y="${y}" width="${sideColumnWidth}" height="${rowHeight * 2}" class="row" />
            <text x="${sideColumnWidth / 2}" y="${y + rowHeight - 10}" text-anchor="middle">${doctor}</text>
            <text x="${sideColumnWidth / 2}" y="${y + rowHeight + 20}" text-anchor="middle" font-size="${fontSize - 2}px">${doctorIndex === 0 ? '主任医师' : '副主任医师'}</text>
            <line x1="${sideColumnWidth}" y1="${y}" x2="${sideColumnWidth}" y2="${y + rowHeight * 2}" stroke="#e0e0e0" stroke-width="2" />
            <rect x="${sideColumnWidth}" y="${y}" width="${timeColumnWidth}" height="${rowHeight}" class="row" />
            <rect x="${sideColumnWidth}" y="${y + rowHeight}" width="${timeColumnWidth}" height="${rowHeight}" class="row" />
            <text x="${sideColumnWidth + timeColumnWidth / 2}" y="${y + rowHeight / 2 + 5}" text-anchor="middle" class="time-label">上午</text>
            <text x="${sideColumnWidth + timeColumnWidth / 2}" y="${y + rowHeight * 1.5 + 5}" text-anchor="middle" class="time-label">下午</text>
            <line x1="${sideColumnWidth + timeColumnWidth}" y1="${y}" x2="${sideColumnWidth + timeColumnWidth}" y2="${y + rowHeight * 2}" stroke="#e0e0e0" stroke-width="3" />`;
        // 添加日期行
        const createDateSlot = (doctor, date, x, y) => {
            const morningSlot = data.find(item => item.doctor_name === doctor && item.to_date === date && item.time_type === 'am');
            const afternoonSlot = data.find(item => item.doctor_name === doctor && item.to_date === date && item.time_type === 'pm');
            return `
                <rect x="${x}" y="${y}" width="${dateColumnWidth}" height="${rowHeight}" class="row" />
                <rect x="${x}" y="${y + rowHeight}" width="${dateColumnWidth}" height="${rowHeight}" class="row" />
                ${morningSlot ? `<text x="${x + dateColumnWidth / 2}" y="${y + rowHeight / 2 + 5}" text-anchor="middle" class="${morningSlot.y_state_desc === '可预约' ? 'available' : 'unavailable'}">${morningSlot.y_state_desc}</text>` : ''}
                ${afternoonSlot ? `<text x="${x + dateColumnWidth / 2}" y="${y + rowHeight * 1.5 + 5}" text-anchor="middle" class="${afternoonSlot.y_state_desc === '可预约' ? 'available' : 'unavailable'}">${afternoonSlot.y_state_desc}</text>` : ''}`;
        };
        let svg = `<svg width="${width}" height="${svgHeight}" xmlns="http://www.w3.org/2000/svg">
            <style>
                .header { fill: #f0f0f0; }
                .row { fill: #f9f9f9; }
                text { font-family: Arial, sans-serif; font-size: ${fontSize}px; }
                .available { fill: #4CAF50; }
                .unavailable { fill: #F44336; }
                .time-label { font-size: ${fontSize}px; fill: #666; }
                .separator { fill: #e0e0e0; }
                .status-text { font-weight: bold; font-size: ${fontSize + 6}px; }
            </style>
            ${createSvgHeader()}`;
        let currentY = headerHeight;
        svg += doctors.map((doctor, doctorIndex) => {
            const y = currentY;
            let rowSvg = createDoctorRow(doctor, doctorIndex, y);
            rowSvg += dates.map((date, dateIndex) => {
                const x = sideColumnWidth + timeColumnWidth + dateIndex * dateColumnWidth;
                return createDateSlot(doctor, date, x, y);
            }).join('');
            currentY += rowHeight * 2;
            if (doctorIndex < doctors.length - 1) {
                rowSvg += `<rect x="0" y="${currentY}" width="${width}" height="${separatorHeight}" class="separator" />`;
                currentY += separatorHeight;
            }
            return rowSvg;
        }).join('');
        svg += '</svg>';
        return Buffer.from(svg);
    };
    // 生成SVG表格
    try {
        await sharp(createSvgTable(data, tableWidth, fontSize))
            .png()
            .toFile(finalFile);
        console.log('表格图片已生成！');
    } catch (err) {
        console.error('生成表格图片时出错:', err);
    }
}

async function log_in() {
    const url = "https://weixin.91160.com/user/login.html";
    const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 Edg/129.0.0.0',
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "origin": "https://weixin.91160.com",
        "referer": "https://weixin.91160.com/user/login.html?login_type=pwd&mobile=&from_function_id=&from_function_name="
    };
    const data = new URLSearchParams({
        "username": encrypt_data('18588445449'),
        "password": encrypt_data('Aassdf1234'),
        "inteCode": "+86",
        "from_function_id": "",
        "from_function_name": "",
        "oaid": "",
        "hw_open_id": "",
        "hw_union_id": "",
    });
    try {
        const response = await axios.post(url, data, { headers });
        const responseData = response.data;
        if (!responseData.user_key) {
            throw new Error("Login response does not contain user_key");
        }
        console.log("登录成功！");
        return responseData.user_key
    } catch (error) {
        let errorMessage = "登录失败: ";
        if (error.response) {
            errorMessage += `请求失败 (${error.response.status}): ${error.message}`;
        } else if (error.request) {
            errorMessage += `未收到响应: ${error.message}`;
        } else if (error instanceof SyntaxError) {
            errorMessage += `解析登录响应JSON失败: ${error.message}`;
        } else {
            errorMessage += `未知错误: ${error.message}`;
        }
        console.error(errorMessage);
        throw new Error(errorMessage);
    }
}

function encrypt_data(data, publicKey = PUBLIC_KEY) {
    const buffer = Buffer.from(data);
    const encrypted = crypto.publicEncrypt(
        {
            key: publicKey,
            padding: crypto.constants.RSA_PKCS1_PADDING,
        },
        buffer
    );
    return encrypted.toString('base64');
}
