/**
 * <AUTHOR>
 * @name stop_start
 * @team hhgg
 * @version 2.0.1
 * @description 暂停或恢复执行某个js脚本
 * @platform tgBot qq ssh HumanTG wechatpadpro wxXyo
 * @rule ^(停止|启动)([^ \n]+)$
 * @rule ^(停止|启动)$
 * @admin true
 * @disable false
 * @public false
 */

const fs = require('fs');
const path = require('path');

// 配置常量
const CONFIG = {
    PLUGIN_DIR: '/bncr/BncrData/plugins/misc/',
    TIMEOUT_SECONDS: 30,
    DESCRIPTION_LINE_INDEX: 5,
    CURRENT_SCRIPT_NAME: 'stop_start.js',
    JS_EXTENSION: '.js',
    RESTART_DELAY: 1000
};
// 操作类型枚举
const OPERATION = {
    STOP: '停止',
    START: '启动'
};
// 用户提示消息 (保持原有内容不变)
const MESSAGES = {
    SCRIPT_ERROR: '脚本执行出错，请检查日志',
    EXIT_MESSAGE: '已退出',
    INPUT_ERROR: '错误！输入的数字超过js脚本数\n请重新执行停止/启动命令',
    NO_SCRIPTS_FOUND: '未找到指定js脚本,默认列出全部js脚本',
    INPUT_PROMPT: [
        `请在{timeout}秒内输入数字，选择你要停止/启动的js脚本`,
        '可多选，请使用中英文的逗号或句号区隔',
        '超时默认不选择js脚本，按q或Q可直接退出'
    ],
    OPERATION_SUCCESS: '{files}脚本即将{action}!'
};
// 正则表达式
const REGEX = {
    INPUT_SEPARATORS: /[.,，。]/,
    EXIT_COMMANDS: /^q$/i
};
/**
 * 主函数 - 处理脚本停止/启动流程
 * @param {Object} s - 消息对象
 */
module.exports = async (s) => {
    try {
        console.log('开始处理脚本停止/启动请求...');
        // 1. 解析用户输入
        const { operation, keyword, isStopOperation } = parseCommandInput(s);
        // 2. 获取脚本文件列表
        const scriptFiles = await getScriptFiles(isStopOperation);
        if (scriptFiles.length === 0) {
            await s.reply(`没有找到可${operation}的脚本文件`);
            return;
        }
        // 3. 过滤脚本文件
        const filteredResult = filterScripts(keyword, scriptFiles);
        // 4. 处理用户交互
        await handleUserInteraction(s, filteredResult, isStopOperation);
        console.log('脚本停止/启动处理完成');
    } catch (error) {
        console.error('脚本执行出错:', error);
        await s.reply(MESSAGES.SCRIPT_ERROR);
    }
};
/**
 * 解析命令输入
 * @param {Object} s - 消息对象
 * @returns {Object} 解析结果
 */
function parseCommandInput(s) {
    try {
        const operation = s.param(1) || '';
        const keyword = s.param(2) || '';
        const isStopOperation = operation.includes(OPERATION.STOP);
        return {
            operation,
            keyword,
            isStopOperation
        };
    } catch (error) {
        console.error('解析用户输入时发生错误:', error);
        return {
            operation: '',
            keyword: '',
            isStopOperation: false
        };
    }
}
/**
 * 获取脚本文件列表
 * @param {boolean} isStopOperation - 是否为停止操作
 * @returns {Promise<Array>} 脚本文件列表
 */
async function getScriptFiles(isStopOperation) {
    try {
        if (!fs.existsSync(CONFIG.PLUGIN_DIR)) {
            throw new Error(`目录不存在: ${CONFIG.PLUGIN_DIR}`);
        }
        const allFiles = fs.readdirSync(CONFIG.PLUGIN_DIR);
        const scriptFiles = [];
        for (const file of allFiles) {
            try {
                // 跳过当前脚本文件
                if (file === CONFIG.CURRENT_SCRIPT_NAME) continue;
                const filePath = path.join(CONFIG.PLUGIN_DIR, file);
                // 检查是否为文件
                const stats = fs.statSync(filePath);
                if (!stats.isFile()) continue;
                // 根据操作类型过滤文件
                const isJsFile = file.endsWith(CONFIG.JS_EXTENSION);
                const hasNoExtension = !path.extname(file);
                if (isStopOperation && !isJsFile) continue;
                if (!isStopOperation && !hasNoExtension) continue;
                // 提取描述信息
                const description = await extractDescription(filePath);
                scriptFiles.push({
                    fileName: file,
                    description: description,
                    filePath: filePath
                });
            } catch (fileError) {
                console.error(`处理文件 ${file} 时发生错误:`, fileError);
                continue;
            }
        }
        console.log(`找到 ${scriptFiles.length} 个可${isStopOperation ? '停止' : '启动'}的脚本文件`);
        return scriptFiles;
    } catch (error) {
        console.error('获取脚本文件列表时发生错误:', error);
        throw error;
    }
}
/**
 * 提取文件描述信息
 * @param {string} filePath - 文件路径
 * @returns {Promise<string>} 描述信息
 */
async function extractDescription(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf-8');
        const lines = content.split('\n');
        if (lines.length <= CONFIG.DESCRIPTION_LINE_INDEX) {
            return '无描述';
        }
        const descriptionLine = lines[CONFIG.DESCRIPTION_LINE_INDEX];
        // 清理描述文本
        const cleanDescription = descriptionLine
            .replace(/\s/g, '')
            .replace(/\*/g, '')
            .replace(/@description/g, '')
            .trim();
        return cleanDescription || '无描述';
    } catch (error) {
        console.error(`读取文件描述失败 (${filePath}):`, error);
        return '读取描述失败';
    }
}
/**
 * 过滤脚本文件
 * @param {string} keyword - 关键词
 * @param {Array} scripts - 脚本列表
 * @returns {Object} 过滤结果
 */
function filterScripts(keyword, scripts) {
    try {
        // 根据关键词过滤脚本
        const filtered = keyword
            ? scripts.filter(script =>
                script.fileName.toLowerCase().includes(keyword.toLowerCase()) ||
                script.description.toLowerCase().includes(keyword.toLowerCase())
              )
            : scripts;
        // 构建显示列表
        const displayList = filtered.map((script, index) =>
            `${index} --> ${script.fileName}:${script.description}`
        );
        // 如果没有找到匹配的脚本，添加提示信息
        if (keyword && filtered.length === 0) {
            displayList.push(MESSAGES.NO_SCRIPTS_FOUND);
        }
        // 添加用户提示信息
        const promptMessages = MESSAGES.INPUT_PROMPT.map(msg =>
            msg.replace('{timeout}', CONFIG.TIMEOUT_SECONDS)
        );
        displayList.push(...promptMessages);
        console.log(`过滤后找到 ${filtered.length} 个脚本文件`);
        return {
            scripts: filtered,
            displayList: displayList
        };
    } catch (error) {
        console.error('过滤脚本时发生错误:', error);
        return {
            scripts: [],
            displayList: ['过滤脚本时发生错误']
        };
    }
}
/**
 * 处理用户交互
 * @param {Object} s - 消息对象
 * @param {Object} filteredResult - 过滤结果
 * @param {boolean} isStopOperation - 是否为停止操作
 */
async function handleUserInteraction(s, filteredResult, isStopOperation) {
    try {
        // 显示脚本列表
        await s.reply(filteredResult.displayList.join('\n\n'));
        // 等待用户输入
        const userInput = await waitForUserInput(s);
        if (!userInput) return;
        // 检查是否为退出命令
        if (isExitCommand(userInput.getMsg())) {
            await userInput.reply(MESSAGES.EXIT_MESSAGE);
            return;
        }
        // 解析用户选择
        const selectedFiles = parseUserInput(userInput.getMsg(), filteredResult.scripts);
        if (selectedFiles.length === 0) {
            await userInput.reply(MESSAGES.INPUT_ERROR);
            return;
        }
        // 执行脚本操作
        const operationResult = await toggleScripts(selectedFiles, isStopOperation);
        // 发送操作结果
        const action = isStopOperation ? OPERATION.STOP : OPERATION.START;
        const successMessage = MESSAGES.OPERATION_SUCCESS
            .replace('{files}', operationResult.join(','))
            .replace('{action}', action);
        await userInput.reply(successMessage);
        // 重启系统
        await new Promise(resolve => setTimeout(resolve, CONFIG.RESTART_DELAY));
        await sysMethod.inline('重启');
    } catch (error) {
        console.error('处理用户交互时发生错误:', error);
        await s.reply('处理用户交互时发生错误，请重试');
    }
}
/**
 * 等待用户输入
 * @param {Object} s - 消息对象
 * @returns {Promise<Object|null>} 用户输入消息对象
 */
async function waitForUserInput(s) {
    try {
        const newMsg = await s.waitInput(() => {}, CONFIG.TIMEOUT_SECONDS);
        return newMsg;
    } catch (error) {
        console.error('等待用户输入时发生错误:', error);
        return null;
    }
}
/**
 * 检查是否为退出命令
 * @param {string} input - 用户输入
 * @returns {boolean} 是否为退出命令
 */
function isExitCommand(input) {
    return !input || REGEX.EXIT_COMMANDS.test(input);
}
/**
 * 解析用户输入的数字选择
 * @param {string} input - 用户输入
 * @param {Array} scripts - 脚本列表
 * @returns {Array} 选中的文件名列表
 */
function parseUserInput(input, scripts) {
    try {
        const numbers = input
            .split(REGEX.INPUT_SEPARATORS)
            .map(num => parseInt(num.trim(), 10))
            .filter(num => !isNaN(num) && num >= 0 && num < scripts.length);
        // 去重并排序
        const uniqueNumbers = [...new Set(numbers)].sort((a, b) => a - b);
        const selectedFiles = uniqueNumbers.map(index => scripts[index].fileName);
        console.log(`用户选择了 ${selectedFiles.length} 个脚本文件`);
        return selectedFiles;
    } catch (error) {
        console.error('解析用户输入时发生错误:', error);
        return [];
    }
}
/**
 * 切换脚本状态（停止/启动）
 * @param {Array} fileNames - 文件名列表
 * @param {boolean} isStopOperation - 是否为停止操作
 * @returns {Promise<Array>} 操作成功的文件列表
 */
async function toggleScripts(fileNames, isStopOperation) {
    const results = [];
    for (const fileName of fileNames) {
        try {
            const currentPath = path.join(CONFIG.PLUGIN_DIR, fileName);
            const newPath = isStopOperation
                ? currentPath.replace(CONFIG.JS_EXTENSION, '')
                : currentPath + CONFIG.JS_EXTENSION;
            // 检查源文件是否存在
            if (!fs.existsSync(currentPath)) {
                console.error(`源文件不存在: ${currentPath}`);
                continue;
            }
            // 检查目标文件是否已存在
            if (fs.existsSync(newPath)) {
                console.error(`目标文件已存在: ${newPath}`);
                continue;
            }
            // 执行重命名操作
            fs.renameSync(currentPath, newPath);
            results.push(fileName);
            const action = isStopOperation ? '停止' : '启动';
            console.log(`${action}脚本成功: ${fileName}`);
        } catch (error) {
            console.error(`操作文件 ${fileName} 失败:`, error);
        }
    }
    console.log(`成功操作 ${results.length} 个脚本文件`);
    return results;
}