/**作者
 * <AUTHOR>
 * @name test
 * @team hhgg
 * @version 1.0.0
 * @description 测试用
 * @platform tgBot qq ssh HumanTG wechatpadpro wxXyo
 * @rule test
 * @admin true
 * @disable false
 * @public false
 */
const {notifyAdmin}= require('./mod/utils');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { Readable } = require('stream');
const { decode } = require('silk-wasm'); 

module.exports = async () => {
    await sysMethod.push({ platform: 'wechatpadpro', userId: 'wxid_ifk4bxq7m1u312', path: '测试\n测试', type: 'recorder' })
}

async function sendMessage(){
    // await sysMethod.push({ platform: 'wxXyo', userId: 'wxid_iim9wf4t0s3b12', msg: '测试测试', type: 'text' });
    // await sysMethod.push({ platform: 'wxXyo', userId: 'wxid_ifk4bxq7m1u312', msg: '测试测试', type: 'text' });
}