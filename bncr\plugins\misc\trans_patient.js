/**作者
 * <AUTHOR>
 * @name trans_patient
 * @team hhgg
 * @version 1.0.0
 * @description 将主服务器中的就诊人转移到1号或2号服务器
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule ^(转移)(.*)$
 * @admin true
 * @disable false
 */

sysMethod.testModule(['axios'], { install: true });
const {again, get_ql_token, write_ql_task} = require('./mod/utils');
const axios = require('axios');

module.exports = async s => {
    const info = s.param(2) || await again(s, `请在30秒内输入转移的信息`) || null;
    userId = s.getUserId();
    if (!info) return;
    const patient = info.split(/[.,，。]/)[0]
    if (info.includes('1')) {
        url = 'https://ql1.ooxx.gq'
    } else if (info.includes('2')) {
        url = 'https://ql2.ooxx.gq'
    } else {
        return await again(s, `请在30秒内输入转入的服务器编号，如：1或2`)
    }
    const main_url = 'http://***************:5700'
    const main_token = await get_ql_token(main_url)
    const {env_name, env_value} = await getENV(patient, main_token)
    if (!env_name || !env_value) return await s.reply('未找到就诊人，请重新输入')
    await disableScript(main_url, env_name, main_token, 'stop')
    await disableScript(main_url, env_name, main_token, 'delete')
    const aux_token = await get_ql_token(url)
    await updateENV(url, env_name, env_value, aux_token);
    await write_ql_task(url, aux_token, env_name, '160_normal.py')
    await disableScript(url, env_name, aux_token, 'run')
    await sendMessage(userId, '转移成功！');
}

async function sendMessage(u,m){
    await sysMethod.push({ platform: 'wxXyo', userId: u, msg: m, type: 'text' });
}

async function getENV(name, token) {
    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    try {
        const response = await axios.get('http://***************:5700/api/envs', { headers });
        const extractedData = response.data.data.filter(item => item.value.includes(name));
        if (extractedData.length === 0) {
            console.log('未查找到就诊人，请重新输入');
            await sendMessage(userId, '未查找到就诊人，请重新输入')
            return;
        }
        if (extractedData.length > 1) {
            const show_result = extractedData.map((item, index) => `${index + 1}： ${item.name}`).join('\n\n');
            console.log(`查询到多个就诊人，请选择：\n${show_result}\n`);
            const chosen_num = await again(s, `查询到多个就诊人，请选择：\n${show_result}`)
            if (!chosen_num) return
            extractedData = [extractedData[chosen_num-1]]
        }
        const body_data = [extractedData[0].id]
        const env_value = extractedData[0].value
        const response2 = await axios({
            method: 'DELETE',
            url: 'http://***************:5700/api/envs',
            headers: headers,
            data: body_data
        });console.log(response2.data)
        if (response2.data.code !== 200) console.log('删除环境变量失败，请检查原因!');
        const Regex = /"task_name"\s*:\s*"([^"]+)"/;
        const match = env_value.match(Regex);
        const env_name = match[1]
        return {env_name, env_value}
    } catch (error) {
        console.error('getENV-Error:', error.message);
    }
}

async function updateENV(url, name, value, token) {
    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    try {
        const response = await axios.get(`${url}/api/envs`, { headers });
        if (response.data.code !== 200) {
            console.log('updateENV请求失败!');
            return;
        }
        const extractedData = response.data.data.map(item => ({
            id: item.id,
            name: item.name,
            remarks: item.remarks,
            value: item.value
        }));
        const existingVar = extractedData.find(item => item.name === name);
        let payload, method, successMsg;
        if (existingVar) {
            payload = { ...existingVar, value };
            method = 'put';
            successMsg = `${name}环境变量修改成功`;
        } else {
            payload = [{ name, value }];
            method = 'post';
            successMsg = `${name}环境变量新增成功`;
        }
        // // 更新或新增环境变量
        const updateResponse = await axios({
            method: method,
            url: `${url}/api/envs`,
            headers: headers,
            data: payload
        });
        if (updateResponse.data.code === 200) {
            console.log(successMsg);
        }
    } catch (error) {
        console.error('updateENV-Error:', error.message);
    }
}

async function disableScript(url, name, token, action = 'disable') {
    const baseUrl = `${url}/api/crons`;
    const getUrl = `${baseUrl}?searchValue=&page=1&size=100&filters={}&queryString={"filters":null,"sorts":null,"filterRelation":"and"}`;
    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    try {
        const response = await axios.get(getUrl, { headers });
        if (response.data.code === 200) {
            const extractedData = response.data.data.data
                .filter(item => item.isDisabled === 0)
                .map(item => ({ id: item.id, name: item.name }));
            console.log(extractedData);
            const payload = name.match(/^\d+$/) 
                ? extractedData.filter(item => item.id === parseInt(name)).map(item => item.id)
                : extractedData.filter(item => item.name === name).map(item => item.id);
            console.log(payload);
            if (payload.length) {
                let url, method, successMessage, failureMessage;
                switch (action) {
                    case 'disable':
                        url = `${baseUrl}/disable`;
                        method = 'put';
                        successMessage = `${name}任务禁用成功！`;
                        failureMessage = '脚本已经处于禁用状态！';
                        break;
                    case 'delete':
                        url = baseUrl;
                        method = 'delete';
                        successMessage = `${name}任务删除成功！`;
                        failureMessage = '脚本不存在或已被删除！';
                        break;
                    case 'stop':
                        url = `${baseUrl}/stop`;
                        method = 'put';
                        successMessage = `${name}任务停止成功！`;
                        failureMessage = '脚本不存在或已停止！';
                        break;
                    case 'run':
                        url = `${baseUrl}/run`;
                        method = 'put';
                        successMessage = `${name}任务运行成功！`;
                        failureMessage = '脚本不存在或已运行！';
                        break;
                    default:
                        console.log('不支持的操作！');
                        return;
                }
                const actionResponse = await axios({
                    method,
                    url,
                    headers,
                    data: payload
                });
                if (actionResponse.data.code === 200) {
                    console.log(successMessage);
                } else {
                    console.log(`${action.charAt(0).toUpperCase() + action.slice(1)}操作失败！`);
                }
            } else {
                console.log(failureMessage);
            }
        } else {
            console.log('disableScript请求失败！');
        }
    } catch (error) {
        console.error('发生错误：', error.message);
    }
}