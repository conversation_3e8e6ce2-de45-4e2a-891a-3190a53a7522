/**作者
 * <AUTHOR>
 * @name visa_ccb
 * @team hhgg
 * @version 1.0.0
 * @description 查询建行visa的京东E卡是否有货
 * @platform tgBot qq ssh HumanTG wxQianxun wxXyo
 * @rule gdf2323jkk打发士大夫
 * @admin true
 * @cron * 8-12 * * *
 * @disable false
 * @public false
 */


const {requestN}= require('./mod/utils');

module.exports = async s => {
    info = await get_info()
    if (info){
        sysMethod.push({
            platform: 'wxXyo',
            userId: `wxid_iim9wf4t0s3b12`,
            msg: info,
            type: 'text',
        });
    } else {
        console.log('暂时无货')
    }

}

async function get_info() {
    const header = {
        "Host": "vtravel.link2shops.com",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.70",
        "Content-Type": "application/json;charset=UTF-8",
    }
    const option = {
        url: "https://vtravel.link2shops.com/vfuliApi/api/client/ypJyActivity/goodsList",
        method: "post",
        headers: header,
        json: true,
        body: {"activityTag":"ccbyyg","catagoryId":""},
        proxy: "http://***************:8718",
    };
    const [response,body] = await requestN(option);
    if (response.statusCode === 200) {
        if (body.msg == 'success'){
            goodsList = body.data.goodsList
            info = ''
            for (item of goodsList) {
                if (item.name == "20元京东E卡" && item.stock != 0) {
                    info = '20元京东E卡有货啦~~~'
                }
            }
            return info
        }
    }
}
