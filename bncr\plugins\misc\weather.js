/**作者
 * <AUTHOR>
 * @name weather
 * @team hhgg
 * @version 1.0.0
 * @description 天气查询
 * @platform tgBot qq ssh HumanTG wechatpadpro wxXyo
 * @rule x="(.*?)" y="(.*?)" .*?label="(.*?)" .*?poiname="(.*?)"
 * @admin false
 * @disable false
 * @public false
 */
const {requestN}= require('./mod/utils');

module.exports = async s => {
    try {
        const address = s.param(3) + '-' + s.param(4)
        const jinwei = s.param(2) + ',' + s.param(1)
        console.log(address + '\n' + jinwei)
        const token = await caiyun_cookie()
        const data = await caiyun_data(token, jinwei)
        if (!data || !data.result) {
            throw new Error("获取天气数据失败或数据格式不正确")
        }
        const jinwei_text = '经度：' + s.param(2) + '\n纬度：' + s.param(1)
        const rt1 = '地点位于：'+ address + "\n" + jinwei_text + "\n\n"
        const rt2 = '天气： '+ data.result.realtime.skycon + "\n" + 
                 '当前温度： '+ data.result.realtime.temperature + "\n" + 
                 '体感温度： '+ data.result.realtime.apparent_temperature + "\n" + 
                 '湿度： '+ data.result.realtime.humidity*100 + '%' + "\n" + 
                 '风速： ' + data.result.realtime.wind.speed + 'km/h' +"\n" + 
                 'pm2.5： ' + data.result.realtime.air_quality.pm25 + "\n" + 
                 'pm10： ' + data.result.realtime.air_quality.pm10 + "\n" + 
                 '空气质量： ' + data.result.realtime.air_quality.description.usa + data.result.realtime.air_quality.aqi.usa + "\n" + 
                 '紫外线指数： ' + data.result.realtime.life_index.ultraviolet.desc + '\n' + 
                 '舒适指数： ' + data.result.realtime.life_index.comfort.desc + '\n' + 
                 '短时预测： ' + data.result.minutely.description + '\n' + 
                 '今明预测： ' + data.result.hourly.description
        s.reply(rt1+rt2)
    } catch (error) {
        console.error("天气查询出错:", error.message)
        s.reply("抱歉，获取天气信息时出错: " + error.message)
    }
}

async function weather_location(t) {
    const header = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/107.0.1418.62',
    };
    const option = {
        url: "https://restapi.amap.com/v3/geocode/geo?address=" + t + "&key=0547c4192c80ebdaf229be782469e920",
        method: "get",
        headers: header,
    };
    
    try {
        const [response, data] = await requestN(option);
        if (response.status === 200) {
            return data;
        }
    } catch (error) {
        console.error('weather_location-Error:', error.message);
        throw error;
    }
};

async function caiyun_cookie() {
    const header = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/107.0.1418.62',
    };
    const option = {
        url: "https://h5.caiyunapp.com/h5",
        method: "get",
        headers: header,
    };
    
    try {
        const [response, _] = await requestN(option);
        if (response.status === 200) {
            return response.headers['set-cookie'][0];
        }
    } catch (error) {
        console.error('caiyun_cookie-Error:', error.message);
        throw error;
    }
};

async function caiyun_data(cookie, location) {
    const header = {
        'Cookie': cookie,
        'Host': 'h5.caiyunapp.com',
        'Origin': 'https://h5.caiyunapp.com',
        'Referer': 'https://h5.caiyunapp.com/h5',
        'Content-Type': 'application/json;charset=UTF-8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/107.0.1418.62',
    };
    const body_data = {url: "https://api.caiyunapp.com//v2.5/<t2.5>/"+location+"/weather?dailysteps=16&hourlysteps=120"};
    
    try {
        const option = {
            url: "https://h5.caiyunapp.com/api",
            method: "post",
            headers: header,
            data: body_data,
            transformRequest: [(data) => {
                return JSON.stringify(data);
            }]
        };
        const [response, data] = await requestN(option);
        if (response.status === 200) {
            if (typeof data === 'string') {
                try {
                    return JSON.parse(data);
                } catch (e) {
                    console.error("JSON parse error:", e.message);
                    console.log("Raw data:", data.substring(0, 100) + "...");
                    throw e;
                }
            }
            return data;
        }
        throw new Error(`Request failed with status: ${response.status}`);
    } catch (error) {
        console.error('caiyun_data-Error:', error.message);
        throw error;
    }
};
