var u=(c,s,o)=>new Promise((t,l)=>{var a=e=>{try{n(o.next(e))}catch(d){l(d)}},i=e=>{try{n(o.throw(e))}catch(d){l(d)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(a,i);n((o=o.apply(c,s)).next())});import{o as r,c as _,a as p,d as v,k as x,p as h}from"./index-b380aaed.js";const w={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},k=p("path",{fill:"currentColor",d:"M2 12a10 10 0 0 0 13 9.54a10 10 0 0 1 0-19.08A10 10 0 0 0 2 12Z"},null,-1),f=[k];function g(c,s){return r(),_("svg",w,f)}const M={name:"mdi-moon-waning-crescent",render:g},y={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},$=p("path",{fill:"currentColor",d:"m3.55 19.09l1.41 1.41l1.8-1.79l-1.42-1.42M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6s6-2.69 6-6c0-3.32-2.69-6-6-6m8 7h3v-2h-3m-2.76 7.71l1.8 1.79l1.41-1.41l-1.79-1.8M20.45 5l-1.41-1.4l-1.8 1.79l1.42 1.42M13 1h-2v3h2M6.76 5.39L4.96 3.6L3.55 5l1.79 1.81l1.42-1.42M1 13h3v-2H1m12 9h-2v3h2"},null,-1),B=[$];function b(c,s){return r(),_("svg",y,B)}const C={name:"mdi-white-balance-sunny",render:b},V=v({name:"DarkModeSwitch",__name:"dark-mode-switch",props:{dark:{type:Boolean,default:!1},customizeTransition:{type:Boolean}},emits:["update:dark"],setup(c,{emit:s}){const o=c,t=x({get(){return o.dark},set(a){s("update:dark",a)}});function l(a){return u(this,null,function*(){const i=a.clientX,n=a.clientY;if(!o.customizeTransition||!document.startViewTransition){t.value=!t.value;return}const e=Math.hypot(Math.max(i,innerWidth-i),Math.max(n,innerHeight-n));yield document.startViewTransition(()=>{t.value=!t.value}).ready;const m=[`circle(0px at ${i}px ${n}px)`,`circle(${e}px at ${i}px ${n}px)`];document.documentElement.animate({clipPath:t.value?m:[...m].reverse()},{duration:300,easing:"ease-in",pseudoElement:t.value?"::view-transition-new(root)":"::view-transition-old(root)"})})}return(a,i)=>{const n=M,e=C;return r(),_("div",{class:"flex-center text-18px cursor-pointer",onClick:l},[t.value?(r(),h(n,{key:0})):(r(),h(e,{key:1}))])}}});export{C as _,M as a,V as b};
