var p=(e,o,a)=>new Promise((d,r)=>{var c=l=>{try{i(a.next(l))}catch(h){r(h)}},s=l=>{try{i(a.throw(l))}catch(h){r(h)}},i=l=>l.done?d(l.value):Promise.resolve(l.value).then(c,s);i((a=a.apply(e,o)).next())});import{aG as t}from"./index-b380aaed.js";var n=(e=>(e.JSON="application/json;charset=UTF-8",e.FORM_URLENCODED="application/x-www-form-urlencoded;charset=UTF-8",e.FORM_DATA="multipart/form-data",e))(n||{});const y=({path:e})=>t.get(`/getFileContent?path=${e}`),S=({path:e})=>t.get(`/getJsonSchemaAndNowSet?path=${e}`),T=e=>t.post("/saveContent",e,{headers:{"Content-Type":n.JSON}}),m=e=>t.post("/dataBaseEdit",e,{headers:{"Content-Type":n.JSON}}),u=e=>t.post("/dataBaseDelete",e,{headers:{"Content-Type":n.JSON}}),J=e=>t.post("/dataBaseDeleteAll",e,{headers:{"Content-Type":n.JSON}}),F=c=>p(void 0,[c],function*({type:e="",isDir:o="",keywords:a="",startTime:d="",endTime:r=""}){var i;const s=yield t.get(`/getAllFileTree?type=${e}&isDir=${o}&keywords=${a}&startTime=${d}&endTime=${r}`);return Array.isArray(s.data)&&s.data.length>0?s.data.length===1&&((i=s.data[0])!=null&&i.children)?s.data:[]:[]}),N=()=>p(void 0,null,function*(){var o;const e=yield t.get("/getJsonSchemaInfo");return Array.isArray(e.data)&&e.data.length>0?e.data.length===1&&((o=e.data[0])!=null&&o.children)?e.data:[]:[]}),O=e=>t.post("/deleteFile",e,{headers:{"Content-Type":n.JSON}}),w=o=>p(void 0,[o],function*({path:e}){var d,r;const a=yield t.get(`/fileDownload?path=${e}`,{responseType:"blob"});if((d=a==null?void 0:a.data)!=null&&d.headers["content-disposition"])return{code:1,blob:a.data.data};{const c=new FileReader;c.readAsText((r=a.data)==null?void 0:r.data),c.onload=()=>{const s=c.result||"{}";return JSON.parse(s)}}}),A=e=>t.post("/renameFile",e,{headers:{"Content-Type":n.JSON}}),C=({path:e})=>t.get(`/getFileInfo?path=${e}`),D=e=>t.post("/moveFile",e,{headers:{"Content-Type":n.JSON}}),$=e=>t.post("/createFile",e,{headers:{"Content-Type":n.JSON}}),B=({params:e,path:o,onUploadProgress:a})=>t.post(`/fileUpload?path=${o}`,e,{onUploadProgress:a,timeout:600*1e3});export{u as a,J as b,D as c,m as d,O as e,A as f,y as g,w as h,C as i,F as j,$ as k,B as l,S as m,N as n,T as s};
