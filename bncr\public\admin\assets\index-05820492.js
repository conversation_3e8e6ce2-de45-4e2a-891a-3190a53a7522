import{d as r,a1 as l,Z as i,o as d,p,w as e,e as t,a as n,b as m,h as f,f as h,aE as b,H as x,j as T,aq as g}from"./index-b380aaed.js";const v=n("div",null,"当前路由的描述数据(meta)：",-1),B=r({__name:"index",setup(N){const{routerPush:o}=l(),s=i();function _(){o({name:b("function_tab")})}return(k,w)=>{const u=x,a=T,c=g;return d(),p(a,{vertical:!0,size:16},{default:e(()=>[t(c,{title:"Tab Detail",bordered:!1,size:"small",class:"rounded-8px shadow-sm"},{default:e(()=>[t(a,{vertical:!0,size:12},{default:e(()=>[v,n("div",null,m(f(s).meta),1),t(u,{onClick:_},{default:e(()=>[h("返回Tab")]),_:1})]),_:1})]),_:1})]),_:1})}}});export{B as default};
