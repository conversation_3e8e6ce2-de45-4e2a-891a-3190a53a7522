var F=(V,j,o)=>new Promise((x,r)=>{var N=c=>{try{S(o.next(c))}catch(i){r(i)}},U=c=>{try{S(o.throw(c))}catch(i){r(i)}},S=c=>c.done?x(c.value):Promise.resolve(c.value).then(N,U);S((o=o.apply(V,j)).next())});import{_ as xe,a as De,b as je}from"./index.vue_vue_type_script_setup_true_lang-1b25d6eb.js";import{_ as ue}from"./_plugin-vue_export-helper-c27b6911.js";import{o as g,p as I,w as t,e as a,be as re,d as le,ak as te,K as s,c as K,a2 as Se,a as A,f as p,F as ie,J as de,aI as q,i as ve,ar as ce,H as Q,L as ee,k as Ce,ae as Z,h as P,j as ae,b as R,an as Te,b2 as Ve,ao as Ne,aX as Ue,aG as fe,aY as $e,D as pe,M as Fe,q as Y,I as Ie,b9 as Be,aq as Oe,bf as Le,as as Me,bg as ze}from"./index-b380aaed.js";import{u as _e}from"./use-loading-empty-2d142965.js";import{d as me,a as Ee,b as Ke}from"./file-b561a0a1.js";const Je={};function Pe(V,j){const o=xe,x=re;return g(),I(x,null,{default:t(()=>[a(o,{style:{"vertical-align":"baseline"}})]),_:1})}const Ae=ue(Je,[["render",Pe]]),Ge={};function He(V,j){const o=De,x=re;return g(),I(x,null,{default:t(()=>[a(o,{style:{"vertical-align":"baseline"}})]),_:1})}const We=ue(Ge,[["render",He]]),Xe={style:{display:"flex","justify-content":"flex-end"}},Ye=le({__name:"CreateForm",props:{jsonData:{type:Object,default:{}},isShow:{type:Boolean,default:!1},database:String,formName:String},emits:["updateParent"],setup(V,{emit:j}){const o=V,x=te(),r=s({}),N=s(),U=s(!1),S=s(U.value?13:15),c=s(!1),i=s(U.value?"on":"off"),w=s({content:"",language:"json"}),y=()=>{const _=o.jsonData.key,b=o.jsonData.val,f=o.jsonData.dataType;switch(r.value.key=_,r.value.val=b,r.value.type=f,f){case"string":return a(q,{span:12,"show-label":!1},{default:()=>[a(ce,{value:r.value.val,"onUpdate:value":h=>r.value.val=h,onKeydown:O,type:"textarea"},null)]});case"boolean":return a(q,{span:12,"show-label":!1},{default:()=>[a(ve,{value:r.value.val,"onUpdate:value":h=>r.value.val=h,onKeydown:O},null)]});case"number":return a(q,{span:12,"show-label":!1},{default:()=>[a(de,{value:r.value.val,"onUpdate:value":h=>r.value.val=h,onKeydown:O},null)]});case"array":case"object":return w.value.content=JSON.stringify(r.value.val,null,2),a(je,{ref:"editor",value:w.value,"word-wrap":i.value,fontSize:S.value,minimap:c.value,style:{height:"500px"}},null)}},O=_=>{_.key==="Enter"&&_.preventDefault()};function G(){return F(this,null,function*(){var f;let _="";(f=N==null?void 0:N.value)!=null&&f.getValue?(_=N.value.getValue(),["object","array"].includes(r.value.type)&&(_=JSON.parse(_))):_=r.value.val;const b={database:o.database||"",form:o.jsonData.form||"",key:r.value.key,val:_};(yield me(b)).data?(j("updateParent",b.key,b.val),x.success("数据修改成功")):x.error("数据修改失败")})}return(_,b)=>{const f=Q;return g(),K(ie,null,[(g(),I(Se(V.isShow?y():null))),A("div",Xe,[a(f,{round:"",type:"primary",onClick:G},{default:t(()=>[p(" 保存修改 ")]),_:1})])],64)}}}),qe=le({__name:"DisplayDataTable",props:{dataBaseInfo:Object,height:Number,filterValue:String},setup(V,{expose:j}){const o=V,x=Te.isMobile,{loading:r,startLoading:N,endLoading:U,setEmpty:S}=_e(),c=s();ee(()=>o.dataBaseInfo,(l,n)=>F(this,null,function*(){if(c.value=l,l!=null&&l.database)return yield C();i.value=[]}));const i=s([]),w=s({}),y=s(!1),O=s(""),G=(l,n)=>{C(),y.value=!1},_=Ce(()=>o.filterValue?i.value.filter(n=>Object.values(n).some(m=>m.toString().toLowerCase().includes(o.filterValue.toLowerCase()))):i.value),b=Z({title:"键",key:"key",align:"center",fixed:"left",resizable:!0,width:x.value?90:150,filter:"default",filterOptionValue:null}),f=Z({title:"值",key:"val",filter:"default",filterOptionValue:null}),h=()=>[b,f,{title:"数据类型",key:"dataType",align:"center",width:100,render(l){return l.dataType.map(m=>Ve(Ne,{style:{marginRight:"6px"},type:"info",bordered:!1},{default:()=>m}))}},{title:"操作",key:"action",width:70,align:"center",fixed:"right",render:l=>a(ae,{justify:"center"},{default:()=>[a(Q,{size:"small",onClick:()=>{M(l)}},{default:()=>[p(" 编辑 ")]}),a(Ue,{onPositiveClick:()=>{H(l)}},{default:()=>"确认删除",trigger:()=>a(Q,{size:"small"},{default:()=>[p("删除")]})})]})}],L=te(),D=Z(h());function C(){return F(this,null,function*(){var m;N();const l={database:c.value.database,form:c.value.value};if(!(l!=null&&l.database)||!(l!=null&&l.form))return U();let n=yield fe.post("/getAllFromValue",l);n!=null&&n.data&&d(n.data)==="array"?i.value=(m=n==null?void 0:n.data)==null?void 0:m.map(k=>(k.dataType=[d(k.val)],typeof k.val=="object"&&(k.val=JSON.stringify(k.val)),k.val=k.val.toString(),k)):i.value=[],U(),S(!i.value.length)})}function d(l){let n=Object.prototype.toString.call(l),m=new RegExp("(?<=^\\[object )([^\\]])+").exec(n);return(m==null?void 0:m[0])&&(m==null?void 0:m[0].toLowerCase())||"未知"}function M(l){let n={};n.originalVal=l.val,n.dataType=l.dataType[0],n.form=l.form,n.key=l.key,["object","array"].includes(l.dataType[0])?n.val=JSON.parse(l.val):["number"].includes(l.dataType[0])?n.val=+l.val:["string"].includes(l.dataType[0])?n.val=l.val:["boolean"].includes(l.dataType[0])&&(n.val=l.val==="false"?!1:l.val==="true"),w.value=n,O.value=l.form,y.value=!0}function H(l){return F(this,null,function*(){const n={database:c.value.database||"",form:l.form||"",key:l.key};(yield Ee(n)).data?(C(),L.success("删除成功")):L.error("删除失败!")})}return j({getDataSource:C,dataSource:i}),(l,n)=>{const m=$e,k=pe;return g(),K(ie,null,[a(P(ae),{style:{"margin-top":"5px"},size:12},{default:t(()=>[a(m,{style:{width:"auto"},"single-line":!1,bordered:!0,columns:D,data:_.value,pagination:{pageSize:10},"table-layout":"auto",striped:!1,"max-height":o.height,"scroll-x":1200,loading:P(r)},null,8,["columns","data","max-height","loading"])]),_:1}),a(k,{show:y.value,"onUpdate:show":n[0]||(n[0]=z=>y.value=z),preset:"dialog",title:"Dialog",style:{height:"80%",width:"70%","max-width":"800px"}},{header:t(()=>[p(" 修改数据 ")]),default:t(()=>{var z;return[A("p",null,"键:"+R(w.value.key)+", 类型:"+R(w.value.dataType),1),a(Ye,{jsonData:w.value,isShow:y.value,database:(z=V.dataBaseInfo)==null?void 0:z.database,onUpdateParent:G},null,8,["jsonData","isShow","database"])]}),_:1},8,["show"])],64)}}}),Qe={style:{display:"flex","justify-content":"center","align-items":"center"}},Re={style:{display:"flex","justify-content":"flex-end"}},Ze={key:0},ea={key:1},aa=A("br",null,null,-1),la=A("br",null,null,-1),ta={key:0},na={key:1},va=le({__name:"index",setup(V){const j=s(550),o=te(),{loading:x,startLoading:r,endLoading:N,setEmpty:U}=_e(),S=s(),c=s(null),i=s(!1),w=s(!1),y=s("string"),O=[{label:"string",value:"string"},{label:"number",value:"number"},{label:"object",value:"object",disabled:!0},{label:"boolean",value:"boolean"}];function G(v){v==="boolean"?d.value=!1:v==="number"?d.value=0:d.value=null}const _=s([]),b=s([]),f=s({database:"",label:"",value:""}),h=s(null),L=s(null),D=s(null),C=s(null),d=s(null),M=s(null),H=s(!1);function l(){return F(this,null,function*(){var e;H.value=!0;const v={database:((e=f.value)==null?void 0:e.database)||"",form:D.value||""};(yield Ke(v)).data?(o.success("删除成功"),yield k()):o.error("删除失败!"),H.value=!1,w.value=!1})}const n=s(!1);function m(){return F(this,null,function*(){var e,T,J,B;n.value=!0;const v={database:((e=f.value)==null?void 0:e.database)||"",form:D.value||"",key:C.value||"",val:d.value};(yield me(v)).data?(o.success("创建数据成功"),((T=f.value)==null?void 0:T.value)===D.value?(B=(J=c.value)==null?void 0:J.getDataSource)==null||B.call(J):yield k()):o.error("数据修改失败"),n.value=!1,i.value=!1})}function k(){return F(this,null,function*(){r();let v=yield fe.get("/getDatabaseFilename");if(v.data)for(const e of v.data){let T={label:e.name,value:e.name,allForm:e.allForm};_.value.push(T),e.name==="user"&&(L.value="uesr",z("user",T))}N(),U(!_.value.length)})}function z(v,e){if(b.value=[],!(e!=null&&e.allForm)){f.value=void 0,h.value=null;return}h.value=null;for(const T of e==null?void 0:e.allForm)b.value.push({database:v,label:T,value:T});h.value=b.value[0].value,D.value=b.value[0].value,f.value=b.value[0]}function ge(v,e){if(!v){f.value=void 0,D.value=null;return}f.value=e,D.value=v}return Fe(()=>F(this,null,function*(){k()})),ee(i,v=>{v||(y.value="string",C.value=null,d.value=null)}),ee(w,v=>{v||(M.value=null)}),(v,e)=>{const T=Ie,J=Be,B=ce,E=Q,ye=ae,be=Oe,X=q,he=Le,we=de,ke=ve,ne=Me,se=pe,W=ze;return g(),K("div",null,[a(be,{bordered:!1,class:"h-full rounded-8px shadow-sm"},{default:t(()=>[a(ye,{style:{display:"flex","justify-content":"space-between"}},{default:t(()=>{var u,oe;return[A("div",Qe,[a(T,{style:{width:"100px","margin-right":"5px"},value:L.value,"onUpdate:value":[e[0]||(e[0]=$=>L.value=$),z],filterable:"",placeholder:"选择库",clearable:"",options:_.value,loading:P(x),"consistent-menu-width":!1},null,8,["value","options","loading"]),a(T,{style:{width:"150px","margin-right":"5px"},value:h.value,"onUpdate:value":[e[1]||(e[1]=$=>h.value=$),ge],filterable:"",placeholder:"选择表",clearable:"",options:b.value,loading:P(x),"consistent-menu-width":!1},null,8,["value","options","loading"]),a(J,{style:{width:"150px"},class:"slider-right",value:j.value,"onUpdate:value":e[2]||(e[2]=$=>j.value=$),min:100,max:1e3,step:50},null,8,["value"])]),A("div",Re,[(oe=(u=c.value)==null?void 0:u.dataSource)!=null&&oe.length?(g(),I(B,{key:0,placeholder:"输入要过滤信息",value:S.value,"onUpdate:value":e[3]||(e[3]=$=>S.value=$),style:{"max-width":"150px"}},null,8,["value"])):Y("",!0),h.value?(g(),I(E,{key:1,tertiary:"",type:"error",onClick:e[4]||(e[4]=$=>w.value=!0),style:{"margin-left":"5px"}},{icon:t(()=>[a(P(Ae))]),default:t(()=>[p(" 删除表 ")]),_:1})):Y("",!0),L.value?(g(),I(E,{key:2,ghost:"",type:"success",style:{"margin-left":"5px"},onClick:e[5]||(e[5]=$=>i.value=!0)},{icon:t(()=>[a(P(We))]),default:t(()=>[p(" 新增 ")]),_:1})):Y("",!0)]),a(qe,{ref_key:"DisplayDataTableRef",ref:c,dataBaseInfo:f.value,height:j.value,filterValue:S.value},null,8,["dataBaseInfo","height","filterValue"])]}),_:1})]),_:1}),a(se,{show:i.value,"onUpdate:show":e[13]||(e[13]=u=>i.value=u),preset:"dialog","mask-closable":!1},{header:t(()=>[p(" 新增数据 ")]),action:t(()=>[a(E,{type:"default",onClick:e[12]||(e[12]=u=>i.value=!1)},{default:t(()=>[p(" 取消 ")]),_:1}),a(ne,{trigger:"hover"},{trigger:t(()=>[a(E,{type:"primary",onClick:m,loading:n.value,disabled:!D.value||!C.value||d.value===null||typeof d.value=="string"&&d.value===""},{default:t(()=>[p(" 确定新增 ")]),_:1},8,["loading","disabled"])]),default:t(()=>[!D.value||!C.value||d.value===null||typeof d.value=="string"&&d.value===""?(g(),K("span",Ze," 请完善必填项 ")):(g(),K("span",ea," 当表和键值同时存在时,新增的数据会覆盖旧数据 "))]),_:1})]),default:t(()=>[a(X,{label:"表(form,默认当前表)"},{default:t(()=>[a(B,{value:D.value,"onUpdate:value":e[6]||(e[6]=u=>D.value=u)},null,8,["value"])]),_:1}),a(X,{label:"键(key)"},{default:t(()=>[a(B,{value:C.value,"onUpdate:value":e[7]||(e[7]=u=>C.value=u)},null,8,["value"])]),_:1}),a(X,{label:"数据类型"},{default:t(()=>[a(he,{value:y.value,"onUpdate:value":[e[8]||(e[8]=u=>y.value=u),G],options:O,trigger:"click"},{default:t(()=>[a(E,null,{default:t(()=>[p(R(y.value||"选择数据类型"),1)]),_:1})]),_:1},8,["value"])]),_:1}),a(X,{label:"值(value)"},{default:t(()=>[y.value==="string"?(g(),I(B,{key:0,value:d.value,"onUpdate:value":e[9]||(e[9]=u=>d.value=u),type:"textarea"},null,8,["value"])):y.value==="number"?(g(),I(we,{key:1,value:d.value,"onUpdate:value":e[10]||(e[10]=u=>d.value=u)},null,8,["value"])):y.value==="boolean"?(g(),I(ke,{key:2,value:d.value,"onUpdate:value":e[11]||(e[11]=u=>d.value=u)},null,8,["value"])):Y("",!0)]),_:1})]),_:1},8,["show"]),a(se,{show:w.value,"onUpdate:show":e[16]||(e[16]=u=>w.value=u),preset:"dialog","mask-closable":!1},{header:t(()=>[p(" 确认删除? ")]),action:t(()=>[a(E,{type:"default",onClick:e[15]||(e[15]=u=>w.value=!1)},{default:t(()=>[p(" 取消 ")]),_:1}),a(ne,{trigger:"hover"},{trigger:t(()=>[a(E,{type:"error",onClick:l,loading:H.value,disabled:M.value!=="确认删除"},{default:t(()=>[p(" 确认删除! ")]),_:1},8,["loading","disabled"])]),default:t(()=>[M.value!=="确认删除"?(g(),K("span",ta," 请输入'确认删除' ")):(g(),K("span",na," 删除后无法恢复数据!你确认? "))]),_:1})]),default:t(()=>[a(W,{strong:""},{default:t(()=>[p("警告: 此操作将删除当前选中表[ ")]),_:1}),a(W,{type:"error",strong:""},{default:t(()=>[p(R(h.value),1)]),_:1}),a(W,null,{default:t(()=>[p(" ]中的所有数据,包括未展开的分页,且删除后 ")]),_:1}),a(W,{type:"error",strong:""},{default:t(()=>[p(" 无法恢复! ")]),_:1}),aa,la,a(W,{strong:""},{default:t(()=>[p(" 请输入'确认删除' ")]),_:1}),a(B,{placeholder:"请输入'确认删除'",value:M.value,"onUpdate:value":e[14]||(e[14]=u=>M.value=u)},null,8,["value"])]),_:1},8,["show"])])}}});export{va as default};
