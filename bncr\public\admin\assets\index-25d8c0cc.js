import{_}from"./_plugin-vue_export-helper-c27b6911.js";import{o as n,c,e as t,w as e,f as o,a as s,aq as d,j as r}from"./index-b380aaed.js";const i={},f=s("p",{class:"pb-16px"},"基础卡片",-1),u=s("p",{class:"pb-16px"},"卡片有 small、medium、large、huge 尺寸。",-1),p=s("p",{class:"pb-16px"}," content 和 footer 可以被 hard 或 soft 分段，action 可以被分段。分段分割线会在区域的上方出现。 ",-1);function m(h,x){const a=d,l=r;return n(),c("div",null,[t(a,{title:"卡片",bordered:!1,class:"h-full rounded-8px shadow-sm"},{default:e(()=>[t(l,{vertical:!0},{default:e(()=>[t(a,{title:"基本用法"},{default:e(()=>[f,t(a,{title:"卡片"},{default:e(()=>[o("卡片内容")]),_:1})]),_:1}),t(a,{title:"尺寸"},{default:e(()=>[u,t(l,{vertical:""},{default:e(()=>[t(a,{title:"小卡片",size:"small"},{default:e(()=>[o("卡片内容")]),_:1}),t(a,{title:"中卡片",size:"medium"},{default:e(()=>[o("卡片内容")]),_:1}),t(a,{title:"大卡片",size:"large"},{default:e(()=>[o("卡片内容")]),_:1}),t(a,{title:"超大卡片",size:"huge"},{default:e(()=>[o("卡片内容")]),_:1})]),_:1})]),_:1}),t(a,{title:"文本按钮"},{default:e(()=>[p,t(a,{title:"卡片分段示例",segmented:{content:!0,footer:"soft"}},{"header-extra":e(()=>[o("#header-extra")]),footer:e(()=>[o("#footer")]),action:e(()=>[o("#action")]),default:e(()=>[o(" 卡片内容 ")]),_:1})]),_:1})]),_:1})]),_:1})])}const z=_(i,[["render",m]]);export{z as default};
