var F=Object.defineProperty;var V=Object.getOwnPropertySymbols;var W=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable;var q=(n,e,t)=>e in n?F(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,H=(n,e)=>{for(var t in e||(e={}))W.call(e,t)&&q(n,t,e[t]);if(V)for(var t of V(e))X.call(e,t)&&q(n,t,e[t]);return n};var I=(n,e,t)=>new Promise((c,p)=>{var f=i=>{try{l(t.next(i))}catch(u){p(u)}},s=i=>{try{l(t.throw(i))}catch(u){p(u)}},l=i=>i.done?c(i.value):Promise.resolve(i.value).then(f,s);l((t=t.apply(n,e)).next())});import{d as x,o as h,c as k,a as o,k as $,aF as j,e as a,h as m,K as S,aG as Q,a9 as O,a1 as P,aD as U,ae as Y,aH as N,p as b,w as d,f as w,b as g,q as E,ar as D,aI as ee,aJ as oe,H as R,j as T,aK as te,$ as J,u as se,aL as ne,az as ae,a2 as le,x as re,aM as G,X as ce,y as ie,aq as de}from"./index-b380aaed.js";import{b as pe}from"./dark-mode-switch.vue_vue_type_script_setup_true_lang-8ee9fa87.js";/* empty css                                                         */import{f as _e}from"./rule-853ff5ed.js";const ue={height:"1337",width:"1337"},me=o("path",{id:"path-1",opacity:"1","fill-rule":"evenodd",d:"M1337,668.5 C1337,1037.455193874239 1037.455193874239,1337 668.5,1337 C523.6725684305388,1337 337,1236 370.50000000000006,1094 C434.03835568300906,824.6732385973953 6.906089672974592e-14,892.6277623047779 0,668.5000000000001 C0,299.5448061257611 299.5448061257609,1.1368683772161603e-13 668.4999999999999,0 C1037.455193874239,0 1337,299.544806125761 1337,668.5Z"},null,-1),fe={id:"linearGradient-2",x1:"0.79",y1:"0.62",x2:"0.21",y2:"0.86"},he=["stop-color"],ge=["stop-color"],ve=o("g",{opacity:"1"},[o("use",{"xlink:href":"#path-1",fill:"url(#linearGradient-2)","fill-opacity":"1"})],-1),we=x({__name:"corner-top",props:{startColor:{default:"#28aff0"},endColor:{default:"#120fc4"}},setup(n){return(e,t)=>(h(),k("svg",ue,[o("defs",null,[me,o("linearGradient",fe,[o("stop",{offset:"0","stop-color":e.startColor,"stop-opacity":"1"},null,8,he),o("stop",{offset:"1","stop-color":e.endColor,"stop-opacity":"1"},null,8,ge)])]),ve]))}}),xe={version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",height:"896",width:"967.8852157128662"},ye=o("path",{id:"path-2",opacity:"1","fill-rule":"evenodd",d:"M896,448 C1142.6325445712241,465.5747656464056 695.2579309733121,896 448,896 C200.74206902668806,896 5.684341886080802e-14,695.2579309733121 0,448.0000000000001 C0,200.74206902668806 200.74206902668791,5.684341886080802e-14 447.99999999999994,0 C695.2579309733121,0 475,418 896,448Z"},null,-1),$e={id:"linearGradient-3",x1:"0.5",y1:"0",x2:"0.5",y2:"1"},be=["stop-color"],ke=["stop-color"],Ce=o("g",{opacity:"1"},[o("use",{"xlink:href":"#path-2",fill:"url(#linearGradient-3)","fill-opacity":"1"})],-1),ze=x({__name:"corner-bottom",props:{startColor:{default:"#28aff0"},endColor:{default:"#120fc4"}},setup(n){return(e,t)=>(h(),k("svg",xe,[o("defs",null,[ye,o("linearGradient",$e,[o("stop",{offset:"0","stop-color":e.startColor,"stop-opacity":"1"},null,8,be),o("stop",{offset:"1","stop-color":e.endColor,"stop-opacity":"1"},null,8,ke)])]),Ce]))}}),Ne={class:"absolute-lt z-1 wh-full overflow-hidden"},Le={class:"absolute -right-300px -top-900px <sm:(-right-100px -top-1170px)"},Me={class:"absolute -left-200px -bottom-400px <sm:(-left-100px -bottom-760px)"},Be=x({__name:"index",props:{themeColor:{}},setup(n){const e=n,t=$(()=>j(e.themeColor,3)),c=$(()=>j(e.themeColor,6));return(p,f)=>(h(),k("div",Ne,[o("div",Le,[a(m(we),{"start-color":t.value,"end-color":c.value},null,8,["start-color","end-color"])]),o("div",Me,[a(m(ze),{"start-color":c.value,"end-color":t.value},null,8,["start-color","end-color"])])]))}}),Se={class:"flex-y-center justify-between"},Ue={key:0,class:"flex-y-center justify-between"},Ge=x({__name:"index",setup(n){const e=S({name:!1,password:!1});Q.get("/getUnregistered").then(r=>{r.data&&(e.value=r.data)});const t=O(),{login:c}=O(),{toLoginModule:p}=P(),f=S(),s=U.get("rememberMeData"),l=Y({userName:(s==null?void 0:s.userName)&&z(s==null?void 0:s.userName)||"",password:(s==null?void 0:s.password)&&z(s==null?void 0:s.password)||""}),i={password:_e.pwd},u=S((s==null?void 0:s.rememberMe)||!1);function L(){return I(this,null,function*(){var y;yield(y=f.value)==null?void 0:y.validate();const{userName:r,password:_}=l;(yield c(r,_))&&(u.value?U.set("rememberMeData",{userName:C(r),password:C(_),rememberMe:u.value}):U.remove("rememberMeData"))})}function C(r){return N.enc.Base64.stringify(N.enc.Utf8.parse(r))}function z(r){return N.enc.Base64.parse(r).toString(N.enc.Utf8)}return(r,_)=>{const M=D,y=ee,K=oe,B=R,Z=T,A=te;return h(),b(A,{ref_key:"formRef",ref:f,model:l,rules:i,size:"large","show-label":!1},{default:d(()=>[a(y,{path:"userName"},{default:d(()=>[a(M,{value:l.userName,"onUpdate:value":_[0]||(_[0]=v=>l.userName=v),placeholder:e.value.name?r.$t("page.login.common.userNamePlaceholder"):"请先初始化账号",disabled:!e.value.name},null,8,["value","placeholder","disabled"])]),_:1}),a(y,{path:"password"},{default:d(()=>[a(M,{value:l.password,"onUpdate:value":_[1]||(_[1]=v=>l.password=v),type:"password","show-password-on":"click",placeholder:e.value.password?r.$t("page.login.common.passwordPlaceholder"):"请先初始化密码",disabled:!e.value.password},null,8,["value","placeholder","disabled"])]),_:1}),a(Z,{vertical:!0,size:24},{default:d(()=>[o("div",Se,[a(K,{checked:u.value,"onUpdate:checked":_[2]||(_[2]=v=>u.value=v)},{default:d(()=>[w(g(r.$t("page.login.pwdLogin.rememberMe")),1)]),_:1},8,["checked"]),e.value.name&&e.value.password?(h(),b(B,{key:0,text:!0,onClick:_[3]||(_[3]=v=>m(p)("reset-pwd"))},{default:d(()=>[w(g(r.$t("page.login.pwdLogin.forgetPassword")),1)]),_:1})):E("",!0)]),a(B,{type:"primary",size:"large",block:!0,round:!0,loading:m(t).loginLoading,onClick:L,disabled:!e.value.name||!e.value.password},{default:d(()=>[w(g(!e.value.name||!e.value.password?"暂不能登录":r.$t("page.login.common.confirm")),1)]),_:1},8,["loading","disabled"]),e.value.name&&e.value.password?E("",!0):(h(),k("div",Ue,[a(B,{size:"large",block:!0,round:!0,onClick:_[4]||(_[4]=v=>m(p)("register"))},{default:d(()=>[w(g(r.$t("page.login.pwdLogin.register")),1)]),_:1})]))]),_:1})]),_:1},8,["model"])}}}),je=o("p",{class:"text-18px font-bold text-primary"},"准备工作：",-1),Pe=o("p",null,"打开主机bash执行：docker attach bncr进入容器内部. 或在拥有管理员的平台发送以下命令:",-1),Re=o("p",{class:"text-18px font-bold text-primary"},"设置账号/密码：",-1),Te=o("p",null,"设置账号：set system name 你的账号",-1),Ve=o("p",null,"设置密码：set system password 你的密码",-1),qe=x({__name:"index",setup(n){const{toLoginModule:e}=P();return(t,c)=>{const p=R,f=T;return h(),b(f,{vertical:!0,size:"large"},{default:d(()=>[je,Pe,Re,Te,Ve,a(p,{size:"large",block:!0,round:!0,onClick:c[0]||(c[0]=s=>m(e)("pwd-login"))},{default:d(()=>[w(g(t.$t("page.login.common.back")),1)]),_:1})]),_:1})}}}),He=o("p",{class:"text-18px font-bold text-primary"},"准备工作：",-1),Ie=o("p",null,"打开主机bash执行：docker attach bncr进入容器内部. 或在拥有管理员的平台发送以下命令:",-1),Oe=o("p",{class:"text-18px font-bold text-primary"},"获取账号/密码：",-1),Ee=o("p",null,"获取账号：get system name",-1),Je=o("p",null,"获取密码：get system password",-1),Ke=o("p",null,null,-1),Ze=o("p",{class:"text-18px font-bold text-primary"},"重设账号/密码：",-1),Ae=o("p",null,"设置账号：set system name 你的账号",-1),Fe=o("p",null,"设置密码：set system password 你的密码",-1),We=x({__name:"index",setup(n){const{toLoginModule:e}=P();return(t,c)=>{const p=R,f=T;return h(),b(f,{vertical:!0,size:"large"},{default:d(()=>[He,Ie,Oe,Ee,Je,Ke,Ze,Ae,Fe,a(p,{size:"large",block:!0,round:!0,onClick:c[0]||(c[0]=s=>m(e)("pwd-login"))},{default:d(()=>[w(g(m(J)("page.login.common.back")),1)]),_:1})]),_:1})}}}),Xe={class:"w-300px sm:w-360px"},Qe={class:"flex-y-center justify-between"},Ye={class:"pt-24px"},De={class:"text-18px text-primary font-medium"},eo={class:"pt-24px"},lo=x({__name:"index",props:{module:{}},setup(n){const e=n,t=se(),c=[{key:"pwd-login",label:G["pwd-login"],component:Ge},{key:"register",label:G.register,component:qe},{key:"reset-pwd",label:G["reset-pwd"],component:We}],p=$(()=>{const l=H({},c[0]),i=c.find(u=>u.key===e.module);return i&&Object.assign(l,i),l}),f=$(()=>t.darkMode?j(t.themeColor,7):t.themeColor),s=$(()=>{const l="#ffffff",i=t.darkMode?.5:.2;return ne(l,t.themeColor,i)});return(l,i)=>{const u=pe,L=ce,C=ie,z=de;return h(),k("div",{class:"relative flex-center wh-full",style:re({backgroundColor:s.value})},[a(u,{dark:m(t).darkMode,class:"absolute left-48px top-24px z-3 text-20px","onUpdate:dark":m(t).setDarkMode},null,8,["dark","onUpdate:dark"]),a(z,{bordered:!1,size:"large",class:"z-4 !w-auto rounded-20px shadow-sm"},{default:d(()=>[o("div",Xe,[o("header",Qe,[a(L,{"local-icon":"logo",class:"text-64px text-primary"}),a(C,{type:"primary",size:28},{default:d(()=>[w(g(m(J)("system.title")),1)]),_:1})]),o("main",Ye,[o("h3",De,g(p.value.label),1),o("div",eo,[a(ae,{name:"fade-slide",mode:"out-in",appear:""},{default:d(()=>[(h(),b(le(p.value.component)))]),_:1})])])])]),_:1}),a(m(Be),{"theme-color":f.value},null,8,["theme-color"])],4)}}});export{lo as default};
