var Y=Object.defineProperty;var V=Object.getOwnPropertySymbols;var j=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable;var P=(s,a,e)=>a in s?Y(s,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[a]=e,A=(s,a)=>{for(var e in a||(a={}))j.call(a,e)&&P(s,e,a[e]);if(V)for(var e of V(a))W.call(a,e)&&P(s,e,a[e]);return s};import{d as g,K as b,aZ as D,k as T,a_ as q,L as H,a$ as I,M as K,o as p,c as k,b as z,b0 as U,b1 as R,p as v,w as n,e as t,a as l,f as X,H as Z,aq as L,z as N,A as C,r as Q,x as ee,l as E,h as y,F as J,X as te,aU as ae,b2 as ne,ao as oe,b3 as se,b4 as re,aY as le,j as ce}from"./index-b380aaed.js";const M=g({name:"CountTo",__name:"count-to",props:{startValue:{default:0},endValue:{default:2021},duration:{default:1500},autoplay:{type:Boolean,default:!0},decimals:{default:0},prefix:{default:""},suffix:{default:""},separator:{default:","},decimal:{default:"."},useEasing:{type:Boolean,default:!0},transition:{default:"linear"}},emits:["on-started","on-finished"],setup(s,{emit:a}){const e=s,r=b(e.startValue);let c=D(r);const f=T(()=>o(c.value)),u=b(!1);function _(){c=D(r,A({disabled:u,duration:e.duration,onStarted:()=>a("on-started"),onFinished:()=>a("on-finished")},e.useEasing?{transition:U[e.transition]}:{}))}function i(){_(),r.value=e.endValue}function o(d){if(d!==0&&!d)return"";const{decimals:m,decimal:h,separator:w,suffix:O,prefix:F}=e;let $=Number(d).toFixed(m);$=String($);const S=$.split(".");let x=S[0];const G=S.length>1?h+S[1]:"",B=/(\d+)(\d{3})/;if(w&&!q(w))for(;B.test(x);)x=x.replace(B,`$1${w}$2`);return F+x+G+O}return H([()=>e.startValue,()=>e.endValue],()=>{e.autoplay&&i()}),I(()=>{r.value=e.startValue}),K(()=>{e.autoplay&&i()}),(d,m)=>(p(),k("span",null,z(f.value),1))}}),de={class:"w-full h-360px py-12px"},ie=l("h3",{class:"text-16px font-bold"},"Dashboard",-1),ue=l("p",{class:"text-#aaa"},"Overview Of Lasted Month",-1),_e={class:"pt-32px text-24px font-bold"},pe=l("p",{class:"text-#aaa"},"Current Month Earnings",-1),fe={class:"pt-32px text-24px font-bold"},me=l("p",{class:"text-#aaa"},"Current Month Sales",-1),ge=g({name:"DashboardAnalysisTopCard",__name:"index",setup(s){const a=b({tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:"#6a7985"}}},legend:{data:["下载量","注册数"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",boundaryGap:!1,data:["06:00","08:00","10:00","12:00","14:00","16:00","18:00","20:00","22:00","24:00"]}],yAxis:[{type:"value"}],series:[{color:"#8e9dff",name:"下载量",type:"line",smooth:!0,stack:"Total",areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:.25,color:"#8e9dff"},{offset:1,color:"#fff"}]}},emphasis:{focus:"series"},data:[4623,6145,6268,6411,1890,4251,2978,3880,3606,4311]},{color:"#26deca",name:"注册数",type:"line",smooth:!0,stack:"Total",areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:.25,color:"#26deca"},{offset:1,color:"#fff"}]}},emphasis:{focus:"series"},data:[2208,2016,2916,4512,8281,2008,1963,2367,2956,678]}]}),{domRef:e}=R(a),r=b({tooltip:{trigger:"item"},legend:{bottom:"1%",left:"center",itemStyle:{borderWidth:0}},series:[{color:["#5da8ff","#8e9dff","#fedc69","#26deca"],name:"时间安排",type:"pie",radius:["45%","75%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:1},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"12"}},labelLine:{show:!1},data:[{value:20,name:"学习"},{value:10,name:"娱乐"},{value:30,name:"工作"},{value:40,name:"休息"}]}]}),{domRef:c}=R(r);return(f,u)=>{const _=M,i=Z,o=L,d=N,m=C;return p(),v(m,{"x-gap":16,"y-gap":16,"item-responsive":!0},{default:n(()=>[t(d,{span:"0:24 640:24 1024:6"},{default:n(()=>[t(o,{bordered:!1,class:"rounded-8px shadow-sm"},{default:n(()=>[l("div",de,[ie,ue,l("h3",_e,[t(_,{prefix:"$","start-value":0,"end-value":7754})]),pe,l("h3",fe,[t(_,{"start-value":0,"end-value":1234})]),me,t(i,{class:"mt-24px whitespace-pre-wrap",type:"primary"},{default:n(()=>[X("Last Month Summary")]),_:1})])]),_:1})]),_:1}),t(d,{span:"0:24 640:24 1024:10"},{default:n(()=>[t(o,{bordered:!1,class:"rounded-8px shadow-sm"},{default:n(()=>[l("div",{ref_key:"lineRef",ref:e,class:"w-full h-360px"},null,512)]),_:1})]),_:1}),t(d,{span:"0:24 640:24 1024:8"},{default:n(()=>[t(o,{bordered:!1,class:"rounded-8px shadow-sm"},{default:n(()=>[l("div",{ref_key:"pieRef",ref:c,class:"w-full h-360px"},null,512)]),_:1})]),_:1})]),_:1})}}}),he=g({__name:"gradient-bg",props:{startColor:{default:"#56cdf3"},endColor:{default:"#719de3"}},setup(s){const a=s,e=T(()=>`linear-gradient(to bottom right, ${a.startColor}, ${a.endColor})`);return(r,c)=>(p(),k("div",{class:"p-16px rounded-8px text-white",style:ee({backgroundImage:e.value})},[Q(r.$slots,"default")],4))}}),xe={class:"text-16px"},ye={class:"flex justify-between pt-12px"},be=g({name:"DashboardAnalysisDataCard",__name:"index",setup(s){const a=[{id:"visit",title:"访问量",value:1e6,unit:"",colors:["#ec4786","#b955a4"],icon:"ant-design:bar-chart-outlined"},{id:"amount",title:"成交额",value:234567.89,unit:"$",colors:["#865ec0","#5144b4"],icon:"ant-design:money-collect-outlined"},{id:"download",title:"下载数",value:666666,unit:"",colors:["#56cdf3","#719de3"],icon:"carbon:document-download"},{id:"trade",title:"成交数",value:999999,unit:"",colors:["#fcbc25","#f68057"],icon:"ant-design:trademark-circle-outlined"}];return(e,r)=>{const c=te,f=M,u=N,_=C,i=L;return p(),v(i,{bordered:!1,class:"h-full rounded-8px shadow-sm"},{default:n(()=>[t(_,{cols:"s:1 m:2 l:4",responsive:"screen","x-gap":16,"y-gap":16},{default:n(()=>[(p(),k(J,null,E(a,o=>t(u,{key:o.id},{default:n(()=>[t(y(he),{class:"h-100px","start-color":o.colors[0],"end-color":o.colors[1]},{default:n(()=>[l("h3",xe,z(o.title),1),l("div",ye,[t(c,{icon:o.icon,class:"text-32px"},null,8,["icon"]),t(f,{prefix:o.unit,"start-value":1,"end-value":o.value,class:"text-30px text-white dark:text-dark"},null,8,["prefix","end-value"])])]),_:2},1032,["start-color","end-color"])]),_:2},1024)),64))]),_:1})]),_:1})}}}),ke=g({name:"DashboardAnalysisBottomPart",__name:"index",setup(s){const a=[{type:"default",title:"啊",content:"",time:"2021-10-10 20:46"},{type:"success",title:"成功",content:"哪里成功",time:"2021-10-10 20:46"},{type:"error",title:"错误",content:"哪里错误",time:"2021-10-10 20:46"},{type:"warning",title:"警告",content:"哪里警告",time:"2021-10-10 20:46"},{type:"info",title:"信息",content:"是的",time:"2021-10-10 20:46"}],e=[{title:"Name",key:"name"},{title:"Age",key:"age"},{title:"Address",key:"address"},{title:"Tags",key:"tags",render(c){return c.tags.map(u=>ne(oe,{style:{marginRight:"6px"},type:"info"},{default:()=>u}))}}],r=[{key:0,name:"John Brown",age:32,address:"New York No. 1 Lake Park",tags:["nice","developer"]},{key:1,name:"Jim Green",age:42,address:"London No. 1 Lake Park",tags:["wow"]},{key:2,name:"Joe Black",age:32,address:"Sidney No. 1 Lake Park",tags:["cool","teacher"]},{key:3,name:"Soybean",age:25,address:"China Shenzhen",tags:["handsome","programmer"]},{key:4,name:"John Brown",age:32,address:"New York No. 1 Lake Park",tags:["nice","developer"]},{key:5,name:"Jim Green",age:42,address:"London No. 1 Lake Park",tags:["wow"]},{key:6,name:"Joe Black",age:32,address:"Sidney No. 1 Lake Park",tags:["cool","teacher"]}];return(c,f)=>{const u=se,_=re,i=L,o=N,d=le,m=C;return p(),v(m,{"x-gap":16,"y-gap":16,"item-responsive":!0},{default:n(()=>[t(o,{span:"0:24 640:24 1024:8"},{default:n(()=>[t(i,{title:"时间线",bordered:!1,class:"h-full rounded-8px shadow-sm"},{default:n(()=>[t(_,null,{default:n(()=>[(p(),k(J,null,E(a,h=>t(u,ae({key:h.type},h),null,16)),64))]),_:1})]),_:1})]),_:1}),t(o,{span:"0:24 640:24 1024:16"},{default:n(()=>[t(i,{title:"表格",bordered:!1,class:"h-full rounded-8px shadow-sm"},{default:n(()=>[t(d,{size:"small",columns:e,data:r})]),_:1})]),_:1})]),_:1})}}}),$e=g({__name:"index",setup(s){return(a,e)=>{const r=ce;return p(),v(r,{vertical:!0,size:16},{default:n(()=>[t(y(ge)),t(y(be)),t(y(ke))]),_:1})}}});export{$e as default};
