var je=Object.defineProperty,Ke=Object.defineProperties;var Ge=Object.getOwnPropertyDescriptors;var ge=Object.getOwnPropertySymbols;var Xe=Object.prototype.hasOwnProperty,Ze=Object.prototype.propertyIsEnumerable;var ye=(a,t,l)=>t in a?je(a,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[t]=l,ae=(a,t)=>{for(var l in t||(t={}))Xe.call(t,l)&&ye(a,l,t[l]);if(ge)for(var l of ge(t))Ze.call(t,l)&&ye(a,l,t[l]);return a},se=(a,t)=>Ke(a,Ge(t));var oe=(a,t,l)=>new Promise((s,c)=>{var u=_=>{try{d(l.next(_))}catch(b){c(b)}},i=_=>{try{d(l.throw(_))}catch(b){c(b)}},d=_=>_.done?s(_.value):Promise.resolve(_.value).then(u,i);d((l=l.apply(a,t)).next())});import{g as Oe,o as n,c as m,a as p,d as y,b as D,r as Z,u as F,e as o,w as r,f as L,h as e,$ as x,F as S,_ as O,i as ce,j as Y,k as T,n as N,N as ke,l as W,p as h,q as I,s as Ce,v as $e,x as H,y as Ye,z as Me,A as De,B as qe,C as Je,D as Qe,E as ie,G as et,H as le,I as ue,J as tt,K as U,L as j,M as de,O as ot,P as Fe,Q as lt,R as V,S as nt,T as at,U as Be,V as st,W as rt,X as Te,Y as _e,Z as G,a0 as ee,a1 as te,a2 as re,a3 as pe,a4 as ct,a5 as it,a6 as me,a7 as he,a8 as ne,a9 as ut,aa as Ee,ab as Ae,ac as we,ad as be,ae as Se,af as dt,ag as Ue,ah as _t,ai as pt,aj as ze,ak as mt,al as ht,am as bt,an as ft,ao as vt,ap as xt,aq as gt,ar as yt,as as wt,at as kt,au as Ct,av as $t,aw as Mt}from"./index-b380aaed.js";import{_ as Dt,a as Ft,b as Bt}from"./dark-mode-switch.vue_vue_type_script_setup_true_lang-8ee9fa87.js";import{C as Tt,B as Et,_ as At}from"./index.vue_vue_type_style_index_0_scoped_5f06efeb_lang-3c1a6c9e.js";import{_ as K}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                         */import{_ as St,a as Ut}from"./refresh-07a73afb.js";import{u as zt}from"./use-loading-93729ff7.js";import{S as Lt}from"./index-b7461bb8.js";function It(a,t,l){return Pt(a,t).map(u=>Ie(u,l))}function Pt(a,t){const l=[],s=Oe(a,t),c=s?Le(a,s):[];return l.push(...c),l}function Le(a,t){const l=[];return a===t.routeName&&l.push(t),a.includes(t.routeName)&&t.children&&t.children.length&&(l.push(t),l.push(...t.children.map(s=>Le(a,s)).flat(1))),l}function Ie(a,t){var c;const l=!!(a.children&&a.children.length),s={key:a.routeName,label:a.label,routeName:a.routeName,disabled:a.routePath===t,hasChildren:l,i18nTitle:a.i18nTitle};return a.icon&&(s.icon=a.icon),l&&(s.options=(c=a.children)==null?void 0:c.map(u=>Ie(u,t))),s}const Nt=[{label:"红色系",data:[{label:"绾",color:"#A98175"},{label:"檀",color:"#B36D61"},{label:"栗色",color:"#60281E"},{label:"玄",color:"#622A1D"},{label:"胭脂",color:"#9D2933"},{label:"殷红",color:"#BE002F"},{label:"枣红",color:"#C32136"},{label:"赤",color:"#C3272B"},{label:"绯红",color:"#C83C23"},{label:"赫赤",color:"#C91F37"},{label:"樱桃红",color:"#C93756"},{label:"茜色",color:"#CB3A56"},{label:"海棠红",color:"#DB5A6B"},{label:"酡红",color:"#DC3023"},{label:"妃色",color:"#ED5736"},{label:"嫣红",color:"#EF7A82"},{label:"品红",color:"#F00056"},{label:"石榴红",color:"#F20C00"},{label:"银红",color:"#F05654"},{label:"彤",color:"#F35336"},{label:"桃红",color:"#F47983"},{label:"酡颜",color:"#F9906F"},{label:"洋红",color:"#FF0097"},{label:"大红",color:"#FF2121"},{label:"火红",color:"#FF2D51"},{label:"炎",color:"#FF3300"},{label:"朱红",color:"#FF4C00"},{label:"丹",color:"#FF4E20"},{label:"粉红",color:"#FFB3A7"},{label:"藕荷",color:"#E4C6D0"},{label:"藕",color:"#EDD1D8"},{label:"水红",color:"#F3D3E7"},{label:"鱼肚白",color:"#FCEFE8"}]},{label:"橙色系",data:[{label:"褐色",color:"#6E511E"},{label:"棕黑",color:"#7C4B00"},{label:"赭色",color:"#955539"},{label:"棕红",color:"#9B4400"},{label:"赭",color:"#9C5333"},{label:"驼色",color:"#A88462"},{label:"棕色",color:"#B25D25"},{label:"茶色",color:"#B35C44"},{label:"琥珀",color:"#CA6924"},{label:"黄栌",color:"#E29C45"},{label:"橙色",color:"#FA8C35"},{label:"橘红",color:"#FF7500"},{label:"橘黄",color:"#FF8936"},{label:"杏红",color:"#FF8C31"},{label:"橙黄",color:"#FFA400"},{label:"杏黄",color:"#FFA631"},{label:"姜黄",color:"#FFC773"}]},{label:"黄色系",data:[{label:"黧",color:"#5D513C"},{label:"黎",color:"#75664D"},{label:"棕绿",color:"#827100"},{label:"秋色",color:"#896C39"},{label:"苍黄",color:"#A29B7C"},{label:"乌金",color:"#A78E44"},{label:"棕黄",color:"#AE7000"},{label:"昏黄",color:"#C89B40"},{label:"枯黄",color:"#D3B17D"},{label:"秋香色",color:"#D9B611"},{label:"金",color:"#EACD76"},{label:"牙",color:"#EEDEB0"},{label:"缃色",color:"#F0C239"},{label:"赤金",color:"#F2BE45"},{label:"鸭黄",color:"#FAFF72"},{label:"鹅黄",color:"#FFF143"},{label:"缟",color:"#F2ECDE"},{label:"象牙白",color:"#FFFBF0"}]},{label:"绿色系",data:[{label:"竹青",color:"#789262"},{label:"黯",color:"#41555D"},{label:"黛绿",color:"#426666"},{label:"松花绿",color:"#057748"},{label:"绿沈",color:"#0C8918"},{label:"深绿",color:"#009900"},{label:"青葱",color:"#0AA344"},{label:"铜绿",color:"#549688"},{label:"苍翠",color:"#519A73"},{label:"松柏绿",color:"#21A675"},{label:"葱青",color:"#0EB83A"},{label:"油绿",color:"#00BC12"},{label:"绿",color:"#00E500"},{label:"草绿",color:"#40DE5A"},{label:"豆青",color:"#96CE54"},{label:"豆绿",color:"#9ED048"},{label:"葱绿",color:"#9ED900"},{label:"葱黄",color:"#A3D900"},{label:"柳绿",color:"#AFDD22"},{label:"嫩绿",color:"#BDDD22"},{label:"柳黄",color:"#C9DD22"},{label:"松花",color:"#BCE672"},{label:"樱草色",color:"#EAFF56"}]},{label:"青色系",data:[{label:"水",color:"#88ADA6"},{label:"青碧",color:"#48C0A3"},{label:"碧",color:"#1BD1A5"},{label:"石青",color:"#7BCFA6"},{label:"青翠",color:"#00E079"},{label:"青",color:"#00E09E"},{label:"碧绿",color:"#2ADD9C"},{label:"玉",color:"#2EDFA3"},{label:"翡翠",color:"#3DE1AD"},{label:"缥",color:"#7FECAD"},{label:"碧蓝",color:"#3EEDE7"},{label:"湖绿",color:"#25F8CD"},{label:"艾绿",color:"#A4E2C6"},{label:"青白",color:"#C0EBD7"},{label:"水绿",color:"#D4F2E7"},{label:"鸭卵青",color:"#E0EEE8"},{label:"素",color:"#E0F0E9"},{label:"荼白",color:"#F3F9F1"}]},{label:"蓝色系",data:[{label:"藏蓝",color:"#3B2E7E"},{label:"宝蓝",color:"#4B5CC4"},{label:"绀青",color:"#003371"},{label:"藏青",color:"#2E4E7E"},{label:"靛蓝",color:"#065279"},{label:"靛青",color:"#177CB0"},{label:"群青",color:"#4C8DAE"},{label:"蓝",color:"#44CEF6"},{label:"湖蓝",color:"#30DFF3"},{label:"蔚蓝",color:"#70F3FF"},{label:"月白",color:"#D6ECF0"},{label:"水蓝",color:"#D2F0F4"},{label:"莹白",color:"#E3F9FD"},{label:"雪白",color:"#F0FCFF"}]},{label:"紫色系",data:[{label:"黛",color:"#4A4266"},{label:"紫檀",color:"#4C211B"},{label:"紫棠",color:"#56004F"},{label:"黛紫",color:"#574266"},{label:"绛紫",color:"#8C4356"},{label:"紫酱",color:"#815463"},{label:"酱紫",color:"#815476"},{label:"黝",color:"#6B6882"},{label:"青莲",color:"#801DAE"},{label:"紫",color:"#8D4BBB"},{label:"雪青",color:"#B0A4E3"},{label:"丁香",color:"#CCA4E3"}]},{label:"灰色系",data:[{label:"黑",color:"#000000"},{label:"漆黑",color:"#161823"},{label:"象牙黑",color:"#312520"},{label:"乌黑",color:"#392F41"},{label:"玄青",color:"#3D3B4F"},{label:"缁",color:"#493131"},{label:"黝黑",color:"#665757"},{label:"鸦青",color:"#424C50"},{label:"黛蓝",color:"#425066"},{label:"苍黑",color:"#395260"},{label:"墨",color:"#50616D"},{label:"灰",color:"#808080"},{label:"苍",color:"#75878A"},{label:"墨灰",color:"#758A99"},{label:"苍青",color:"#7397AB"},{label:"蓝灰",color:"#A1AFC9"},{label:"老银",color:"#BACAC6"},{label:"蟹壳青",color:"#BBCDC5"},{label:"苍白",color:"#D1D9E0"},{label:"淡青",color:"#D3E0F3"},{label:"银白",color:"#E9E7EF"},{label:"霜",color:"#E9F1F6"},{label:"铅白",color:"#F0F0F4"},{label:"精白",color:"#FFFFFF"}]}],Pe=Nt;function Ht(a){return Pe.some(t=>t.data.some(s=>s.color===a))}const Vt={class:"inline-block",viewBox:"0 0 1024 1024",width:"1em",height:"1em"},Rt=p("path",{fill:"currentColor","fill-rule":"evenodd",d:"M799.855 166.312c.023.007.043.018.084.059l57.69 57.69c.041.041.052.06.059.084a.118.118 0 0 1 0 .069c-.007.023-.018.042-.059.083L569.926 512l287.703 287.703c.041.04.052.06.059.083a.118.118 0 0 1 0 .07c-.007.022-.018.042-.059.083l-57.69 57.69c-.041.041-.06.052-.084.059a.118.118 0 0 1-.069 0c-.023-.007-.042-.018-.083-.059L512 569.926L224.297 857.629c-.04.041-.06.052-.083.059a.118.118 0 0 1-.07 0c-.022-.007-.042-.018-.083-.059l-57.69-57.69c-.041-.041-.052-.06-.059-.084a.118.118 0 0 1 0-.069c.007-.023.018-.042.059-.083L454.073 512L166.371 224.297c-.041-.04-.052-.06-.059-.083a.118.118 0 0 1 0-.07c.007-.022.018-.042.059-.083l57.69-57.69c.041-.041.06-.052.084-.059a.118.118 0 0 1 .069 0c.023.007.042.018.083.059L512 454.073l287.703-287.702c.04-.041.06-.052.083-.059a.118.118 0 0 1 .07 0Z"},null,-1),Wt=[Rt];function jt(a,t){return n(),m("svg",Vt,Wt)}const Kt={name:"ant-design-close-outlined",render:jt},Gt={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Xt=p("path",{fill:"currentColor",d:"M12.04 8.04h-.09l-1.6 4.55h3.29z"},null,-1),Zt=p("path",{fill:"currentColor",d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm3 14.41l-.78-2.22H9.78l-.79 2.22c-.12.35-.46.59-.83.59a.887.887 0 0 1-.83-1.2l3.34-8.88a1.42 1.42 0 0 1 2.66 0l3.34 8.88c.22.58-.21 1.2-.83 1.2c-.38 0-.72-.24-.84-.59z"},null,-1),Ot=[Xt,Zt];function Yt(a,t){return n(),m("svg",Gt,Ot)}const qt={name:"ic-round-hdr-auto",render:Yt},Jt={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Qt=p("path",{fill:"currentColor",d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8c0-1.85.63-3.55 1.69-4.9L16.9 18.31A7.902 7.902 0 0 1 12 20zm6.31-3.1L7.1 5.69A7.902 7.902 0 0 1 12 4c4.42 0 8 3.58 8 8c0 1.85-.63 3.55-1.69 4.9z"},null,-1),eo=[Qt];function to(a,t){return n(),m("svg",Jt,eo)}const oo={name:"ic-baseline-do-not-disturb",render:to},lo={class:"flex-y-center justify-between"},B=y({name:"SettingMenu",__name:"index",props:{label:{}},setup(a){return(t,l)=>(n(),m("div",lo,[p("span",null,D(t.label),1),Z(t.$slots,"default")]))}}),no=y({name:"DarkMode",__name:"index",setup(a){const t=F();return(l,s)=>{const c=O,u=Dt,i=Ft,d=ce,_=oo,b=qt,w=Y;return n(),m(S,null,[o(c,{"title-placement":"center"},{default:r(()=>[L(D(e(x)("layout.settingDrawer.themeModeTitle")),1)]),_:1}),o(w,{vertical:"",size:"large"},{default:r(()=>[o(B,{label:e(x)("layout.settingDrawer.darkMode")},{default:r(()=>[o(d,{value:e(t).darkMode,"onUpdate:value":e(t).setDarkMode},{checked:r(()=>[o(u,{class:"text-14px text-white"})]),unchecked:r(()=>[o(i,{class:"text-14px text-white"})]),_:1},8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.followSystemTheme")},{default:r(()=>[o(d,{value:e(t).followSystemTheme,"onUpdate:value":e(t).setFollowSystemTheme},{checked:r(()=>[o(_,{class:"text-14px text-white"})]),unchecked:r(()=>[o(b,{class:"text-14px text-white"})]),_:1},8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.isCustomizeDarkModeTransition")},{default:r(()=>[o(d,{value:e(t).isCustomizeDarkModeTransition,"onUpdate:value":e(t).setIsCustomizeDarkModeTransition},{checked:r(()=>[o(_,{class:"text-14px text-white"})]),unchecked:r(()=>[o(b,{class:"text-14px text-white"})]),_:1},8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.sider.inverted")},{default:r(()=>[o(d,{value:e(t).sider.inverted,"onUpdate:value":e(t).setSiderInverted},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.header.inverted")},{default:r(()=>[o(d,{value:e(t).header.inverted,"onUpdate:value":e(t).setHeaderInverted},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.footer.inverted")},{default:r(()=>[o(d,{value:e(t).footer.inverted,"onUpdate:value":e(t).setFooterInverted},null,8,["value","onUpdate:value"])]),_:1},8,["label"])]),_:1})],64)}}}),ao=y({name:"LayoutCard",__name:"layout-card",props:{mode:{},label:{},checked:{type:Boolean}},setup(a){const t=a,l={vertical:{placement:"bottom-start",headerClass:"",menuClass:"w-1/3 h-full",mainClass:"w-2/3 h-3/4"},"vertical-mix":{placement:"bottom",headerClass:"",menuClass:"w-1/4 h-full",mainClass:"w-2/3 h-3/4"},horizontal:{placement:"bottom",headerClass:"",menuClass:"w-full h-1/4",mainClass:"w-full h-3/4"},"horizontal-mix":{placement:"bottom-end",headerClass:"",menuClass:"w-full h-1/4",mainClass:"w-2/3 h-3/4"}},s=T(()=>l[t.mode]);return(c,u)=>{const i=ke;return n(),m("div",{class:N(["border-2px rounded-6px cursor-pointer hover:border-primary",[c.checked?"border-primary":"border-transparent"]])},[o(i,{placement:s.value.placement,trigger:"hover"},{trigger:r(()=>[p("div",{class:N(["layout-card__shadow gap-6px w-96px h-64px p-6px rd-4px",[c.mode.includes("vertical")?"flex":"flex-col"]])},[Z(c.$slots,"default",{},void 0,!0)],2)]),default:r(()=>[p("span",null,D(c.label),1)]),_:3},8,["placement"])],2)}}}),so=K(ao,[["__scopeId","data-v-e2b400d3"]]),R=a=>(Ce("data-v-3f56b2fa"),a=a(),$e(),a),ro=R(()=>p("div",{class:"w-18px h-full bg-primary:50 rd-4px"},null,-1)),co=R(()=>p("div",{class:"flex-1 flex-col gap-6px"},[p("div",{class:"h-16px bg-primary rd-4px"}),p("div",{class:"flex-1 bg-primary:25 rd-4px"})],-1)),io=R(()=>p("div",{class:"w-8px h-full bg-primary:50 rd-4px"},null,-1)),uo=R(()=>p("div",{class:"w-16px h-full bg-primary:50 rd-4px"},null,-1)),_o=R(()=>p("div",{class:"flex-1 flex-col gap-6px"},[p("div",{class:"h-16px bg-primary rd-4px"}),p("div",{class:"flex-1 bg-primary:25 rd-4px"})],-1)),po=R(()=>p("div",{class:"h-16px bg-primary rd-4px"},null,-1)),mo=R(()=>p("div",{class:"flex-1 flex gap-6px"},[p("div",{class:"flex-1 bg-primary:25 rd-4px"})],-1)),ho=R(()=>p("div",{class:"h-16px bg-primary rd-4px"},null,-1)),bo=R(()=>p("div",{class:"flex-1 flex gap-6px"},[p("div",{class:"w-18px bg-primary:50 rd-4px"}),p("div",{class:"flex-1 bg-primary:25 rd-4px"})],-1)),fo=y({name:"LayoutMode",__name:"index",setup(a){const t=F();return(l,s)=>{const c=O,u=Y;return n(),m(S,null,[o(c,{"title-placement":"center"},{default:r(()=>[L(D(e(x)("layout.settingDrawer.layoutModelTitle")),1)]),_:1}),o(u,{justify:"space-around",wrap:!0,size:24,class:"px-12px"},{default:r(()=>[(n(!0),m(S,null,W(e(t).layout.modeList,i=>(n(),h(e(so),{key:i.value,mode:i.value,label:i.label,checked:i.value===e(t).layout.mode,onClick:d=>e(t).setLayoutMode(i.value)},{default:r(()=>[i.value==="vertical"?(n(),m(S,{key:0},[ro,co],64)):I("",!0),i.value==="vertical-mix"?(n(),m(S,{key:1},[io,uo,_o],64)):I("",!0),i.value==="horizontal"?(n(),m(S,{key:2},[po,mo],64)):I("",!0),i.value==="horizontal-mix"?(n(),m(S,{key:3},[ho,bo],64)):I("",!0)]),_:2},1032,["mode","label","checked","onClick"]))),128))]),_:1})],64)}}}),vo=K(fo,[["__scopeId","data-v-3f56b2fa"]]),xo={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},go=p("path",{fill:"currentColor",d:"M9 16.17L4.83 12l-1.42 1.41L9 19L21 7l-1.41-1.41L9 16.17z"},null,-1),yo=[go];function wo(a,t){return n(),m("svg",xo,yo)}const ko={name:"ic-outline-check",render:wo},Ne=y({name:"ColorCheckbox",__name:"color-checkbox",props:{color:{},checked:{type:Boolean},iconClass:{default:"text-14px"}},setup(a){const t=a,l=["#ffffff","#fff","rgb(255,255,255)"],s=T(()=>l.includes(t.color));return(c,u)=>{const i=ko;return n(),m("div",{class:"flex-center w-20px h-20px rounded-2px shadow cursor-pointer",style:H({backgroundColor:c.color})},[c.checked?(n(),h(i,{key:0,class:N([c.iconClass,s.value?"text-gray-700":"text-white"])},null,8,["class"])):I("",!0)],4)}}}),Co={class:"flex-x-center"},$o={class:"text-center"},Mo=y({name:"ColorModal",__name:"color-modal",props:{visible:{type:Boolean}},emits:["close"],setup(a,{emit:t}){const l=F();function s(){t("close")}return(c,u)=>{const i=Ye,d=Me,_=De,b=qe,w=Je,v=Qe;return n(),h(v,{show:c.visible,preset:"card",class:"w-640px h-480px","z-index":10001,onClose:s},{default:r(()=>[p("div",Co,[o(i,{type:"primary",size:24},{default:r(()=>[L("中国传统颜色")]),_:1})]),o(w,null,{default:r(()=>[(n(!0),m(S,null,W(e(Pe),f=>(n(),h(b,{key:f.label,name:f.label,tab:f.label},{default:r(()=>[o(_,{cols:8,"x-gap":16,"y-gap":8},{default:r(()=>[(n(!0),m(S,null,W(f.data,k=>(n(),h(d,{key:k.label},{default:r(()=>[o(Ne,{class:"!w-full !h-36px !rounded-4px",color:k.color,checked:k.color===e(l).themeColor,"icon-class":"text-20px",onClick:E=>e(l).setThemeColor(k.color)},null,8,["color","checked","onClick"]),p("p",$o,D(k.label),1)]),_:2},1024))),128))]),_:2},1024)]),_:2},1032,["name","tab"]))),128))]),_:1})]),_:1},8,["show"])}}}),Do=y({name:"ThemeColorSelect",__name:"index",setup(a){const t=F(),{bool:l,setTrue:s,setFalse:c}=ie(),u=T(()=>Ht(t.themeColor)),i=T(()=>u.value?"primary":"default");return(d,_)=>{const b=O,w=Me,v=De,f=et,k=le,E=Y;return n(),m(S,null,[o(b,{"title-placement":"center"},{default:r(()=>[L(D(e(x)("layout.settingDrawer.systemThemeTitle")),1)]),_:1}),o(v,{cols:8,"x-gap":8,"y-gap":12},{default:r(()=>[(n(!0),m(S,null,W(e(t).themeColorList,C=>(n(),h(w,{key:C,class:"flex-x-center"},{default:r(()=>[o(e(Ne),{color:C,checked:C===e(t).themeColor,onClick:$=>e(t).setThemeColor(C)},null,8,["color","checked","onClick"])]),_:2},1024))),128))]),_:1}),o(E,{vertical:!0,class:"pt-12px"},{default:r(()=>[o(f,{value:e(t).themeColor,"show-alpha":!1,onUpdateValue:e(t).setThemeColor},null,8,["value","onUpdateValue"]),o(k,{block:!0,type:i.value,onClick:e(s)},{default:r(()=>[L(D(e(x)("layout.settingDrawer.systemTheme.moreColors")),1)]),_:1},8,["type","onClick"])]),_:1}),o(e(Mo),{visible:e(l),onClose:e(c)},null,8,["visible","onClose"])],64)}}}),Fo=y({name:"PageFunc",__name:"index",setup(a){const t=F();return(l,s)=>{const c=O,u=ue,i=ce,d=tt,_=Y;return n(),m(S,null,[o(c,{"title-placement":"center"},{default:r(()=>[L(D(e(x)("layout.settingDrawer.pageFunctionsTitle")),1)]),_:1}),o(_,{vertical:"",size:"large"},{default:r(()=>[o(B,{label:e(x)("layout.settingDrawer.scrollMode")},{default:r(()=>[o(u,{class:"w-120px",size:"small",value:e(t).scrollMode,options:e(t).scrollModeList,"onUpdate:value":e(t).setScrollMode},null,8,["value","options","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.fixedHeaderAndTab")},{default:r(()=>[o(i,{value:e(t).fixedHeaderAndTab,"onUpdate:value":e(t).setIsFixedHeaderAndTab},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.menu.horizontalPosition")},{default:r(()=>[o(u,{class:"w-120px",size:"small",value:e(t).menu.horizontalPosition,options:e(t).menu.horizontalPositionList,"onUpdate:value":e(t).setHorizontalMenuPosition},null,8,["value","options","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.header.height")},{default:r(()=>[o(d,{class:"w-120px",size:"small",value:e(t).header.height,step:1,"onUpdate:value":e(t).setHeaderHeight},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.tab.height")},{default:r(()=>[o(d,{class:"w-120px",size:"small",value:e(t).tab.height,step:1,"onUpdate:value":e(t).setTabHeight},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.tab.isCache")},{default:r(()=>[o(i,{value:e(t).tab.isCache,"onUpdate:value":e(t).setTabIsCache},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.sider.width")},{default:r(()=>[o(d,{class:"w-120px",size:"small",value:e(t).sider.width,step:10,"onUpdate:value":e(t).setSiderWidth},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.sider.mixWidth")},{default:r(()=>[o(d,{class:"w-120px",size:"small",value:e(t).sider.mixWidth,step:5,"onUpdate:value":e(t).setMixSiderWidth},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.footer.visible")},{default:r(()=>[o(i,{value:e(t).footer.visible,"onUpdate:value":e(t).setFooterVisible},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.footer.fixed")},{default:r(()=>[o(i,{value:e(t).footer.fixed,"onUpdate:value":e(t).setFooterIsFixed},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.footer.right")},{default:r(()=>[o(i,{value:e(t).footer.right,"onUpdate:value":e(t).setFooterIsRight},null,8,["value","onUpdate:value"])]),_:1},8,["label"])]),_:1})],64)}}}),Bo=y({name:"PageView",__name:"index",setup(a){const t=F();return(l,s)=>{const c=O,u=ce,i=ue,d=Y;return n(),m(S,null,[o(c,{"title-placement":"center"},{default:r(()=>[L(D(e(x)("layout.settingDrawer.pageViewTitle")),1)]),_:1}),o(d,{vertical:"",size:"large"},{default:r(()=>[o(B,{label:e(x)("layout.settingDrawer.header.crumb.visible")},{default:r(()=>[o(u,{value:e(t).header.crumb.visible,"onUpdate:value":e(t).setHeaderCrumbVisible},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.header.crumb.icon")},{default:r(()=>[o(u,{value:e(t).header.crumb.showIcon,"onUpdate:value":e(t).setHeaderCrumbIconVisible},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.tab.visible")},{default:r(()=>[o(u,{value:e(t).tab.visible,"onUpdate:value":e(t).setTabVisible},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.tab.modeList.mode")},{default:r(()=>[o(i,{class:"w-120px",size:"small",value:e(t).tab.mode,options:e(t).tab.modeList,"onUpdate:value":e(t).setTabMode},null,8,["value","options","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.page.animate")},{default:r(()=>[o(u,{value:e(t).page.animate,"onUpdate:value":e(t).setPageIsAnimate},null,8,["value","onUpdate:value"])]),_:1},8,["label"]),o(B,{label:e(x)("layout.settingDrawer.page.animateMode")},{default:r(()=>[o(i,{class:"w-120px",size:"small",value:e(t).page.animateMode,options:e(t).page.animateModeList,"onUpdate:value":e(t).setPageAnimateMode},null,8,["value","options","onUpdate:value"])]),_:1},8,["label"])]),_:1})],64)}}}),To=y({name:"ThemeConfig",__name:"index",setup(a){const t=F(),l=U(),s=U(c());function c(){return JSON.stringify(t.$state)}function u(){var _;t.resetThemeStore(),(_=window.$message)==null||_.success(x("layout.settingDrawer.themeConfiguration.resetSuccess"))}function i(){if(!l.value)return;new Tt(l.value).on("success",()=>{var b;(b=window.$dialog)==null||b.success({title:x("layout.settingDrawer.themeConfiguration.operateSuccess"),content:x("layout.settingDrawer.themeConfiguration.copySuccess"),positiveText:x("layout.settingDrawer.themeConfiguration.confirmCopy")})})}const d=j(()=>t.$state,()=>{s.value=c()},{deep:!0});return de(()=>{i()}),ot(()=>{d()}),(_,b)=>{const w=O,v=le,f=Y;return n(),m(S,null,[o(w,{"title-placement":"center"},{default:r(()=>[L(D(e(x)("layout.settingDrawer.themeConfiguration.title")),1)]),_:1}),Fe(p("textarea",{id:"themeConfigCopyTarget","onUpdate:modelValue":b[0]||(b[0]=k=>s.value=k),class:"absolute opacity-0"},null,512),[[lt,s.value]]),o(f,{vertical:""},{default:r(()=>[p("div",{ref_key:"copyRef",ref:l,"data-clipboard-target":"#themeConfigCopyTarget"},[o(v,{type:"primary",block:!0},{default:r(()=>[L(D(e(x)("layout.settingDrawer.themeConfiguration.copy")),1)]),_:1})],512),o(v,{type:"warning",block:!0,onClick:u},{default:r(()=>[L(D(e(x)("layout.settingDrawer.themeConfiguration.reset")),1)]),_:1})]),_:1})],64)}}}),Eo=y({name:"SettingDrawer",__name:"index",setup(a){const t=V(),l={}.VITE_VERCEL==="Y";return(s,c)=>{const u=nt,i=at;return n(),m(S,null,[o(i,{show:e(t).settingDrawerVisible,"display-directive":"show",width:330,onMaskClick:e(t).closeSettingDrawer},{default:r(()=>[o(u,{title:e(x)("layout.settingDrawer.title"),"native-scrollbar":!1},{default:r(()=>[o(e(no)),o(e(vo)),o(e(Do)),o(e(Fo)),o(e(Bo)),e(l)?(n(),h(e(To),{key:0})):I("",!0)]),_:1},8,["title"])]),_:1},8,["show","onMaskClick"]),I("",!0)],64)}}}),q=y({name:"DarkModeContainer",__name:"dark-mode-container",props:{inverted:{type:Boolean,default:!1}},setup(a){return(t,l)=>(n(),m("div",{class:N(["dark:bg-dark dark:text-white dark:text-opacity-82 transition-all",t.inverted?"bg-#001428 text-white":"bg-white text-#333639"])},[Z(t.$slots,"default")],2))}}),fe=y({name:"GlobalLogo",__name:"index",props:{showTitle:{type:Boolean}},setup(a){const t=Be("root");return(l,s)=>{const c=Te,u=st("router-link");return n(),h(u,{to:e(t),class:"flex-center w-full nowrap-hidden"},{default:r(()=>[o(c,{"local-icon":"logo",class:"text-32px text-primary"}),Fe(p("h2",{class:"pl-8px text-16px font-bold text-primary transition duration-300 ease-in-out"},D(e(x)("system.title")),513),[[rt,l.showTitle]])]),_:1},8,["to"])}}}),Ao={key:0},J=y({name:"HoverContainer",__name:"hover-container",props:{tooltipContent:{default:""},placement:{default:"bottom"},contentClass:{default:""},inverted:{type:Boolean,default:!1}},setup(a){const t=a,l=T(()=>!!t.tooltipContent),s=T(()=>`${t.contentClass} ${t.inverted?"hover:bg-primary":"hover:bg-#f6f6f6"}`);return(c,u)=>{const i=ke;return l.value?(n(),m("div",Ao,[o(i,{placement:c.placement,trigger:"hover"},{trigger:r(()=>[p("div",{class:N(["flex-center h-full cursor-pointer dark:hover:bg-#333",s.value])},[Z(c.$slots,"default")],2)]),default:r(()=>[L(" "+D(c.tooltipContent),1)]),_:3},8,["placement"])])):(n(),m("div",{key:1,class:N(["flex-center cursor-pointer dark:hover:bg-#333",s.value])},[Z(c.$slots,"default")],2))}}}),So={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Uo=_e('<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2"><path stroke-dasharray="10" stroke-dashoffset="10" d="M7 9L4 12L7 15"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.6s" dur="0.2s" values="10;0"></animate></path><path stroke-dasharray="16" stroke-dashoffset="16" d="M19 5H5"><animate fill="freeze" attributeName="stroke-dashoffset" dur="0.2s" values="16;0"></animate></path><path stroke-dasharray="12" stroke-dashoffset="12" d="M19 12H10"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.2s" dur="0.2s" values="12;0"></animate></path><path stroke-dasharray="16" stroke-dashoffset="16" d="M19 19H5"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.4s" dur="0.2s" values="16;0"></animate></path></g>',1),zo=[Uo];function Lo(a,t){return n(),m("svg",So,zo)}const Io={name:"line-md-menu-fold-left",render:Lo},Po={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},No=_e('<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2"><path stroke-dasharray="10" stroke-dashoffset="10" d="M21 9L18 12L21 15"><animate fill="freeze" attributeName="stroke-dashoffset" dur="0.2s" values="10;0"></animate></path><path stroke-dasharray="16" stroke-dashoffset="16" d="M19 5H5"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.2s" dur="0.2s" values="16;0"></animate></path><path stroke-dasharray="12" stroke-dashoffset="12" d="M14 12H5"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.4s" dur="0.2s" values="12;0"></animate></path><path stroke-dasharray="16" stroke-dashoffset="16" d="M19 19H5"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.6s" dur="0.2s" values="16;0"></animate></path></g>',1),Ho=[No];function Vo(a,t){return n(),m("svg",Po,Ho)}const Ro={name:"line-md-menu-unfold-left",render:Vo},Wo=y({name:"MenuCollapse",__name:"menu-collapse",setup(a){const t=V(),l=F();return(s,c)=>{const u=Ro,i=Io,d=J;return n(),h(d,{class:"w-40px h-full",inverted:e(l).header.inverted,onClick:e(t).toggleSiderCollapse},{default:r(()=>[e(t).siderCollapse?(n(),h(u,{key:0,class:"text-16px"})):(n(),h(i,{key:1,class:"text-16px"}))]),_:1},8,["inverted","onClick"])}}}),jo=y({name:"GlobalBreadcrumb",__name:"global-breadcrumb",setup(a){const t=G(),l=F(),s=ee(),{routerPush:c}=te(),u=T(()=>It(t.name,s.menus,Be("root")).map(d=>{var _;return se(ae({},d),{label:d.i18nTitle?x(d.i18nTitle):d.label,options:(_=d.options)==null?void 0:_.map(b=>se(ae({},b),{label:b.i18nTitle?x(b.i18nTitle):b.label}))})}));function i(d){c({name:d})}return(d,_)=>{const b=pe,w=ct,v=it;return n(),h(v,{class:"px-12px"},{default:r(()=>[(n(!0),m(S,null,W(u.value,f=>(n(),h(w,{key:f.key},{default:r(()=>[f.hasChildren?(n(),h(b,{key:0,options:f.options,onSelect:i},{default:r(()=>[p("span",null,[e(l).header.crumb.showIcon?(n(),h(re(f.icon),{key:0,class:"inline-block align-text-bottom mr-4px text-16px"})):I("",!0),p("span",null,D(f.label),1)])]),_:2},1032,["options"])):(n(),m(S,{key:1},[e(l).header.crumb.showIcon?(n(),h(re(f.icon),{key:0,class:N(["inline-block align-text-bottom mr-4px text-16px",{"text-#BBBBBB":e(l).header.inverted}])},null,8,["class"])):I("",!0),p("span",{class:N({"text-#BBBBBB":e(l).header.inverted})},D(f.label),3)],64))]),_:2},1024))),128))]),_:1})}}}),Ko={class:"flex-1-hidden h-full px-10px"},Go=y({name:"HeaderMenu",__name:"header-menu",setup(a){const t=G(),l=ee(),s=F(),{routerPush:c}=te(),u=T(()=>me(l.menus)),i=T(()=>{var _;return(_=t.meta)!=null&&_.activeMenu?t.meta.activeMenu:t.name});function d(_,b){c(b.routePath)}return(_,b)=>{const w=he,v=ne;return n(),m("div",Ko,[o(v,{"x-scrollable":!0,class:"flex-1-hidden h-full","content-class":"h-full"},{default:r(()=>[p("div",{class:"flex-y-center h-full",style:H({justifyContent:e(s).menu.horizontalPosition})},[o(w,{value:i.value,mode:"horizontal",options:u.value,inverted:e(s).header.inverted,"onUpdate:value":d},null,8,["value","options","inverted"])],4)]),_:1})])}}}),Xo=K(Go,[["__scopeId","data-v-bd84ad3a"]]),Zo={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Oo=p("path",{fill:"currentColor",d:"M12 2A10 10 0 0 0 2 12c0 4.42 2.87 8.17 6.84 9.5c.5.08.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34c-.46-1.16-1.11-1.47-1.11-1.47c-.91-.62.07-.6.07-.6c1 .07 1.53 1.03 1.53 1.03c.87 1.52 2.34 1.07 2.91.83c.09-.65.35-1.09.63-1.34c-2.22-.25-4.55-1.11-4.55-4.92c0-1.11.38-2 1.03-2.71c-.1-.25-.45-1.29.1-2.64c0 0 .84-.27 2.75 1.02c.79-.22 1.65-.33 2.5-.33c.85 0 1.71.11 2.5.33c1.91-1.29 2.75-1.02 2.75-1.02c.55 1.35.2 2.39.1 2.64c.65.71 1.03 1.6 1.03 2.71c0 3.82-2.34 4.66-4.57 4.91c.***********.69 1.85V21c0 .***********.5C19.14 20.16 22 16.42 22 12A10 10 0 0 0 12 2Z"},null,-1),Yo=[Oo];function qo(a,t){return n(),m("svg",Zo,Yo)}const Jo={name:"mdi-github",render:qo},Qo=y({name:"GithubSite",__name:"github-site",setup(a){const t=F();function l(){window.open("https://github.com/Anmours/Bncr","_blank")}return(s,c)=>{const u=Jo,i=J;return n(),h(i,{"tooltip-content":"github",class:"w-40px h-full",inverted:e(t).header.inverted,onClick:l},{default:r(()=>[o(u,{class:"text-20px"})]),_:1},8,["inverted"])}}}),el=y({name:"ThemeMode",__name:"theme-mode",setup(a){const t=F();return(l,s)=>{const c=Bt,u=J;return n(),h(u,{class:"w-40px",inverted:e(t).header.inverted,"tooltip-content":"主题模式"},{default:r(()=>[o(c,{dark:e(t).darkMode,"customize-transition":e(t).isCustomizeDarkModeTransition,class:"wh-full","onUpdate:dark":e(t).setDarkMode},null,8,["dark","customize-transition","onUpdate:dark"])]),_:1},8,["inverted"])}}}),tl={class:"inline-block",viewBox:"0 0 48 48",width:"1em",height:"1em"},ol=["id"],ll=_e('<g fill="#555" stroke="#fff" stroke-width="4"><path stroke-linecap="round" stroke-linejoin="round" d="M13 42h22l6-21l-10 5l-7-14l-7 14l-10-5l6 21Z"></path><circle cx="7" cy="18" r="3"></circle><circle cx="24" cy="9" r="3"></circle><circle cx="41" cy="18" r="3"></circle></g>',1),nl=[ll],al=["mask"];function sl(a,t){return n(),m("svg",tl,[p("mask",{id:a.idMap.ipTCrownThree0},nl,8,ol),p("path",{fill:"currentColor",d:"M0 0h48v48H0z",mask:"url(#"+a.idMap.ipTCrownThree0+")"},null,8,al)])}const rl={name:"icon-park-twotone-crown-three",render:sl,data(){return{idMap:{ipTCrownThree0:"uicons-"+(()=>Math.random().toString(36).substr(2,10))()}}}},cl={class:"inline-block",viewBox:"0 0 12 12",width:"1em",height:"1em"},il=p("path",{fill:"currentColor",d:"M6 1a2 2 0 1 0 0 4a2 2 0 0 0 0-4Zm2.5 5h-5A1.5 1.5 0 0 0 2 7.5c0 1.116.459 2.01 1.212 2.615C3.953 10.71 4.947 11 6 11c1.053 0 2.047-.29 2.788-.885C9.54 9.51 10 8.616 10 7.5A1.5 1.5 0 0 0 8.5 6Z"},null,-1),ul=[il];function dl(a,t){return n(),m("svg",cl,ul)}const _l={name:"fluent-person12-filled",render:dl},pl={class:"pl-8px text-16px font-medium"},ml=y({name:"UserAvatar",__name:"user-avatar",setup(a){const t=ut(),l=F(),{iconRender:s}=Ee(),c=[{label:"退出登录",key:"logout",icon:s({icon:"carbon:logout"})}];function u(i){var _;i==="logout"&&((_=window.$dialog)==null||_.info({title:"提示",content:"您确定要退出登录吗？",positiveText:"确定",negativeText:"取消",onPositiveClick:()=>{t.resetAuthStore()}}))}return(i,d)=>{const _=_l,b=rl,w=J,v=pe;return n(),h(v,{options:c,onSelect:u},{default:r(()=>[o(w,{class:"px-12px",inverted:e(l).header.inverted},{default:r(()=>[o(_,{class:"text-20px"}),p("span",pl,D(e(t).userInfo.userName),1),e(t).userInfo.isDev?(n(),h(b,{key:0,class:"text-20px text-red",style:{"margin-left":"2px"}})):I("",!0)]),_:1},8,["inverted"])]),_:1})}}}),hl=y({name:"SettingButton",__name:"setting-button",setup(a){const t=V(),l=F();return(s,c)=>{const u=St,i=J;return n(),h(i,{class:"w-40px h-full","tooltip-content":"主题配置",inverted:e(l).header.inverted,onClick:e(t).toggleSettingDrawerVisible},{default:r(()=>[o(u,{class:"text-18px"})]),_:1},8,["inverted","onClick"])}}}),bl={key:1,class:"flex-1-hidden flex-y-center h-full"},fl={class:"flex justify-end h-full"},vl=!0,xl=y({name:"GlobalHeader",__name:"index",props:{showLogo:{},showHeaderMenu:{},showMenuCollapse:{}},setup(a){const t=F(),{isMobile:l}=Ae();return(s,c)=>{const u=q;return n(),h(u,{class:"global-header flex-y-center h-full",inverted:e(t).header.inverted},{default:r(()=>[s.showLogo?(n(),h(fe,{key:0,"show-title":!0,class:"h-full",style:H({width:e(t).sider.width+"px"})},null,8,["style"])):I("",!0),s.showHeaderMenu?(n(),h(e(Xo),{key:2})):(n(),m("div",bl,[s.showMenuCollapse||e(l)?(n(),h(e(Wo),{key:0})):I("",!0),e(t).header.crumb.visible&&!e(l)?(n(),h(e(jo),{key:1})):I("",!0)])),p("div",fl,[o(e(Qo)),o(e(el)),vl?(n(),h(e(hl),{key:0})):I("",!0),o(e(ml))])]),_:1},8,["inverted"])}}}),gl=K(xl,[["__scopeId","data-v-c06d0f0b"]]),yl=y({name:"BetterScroll",__name:"better-scroll",props:{options:{}},setup(a,{expose:t}){const l=a,s=U(),c=U(),u=U(),i=T(()=>!!l.options.scrollY);function d(){s.value&&(c.value=new Et(s.value,l.options))}const{width:_}=we(s),{width:b,height:w}=we(u);return j([()=>_.value,()=>b.value,()=>w.value],()=>{c.value&&c.value.refresh()}),de(()=>{d()}),t({instance:c}),(v,f)=>(n(),m("div",{ref_key:"bsWrap",ref:s,class:"h-full text-left"},[p("div",{ref_key:"bsContent",ref:u,class:N(["inline-block",{"h-full":!i.value}])},[Z(v.$slots,"default")],2)],512))}}),wl=y({name:"ContextMenu",__name:"context-menu",props:{visible:{type:Boolean,default:!1},currentPath:{default:""},affix:{type:Boolean},x:{},y:{}},emits:["update:visible"],setup(a,{emit:t}){const l=a,s=V(),c=be(),{iconRender:u}=Ee(),i=T({get(){return l.visible},set(v){t("update:visible",v)}});function d(){i.value=!1}const _=T(()=>[{label:"内容全屏",key:"full-content",icon:u({icon:"gridicons-fullscreen"})},{label:"重新加载",key:"reload-current",disabled:l.currentPath!==c.activeTab,icon:u({icon:"ant-design:reload-outlined"})},{label:"关闭",key:"close-current",disabled:l.currentPath===c.homeTab.fullPath||!!l.affix,icon:u({icon:"ant-design:close-outlined"})},{label:"关闭其他",key:"close-other",icon:u({icon:"ant-design:column-width-outlined"})},{label:"关闭左侧",key:"close-left",icon:u({icon:"mdi:format-horizontal-align-left"})},{label:"关闭右侧",key:"close-right",icon:u({icon:"mdi:format-horizontal-align-right"})},{label:"关闭所有",key:"close-all",icon:u({icon:"ant-design:line-outlined"})}]),b=new Map([["full-content",()=>{s.setContentFull(!0)}],["reload-current",()=>{s.reloadPage()}],["close-current",()=>{c.removeTab(l.currentPath)}],["close-other",()=>{c.clearTab([l.currentPath])}],["close-left",()=>{c.clearLeftTab(l.currentPath)}],["close-right",()=>{c.clearRightTab(l.currentPath)}],["close-all",()=>{c.clearAllTab()}]]);function w(v){const f=v,k=b.get(f);k&&k(),d()}return(v,f)=>{const k=pe;return n(),h(k,{show:i.value,options:_.value,placement:"bottom-start",x:v.x,y:v.y,onClickoutside:d,onSelect:w},null,8,["show","options","x","y"])}}}),kl=y({name:"TabDetail",__name:"index",emits:["scroll"],setup(a,{emit:t}){const l=F(),s=be(),c=T(()=>l.tab.mode==="chrome"),u=U();function i(){return oe(this,null,function*(){if(yield Ue(),u.value&&u.value.children.length&&u.value.children[s.activeTabIndex]){const f=u.value.children[s.activeTabIndex],{x:k,width:E}=f.getBoundingClientRect(),C=k+E/2;setTimeout(()=>{t("scroll",C)},50)}})}const d=Se({visible:!1,affix:!1,x:0,y:0,currentPath:""});function _(f){Object.assign(d,f)}let b=!1;function w(f){b||_({visible:f})}function v(f,k,E){return oe(this,null,function*(){f.preventDefault();const{clientX:C,clientY:$}=f;b=!0;const A=d.visible?150:0;_({visible:!1}),setTimeout(()=>{_({visible:!0,x:C,y:$,currentPath:k,affix:E}),b=!1},A)})}return j(()=>s.activeTabIndex,()=>{i()},{immediate:!0}),(f,k)=>{const E=Te;return n(),m(S,null,[p("div",{ref_key:"tabRef",ref:u,class:N(["flex h-full pr-18px",[c.value?"items-end":"items-center gap-12px"]])},[(n(!0),m(S,null,W(e(s).tabs,C=>(n(),h(e(dt),{key:C.fullPath,mode:e(l).tab.mode,"dark-mode":e(l).darkMode,active:e(s).activeTab===C.fullPath,"active-color":e(l).themeColor,closable:!(C.name===e(s).homeTab.name||C.meta.affix),onClick:$=>e(s).handleClickTab(C.fullPath),onClose:$=>e(s).removeTab(C.fullPath),onContextmenu:$=>v($,C.fullPath,C.meta.affix)},{prefix:r(()=>[o(E,{icon:C.meta.icon,"local-icon":C.meta.localIcon,class:"inline-block align-text-bottom text-16px"},null,8,["icon","local-icon"])]),default:r(()=>[L(" "+D(C.meta.i18nTitle?e(x)(C.meta.i18nTitle):C.meta.title),1)]),_:2},1032,["mode","dark-mode","active","active-color","closable","onClick","onClose","onContextmenu"]))),128))],2),o(e(wl),{visible:d.visible,"current-path":d.currentPath,affix:d.affix,x:d.x,y:d.y,"onUpdate:visible":w},null,8,["visible","current-path","affix","x","y"])],64)}}}),Cl=y({name:"ReloadButton",__name:"index",setup(a){const{reCacheRoute:t}=ee(),l=G(),{loading:s,startLoading:c,endLoading:u}=zt();function i(){return oe(this,null,function*(){c(),yield t(l.name),setTimeout(()=>{u()},1e3)})}return(d,_)=>{const b=Ut,w=J;return n(),h(w,{class:"w-64px h-full","tooltip-content":"重新加载",placement:"bottom-end",onClick:i},{default:r(()=>[o(b,{class:N(["text-22px",{"animate-spin":e(s)}])},null,8,["class"])]),_:1})}}}),$l=y({name:"GlobalTab",__name:"index",setup(a){const t=G(),l=F(),s=be(),c=_t(),u=U(),{width:i,left:d}=pt(u),_=U(),b=!!c.device.type;function w(f){var C;const E=f-d.value-i.value/2;if(_.value){const{maxScrollX:$,x:A}=_.value.instance,g=$-A,M=E>0?Math.max(-E,g):Math.min(-E,-A);(C=_.value)==null||C.instance.scrollBy(M,0,300)}}function v(){s.iniTabStore(t)}return j(()=>t.fullPath,()=>{s.addTab(t),s.setActiveTab(t.fullPath)}),v(),(f,k)=>{const E=yl,C=q;return n(),h(C,{class:"global-tab flex-y-center w-full pl-16px",style:H({height:e(l).tab.height+"px"})},{default:r(()=>[p("div",{ref_key:"bsWrapper",ref:u,class:"flex-1-hidden h-full"},[o(E,{ref_key:"bsScroll",ref:_,options:{scrollX:!0,scrollY:!1,click:e(b)}},{default:r(()=>[o(e(kl),{onScroll:w})]),_:1},8,["options"])],512),o(e(Cl))]),_:1},8,["style"])}}}),Ml=K($l,[["__scopeId","data-v-2aed69b2"]]),Dl=y({name:"VerticalMenu",__name:"vertical-menu",setup(a){const t=G(),l=V(),s=F(),c=ee(),{routerPush:u}=te(),i=T(()=>me(c.menus)),d=T(()=>{var v;return(v=t.meta)!=null&&v.activeMenu?t.meta.activeMenu:t.name}),_=U([]);function b(v,f){u(f.routePath)}function w(v){_.value=v}return j(()=>t.name,()=>{_.value=ze(d.value,i.value)},{immediate:!0}),(v,f)=>{const k=he,E=ne;return n(),h(E,{class:"flex-1-hidden"},{default:r(()=>[o(k,{value:d.value,collapsed:e(l).siderCollapse,"collapsed-width":e(s).sider.collapsedWidth,"collapsed-icon-size":22,options:i.value,"expanded-keys":_.value,indent:18,inverted:!e(s).darkMode&&e(s).sider.inverted,"onUpdate:value":b,"onUpdate:expandedKeys":w},null,8,["value","collapsed","collapsed-width","options","expanded-keys","inverted"])]),_:1})}}}),Fl=y({name:"VerticalSider",__name:"index",setup(a){const t=V(),l=F(),s=T(()=>l.layout.mode==="horizontal-mix"),c=T(()=>!t.siderCollapse&&l.layout.mode!=="vertical-mix");return(u,i)=>{const d=q;return n(),h(d,{class:"flex-col-stretch h-full",inverted:e(l).sider.inverted},{default:r(()=>[s.value?I("",!0):(n(),h(e(fe),{key:0,"show-title":c.value,style:H({height:e(l).header.height+"px"})},null,8,["show-title","style"])),o(e(Dl))]),_:1},8,["inverted"])}}}),Bl=y({name:"MixMenuDetail",__name:"mix-menu-detail",props:{routeName:{},label:{},activeRouteName:{},icon:{type:[Object,Function],default:void 0},isMini:{type:Boolean,default:!1}},setup(a){const t=a,{bool:l,setTrue:s,setFalse:c}=ie(),u=T(()=>t.routeName===t.activeRouteName);return(i,d)=>(n(),m("div",{class:"mb-6px px-4px cursor-pointer",onMouseenter:d[0]||(d[0]=(..._)=>e(s)&&e(s)(..._)),onMouseleave:d[1]||(d[1]=(..._)=>e(c)&&e(c)(..._))},[p("div",{class:N(["flex-center flex-col py-12px rounded-2px bg-transparent transition-colors duration-300 ease-in-out",{"text-primary !bg-primary_active":u.value,"text-primary":e(l)}])},[(n(),h(re(i.icon),{class:N([i.isMini?"text-16px":"text-20px"])},null,8,["class"])),p("p",{class:N(["w-full text-center ellipsis-text text-12px transition-height duration-300 ease-in-out",[i.isMini?"h-0 pt-0":"h-24px pt-4px"]])},D(i.label),3)],2)],32))}}),Tl={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},El=p("path",{fill:"currentColor",d:"M16 12V4h1V2H7v2h1v8l-2 2v2h5.2v6h1.6v-6H18v-2l-2-2Z"},null,-1),Al=[El];function Sl(a,t){return n(),m("svg",Tl,Al)}const Ul={name:"mdi-pin",render:Sl},zl={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Ll=p("path",{fill:"currentColor",d:"M2 5.27L3.28 4L20 20.72L18.73 22l-5.93-5.93V22h-1.6v-6H6v-2l2-2v-.73l-6-6M16 12l2 2v2h-.18L8 6.18V4H7V2h10v2h-1v8Z"},null,-1),Il=[Ll];function Pl(a,t){return n(),m("svg",zl,Il)}const Nl={name:"mdi-pin-off",render:Pl},Hl={class:"text-primary pl-8px text-16px font-bold"},Vl=y({name:"MixMenuDrawer",__name:"mix-menu-drawer",props:{visible:{type:Boolean},menus:{}},setup(a){const t=a,l=G(),s=V(),c=F(),{routerPush:u}=te(),i=T(()=>t.visible&&t.menus.length||s.mixSiderFixed),d=T(()=>{var v;return(v=l.meta)!=null&&v.activeMenu?l.meta.activeMenu:l.name}),_=U([]);function b(v,f){u(f.routePath)}function w(v){_.value=v}return j(()=>l.name,()=>{_.value=ze(d.value,t.menus)},{immediate:!0}),(v,f)=>{const k=Nl,E=Ul,C=he,$=ne,A=q;return n(),m("div",{class:"relative h-full transition-width duration-300 ease-in-out",style:H({width:e(s).mixSiderFixed?e(c).sider.mixChildMenuWidth+"px":"0px"})},[o(A,{class:"drawer-shadow absolute-lt flex-col-stretch h-full nowrap-hidden",inverted:e(c).sider.inverted,style:H({width:i.value?e(c).sider.mixChildMenuWidth+"px":"0px"})},{default:r(()=>[p("header",{class:"header-height flex-y-center justify-between",style:H({height:e(c).header.height+"px"})},[p("h2",Hl,D(e(x)("system.title")),1),p("div",{class:"px-8px text-16px text-gray-600 cursor-pointer",onClick:f[0]||(f[0]=(...g)=>e(s).toggleMixSiderFixed&&e(s).toggleMixSiderFixed(...g))},[e(s).mixSiderFixed?(n(),h(k,{key:0})):(n(),h(E,{key:1}))])],4),o($,{class:"flex-1-hidden"},{default:r(()=>[o(C,{value:d.value,options:v.menus,"expanded-keys":_.value,indent:18,inverted:!e(c).darkMode&&e(c).sider.inverted,"onUpdate:value":b,"onUpdate:expandedKeys":w},null,8,["value","options","expanded-keys","inverted"])]),_:1})]),_:1},8,["inverted","style"])],4)}}}),Rl=K(Vl,[["__scopeId","data-v-5c7ea91b"]]),Wl={class:"inline-block",viewBox:"0 0 256 256",width:"1em",height:"1em"},jl=p("path",{fill:"currentColor",d:"M208.49 199.51a12 12 0 0 1-17 17l-80-80a12 12 0 0 1 0-17l80-80a12 12 0 0 1 17 17L137 128ZM57 128l71.52-71.51a12 12 0 0 0-17-17l-80 80a12 12 0 0 0 0 17l80 80a12 12 0 0 0 17-17Z"},null,-1),Kl=[jl];function Gl(a,t){return n(),m("svg",Wl,Kl)}const Xl={name:"ph-caret-double-left-bold",render:Gl},Zl={class:"inline-block",viewBox:"0 0 256 256",width:"1em",height:"1em"},Ol=p("path",{fill:"currentColor",d:"m144.49 136.49l-80 80a12 12 0 0 1-17-17L119 128L47.51 56.49a12 12 0 0 1 17-17l80 80a12 12 0 0 1-.02 17Zm80-17l-80-80a12 12 0 1 0-17 17L199 128l-71.52 71.51a12 12 0 0 0 17 17l80-80a12 12 0 0 0 .01-17Z"},null,-1),Yl=[Ol];function ql(a,t){return n(),m("svg",Zl,Yl)}const Jl={name:"ph-caret-double-right-bold",render:ql},Ql=y({name:"MixMenuCollapse",__name:"mix-menu-collapse",setup(a){const t=V();return(l,s)=>{const c=Jl,u=Xl,i=le;return n(),h(i,{text:!0,class:"h-36px",onClick:e(t).toggleSiderCollapse},{default:r(()=>[e(t).siderCollapse?(n(),h(c,{key:0,class:"text-16px"})):(n(),h(u,{key:1,class:"text-16px"}))]),_:1},8,["onClick"])}}}),en={class:"flex-1-hidden flex-col-stretch h-full"},tn=y({name:"VerticalMixSider",__name:"index",setup(a){const t=G(),l=V(),s=F(),c=ee(),{routerPush:u}=te(),{bool:i,setTrue:d,setFalse:_}=ie(),b=U("");function w($){b.value=$}const v=T(()=>c.menus.map($=>{const{routeName:A,label:g,i18nTitle:M}=$,P=$==null?void 0:$.icon,X=!!($.children&&$.children.length);return{routeName:A,label:M?x(M):g,icon:P,hasChildren:X}}));function f(){v.value.some($=>{var M;const A=(M=t.meta)!=null&&M.activeMenu?t.meta.activeMenu:t.name,g=A==null?void 0:A.includes($.routeName);return g&&w($.routeName),g})}function k($,A){w($),A?d():u({name:$})}function E(){f(),_()}const C=T(()=>{const $=[];return c.menus.some(A=>{var M;const g=A.routeName===b.value&&!!((M=A.children)!=null&&M.length);return g&&$.push(...me(A.children||[])),g}),$});return j(()=>t.name,()=>{f()},{immediate:!0}),($,A)=>{const g=ne,M=q;return n(),h(M,{class:"flex h-full",inverted:e(s).sider.inverted,onMouseleave:E},{default:r(()=>[p("div",en,[o(e(fe),{"show-title":!1,style:H({height:e(s).header.height+"px"})},null,8,["style"]),o(g,{class:"flex-1-hidden"},{default:r(()=>[(n(!0),m(S,null,W(v.value,P=>(n(),h(e(Bl),{key:P.routeName,"route-name":P.routeName,"active-route-name":b.value,label:P.label,icon:P.icon,"is-mini":e(l).siderCollapse,onClick:X=>k(P.routeName,P.hasChildren)},null,8,["route-name","active-route-name","label","icon","is-mini","onClick"]))),128))]),_:1}),o(e(Ql))]),o(e(Rl),{visible:e(i),menus:C.value},null,8,["visible","menus"])]),_:1},8,["inverted"])}}}),on=y({name:"GlobalSider",__name:"index",setup(a){const t=F(),l=T(()=>t.layout.mode==="vertical-mix");return(s,c)=>l.value?(n(),h(e(tn),{key:0,class:"global-sider"})):(n(),h(e(Fl),{key:1,class:"global-sider"}))}}),ln=K(on,[["__scopeId","data-v-afe6955b"]]),nn=p("span",null,"Copyright ©2023 Anmour",-1),an=y({name:"GlobalFooter",__name:"index",setup(a){const t=F();return(l,s)=>{const c=q;return n(),h(c,{class:"flex-center h-full",inverted:e(t).footer.inverted},{default:r(()=>[nn]),_:1},8,["inverted"])}}}),sn={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},rn=p("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 3.223a9.003 9.003 0 0 0-5.605 13.592L3 21l4.185-1.395A9.003 9.003 0 0 0 20.777 14m0-4A9.013 9.013 0 0 0 14 3.223M17 12a5 5 0 0 0-5-5m1 5a1 1 0 0 0-1-1"},null,-1),cn=[rn];function un(a,t){return n(),m("svg",sn,cn)}const dn={name:"majesticons-chat-signal-line",render:un},_n=a=>(Ce("data-v-5f06efeb"),a=a(),$e(),a),pn={key:0,style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"100%",height:"100%","background-color":"rgba(151, 151, 151, 0.5)","backdrop-filter":"blur(3px)",display:"flex","align-items":"center","justify-content":"center","z-index":"100","border-radius":"3px"}},mn=_n(()=>p("p",{class:"text-19px text-primary"},"已与后端断开链接~",-1)),hn=[mn],bn={class:"name"},fn={key:0},vn=["src"],xn={key:1,style:{"justify-self":"end"}},gn={key:1,style:{"text-align":"center"}},yn={style:{display:"flex","margin-top":"15px"}},wn=y({__name:"index",setup(a){const t=ft.isMobile,l=mt(),s=U(!1),c=U([]),u=U(500),i=U([]),d=U(""),_=[{name:"管理员",id:300001},{name:"用户",id:300002}];for(const g of _)c.value.push({label:g.name,value:g.name});de(()=>{});const b=U(null),w=()=>{},v=Se({user:_[0]}),f=Lt.Instance;f.registerCallBack("message",g=>{i.value.push(g)});const k=U(!0),E=g=>{g?(k.value=!0,_.forEach((M,P)=>{(M==null?void 0:M.name)===g&&(v.user=M)})):k.value=!1},C=g=>{if(g!=null&&g.shiftKey&&(g==null?void 0:g.keyCode)===13){g.stopPropagation(),g.preventDefault(),d.value=d.value+`
`;return}if(g.stopPropagation(),g.preventDefault(),!!d.value){if(!f.connected.value||!f.ws){l.error("发送消息失败: ws链接处于关闭状态,请检查"),console.error("发送消息失败: ws链接处于关闭状态,请检查");return}f.send({type:"message",sender:{user_id:v.user.id,nickname:v.user.name},messageId:"123412333",message:d.value}),i.value.push({user:v.user,message:d.value,createTime:Date.now()}),d.value=""}},$=U();j(()=>i.value,g=>{Ue(()=>{var M,P;(M=$.value)!=null&&M.length&&((P=$.value[g.length-1])==null||P.scrollIntoView())})},{deep:!0});const A=U();return ht(A,g=>{const M=g[0],{width:P,height:X}=M.contentRect;u.value=X}),(g,M)=>{const P=Kt,X=dn,ve=le,Q=vt,He=xt,xe=gt,Ve=ue,Re=yt,We=wt;return n(),m("div",null,[o(We,{trigger:"click",placement:"top-end",show:s.value,displayDirective:"show",raw:!0,style:{"border-radius":"10px"}},{trigger:r(()=>[o(ve,{class:"fixed bottom-5% right-5% w-42px h-42px z-90000 !p-0 transition-all duration-1000",round:!0,onClick:M[0]||(M[0]=z=>s.value=!s.value)},{default:r(()=>[s.value?(n(),h(P,{key:0,class:"text-24px"})):(n(),h(X,{key:1,class:"text-24px"}))]),_:1})]),default:r(()=>[o(xe,{ref_key:"NcardDom",ref:A,title:"Web交互聊天窗",bordered:!1,class:"rounded-8px shadow-sm",style:H({height:"calc(100vh - 200px)",width:e(t)?"calc(100vw - 40px)":"500px",padding:"2px",position:"relative"})},{default:r(()=>[e(f).connected.value?I("",!0):(n(),m("div",pn,hn)),o(xe,{bordered:!0,embedded:"",class:"rounded-8px shadow-sm bg-primary",style:H({height:u.value-140+"px","background-color":"",overflow:"auto"}),raw:!0},{default:r(()=>[p("div",null,[i.value&&i.value.length?(n(!0),m(S,{key:0},W(i.value,z=>(n(),m("div",{class:N(["message-box",{"right-message":[300001,300002].includes(z.user.id)}]),key:z.createTime,ref_for:!0,ref_key:"scrollContainer",ref:$},[p("div",bn,D(z.user.name),1),[3e5].includes(z.user.id)?(n(),m("div",fn,[z.msgType==="text"?(n(),h(Q,{key:0,round:"",type:"primary",class:"chatMsg-text"},{default:r(()=>[L(D(z.message),1)]),_:2},1024)):z.msgType==="image"?(n(),h(Q,{key:1,round:"",type:"primary",class:"chatMsg-text"},{default:r(()=>[p("p",null,D(z.message),1),o(He,{style:{"margin-top":"5px"},src:z.path},null,8,["src"])]),_:2},1024)):z.msgType==="video"?(n(),h(Q,{key:2,round:"",type:"primary",class:"chatMsg-text"},{default:r(()=>[p("p",null,D(z.message),1),p("video",{ref_for:!0,ref_key:"videoPlayer",ref:b,src:z.path,style:{"margin-top":"5px",width:"200px"},controls:"",onFullscreenchange:w},null,40,vn)]),_:2},1024)):(n(),h(Q,{key:3,round:"",type:"primary",class:"chatMsg-text"},{default:r(()=>[L(" 未知消息类型 ")]),_:1}))])):(n(),m("div",xn,[o(Q,{round:"",style:{"max-width":"auto","text-align":"right",padding:"10px","word-wrap":"break-word","overflow-wrap":"break-word","white-space":"normal",height:"auto",overflow:"hidden"}},{default:r(()=>[L(D(z.message),1)]),_:2},1024)]))],2))),128)):(n(),m("div",gn," 没有消息 "))])]),_:1},8,["style"]),p("div",yn,[o(Ve,{style:{width:"150px","margin-right":"3px"},clearable:"",options:c.value,"value-key":"id","onUpdate:value":E,placeholder:"选择身份","default-value":c.value[0].label},null,8,["options","default-value"]),o(Re,{autosize:{minRows:1,maxRows:2},round:!0,"show-count":"",value:d.value,"onUpdate:value":M[1]||(M[1]=z=>d.value=z),type:"textarea",placeholder:k.value?"请输入要发送的内容":"请先选择身份",disabled:!k.value,onKeydown:bt(C,["enter"])},null,8,["value","placeholder","disabled","onKeydown"]),o(ve,{style:{"margin-left":"3px"},type:"primary",disabled:!d.value,onClick:C},{default:r(()=>[L("发送")]),_:1},8,["disabled"])])]),_:1},8,["style"])]),_:1},8,["show"])])}}}),kn=K(wn,[["__scopeId","data-v-5f06efeb"]]),Sn=y({name:"BasicLayout",__name:"index",setup(a){const t=V(),l=F(),{mode:s,isMobile:c,headerProps:u,siderVisible:i,siderWidth:d,siderCollapsedWidth:_}=Ae();return(b,w)=>{const v=Mt;return n(),m(S,null,[o(e($t),{mode:e(s),"is-mobile":e(c),"scroll-mode":e(l).scrollMode,"scroll-el-id":e(t).scrollElId,"full-content":e(t).contentFull,"fixed-top":e(l).fixedHeaderAndTab,"header-height":e(l).header.height,"tab-visible":e(l).tab.visible,"tab-height":e(l).tab.height,"content-class":e(t).disableMainXScroll?"overflow-x-hidden":"","sider-visible":e(i),"sider-collapse":e(t).siderCollapse,"sider-width":e(d),"sider-collapsed-width":e(_),"footer-visible":e(l).footer.visible,"fixed-footer":e(l).footer.fixed,"right-footer":e(l).footer.right,"footer-height":35,onClickMobileSiderMask:w[0]||(w[0]=f=>e(t).setSiderCollapse(!0))},{header:r(()=>[o(e(gl),kt(Ct(e(u))),null,16)]),tab:r(()=>[o(e(Ml))]),sider:r(()=>[o(e(ln))]),footer:r(()=>[o(e(an))]),default:r(()=>[o(e(At))]),_:1},8,["mode","is-mobile","scroll-mode","scroll-el-id","full-content","fixed-top","header-height","tab-visible","tab-height","content-class","sider-visible","sider-collapse","sider-width","sider-collapsed-width","footer-visible","fixed-footer","right-footer"]),(n(),h(v,{key:e(l).scrollMode,"listen-to":`#${e(t).scrollElId}`,class:"z-100",right:"5%",bottom:"12%"},null,8,["listen-to"])),o(e(Eo)),o(e(kn))],64)}}});export{Sn as default};
