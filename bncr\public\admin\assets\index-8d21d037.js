var B=(s,i,o)=>new Promise((m,Y)=>{var r=F=>{try{Z(o.next(F))}catch(O){Y(O)}},U=F=>{try{Z(o.throw(F))}catch(O){Y(O)}},Z=F=>F.done?m(F.value):Promise.resolve(F.value).then(r,U);Z((o=o.apply(s,i)).next())});import{o as a,c as y,a as c,Y as Tt,p as d,w as n,e,be as v,d as Bt,bk as Vt,ak as Ht,bl as Zt,K as b,al as Dt,M as It,aT as St,h as t,j as M,H as q,f as k,b as g,N,az as De,q as A,x as Ie,bg as Se,F as Le,an as Lt,$ as Nt,b2 as h,bm as Cn,a3 as Ut,J as Pt,as as jt,bn as Rt,bo as Et,ar as Ft,i as At,bp as Ot,a8 as Qt,bq as Wt,aw as Jt,br as Yt,bs as Kt,bt as Xt,bu as Gt,aq as el,D as nl,bv as tl,bw as ll,bx as ol,by as al,aI as sl,z as il,bz as cl,bA as rl,A as ul,aK as _l,ao as dl,ba as pl,bh as fl,s as ml,v as vl}from"./index-b380aaed.js";import{_ as hl,a as gl,b as bl,c as yl,d as $l,e as xl,f as kl,g as wl,h as Cl,i as zl,j as Ml,k as ql,l as Tl,m as Bl,n as Vl,o as Hl,p as Zl,q as Dl,r as Il,s as Sl,t as Ll,u as Nl,v as Ul,w as Pl,x as jl,y as Rl,z as El,A as Fl,B as Al}from"./file-csv-0a1956f1.js";import{_ as Ol,a as Ql,b as Wl}from"./index.vue_vue_type_script_setup_true_lang-1b25d6eb.js";import{_ as Jl}from"./search2-line-b583daf2.js";import{_ as Yl}from"./content-save-check-58cc70d2.js";import{_ as $}from"./_plugin-vue_export-helper-c27b6911.js";import{g as Kl,f as Xl,c as zn,s as Gl,e as eo,h as Mn,i as no,j as qn,k as to,l as lo}from"./file-b561a0a1.js";const oo={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},ao=c("path",{fill:"currentColor",d:"M6.5 20q-2.28 0-3.89-1.57Q1 16.85 1 14.58q0-1.95 1.17-3.48q1.18-1.53 3.08-1.95q.63-2.3 2.5-3.72Q9.63 4 12 4q2.93 0 4.96 2.04Q19 8.07 19 11q1.73.2 2.86 1.5q1.14 1.28 1.14 3q0 1.88-1.31 3.19T18.5 20H13q-.82 0-1.41-.59Q11 18.83 11 18v-5.15L9.4 14.4L8 13l4-4l4 4l-1.4 1.4l-1.6-1.55V18h5.5q1.05 0 1.77-.73q.73-.72.73-1.77t-.73-1.77Q19.55 13 18.5 13H17v-2q0-2.07-1.46-3.54Q14.08 6 12 6Q9.93 6 8.46 7.46Q7 8.93 7 11h-.5q-1.45 0-2.47 1.03Q3 13.05 3 14.5q0 1.45 1.03 2.5q1.02 1 2.47 1H9v2m3-7Z"},null,-1),so=[ao];function io(s,i){return a(),y("svg",oo,so)}const co={name:"mdi-cloud-upload-outline",render:io},ro={class:"inline-block",viewBox:"0 0 256 256",width:"1em",height:"1em"},uo=c("path",{fill:"currentColor",d:"M228 152v56a20 20 0 0 1-20 20H48a20 20 0 0 1-20-20v-56a12 12 0 0 1 24 0v52h152v-52a12 12 0 0 1 24 0Zm-108.49 8.49a12 12 0 0 0 17 0l40-40a12 12 0 0 0-17-17L140 123V40a12 12 0 0 0-24 0v83l-19.51-19.49a12 12 0 0 0-17 17Z"},null,-1),_o=[uo];function po(s,i){return a(),y("svg",ro,_o)}const fo={name:"ph-download-simple-bold",render:po},mo={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},vo=Tt('<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2"><path stroke-dasharray="2 4" stroke-dashoffset="6" d="M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21"><animate attributeName="stroke-dashoffset" dur="0.6s" repeatCount="indefinite" values="6;0"></animate></path><path stroke-dasharray="30" stroke-dashoffset="30" d="M12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.1s" dur="0.3s" values="30;0"></animate></path><path stroke-dasharray="10" stroke-dashoffset="10" d="M12 8v7.5"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.5s" dur="0.2s" values="10;0"></animate></path><path stroke-dasharray="6" stroke-dashoffset="6" d="M12 15.5l3.5 -3.5M12 15.5l-3.5 -3.5"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.7s" dur="0.2s" values="6;0"></animate></path></g>',1),ho=[vo];function go(s,i){return a(),y("svg",mo,ho)}const bo={name:"line-md-downloading-loop",render:go},yo={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},$o=c("path",{fill:"currentColor",d:"m14 2l6 6v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h8m4 18V9h-5V4H6v16h12m-6-1l-4-4h2.5v-3h3v3H16l-4 4Z"},null,-1),xo=[$o];function ko(s,i){return a(),y("svg",yo,xo)}const wo={name:"mdi-file-download-outline",render:ko},Co={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},zo=c("path",{fill:"currentColor",d:"M20 18H4V8h16m0-2h-8l-2-2H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2m-6 3h2v4h3l-4 4l-4-4h3Z"},null,-1),Mo=[zo];function qo(s,i){return a(),y("svg",Co,Mo)}const To={name:"mdi-folder-download-outline",render:qo},Bo={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Vo=c("path",{fill:"currentColor",d:"M20.005 5.995h-1v2h1v8h-1v2h1c1.103 0 2-.897 2-2v-8c0-1.102-.898-2-2-2zm-14 4H15v4H6.005z"},null,-1),Ho=c("path",{fill:"currentColor",d:"M17.005 17.995V4H20V2h-8v2h3.005v1.995h-11c-1.103 0-2 .897-2 2v8c0 1.103.897 2 2 2h11V20H12v2h8v-2h-2.995v-2.005zm-13-2v-8h11v8h-11z"},null,-1),Zo=[Vo,Ho];function Do(s,i){return a(),y("svg",Bo,Zo)}const Io={name:"bx-rename",render:Do},So={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Lo=c("path",{fill:"currentColor",d:"M4 20q-.825 0-1.413-.588T2 18V6q0-.825.588-1.413T4 4h5.175q.4 0 .763.15t.637.425L12 6h8q.825 0 1.413.588T22 8v10q0 .825-.588 1.413T20 20H4Zm0-2h16V8h-8.825l-2-2H4v12Zm0 0V6v12Zm8.2-4l-.925.925q-.275.275-.275.7t.275.7q.275.275.7.275t.7-.275L15.3 13.7q.3-.3.3-.7t-.3-.7l-2.625-2.625q-.275-.275-.7-.275t-.7.275q-.275.275-.275.7t.275.7L12.2 12H9q-.425 0-.713.288T8 13q0 .425.288.713T9 14h3.2Z"},null,-1),No=[Lo];function Uo(s,i){return a(),y("svg",So,No)}const Po={name:"material-symbols-drive-file-move-outline-rounded",render:Uo},jo={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Ro=c("path",{fill:"currentColor",d:"M5 3c-1.11 0-2 .89-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7h-2v7H5V5h7V3H5m12.78 1a.69.69 0 0 0-.48.2l-1.22 1.21l2.5 2.5L19.8 6.7c.26-.26.26-.7 0-.95L18.25 4.2c-.13-.13-.3-.2-.47-.2m-2.41 2.12L8 13.5V16h2.5l7.37-7.38l-2.5-2.5Z"},null,-1),Eo=[Ro];function Fo(s,i){return a(),y("svg",jo,Eo)}const Ao={name:"mdi-square-edit-outline",render:Fo},Oo={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Qo=c("path",{fill:"currentColor",d:"M12 17q.425 0 .713-.288T13 16v-4q0-.425-.288-.713T12 11q-.425 0-.713.288T11 12v4q0 .425.288.713T12 17Zm0-8q.425 0 .713-.288T13 8q0-.425-.288-.713T12 7q-.425 0-.713.288T11 8q0 .425.288.713T12 9Zm0 13q-2.075 0-3.9-.788t-3.175-2.137q-1.35-1.35-2.137-3.175T2 12q0-2.075.788-3.9t2.137-3.175q1.35-1.35 3.175-2.137T12 2q2.075 0 3.9.788t3.175 2.137q1.35 1.35 2.138 3.175T22 12q0 2.075-.788 3.9t-2.137 3.175q-1.35 1.35-3.175 2.138T12 22Zm0-2q3.35 0 5.675-2.325T20 12q0-3.35-2.325-5.675T12 4Q8.65 4 6.325 6.325T4 12q0 3.35 2.325 5.675T12 20Zm0-8Z"},null,-1),Wo=[Qo];function Jo(s,i){return a(),y("svg",Oo,Wo)}const Yo={name:"material-symbols-info-outline-rounded",render:Jo},Ko={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Xo=c("path",{fill:"currentColor",d:"M6 14q-.825 0-1.413-.588T4 12q0-.825.588-1.413T6 10q.825 0 1.413.588T8 12q0 .825-.588 1.413T6 14Zm6 0q-.825 0-1.413-.588T10 12q0-.825.588-1.413T12 10q.825 0 1.413.588T14 12q0 .825-.588 1.413T12 14Zm6 0q-.825 0-1.413-.588T16 12q0-.825.588-1.413T18 10q.825 0 1.413.588T20 12q0 .825-.588 1.413T18 14Z"},null,-1),Go=[Xo];function ea(s,i){return a(),y("svg",Ko,Go)}const na={name:"material-symbols-more-horiz",render:ea},ta={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},la=c("path",{fill:"currentColor",d:"M16 8a7.3 7.3 0 0 0-3.32.89c.2-.6.32-1.23.32-1.89c0-2.97-2.16-5.43-5-5.91V6H6V1.09C3.16 1.57 1 4.03 1 7c0 2.22 1.21 4.15 3 5.19V21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-2.38A6.956 6.956 0 0 0 16 22c3.9 0 7-3.1 7-7s-3.1-7-7-7m-8 3.04V20H6v-8.96l-1-.58C3.77 9.74 3 8.42 3 7c0-1 .37-1.94 1-2.65V8h6V4.35c.63.71 1 1.65 1 2.65c0 1.42-.77 2.74-2 3.46l-1 .58M16 20c-2.8 0-5-2.2-5-5s2.2-5 5-5s5 2.2 5 5s-2.2 5-5 5m.5-4.7l2.9 1.7l-.8 1.2L15 16v-5h1.5v4.3Z"},null,-1),oa=[la];function aa(s,i){return a(),y("svg",ta,oa)}const sa={name:"mdi-wrench-clock-outline",render:aa},ia={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},ca=c("path",{fill:"currentColor",d:"M11 7v6l5.2 3.1l.8-1.2l-4.5-2.7V7H11m9 5v6h2v-6h-2m0 8v2h2v-2h-2m-2 0c-1.7 1.3-3.7 2-6 2c-5.5 0-10-4.5-10-10S6.5 2 12 2c4.8 0 8.9 3.4 9.8 8h-2.1c-.9-3.4-4-6-7.7-6c-4.4 0-8 3.6-8 8s3.6 8 8 8c2.4 0 4.5-1.1 6-2.7V20Z"},null,-1),ra=[ca];function ua(s,i){return a(),y("svg",ia,ra)}const _a={name:"mdi-clock-alert-outline",render:ua},da={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},pa=c("path",{fill:"currentColor",d:"M21 13.1c-.1 0-.3.1-.4.2l-1 1l2.1 2.1l1-1c.2-.2.2-.6 0-.8l-1.3-1.3c-.1-.1-.2-.2-.4-.2m-1.9 1.8l-6.1 6V23h2.1l6.1-6.1l-2.1-2M12.5 7v5.2l4 2.4l-1 1L11 13V7h1.5M11 21.9c-5.1-.5-9-4.8-9-9.9C2 6.5 6.5 2 12 2c5.3 0 9.6 4.1 10 9.3c-.3-.1-.6-.2-1-.2s-.7.1-1 .2C19.6 7.2 16.2 4 12 4c-4.4 0-8 3.6-8 8c0 4.1 3.1 7.5 7.1 7.9l-.1.2v1.8Z"},null,-1),fa=[pa];function ma(s,i){return a(),y("svg",da,fa)}const va={name:"mdi-clock-edit-outline",render:ma},ha={class:"inline-block",viewBox:"0 0 256 256",width:"1em",height:"1em"},ga=c("path",{fill:"currentColor",d:"M140 180a12 12 0 1 1-12-12a12 12 0 0 1 12 12ZM128 72c-22.06 0-40 16.15-40 36v4a8 8 0 0 0 16 0v-4c0-11 10.77-20 24-20s24 9 24 20s-10.77 20-24 20a8 8 0 0 0-8 8v8a8 8 0 0 0 16 0v-.72c18.24-3.35 32-17.9 32-35.28c0-19.85-17.94-36-40-36Zm104 56A104 104 0 1 1 128 24a104.11 104.11 0 0 1 104 104Zm-16 0a88 88 0 1 0-88 88a88.1 88.1 0 0 0 88-88Z"},null,-1),ba=[ga];function ya(s,i){return a(),y("svg",ha,ba)}const $a={name:"ph-question",render:ya},xa={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},ka=c("path",{fill:"currentColor",d:"M14.47 15.08L11 13V7h1.5v5.25l3.08 1.83c-.41.28-.79.62-1.11 1m-1.39 4.84c-.36.05-.71.08-1.08.08c-4.42 0-8-3.58-8-8s3.58-8 8-8s8 3.58 8 8c0 .37-.03.72-.08 1.08c.69.1 1.33.32 1.92.64c.1-.56.16-1.13.16-1.72c0-5.5-4.5-10-10-10S2 6.5 2 12s4.47 10 10 10c.59 0 1.16-.06 1.72-.16c-.32-.59-.54-1.23-.64-1.92M18 15v3h-3v2h3v3h2v-3h3v-2h-3v-3h-2Z"},null,-1),wa=[ka];function Ca(s,i){return a(),y("svg",xa,wa)}const za={name:"mdi-clock-plus-outline",render:Ca},Ma={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},qa=c("path",{fill:"currentColor",d:"M21 11.1V8c0-1.1-.9-2-2-2h-8L9 4H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h7.2c1.2 1.8 3.4 3 5.8 3c3.9 0 7-3.1 7-7c0-1.9-.8-3.6-2-4.9M9.3 18H3V8h16v1.7c-.9-.5-1.9-.7-3-.7c-3.9 0-7 3.1-7 7c0 .7.1 1.4.3 2m6.7 3c-2.8 0-5-2.2-5-5s2.2-5 5-5s5 2.2 5 5s-2.2 5-5 5m1-7h-2v-2h2v2m0 6h-2v-5h2v5Z"},null,-1),Ta=[qa];function Ba(s,i){return a(),y("svg",Ma,Ta)}const Va={name:"mdi-folder-information-outline",render:Ba},Ha={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Za=c("path",{fill:"currentColor",d:"M6 2a2 2 0 0 0-2 2v16c0 1.11.89 2 2 2h6v-2H6V4h7v5h5v3h2V8l-6-6m4 12a.26.26 0 0 0-.26.21l-.19 1.32c-.3.13-.59.29-.85.47l-1.24-.5c-.11 0-.24 0-.31.13l-1 1.73c-.06.11-.04.24.06.32l1.06.82a4.193 4.193 0 0 0 0 1l-1.06.82a.26.26 0 0 0-.06.32l1 1.73c.06.13.19.13.31.13l1.24-.5c.26.18.54.35.85.47l.19 1.32c.02.12.12.21.26.21h2c.11 0 .22-.09.24-.21l.19-1.32c.3-.13.57-.29.84-.47l1.23.5c.13 0 .26 0 .33-.13l1-1.73a.26.26 0 0 0-.06-.32l-1.07-.82c.02-.17.04-.33.04-.5c0-.17-.01-.33-.04-.5l1.06-.82a.26.26 0 0 0 .06-.32l-1-1.73c-.06-.13-.19-.13-.32-.13l-1.23.5c-.27-.18-.54-.35-.85-.47l-.19-1.32A.236.236 0 0 0 20 14m-1 3.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5c-.84 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5Z"},null,-1),Da=[Za];function Ia(s,i){return a(),y("svg",Ha,Da)}const Sa={name:"mdi-file-cog-outline",render:Ia},La={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Na=c("path",{fill:"currentColor",d:"M14 2H6a2 2 0 0 0-2 2v16c0 1.11.89 2 2 2h12c1.11 0 2-.89 2-2V8l-6-6m4 18H6V4h7v5h5v11m-3-7c0 1.89-2.25 2.07-2.25 3.76h-1.5c0-2.44 2.25-2.26 2.25-3.76c0-.82-.67-1.5-1.5-1.5s-1.5.68-1.5 1.5H9c0-1.65 1.34-3 3-3s3 1.35 3 3m-2.25 4.5V19h-1.5v-1.5h1.5Z"},null,-1),Ua=[Na];function Pa(s,i){return a(),y("svg",La,Ua)}const ja={name:"mdi-file-question-outline",render:Pa},Ra={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Ea=c("path",{fill:"currentColor",d:"m11.17 8l-.58-.59L9.17 6H4v12h16V8h-8z",opacity:".3"},null,-1),Fa=c("path",{fill:"currentColor",d:"M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V6h5.17l1.41 1.41l.59.59H20v10z"},null,-1),Aa=[Ea,Fa];function Oa(s,i){return a(),y("svg",Ra,Aa)}const Qa={name:"ic-twotone-folder",render:Oa},Wa={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Ja=c("path",{fill:"currentColor",d:"M13 4H6v16h12V9h-5z",opacity:".3"},null,-1),Ya=c("path",{fill:"currentColor",d:"m20 8l-6-6H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8zm-2 12H6V4h7v5h5v11z"},null,-1),Ka=[Ja,Ya];function Xa(s,i){return a(),y("svg",Wa,Ka)}const Ga={name:"ic-twotone-insert-drive-file",render:Xa},es={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},ns=c("path",{fill:"currentColor",d:"m16 13l6.964 4.062l-2.973.85l2.125 3.681l-1.732 1l-2.125-3.68l-2.223 2.15L16 13Zm-2-7h2v2h5a1 1 0 0 1 1 1v4h-2v-3H10v10h4v2H9a1 1 0 0 1-1-1v-5H6v-2h2V9a1 1 0 0 1 1-1h5V6ZM4 14v2H2v-2h2Zm0-4v2H2v-2h2Zm0-4v2H2V6h2Zm0-4v2H2V2h2Zm4 0v2H6V2h2Zm4 0v2h-2V2h2Zm4 0v2h-2V2h2Z"},null,-1),ts=[ns];function ls(s,i){return a(),y("svg",es,ts)}const os={name:"ri-drag-drop-line",render:ls},as={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},ss=c("path",{fill:"currentColor",d:"m12 13.4l-4.9 4.9q-.275.275-.7.275t-.7-.275q-.275-.275-.275-.7t.275-.7l4.9-4.9l-4.9-4.9q-.275-.275-.275-.7t.275-.7q.275-.275.7-.275t.7.275l4.9 4.9l4.9-4.9q.275-.275.7-.275t.7.275q.275.275.275.7t-.275.7L13.4 12l4.9 4.9q.275.275.275.7t-.275.7q-.275.275-.7.275t-.7-.275L12 13.4Z"},null,-1),is=[ss];function cs(s,i){return a(),y("svg",as,is)}const rs={name:"material-symbols-close-rounded",render:cs},us={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},_s=c("g",{fill:"currentColor"},[c("path",{d:"M16.192 7.707a1 1 0 0 0-1.414 0l-7.07 7.071a1 1 0 1 0 1.413 1.414l7.071-7.07a1 1 0 0 0 0-1.415Z"}),c("path",{"fill-rule":"evenodd",d:"M3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V6Zm3-1h12a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z","clip-rule":"evenodd"})],-1),ds=[_s];function ps(s,i){return a(),y("svg",us,ds)}const fs={name:"gg-shortcut",render:ps},ms={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},vs=c("path",{fill:"currentColor",d:"m20.5 3l-.16.03L15 5.1L9 3L3.36 4.9c-.21.07-.36.25-.36.48V20.5a.5.5 0 0 0 .5.5l.16-.03L9 18.9l6 2.1l5.64-1.9c.21-.07.36-.25.36-.48V3.5a.5.5 0 0 0-.5-.5M10 5.47l4 1.4v11.66l-4-1.4V5.47m-5 .99l3-1.01v11.7l-3 1.16V6.46m14 11.08l-3 1.01V6.86l3-1.16v11.84Z"},null,-1),hs=[vs];function gs(s,i){return a(),y("svg",ms,hs)}const bs={name:"mdi-map-outline",render:gs},ys={class:"inline-block",viewBox:"0 0 1024 1024",width:"1em",height:"1em"},$s=c("path",{fill:"currentColor",d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"},null,-1),xs=[$s];function ks(s,i){return a(),y("svg",ys,xs)}const ws={name:"ant-design-swap-outlined",render:ks},Cs={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},zs=c("path",{fill:"currentColor",d:"M11 6c1.38 0 2.63.56 3.54 1.46L12 10h6V4l-2.05 2.05A6.976 6.976 0 0 0 11 4c-3.53 0-6.43 2.61-6.92 6H6.1A5 5 0 0 1 11 6m5.64 9.14A6.89 6.89 0 0 0 17.92 12H15.9a5 5 0 0 1-4.9 4c-1.38 0-2.63-.56-3.54-1.46L10 12H4v6l2.05-2.05A6.976 6.976 0 0 0 11 18c1.55 0 3-.5 4.14-1.36L20 21.5l1.5-1.5l-4.86-4.86Z"},null,-1),Ms=[zs];function qs(s,i){return a(),y("svg",Cs,Ms)}const Ts={name:"mdi-find-replace",render:qs},Bs={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Vs=c("path",{fill:"currentColor",d:"M11.245 15h-6.49l-2 5H.6L7 4h2l6.4 16h-2.155l-2-5Zm-.8-2L8 6.885L5.554 13h4.891ZM21 12.535V12h2v8h-2v-.535a4 4 0 1 1 0-6.93ZM19 18a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z"},null,-1),Hs=[Vs];function Zs(s,i){return a(),y("svg",Bs,Hs)}const Ds={name:"ri-font-size",render:Zs},Is={class:"inline-block",viewBox:"0 0 256 256",width:"1em",height:"1em"},Ss=c("path",{fill:"currentColor",d:"M40 92h30.06a36 36 0 0 0 67.88 0H216a12 12 0 0 0 0-24h-78.06a36 36 0 0 0-67.88 0H40a12 12 0 0 0 0 24Zm64-24a12 12 0 1 1-12 12a12 12 0 0 1 12-12Zm112 96h-14.06a36 36 0 0 0-67.88 0H40a12 12 0 0 0 0 24h94.06a36 36 0 0 0 67.88 0H216a12 12 0 0 0 0-24Zm-48 24a12 12 0 1 1 12-12a12 12 0 0 1-12 12Z"},null,-1),Ls=[Ss];function Ns(s,i){return a(),y("svg",Is,Ls)}const Us={name:"ph-sliders-horizontal-bold",render:Ns},Ps={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},js=c("path",{fill:"currentColor",d:"M17 16.47V7.39l-6 4.54M2.22 9.19a.858.858 0 0 1-.02-1.15l1.2-1.11c.2-.18.69-.26 1.05 0l3.42 2.61l7.93-7.25c.32-.32.87-.45 1.5-.12l4 1.91c.36.21.7.54.7 1.15v13.5c0 .4-.29.83-.6 1l-4.4 2.1c-.32.13-.92.01-1.13-.2l-8.02-7.3l-3.4 2.6c-.38.26-.85.19-1.05 0l-1.2-1.1c-.32-.33-.28-.87.05-1.2l3-2.7"},null,-1),Rs=[js];function Es(s,i){return a(),y("svg",Ps,Rs)}const Fs={name:"mdi-microsoft-visual-studio-code",render:Es},As={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Os=c("path",{fill:"currentColor",d:"M13 19c0 .34.04.67.09 1H6.5c-1.5 0-2.81-.5-3.89-1.57C1.54 17.38 1 16.09 1 14.58c0-1.3.39-2.46 1.17-3.48S4 9.43 5.25 9.15c.42-1.53 1.25-2.77 2.5-3.72S10.42 4 12 4c1.95 0 3.6.68 4.96 2.04C18.32 7.4 19 9.05 19 11c1.15.13 2.1.63 2.86 1.5c.51.57.84 1.21 1 1.92A5.908 5.908 0 0 0 19 13h-2v-2c0-1.38-.5-2.56-1.46-3.54C14.56 6.5 13.38 6 12 6s-2.56.5-3.54 1.46C7.5 8.44 7 9.62 7 11h-.5c-.97 0-1.79.34-2.47 1.03c-.69.68-1.03 1.5-1.03 2.47s.34 1.79 1.03 2.5c.68.66 1.5 1 2.47 1h6.59c-.05.33-.09.66-.09 1m7-1v-3h-2v3h-3v2h3v3h2v-3h3v-2h-3Z"},null,-1),Qs=[Os];function Ws(s,i){return a(),y("svg",As,Qs)}const Js={name:"mdi-cloud-plus-outline",render:Ws},Ys={};function Ks(s,i){const o=co,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const Tn=$(Ys,[["render",Ks]]),Xs={};function Gs(s,i){const o=fo,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const ei=$(Xs,[["render",Gs]]),ni={};function ti(s,i){const o=bo,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const li=$(ni,[["render",ti]]),oi={};function ai(s,i){const o=wo,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const si=$(oi,[["render",ai]]),ii={};function ci(s,i){const o=To,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const ri=$(ii,[["render",ci]]),ui={};function _i(s,i){const o=Ol,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const Ne=$(ui,[["render",_i]]),di={};function pi(s,i){const o=Io,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const Ue=$(di,[["render",pi]]),fi={};function mi(s,i){const o=Po,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const Pe=$(fi,[["render",mi]]),vi={};function hi(s,i){const o=Ao,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const Bn=$(vi,[["render",hi]]),gi={};function bi(s,i){const o=Yo,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const Vn=$(gi,[["render",bi]]),yi={};function $i(s,i){const o=na,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const Hn=$(yi,[["render",$i]]),xi={};function ki(s,i){const o=Ql,m=v;return a(),d(m,null,{default:n(()=>[e(o,{style:{"vertical-align":"baseline"}})]),_:1})}const Zn=$(xi,[["render",ki]]),wi={};function Ci(s,i){const o=hl,m=v;return a(),d(m,null,{default:n(()=>[e(o)]),_:1})}const zi=$(wi,[["render",Ci]]),Mi={};function qi(s,i){const o=gl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const be=$(Mi,[["render",qi]]),Ti={};function Bi(s,i){const o=bl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Vi=$(Ti,[["render",Bi]]),Hi={};function Zi(s,i){const o=yl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Dn=$(Hi,[["render",Zi]]),Di={};function Ii(s,i){const o=$l;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Si=$(Di,[["render",Ii]]),Li={};function Ni(s,i){const o=xl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Ui=$(Li,[["render",Ni]]),Pi={};function ji(s,i){const o=kl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Ri=$(Pi,[["render",ji]]),Ei={};function Fi(s,i){const o=wl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Ai=$(Ei,[["render",Fi]]),Oi={};function Qi(s,i){const o=Cl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Wi=$(Oi,[["render",Qi]]),Ji={};function Yi(s,i){const o=zl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Ki=$(Ji,[["render",Yi]]),Xi={};function Gi(s,i){const o=Ml;return a(),d(o,{style:{"vertical-align":"baseline"}})}const ec=$(Xi,[["render",Gi]]),nc={};function tc(s,i){const o=ql;return a(),d(o,{style:{"vertical-align":"baseline"}})}const lc=$(nc,[["render",tc]]),oc={};function ac(s,i){const o=Tl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const sc=$(oc,[["render",ac]]),ic={};function cc(s,i){const o=Bl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const rc=$(ic,[["render",cc]]),uc={};function _c(s,i){const o=Vl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const In=$(uc,[["render",_c]]),dc={};function pc(s,i){const o=Hl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Sn=$(dc,[["render",pc]]),fc={};function mc(s,i){const o=Zl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const vc=$(fc,[["render",mc]]),hc={};function gc(s,i){const o=Dl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const bc=$(hc,[["render",gc]]),yc={};function $c(s,i){const o=Il;return a(),d(o,{style:{"vertical-align":"baseline"}})}const xc=$(yc,[["render",$c]]),kc={};function wc(s,i){const o=Sl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Cc=$(kc,[["render",wc]]),zc={};function Mc(s,i){const o=Ll;return a(),d(o,{style:{"vertical-align":"baseline"}})}const qc=$(zc,[["render",Mc]]),Tc={};function Bc(s,i){const o=Nl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Vc=$(Tc,[["render",Bc]]),Hc={};function Zc(s,i){const o=Ul;return a(),d(o,{style:{"vertical-align":"baseline"}})}const je=$(Hc,[["render",Zc]]),Dc={};function Ic(s,i){const o=Pl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Re=$(Dc,[["render",Ic]]),Sc={};function Lc(s,i){const o=jl;return a(),d(o,{style:{"vertical-align":"baseline"}})}const Nc=$(Sc,[["render",Lc]]),Uc={txt:{language:"plaintext",icon:Vc},log:{language:"plaintext",icon:rc},sh:{language:"shell",icon:Ri,canRun:!0},py:{language:"python",icon:Ai,canRun:!0},js:{language:"javascript",icon:Wi,canRun:!0},ts:{language:"typescript",icon:Ki,canRun:!0},json:{language:"json",icon:ec},md:{language:"markdown",icon:lc},go:{language:"go",icon:sc},yml:{language:"yaml",icon:In},yaml:{language:"yaml",icon:In},xml:{language:"xml",icon:Sn},html:{language:"html",icon:Sn},css:{language:"css",icon:vc},java:{language:"java",icon:bc},c:{language:"c",icon:xc},cpp:{language:"cpp",icon:Cc},cs:{language:"csharp",icon:qc},zip:{icon:je,disabled:!0},"7z":{icon:je,disabled:!0},rar:{icon:je,disabled:!0},png:{icon:Re,disabled:!0},jpeg:{icon:Re,disabled:!0},gif:{icon:Re,disabled:!0},csv:{icon:Nc,disabled:!0},db:{icon:be,disabled:!0},mdb:{icon:be,disabled:!0},accdb:{icon:be,disabled:!0},sql:{icon:be,disabled:!0}};function Ln(s,i,o,m){const Y=typeof m!="undefined"?[m,s]:[s],r=new Blob(Y,{type:o||"application/octet-stream"}),U=window.URL.createObjectURL(r),Z=document.createElement("a");Z.style.display="none",Z.href=U,Z.setAttribute("download",i),typeof Z.download=="undefined"&&Z.setAttribute("target","_blank"),document.body.appendChild(Z),Z.click(),document.body.removeChild(Z),window.URL.revokeObjectURL(U)}const I=s=>(ml("data-v-e5f7ba27"),s=s(),vl(),s),Pc={key:0,class:"font-bold text-20px"},jc={class:"font-bold text-15px"},Rc={class:"pb-1 mr-3"},Ec={class:"pb-1 mr-3"},Fc={style:{"margin-bottom":"12px"}},Ac={style:{"line-height":".5"}},Oc=I(()=>c("br",null,null,-1)),Qc=I(()=>c("br",null,null,-1)),Wc={key:0,style:{"line-height":"2.5"}},Jc=I(()=>c("br",null,null,-1)),Yc=I(()=>c("br",null,null,-1)),Kc={key:1,style:{"line-height":"2","padding-bottom":"5px"}},Xc=I(()=>c("br",null,null,-1)),Gc=I(()=>c("br",null,null,-1)),er={style:{"line-height":".5"}},nr=I(()=>c("br",null,null,-1)),tr=I(()=>c("br",null,null,-1)),lr=I(()=>c("br",null,null,-1)),or={key:0,style:{"line-height":"2.5"}},ar=I(()=>c("br",null,null,-1)),sr=I(()=>c("br",null,null,-1)),ir=I(()=>c("br",null,null,-1)),cr={key:1,style:{"line-height":"2","padding-bottom":"5px"}},rr=I(()=>c("br",null,null,-1)),ur=I(()=>c("br",null,null,-1)),_r=I(()=>c("br",null,null,-1)),dr=Bt({__name:"index",setup(s){const i=Vt(),o=Lt.isMobile,m=Ht(),Y=Zt(),r=Nt,U=b([""]),Z=b(),F=b(500),O=b("文件编辑"),ye=b([]),ae=b([]),K=b(""),Ee=b([]),Fe=b(0),Ae=b(0),$e=b(),se=b({content:"",language:""}),X=b(""),xe=b(""),ee=b(!o.value),ne=b(o.value?"on":"off"),ke=b(!1),G=b(o.value?13:15),P=b(!1),ie=b(!1),ce=b(!1),re=b(!1),we=b(!1),ue=b(!1),_e=b(!1),E=b(!1),j=b(void 0),de=b(!1),Oe=b(!0),Ce=b(!1),pe=b(!1),te=b(!1),w=b(),z=b(null),R=b(null),fe=b(""),T=b(null),L=b({type:1,name:null,dir:null});function ze(){return{ghost:!1,tertiary:!0}}function Nn({option:u}){return{onContextmenu(l){Ee.value=qe("ContextMenu",{option:u}),ie.value=!0,Fe.value=l.clientX,Ae.value=l.clientY,l.preventDefault(),z.value=u}}}function Un(u){return h(N,{placement:"right",delay:800,duration:500,style:{width:"max-content",fontSize:"12px"}},{trigger:()=>u.option.label,default:()=>h(M,{style:"gap: 4px"},{default:()=>[u.option.key,h(q,{text:!0,type:"primary",focusable:!1,onClick:()=>Pn(u.option.key)},{default:()=>h(zi)})]})})}function Pn(u){Rl(u)?m.success(r("common.copyOk")):m.error(r("common.copyFail"))}function Qe(){return z.value?(R.value=z.value,L.value.dir=R.value.key,x("contextMenu"),R.value.key):null}function We(u,l,_){~(()=>B(this,null,function*(){const f=_.node;if(_.action==="select"){if(!f.isDir&&!f.disabled){if(o.value&&(j.value=!0),!f.key||!f.language||(yield Je(f.key,f.language))===-3e3)return;w.value=f,O.value=f.label}}else f.isDir||(x("title"),x("code"),x("content"),x("contextMenu"))}))()}function Je(u,l){return B(this,null,function*(){var _;xe.value=l,Ce.value=!0;try{const f=yield Kl({path:encodeURI(u)});if(Ce.value=!1,(_=f.error)!=null&&_.msg)throw new Error("错误");f.data?(X.value=f.data,se.value={content:f.data,language:xe.value}):(X.value="",se.value={content:"",language:l},m.info("文件内容为空"),P.value=!0)}catch(f){return x("content"),x("contextMenu"),-3e3}finally{Ce.value=!1}})}function jn(){return h(v,{style:"vertical-align: 0"},{default:()=>h(Ue)})}function Ye(){ce.value=!0}function Rn(){return B(this,null,function*(){const u=z.value?z.value.key:w.value.key;try{if((yield Xl({path:u,name:fe.value})).error)return;m.success(r("file.rename.success")),w.value&&w.value.key===u&&(x("title"),x("code"),x("content"),P.value=!1),Q()}catch(l){}finally{x("contextMenu")}})}function En(){return{disabled:!fe.value}}function Ke(){ce.value=!1,x("contextMenu"),x("renameInput")}function Fn(u){fe.value=u}function An(u){return!["<",">","?","*","/"].some(l=>u.includes(l))}function le(u,l,_){if(_.node)switch(_.action){case"expand":_.node.prefix=()=>h(v,null,{default:()=>h(Si)});break;case"collapse":_.node.prefix=()=>h(v,null,{default:()=>h(Dn)});break}}function On(_){return B(this,arguments,function*({node:u,dragNode:l}){if(!u.isDir)return;const f=l.key,C=`${u.key}${f.includes("\\")?"\\":"/"}${l.label}`;if(f!==C)try{if((yield zn({path:l.key,newPath:C})).error)return;m.success(r("file.move.success")),Q()}catch(V){}})}function Me(u,l,_){switch(_.action){case"select":_.node&&(R.value=_.node,L.value.dir=_.node.key);break;case"delete":case"clear":case"unselect":x("treeSelect");break}}const me=b(!1);function Xe(u){return u?Q("plugins"):Q()}function D(u){return B(this,null,function*(){switch(u){case"findBox":ke.value=!ke.value,E.value=!1;break;case"wordWrap":ne.value=ne.value==="on"?"off":"on",E.value=!1;break;case"minimap":ee.value=!ee.value,E.value=!1;break;case"showEditorShortcutsInfo":$e.value.showShortCuts=!0;break;case"changeToEditor":P.value=!P.value,P.value&&(m.info(r("editor.switch.changeToEditorMsg"),{duration:1e3}),j.value=!0);break;case"siderCollapse":j.value=!j.value;break;case"save":de.value=!0;const l=t($e).getValue();try{if((yield Gl({content:l,path:w.value.key})).error)return;m.success(r("editor.main.savedSuccess")),X.value=l}finally{de.value=!1}break;case"clickContextMenu":ie.value=!1;break;case"exitContextMenu":ie.value=!1;break}})}function qe(u,{option:l}){const _={edit:{label:"编辑",key:"edit",icon:()=>h(v,null,{default:()=>h(Bn)}),props:{onClick:()=>{tn()}}},attribute:{label:"属性",key:"attribute",icon:()=>h(v,null,{default:()=>h(Vn)}),props:{onClick:()=>{nn()}}},create:{label:"新建",key:"create",icon:()=>h(v,null,{default:()=>h(Zn)}),props:{onClick:()=>{Kn()}}},upload:{label:"上传",key:"upload",icon:()=>h(v,null,{default:()=>h(Tn)}),props:{onClick:()=>{nt()}}},more:{label:()=>h(Se,{style:{color:"var(--app-primary-color)"}},{default:()=>"更多"}),key:"more",icon:()=>h(v,{color:"var(--app-primary-color)"},{default:()=>h(Hn)}),children:[{label:"下载",key:"download",icon:()=>h(v,null,{default:()=>h(l.isDir?ri:si)}),props:{onClick:()=>{en()}}},{label:"移动",key:"move",icon:()=>h(v,null,{default:()=>h(Pe)}),props:{onClick:()=>{ln()}}},{label:"重命名",key:"rename",icon:()=>h(v,null,{default:()=>h(Ue)}),props:{onClick:()=>{Ye()}}},{type:"divider",key:"d1"},{label:()=>h(Se,{style:{color:"var(--app-error-color)"}},{default:()=>"删除"}),key:"delete",icon:()=>h(v,{color:"var(--app-error-color)"},{default:()=>h(Ne)}),props:{onClick:()=>{Ge()}}}]}};let f=[];return u==="ContextMenu"?(f=[_.attribute],l.isDir?f=[_.create,_.upload,...f,..._.more.children]:l.disabled?f=[...f,..._.more.children]:f=[_.edit,...f,_.more]):u==="MobileHeader"?f=[_.edit,_.more.children[0],_.more.children[1],_.more.children[2],_.attribute,_.more.children[3],_.more.children[4]]:u==="HeaderSubTitle"&&(f=[_.create,_.upload]),f}function Ge(){return B(this,null,function*(){const u=z.value?z.value.key:w.value.key;Y.error({title:r("file.delete.title"),content:r("file.delete.confirm"),icon:()=>h(v,{style:"vertical-align: 0"},{default:()=>h(Ne)}),closable:!1,maskClosable:!1,autoFocus:!1,positiveText:r("file.delete.button"),onPositiveClick:()=>B(this,null,function*(){try{if((yield eo({path:u})).error)return;m.success(r("file.delete.success")),w.value&&w.value.key===u&&(x("title"),x("code"),x("content"),P.value=!1),Q()}catch(l){}finally{x("contextMenu")}}),onNegativeClick:()=>{x("contextMenu")},negativeButtonProps:{ghost:!1,tertiary:!0}})})}function en(){return B(this,null,function*(){const u=z.value?z.value.key:w.value.key,l=z.value?z.value.label:w.value.label,_=z.value?z.value.isDir:w.value.isDir,f=m.info(r("file.download.wait"),{duration:0,icon:()=>h(v,null,{default:()=>h(li)})});try{if(_){const C=yield Mn({path:u});let V=0,H;C!=null&&C.code&&(V=C.code,H=C.blob),f.destroy(),V?(m.success(r("file.download.success")),H&&Ln(H,`${l}.zip`)):m.error("下载失败,未知原因")}else{const C=yield Mn({path:u});let V=0,H="";C!=null&&C.code&&(V=C.code,H=C.blob),f.destroy(),V?(m.success(r("file.download.success")),H&&Ln(H,l)):m.error("下载失败,未知原因")}}catch(C){f.destroy(),m.error(r("file.download.fail"),{keepAliveOnHover:!0})}finally{x("contextMenu")}})}function nn(){return B(this,null,function*(){const u=z.value?z.value.key:w.value.key,l=z.value?z.value.isDir:w.value.isDir;try{pe.value=!0,we.value=!0;const _=yield no({path:u});if(_.error)return;const f=_.data;if(!f)return;l&&(f.fileSize=""),T.value=f,pe.value=!1}catch(_){x("fileAttributeData")}finally{x("contextMenu")}})}function tn(){return B(this,null,function*(){z.value&&(w.value=z.value),(yield Je(w.value.key,w.value.language||"plaintext"))!==-3e3&&(P.value=!0,j.value=!0,m.info(r("editor.switch.changeToEditorMsg"),{duration:1e3}))})}function Qn(){return h(v,{style:"vertical-align: 0"},{default:()=>h(Pe)})}function ln(){re.value=!0,Te()}function Wn(){return B(this,null,function*(){const u=z.value?z.value.key:w.value.key,l=z.value?z.value.label:w.value.label;let _=R.value?R.value.key:"";_=`${_}${u.includes("\\")?"\\":"/"}${l}`;try{if((yield zn({path:u,newPath:_})).error)return;m.success(r("file.move.success")),w.value&&w.value.key===u&&(x("title"),x("code"),x("content"),P.value=!1),Q()}catch(f){}finally{x("treeSelect"),x("contextMenu")}})}function Jn(){return{disabled:!R.value}}function on(){re.value=!1,x("treeSelect")}function x(u){switch(u){case"title":O.value="文件编辑";break;case"code":X.value="",se.value={content:"",language:"plaintext"};break;case"content":w.value=null,o.value&&(j.value=!1);break;case"contextMenu":z.value=null;break;case"renameInput":fe.value="";break;case"treeSelect":R.value=null,L.value.dir=null;break;case"fileAttributeData":T.value=null;break;case"fileCreate":L.value={type:1,name:null,dir:null};break}}function an(u,l){return B(this,null,function*(){const _=f=>{f.length&&f.forEach(C=>{if(C.type===0)Object.assign(C,{key:C.path,label:C.title,isDir:!0,prefix:()=>h(v,null,{default:()=>h(Dn)})}),_(C.children),C.children.length||(Object.assign(C,{children:null,disabled:!l}),C.prefix=()=>h(v,null,{default:()=>h(Ui)}));else{const V=C.name;if(!V)return;const H=V.substring(V.lastIndexOf(".")+1),W=Uc[H]||{};Object.assign(C,{key:C.path,label:V,isDir:!1,language:W.language||"plaintext",canRun:W.canRun||!1,disabled:W.disabled||!W.language,prefix:()=>h(v,null,{default:()=>h(W.icon||Vi)})})}})};return _(u),u})}function Q(u){return B(this,null,function*(){Oe.value=!0;const l={};u&&(l.type=u);const _=yield an(yield qn(l));U.value=[_[0].key],ye.value=_,Oe.value=!1})}function Te(u){return B(this,null,function*(){te.value=!0;const l={isDir:"true"};u&&(l.type=u);const _=yield an(yield qn(l),!0);U.value=[_[0].key],ae.value=_,te.value=!1})}function Yn(){return h(v,{style:"vertical-align: 0"},{default:()=>h(Zn)})}function Kn(){ue.value=!0,Te()}function Xn(){return B(this,null,function*(){const u=L.value.type,l=L.value.name,_=L.value.dir;try{if((yield to({type:u,name:l,dir:_})).error)return;m.success(r("file.create.success")),Q()}catch(f){}finally{x("fileCreate"),x("contextMenu")}})}function sn(){ue.value=!1,x("fileCreate"),x("contextMenu")}function Gn(){return{disabled:!R.value||!L.value.name}}function et(){return h(v,{style:"vertical-align: 0"},{default:()=>h(Tn)})}function nt(){_e.value=!0,Te()}function tt(C){return B(this,arguments,function*({file:u,onFinish:l,onError:_,onProgress:f}){const V=u.name,H=new FormData;H.append("file",u.file),(yield lo({params:H,path:R.value.key,onUploadProgress:({progress:ve})=>{f({percent:Math.ceil((ve||0)*100)})}})).error?_():(m.success(`${V} ${r("file.upload.success")}`),Q(),l())})}function lt(){_e.value=!1,x("fileCreate"),x("contextMenu")}return Dt(Z,u=>{const l=u[0],{width:_,height:f}=l.contentRect;F.value=f}),It(()=>{Q()}),(u,l)=>{const _=El,f=Fl,C=Cn,V=Js,H=Ut,W=Fs,ve=Us,cn=Ds,rn=Pt,un=Ts,_n=ws,dn=bs,pn=jt,ot=fs,fn=rs,mn=Yl,vn=Rt,at=Et,hn=Jl,he=Ft,gn=At,bn=Ot,Be=Qt,yn=Wt,$n=Jt,st=Yt,it=Wl,xn=Kt,ct=Al,rt=Xt,ut=Gt,Ve=el,oe=nl,He=tl,_t=ll,dt=os,pt=ol,ft=al,Ze=sl,kn=il,mt=Ga,wn=cl,vt=Qa,ht=rl,gt=ul,bt=_l,yt=ja,S=dl,$t=Sa,xt=Va,kt=Cn,J=pl,wt=za,ge=$a,Ct=va,zt=_a,Mt=sa,qt=fl;return a(),y("div",null,[e(Ve,{ref_key:"NcardDom",ref:Z,bordered:!1,class:"h-full rounded-8px shadow-sm"},{default:n(()=>[e(at,null,St({title:n(()=>[e(t(M),null,{default:n(()=>[t(o)&&w.value?(a(),d(De,{key:0,name:"fade-slide",mode:"in-out",appear:""},{default:n(()=>[e(t(q),{strong:"",secondary:"",size:"tiny",style:{height:"29px",padding:"0 8px"},focusable:!1,onClick:l[0]||(l[0]=p=>D("siderCollapse"))},{default:n(()=>[j.value?(a(),d(f,{key:1,style:{"vertical-align":"baseline"}})):(a(),d(_,{key:0,style:{"vertical-align":"baseline"}}))]),_:1})]),_:1})):A("",!0),c("div",null,[t(o)?(a(),d(C,{key:1,style:{height:"100%","max-width":"100px"}},{tooltip:n(()=>[c("span",null,g(O.value),1)]),default:n(()=>[c("span",jc,g(O.value),1)]),_:1})):(a(),y("span",Pc,g(O.value),1))])]),_:1})]),subtitle:n(()=>[e(t(M),{style:Ie(t(o)?"gap: 4px":"gap: 0")},{default:n(()=>[t(o)&&w.value?A("",!0):(a(),d(H,{key:0,trigger:"click",options:qe("HeaderSubTitle",{option:w.value})},{default:n(()=>[e(t(q),{circle:"",strong:"",quaternary:!t(o),secondary:t(o),size:"small",focusable:!1,style:{"vertical-align":"-0.15em"}},{default:n(()=>[e(V)]),_:1},8,["quaternary","secondary"])]),_:1},8,["options"])),t(o)?A("",!0):(a(),d(t(N),{key:1,placement:"bottom",trigger:t(o)?"click":"hover",delay:500},{trigger:n(()=>[e(t(q),{circle:"",strong:"",quaternary:!t(o),secondary:t(o),size:"small",disabled:!X.value,type:P.value?"primary":"tertiary",focusable:!1,style:{"vertical-align":"-0.15em"},onClick:l[1]||(l[1]=p=>D("changeToEditor"))},{default:n(()=>[e(W)]),_:1},8,["quaternary","secondary","disabled","type"])]),default:n(()=>[c("span",null,g(t(r)("editor.switch.changeToEditorTips")),1)]),_:1},8,["trigger"]))]),_:1},8,["style"])]),_:2},[t(o)?{name:"extra",fn:n(()=>[w.value?(a(),d(De,{key:0,name:"fade-slide",mode:"out-in",appear:""},{default:n(()=>[P.value?(a(),d(t(M),{key:0},{default:n(()=>[e(pn,{trigger:"manual",show:E.value,"show-arrow":!1,placement:"bottom"},{trigger:n(()=>[e(t(q),{strong:"",quaternary:"",size:"small",type:E.value?"primary":"tertiary",style:{padding:"6px"},focusable:!1,onClick:l[16]||(l[16]=p=>E.value=!E.value)},{default:n(()=>[e(t(v),{size:"18"},{default:n(()=>[e(ve,{style:{"vertical-align":"baseline"}})]),_:1})]),_:1},8,["type"])]),default:n(()=>[e(t(M),{vertical:""},{default:n(()=>[e(t(M),{size:"small"},{default:n(()=>[e(t(v),{size:20},{default:n(()=>[e(cn,{style:{"vertical-align":"-0.4em"}})]),_:1}),e(rn,{value:G.value,"onUpdate:value":l[17]||(l[17]=p=>G.value=p),min:12,max:40,"button-placement":"both",size:"small",class:"max-w-94px"},null,8,["value"])]),_:1}),e(t(M),{size:"small"},{default:n(()=>[e(t(v),{size:20},{default:n(()=>[e(un,{style:{"vertical-align":"-0.4em"}})]),_:1}),e(t(q),{strong:"",secondary:"",size:"small",style:{width:"94px"},onClick:l[18]||(l[18]=p=>D("findBox"))},{default:n(()=>[k(g(t(r)("editor.switch.findBtn")),1)]),_:1})]),_:1}),e(t(M),{size:"small"},{default:n(()=>[e(t(v),{size:20,color:ne.value==="on"?"var(--app-primary-color)":void 0},{default:n(()=>[e(_n,{style:{"vertical-align":"-0.4em"}})]),_:1},8,["color"]),e(t(q),{strong:"",secondary:"",size:"small",style:{width:"94px"},onClick:l[19]||(l[19]=p=>D("wordWrap"))},{default:n(()=>[k(g(t(r)("editor.switch.wordWrapBtn")),1)]),_:1})]),_:1}),e(t(M),{size:"small"},{default:n(()=>[e(t(v),{size:20,color:ee.value?"var(--app-primary-color)":void 0},{default:n(()=>[e(dn,{style:{"vertical-align":"-0.4em"}})]),_:1},8,["color"]),e(t(q),{strong:"",secondary:"",size:"small",style:{width:"94px"},onClick:l[20]||(l[20]=p=>D("minimap"))},{default:n(()=>[k(g(t(r)("editor.switch.minimapBtn")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["show"]),e(t(q),{secondary:"",strong:"",size:"small",focusable:!1,onClick:l[21]||(l[21]=p=>D("changeToEditor"))},{icon:n(()=>[e(fn)]),_:1}),e(t(q),{strong:"",type:"primary",size:"small",loading:de.value,focusable:!1,onClick:l[22]||(l[22]=p=>D("save"))},{icon:n(()=>[e(mn)]),_:1},8,["loading"])]),_:1})):(a(),d(t(M),{key:1},{default:n(()=>[e(vn,null,{default:n(()=>[e(H,{trigger:"click",placement:"bottom-start",options:qe("MobileHeader",{option:w.value})},{default:n(()=>[e(t(q),{strong:"",secondary:"",size:"small",focusable:!1},{icon:n(()=>[e(t(Hn))]),default:n(()=>[k(" "+g(t(r)("file.more.button")),1)]),_:1})]),_:1},8,["options"])]),_:1})]),_:1}))]),_:1})):A("",!0)]),key:"1"}:{name:"extra",fn:n(()=>[w.value?(a(),d(De,{key:0,name:"fade-slide",mode:"out-in",appear:""},{default:n(()=>[P.value?(a(),d(t(M),{key:0},{default:n(()=>[e(t(M),{style:{gap:"0"}},{default:n(()=>[e(pn,{trigger:"manual",show:E.value,"show-arrow":!1,placement:"bottom"},{trigger:n(()=>[e(t(q),{strong:"",quaternary:"",size:"small",type:E.value?"primary":"tertiary",style:{padding:"6px"},focusable:!1,onClick:l[2]||(l[2]=p=>E.value=!E.value)},{default:n(()=>[e(t(v),{size:"18"},{default:n(()=>[e(ve,{style:{"vertical-align":"baseline"}})]),_:1})]),_:1},8,["type"])]),default:n(()=>[e(t(M),{vertical:""},{default:n(()=>[e(t(M),{size:"small"},{default:n(()=>[e(t(v),{size:20},{default:n(()=>[e(cn,{style:{"vertical-align":"-0.4em"}})]),_:1}),e(rn,{value:G.value,"onUpdate:value":l[3]||(l[3]=p=>G.value=p),min:12,max:40,"button-placement":"both",size:"small",class:"max-w-94px"},null,8,["value"])]),_:1}),e(t(M),{size:"small"},{default:n(()=>[e(t(v),{size:20},{default:n(()=>[e(un,{style:{"vertical-align":"-0.4em"}})]),_:1}),e(t(q),{strong:"",secondary:"",size:"small",style:{width:"94px"},onClick:l[4]||(l[4]=p=>D("findBox"))},{default:n(()=>[k(g(t(r)("editor.switch.findBtn")),1)]),_:1})]),_:1}),e(t(M),{size:"small"},{default:n(()=>[e(t(v),{size:20,color:ne.value==="on"?"var(--app-primary-color)":void 0},{default:n(()=>[e(_n,{style:{"vertical-align":"-0.4em"}})]),_:1},8,["color"]),e(t(q),{strong:"",secondary:"",size:"small",style:{width:"94px"},onClick:l[5]||(l[5]=p=>D("wordWrap"))},{default:n(()=>[k(g(t(r)("editor.switch.wordWrapBtn")),1)]),_:1})]),_:1}),e(t(M),{size:"small"},{default:n(()=>[e(t(v),{size:20,color:ee.value?"var(--app-primary-color)":void 0},{default:n(()=>[e(dn,{style:{"vertical-align":"-0.4em"}})]),_:1},8,["color"]),e(t(q),{strong:"",secondary:"",size:"small",style:{width:"94px"},onClick:l[6]||(l[6]=p=>D("minimap"))},{default:n(()=>[k(g(t(r)("editor.switch.minimapBtn")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["show"]),e(t(N),{placement:"bottom",trigger:"hover",delay:500},{trigger:n(()=>[e(t(q),{strong:"",quaternary:"",size:"small",type:"tertiary",style:{padding:"6px"},focusable:!1,onClick:l[7]||(l[7]=p=>D("showEditorShortcutsInfo"))},{default:n(()=>[e(t(v),{size:"18"},{default:n(()=>[e(ot,{style:{"vertical-align":"baseline"}})]),_:1})]),_:1})]),default:n(()=>[c("span",null,g(t(r)("editor.switch.shortcutsTips")),1)]),_:1})]),_:1}),e(t(q),{secondary:"",strong:"",size:"small",focusable:!1,onClick:l[8]||(l[8]=p=>D("changeToEditor"))},{icon:n(()=>[e(fn)]),default:n(()=>[k(" "+g(t(r)("editor.main.exitButton")),1)]),_:1}),e(t(q),{strong:"",type:"primary",size:"small",loading:de.value,focusable:!1,onClick:l[9]||(l[9]=p=>D("save"))},{icon:n(()=>[e(mn)]),default:n(()=>[k(" "+g(t(r)("editor.main.saveButton")),1)]),_:1},8,["loading"])]),_:1})):(a(),d(t(M),{key:1},{default:n(()=>[e(vn,null,{default:n(()=>[e(t(N),{placement:"bottom",trigger:"hover",delay:500},{trigger:n(()=>[e(t(q),{strong:"",secondary:"",size:"small",focusable:!1,onClick:l[10]||(l[10]=p=>nn())},{icon:n(()=>[e(t(Vn))]),_:1})]),default:n(()=>[c("span",null,g(t(r)("file.attribute.button")),1)]),_:1}),e(t(N),{placement:"bottom",trigger:"hover",delay:500},{trigger:n(()=>[e(t(q),{strong:"",secondary:"",size:"small",focusable:!1,onClick:l[11]||(l[11]=p=>en())},{icon:n(()=>[e(t(ei))]),_:1})]),default:n(()=>[c("span",null,g(t(r)("file.download.button")),1)]),_:1}),e(t(N),{placement:"bottom",trigger:"hover",delay:500},{trigger:n(()=>[e(t(q),{strong:"",secondary:"",size:"small",focusable:!1,onClick:l[12]||(l[12]=p=>Ge())},{icon:n(()=>[e(t(Ne))]),_:1})]),default:n(()=>[c("span",null,g(t(r)("file.delete.button")),1)]),_:1}),e(t(N),{placement:"bottom",trigger:"hover",delay:500},{trigger:n(()=>[e(t(q),{strong:"",secondary:"",size:"small",focusable:!1,onClick:l[13]||(l[13]=p=>Ye())},{icon:n(()=>[e(t(Ue))]),_:1})]),default:n(()=>[c("span",null,g(t(r)("file.rename.button")),1)]),_:1}),e(t(N),{placement:"bottom",trigger:"hover",delay:500},{trigger:n(()=>[e(t(q),{strong:"",secondary:"",size:"small",focusable:!1,onClick:l[14]||(l[14]=p=>ln())},{icon:n(()=>[e(t(Pe))]),_:1})]),default:n(()=>[c("span",null,g(t(r)("file.move.button")),1)]),_:1}),e(t(N),{placement:"bottom",trigger:"hover",delay:500},{trigger:n(()=>[e(t(q),{strong:"",secondary:"",size:"small",focusable:!1,onClick:l[15]||(l[15]=p=>tn())},{icon:n(()=>[e(t(Bn))]),_:1})]),default:n(()=>[c("span",null,g(t(r)("file.edit.button")),1)]),_:1})]),_:1})]),_:1}))]),_:1})):A("",!0)]),key:"0"}]),1024),e(Ve,{bordered:!0,style:Ie({height:F.value*.88+"px","margin-top":"10px"}),"content-style":"padding: 0; height: 90% ;"},{default:n(()=>[e(ut,{"has-sider":"",style:{height:"100%"},"content-style":"height: 100%"},{default:n(()=>[t(o)?(a(),d(yn,{key:1,class:"scripts-container",width:"100%","content-style":"display: flex; flex-direction: column; height: 100%; padding: .8em 0 0 .8em","trigger-style":"margin-right: .75em","collapsed-trigger-style":"margin-right: -0.75em","collapsed-width":0,bordered:!1,"show-trigger":!1,"show-collapsed-content":!1,collapsed:j.value,"on-update:collapsed":p=>j.value=p,"native-scrollbar":!1},{default:n(()=>[c("div",Ec,[e(he,{value:K.value,"onUpdate:value":l[27]||(l[27]=p=>K.value=p),clearable:"",size:"small",style:{"margin-bottom":".5em"},placeholder:"请输入目录或文件名"},{prefix:n(()=>[e(hn)]),_:1},8,["value"]),e(gn,{value:me.value,"onUpdate:value":[l[28]||(l[28]=p=>me.value=p),Xe],"rubber-band":"",size:"medium"},{checked:n(()=>[k(" 查看全部 ")]),unchecked:n(()=>[k(" 只看插件 ")]),_:1},8,["value"])]),e(Be,{"x-scrollable":"",class:"flex-1 pr-3"},{default:n(()=>[e(bn,{"expand-on-click":"","block-line":"",style:{height:"100%","font-size":"15px"},"show-irrelevant-nodes":!1,pattern:K.value,data:ye.value,"on-update:expanded-keys":le,"on-update:selected-keys":We,"default-expanded-keys":U.value},null,8,["pattern","data","default-expanded-keys"]),e($n,{right:20,bottom:20})]),_:1})]),_:1},8,["collapsed","on-update:collapsed"])):(a(),d(yn,{key:0,class:"scripts-container","content-style":"display: flex; flex-direction: column; height: 100%; padding: .8em 0 0 .8em",width:"280",bordered:"","collapsed-width":0,"show-trigger":"bar","show-collapsed-content":!1,"native-scrollbar":!1,collapsed:j.value,"on-update:collapsed":j.value=void 0},{default:n(()=>[c("div",Rc,[e(he,{value:K.value,"onUpdate:value":l[23]||(l[23]=p=>K.value=p),clearable:"",style:{"margin-bottom":".5em"},placeholder:"请输入目录或文件名"},{prefix:n(()=>[e(hn)]),_:1},8,["value"]),e(gn,{value:me.value,"onUpdate:value":[l[24]||(l[24]=p=>me.value=p),Xe],"rubber-band":"",size:"medium"},{checked:n(()=>[k(" 查看全部 ")]),unchecked:n(()=>[k(" 只看插件 ")]),_:1},8,["value"])]),e(Be,{"x-scrollable":"",class:"flex-1 pr-3"},{default:n(()=>[e(bn,{"expand-on-click":"","block-line":"",draggable:"","show-line":"",style:{height:"100%"},"show-irrelevant-nodes":!1,"node-props":Nn,pattern:K.value,data:ye.value,"render-label":Un,"on-update:expanded-keys":le,"on-update:selected-keys":We,onDrop:On,"default-expanded-keys":U.value},null,8,["pattern","data","default-expanded-keys"]),e(H,{trigger:"manual",placement:"bottom-start",show:ie.value,options:Ee.value,x:Fe.value,y:Ae.value,onSelect:l[25]||(l[25]=p=>D("clickContextMenu")),onClickoutside:l[26]||(l[26]=p=>D("exitContextMenu"))},null,8,["show","options","x","y"])]),_:1})]),_:1},8,["collapsed","on-update:collapsed"])),w.value?(a(),d(xn,{key:2},{default:n(()=>[P.value?(a(),d(it,{key:1,ref_key:"editor",ref:$e,style:{height:"100%",padding:"0"},value:se.value,"word-wrap":ne.value,minimap:ee.value,"font-size":G.value,"find-box":ke.value},null,8,["value","word-wrap","minimap","font-size","find-box"])):(a(),d(Be,{key:0,"x-scrollable":""},{default:n(()=>[e(st,{code:X.value,hljs:t(i).getHljs,language:xe.value,"show-line-numbers":"",style:Ie(`font-size: ${G.value}px; line-height: 23px; height: 100%; padding: 1.3em .8em 1em 1.6em`)},null,8,["code","hljs","language","style"]),t(o)?(a(),d($n,{key:0,right:20,bottom:20})):A("",!0)]),_:1}))]),_:1})):(a(),d(xn,{key:3},{default:n(()=>[j.value!==!0?(a(),d(rt,{key:0,size:"huge",description:"请选择文件",class:"h-full flex-x-center lex-y-center"},{icon:n(()=>[e(ct)]),_:1})):A("",!0)]),_:1}))]),_:1})]),_:1},8,["style"]),e(oe,{show:ce.value,"onUpdate:show":l[29]||(l[29]=p=>ce.value=p),preset:"dialog","auto-focus":!1,"mask-closable":!1,icon:jn,title:t(r)("file.rename.title"),"positive-text":t(r)("common.confirm"),"negative-text":t(r)("common.cancel"),"on-close":Ke,"positive-button-props":En(),"negative-button-props":ze(),onNegativeClick:l[30]||(l[30]=p=>Ke()),onPositiveClick:l[31]||(l[31]=p=>Rn())},{default:n(()=>[e(he,{clearable:"",style:{"margin-top":"5px"},"default-value":z.value?z.value.label:w.value?w.value.label:null,"allow-input":An,onInput:Fn},null,8,["default-value"])]),_:1},8,["show","title","positive-text","negative-text","positive-button-props","negative-button-props"]),e(oe,{show:re.value,"onUpdate:show":l[32]||(l[32]=p=>re.value=p),preset:"dialog","auto-focus":!1,"mask-closable":!1,icon:Qn,title:t(r)("file.move.title"),"positive-text":t(r)("common.confirm"),"negative-text":t(r)("common.cancel"),"on-close":on,"positive-button-props":Jn(),"negative-button-props":ze(),onNegativeClick:l[33]||(l[33]=p=>on()),onPositiveClick:l[34]||(l[34]=p=>Wn())},{default:n(()=>[e(He,{clearable:"","show-path":!0,separator:"/",style:{"margin-top":"5px"},"consistent-menu-width":!1,loading:te.value,options:ae.value,"on-update:expanded-keys":le,"on-update:value":Me,"default-expanded-keys":U.value},null,8,["loading","options","default-expanded-keys"])]),_:1},8,["show","title","positive-text","negative-text","positive-button-props","negative-button-props"]),e(oe,{show:_e.value,"onUpdate:show":l[35]||(l[35]=p=>_e.value=p),preset:"dialog","auto-focus":!1,"mask-closable":!1,icon:et,title:!R.value&&!z.value?t(r)("file.upload.needSelect"):t(r)("file.upload.title"),"on-close":lt},{default:n(()=>[e(t(M),{vertical:""},{default:n(()=>[e(He,{"default-value":Qe(),clearable:"","show-path":!0,separator:"/",style:{"margin-top":"5px"},placeholder:t(r)("file.upload.selectPlaceholder"),"consistent-menu-width":!1,loading:te.value,options:ae.value,"on-update:expanded-keys":le,"on-update:value":Me,"default-expanded-keys":U.value},null,8,["default-value","placeholder","loading","options","default-expanded-keys"]),e(_t,{type:"info"},{default:n(()=>[k(g(t(r)("file.upload.info")),1)]),_:1}),e(ft,{multiple:"","directory-dnd":"",disabled:!R.value&&!z.value,"custom-request":tt},{default:n(()=>[e(pt,null,{default:n(()=>[c("div",Fc,[e(t(v),{size:"48",depth:3},{default:n(()=>[e(dt)]),_:1})]),e(t(Se),{style:{"font-size":"16px"}},{default:n(()=>[k(g(t(r)("file.upload.draggerText")),1)]),_:1})]),_:1})]),_:1},8,["disabled"])]),_:1})]),_:1},8,["show","title"]),e(oe,{show:ue.value,"onUpdate:show":l[38]||(l[38]=p=>ue.value=p),preset:"dialog","auto-focus":!1,"mask-closable":!1,icon:Yn,title:t(r)("file.create.title"),"on-close":sn,"positive-text":t(r)("common.confirm"),"negative-text":t(r)("common.cancel"),"positive-button-props":Gn(),"negative-button-props":ze(),onNegativeClick:l[39]||(l[39]=p=>sn()),onPositiveClick:l[40]||(l[40]=p=>Xn())},{default:n(()=>[e(Ve,{bordered:!1,style:{height:"100%","word-break":"unset"},"content-style":"padding: 0"},{default:n(()=>[e(bt,{ref:"formRef",model:L.value,style:{"padding-top":"1em"}},{default:n(()=>[e(t(M),{vertical:""},{default:n(()=>[e(gt,{"x-gap":12,cols:2},{default:n(()=>[e(kn,null,{default:n(()=>[e(Ze,{"show-feedback":!1,label:t(r)("file.create.form.name"),path:"name"},{default:n(()=>[e(he,{value:L.value.name,"onUpdate:value":l[36]||(l[36]=p=>L.value.name=p),clearable:"",placeholder:t(r)("file.create.form.namePlaceholder")},null,8,["value","placeholder"])]),_:1},8,["label"])]),_:1}),e(kn,null,{default:n(()=>[e(Ze,{"show-feedback":!1,label:t(r)("file.create.form.type"),path:"type"},{default:n(()=>[e(ht,{value:L.value.type,"onUpdate:value":l[37]||(l[37]=p=>L.value.type=p)},{default:n(()=>[e(t(M),null,{default:n(()=>[e(wn,{value:1},{default:n(()=>[k(g(t(r)("file.create.form.typeFile"))+" ",1),e(t(v),null,{default:n(()=>[e(mt,{style:{"vertical-align":"-0.15em"}})]),_:1})]),_:1}),e(wn,{value:0},{default:n(()=>[k(g(t(r)("file.create.form.typeFolder"))+" ",1),e(t(v),null,{default:n(()=>[e(vt,{style:{"vertical-align":"-0.15em"}})]),_:1})]),_:1})]),_:1})]),_:1},8,["value"])]),_:1},8,["label"])]),_:1})]),_:1}),e(Ze,{"show-feedback":!1,label:t(r)("file.create.form.path"),path:"dir"},{default:n(()=>[e(He,{"default-value":Qe(),clearable:"","show-path":!0,separator:"/",style:{"margin-top":"5px"},"consistent-menu-width":!1,loading:te.value,options:ae.value,"on-update:expanded-keys":le,"on-update:value":Me,"default-expanded-keys":U.value},null,8,["default-value","loading","options","default-expanded-keys"])]),_:1},8,["label"])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1},8,["show","title","positive-text","negative-text","positive-button-props","negative-button-props"]),e(oe,{show:we.value,"onUpdate:show":l[41]||(l[41]=p=>we.value=p),preset:"card",style:{"max-width":"fit-content"},"auto-focus":!1,title:T.value?T.value.name:t(r)("file.attribute.title")},{default:n(()=>[e(qt,{"single-column":"",bordered:!1,"bottom-bordered":!1},{default:n(()=>[c("tbody",null,[c("tr",null,[c("td",Ac,[T.value&&T.value.fileSize?(a(),y(Le,{key:0},[e(t(M),{size:1},{default:n(()=>[e(S,null,{icon:n(()=>[e(yt)]),default:n(()=>[k(g(t(r)("file.attribute.fileSize"))+" ",1)]),_:1})]),_:1}),Oc],64)):A("",!0),e(t(M),{size:1},{default:n(()=>[e(S,null,{icon:n(()=>[e($t)]),default:n(()=>[k(g(t(r)("file.attribute.mode"))+" ",1)]),_:1})]),_:1}),Qc,e(t(M),{size:1},{default:n(()=>[e(S,null,{icon:n(()=>[e(xt)]),default:n(()=>[k(g(t(r)("file.attribute.path"))+" ",1)]),_:1})]),_:1})]),pe.value?(a(),y("td",Kc,[T.value&&T.value.fileSize?(a(),y(Le,{key:0},[e(J,{text:"",size:"small",width:130,sharp:!1}),Xc],64)):A("",!0),e(J,{text:"",size:"small",width:130,sharp:!1}),Gc,e(J,{text:"",size:"small",width:130,sharp:!1})])):(a(),y("td",Wc,[T.value&&T.value.fileSize?(a(),y(Le,{key:0},[e(S,{bordered:!1},{default:n(()=>[k(g(T.value?T.value.fileSize:"Unknown"),1)]),_:1}),Jc],64)):A("",!0),e(S,{bordered:!1},{default:n(()=>[k(g(T.value?T.value.mode:"Unknown"),1)]),_:1}),Yc,e(S,{bordered:!1},{default:n(()=>[e(kt,{style:{"max-width":"138px"}},{default:n(()=>[k(g(T.value?T.value.path:"Unknown"),1)]),_:1})]),_:1})]))]),c("tr",er,[c("td",null,[e(t(M),{size:1},{default:n(()=>[e(S,{type:"warning",bordered:!1},{icon:n(()=>[e(wt)]),default:n(()=>[k(g(t(r)("file.attribute.birthtime"))+" ",1)]),_:1}),e(t(N),{trigger:t(o)?"click":"hover"},{trigger:n(()=>[e(t(q),{quaternary:"",circle:"",size:"small",focusable:!1},{default:n(()=>[e(ge)]),_:1})]),default:n(()=>[c("span",null,g(t(r)("file.attribute.birthtimeExplanation")),1)]),_:1},8,["trigger"])]),_:1}),nr,e(t(M),{size:1},{default:n(()=>[e(S,{type:"info",bordered:!1},{icon:n(()=>[e(Ct)]),default:n(()=>[k(g(t(r)("file.attribute.mtime"))+" ",1)]),_:1}),e(t(N),{trigger:t(o)?"click":"hover"},{trigger:n(()=>[e(t(q),{quaternary:"",circle:"",size:"small",focusable:!1},{default:n(()=>[e(ge)]),_:1})]),default:n(()=>[c("span",null,g(t(r)("file.attribute.mtimeExplanation")),1)]),_:1},8,["trigger"])]),_:1}),tr,e(t(M),{size:1},{default:n(()=>[e(S,{type:"success",bordered:!1},{icon:n(()=>[e(zt)]),default:n(()=>[k(g(t(r)("file.attribute.atime"))+" ",1)]),_:1}),e(t(N),{trigger:t(o)?"click":"hover"},{trigger:n(()=>[e(t(q),{quaternary:"",circle:"",size:"small",focusable:!1},{default:n(()=>[e(ge)]),_:1})]),default:n(()=>[c("span",null,g(t(r)("file.attribute.atimeExplanation")),1)]),_:1},8,["trigger"])]),_:1}),lr,e(t(M),{size:1},{default:n(()=>[e(S,{type:"error",bordered:!1},{icon:n(()=>[e(Mt)]),default:n(()=>[k(g(t(r)("file.attribute.ctime"))+" ",1)]),_:1}),e(t(N),{trigger:t(o)?"click":"hover"},{trigger:n(()=>[e(t(q),{quaternary:"",circle:"",size:"small",focusable:!1},{default:n(()=>[e(ge)]),_:1})]),default:n(()=>[c("span",null,g(t(r)("file.attribute.ctimeExplanation")),1)]),_:1},8,["trigger"])]),_:1})]),pe.value?(a(),y("td",cr,[e(J,{text:"",size:"small",width:130,sharp:!1}),rr,e(J,{text:"",size:"small",width:130,sharp:!1}),ur,e(J,{text:"",size:"small",width:130,sharp:!1}),_r,e(J,{text:"",size:"small",width:130,sharp:!1})])):(a(),y("td",or,[e(S,{bordered:!1},{default:n(()=>[k(g(T.value?T.value.birthtime:"Unknown"),1)]),_:1}),ar,e(S,{bordered:!1},{default:n(()=>[k(g(T.value?T.value.mtime:"Unknown"),1)]),_:1}),sr,e(S,{bordered:!1},{default:n(()=>[k(g(T.value?T.value.atime:"Unknown"),1)]),_:1}),ir,e(S,{bordered:!1},{default:n(()=>[k(g(T.value?T.value.ctime:"Unknown"),1)]),_:1})]))])])]),_:1})]),_:1},8,["show","title"])]),_:1},512)])}}});const $r=$(dr,[["__scopeId","data-v-e5f7ba27"]]);export{$r as default};
