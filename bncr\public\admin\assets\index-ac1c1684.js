import{d as v,a1 as g,ad as D,K as h,o as k,p as w,w as e,e as n,f as u,aE as r,H as x,ar as C,bB as H,j as M,aq as S}from"./index-b380aaed.js";const q=v({__name:"index",setup(y){const{routerPush:_}=g(),p=D(),o=h("");function d(){_({name:r("function_tab-detail"),query:{name:"abc"},hash:"#DEMO_HASH"})}function i(a){_({name:r("function_tab-multi-detail"),query:{name:"abc",num:a},hash:"#DEMO_HASH"})}function m(){var a;o.value?p.setActiveTabTitle(o.value):(a=window.$message)==null||a.warning("请输入要设置的标题名称")}return(a,t)=>{const l=x,f=C,b=H,c=M,T=S;return k(),w(c,{vertical:!0,size:16},{default:e(()=>[n(T,{title:"Tab Home",bordered:!1,size:"small",class:"rounded-8px shadow-sm"},{default:e(()=>[n(c,{vertical:!0,size:12},{default:e(()=>[n(l,{onClick:d},{default:e(()=>[u("跳转Tab Detail")]),_:1}),n(l,{onClick:t[0]||(t[0]=s=>i(1))},{default:e(()=>[u("跳转Tab Multi Detail 1")]),_:1}),n(l,{onClick:t[1]||(t[1]=s=>i(2))},{default:e(()=>[u("跳转Tab Multi Detail 2")]),_:1}),n(b,null,{default:e(()=>[n(f,{value:o.value,"onUpdate:value":t[2]||(t[2]=s=>o.value=s)},null,8,["value"]),n(l,{type:"primary",onClick:m},{default:e(()=>[u("设置当前Tab页标题")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})}}});export{q as default};
