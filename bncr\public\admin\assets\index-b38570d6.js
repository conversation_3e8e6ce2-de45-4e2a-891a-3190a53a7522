var Ie=Object.defineProperty,Ve=Object.defineProperties;var Le=Object.getOwnPropertyDescriptors;var ee=Object.getOwnPropertySymbols;var Be=Object.prototype.hasOwnProperty,Pe=Object.prototype.propertyIsEnumerable;var te=(u,r,d)=>r in u?Ie(u,r,{enumerable:!0,configurable:!0,writable:!0,value:d}):u[r]=d,ne=(u,r)=>{for(var d in r||(r={}))Be.call(r,d)&&te(u,d,r[d]);if(ee)for(var d of ee(r))Pe.call(r,d)&&te(u,d,r[d]);return u},se=(u,r)=>Ve(u,Le(r));var D=(u,r,d)=>new Promise((K,L)=>{var B=x=>{try{z(d.next(x))}catch(A){L(A)}},$=x=>{try{z(d.throw(x))}catch(A){L(A)}},z=x=>x.done?K(x.value):Promise.resolve(x.value).then(B,$);z((d=d.apply(u,r)).next())});import{aG as V,o,c as h,a as i,d as Fe,ak as je,bl as He,dr as Ne,K as m,L as Re,k as Ke,al as Ee,M as Ge,e as s,w as e,h as le,p as _,am as ae,q as b,f as v,F as G,l as T,b as U,x as q,an as Te,ar as qe,H as Oe,ds as Xe,j as Je,bq as Qe,ao as We,bm as Ye,as as Ze,aq as et,z as tt,A as nt,a8 as st,dt as lt,bs as at,bu as ot,I as it,du as rt,B as ut,aI as ct,C as pt,D as dt}from"./index-b380aaed.js";import{_ as _t}from"./search2-line-b583daf2.js";import{e as ft}from"./file-b561a0a1.js";const mt=()=>V.get("/getSubUrlArray"),gt=u=>V.post("/setSubUrlArray",u),ht=u=>V.post("/getSubscriptionUrl",u),yt=u=>V.post("/getSubUrlData",u),vt=u=>V.post("/getSubUrlPluginsContent",u),bt=u=>V.post("/savePluginsContent",u),xt={class:"inline-block",viewBox:"0 0 1024 1024",width:"1em",height:"1em"},kt=i("path",{fill:"currentColor",d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"},null,-1),wt=[kt];function Ct(u,r){return o(),h("svg",xt,wt)}const Ut={name:"ant-design-delete-outlined",render:Ct},$t={class:"inline-block",viewBox:"0 0 1024 1024",width:"1em",height:"1em"},zt=i("path",{fill:"currentColor",d:"M624 706.3h-74.1V464c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v242.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.7a8 8 0 0 0 12.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9z"},null,-1),St=i("path",{fill:"currentColor",d:"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7c-23.5-24.2-36-56.8-34.9-90.6c.9-26.4 9.9-51.2 26.2-72.1c16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9l13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4a245.6 245.6 0 0 1 52.4-49.9c41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9c15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5l37.8 10C846.1 454.5 884 503.8 884 560c0 33.1-12.9 64.3-36.3 87.7a123.07 123.07 0 0 1-87.6 36.3H720c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C870.5 760 960 670.5 960 560c0-92.7-63.1-170.7-148.6-193.3z"},null,-1),Dt=[zt,St];function At(u,r){return o(),h("svg",$t,Dt)}const Mt={name:"ant-design-cloud-download-outlined",render:At},It={class:"inline-block",viewBox:"0 0 1024 1024",width:"1em",height:"1em"},Vt=i("path",{fill:"currentColor",d:"M868 545.5L536.1 163a31.96 31.96 0 0 0-48.3 0L156 545.5a7.97 7.97 0 0 0 6 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"},null,-1),Lt=[Vt];function Bt(u,r){return o(),h("svg",It,Lt)}const Pt={name:"ant-design-arrow-up-outlined",render:Bt},Ft={key:0,style:{"font-size":"20px","font-weight":"bold"}},jt={key:1,style:{"font-size":"20px","font-weight":"bold"}},Ht={style:{display:"flex","justify-content":"flex-end"}},Nt=i("span",{style:{"font-size":"18px","font-weight":"bold"}},"插件类型",-1),Rt={style:{"margin-left":"1px"}},Kt=i("span",{style:{"margin-right":"1px"}},null,-1),Et=i("span",{style:{"font-size":"18px","font-weight":"bold"}},"用途分类",-1),Gt={style:{"margin-left":"1px"}},Tt=i("span",{style:{"margin-right":"1px"}},null,-1),qt={key:1},Ot=i("span",{style:{"margin-left":"1px"}},"没有数据",-1),Xt=[Ot],Jt={style:{"font-size":"20px","font-weight":"bold",margin:"auto"}},Qt={class:"text-12px font-medium ml-3px"},Wt={class:"text-12px font-medium m-auto"},Yt=i("span",{class:"text-12px font-medium m-auto"},"/",-1),Zt={class:"text-15px font-medium m-auto"},en={style:{"text-align":"center","max-width":"300px"}},tn={style:{position:"absolute",top:"20px",right:"20px"}},nn=i("span",null,"更新",-1),sn=i("span",null,"安装",-1),ln=i("span",null,"卸载",-1),an={class:"text-primary",style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}},on={style:{"text-align":"center","line-height":"2"}},rn={style:{display:"flex","align-items":"center",width:"100%"}},un=i("p",{style:{"font-size":"18px","font-weight":"bold"}}," 生成端到端订阅链接",-1),cn=i("p",{style:{"font-size":"14px","font-weight":"bold"}}," 注意:",-1),pn=i("p",{style:{"font-size":"14px","font-weight":"bold"}}," 1.填写的地址格式为http(s)://ip或域名:端口,结尾不要带/,请确保能从公网访问到该地址.",-1),dn=i("p",{style:{"font-size":"14px","font-weight":"bold"}}," 2.采用GitHub发布插件模式,不需要生成订阅链接,否则会有不可预估的错误.",-1),_n={key:0},fn={key:1},bn=Fe({__name:"index",setup(u){const r=je(),d=He(),K=Ne(),L=Te.isMobile,B=m(!0),$=m(!1),z=m("订阅链接");function x(n){z.value=n}const A=m("获取数据中,速度快慢完全取决于您的Bncr所在网络,请确保所在网络能连通Github....");function oe(n){d.warning({title:"警告",content:"确定卸载? 此操作不可逆, 卸载后本地文件将会一起删除并且无法找回!",positiveText:"确定",negativeText:"取消",onPositiveClick:()=>{he(n)},onNegativeClick:()=>{r.warning("已取消")}})}const M=m({inputValue:"",leftSelectSystemValue:"",systemClassification:[{label:"全部",key:"isAll"},{label:"适配器",key:"isA"},{label:"会话插件",key:"isC"},{label:"需认证插件",key:"isAu"},{label:"模块",key:"isM"},{label:"服务",key:"isS"}],leftSelectDevValue:"",devClassification:[]});function mn(n){}const P=m(""),ie=m(0),F=m(1);function re(n){var l;(l=O.value)==null||l.scrollTo({top:0}),F.value=n}const S=m([]),E=m(!1);Re(()=>S.value,n=>{n.find(a=>{if(!a.remarks||!a.subUrl||!a.type)return!0})?E.value=!0:E.value=!1},{deep:!0});function ue(){return D(this,null,function*(){const n=yield gt({subArray:S.value});n.data?(r.success("保存成功,请刷新页面获取订阅插件"),S.value=n.data||[]):r.error("保存失败"),$.value=!1})}function ce(){return D(this,null,function*(){const n=yield mt();S.value=n.data||[],$.value=!1,yield _e()})}const f=m({url:"",author:"",team:"",subscriptionUrl:""});function pe(){return D(this,null,function*(){const n=yield ht(f.value);n.data?(f.value.subscriptionUrl=n.data,r.success("获取订阅链接成功")):r.error("获取订阅链接成功失败")})}function de(){return{type:null,remarks:"",subUrl:""}}const O=m();function X(){M.value.inputValue=P.value}K.warning({title:"插件市场",content:"插件市场还在开发中...当前分类筛选不可用~",duration:3e3,closable:!0});const k=m([]);function _e(){return D(this,null,function*(){var g,w;const n=yield yt({subArray:S.value});if((g=n.error)!=null&&g.msg){A.value=n.error.msg;return}const l=[],a=(w=n.data)==null?void 0:w.map(c=>{var I;if((I=c==null?void 0:c.error)!=null&&I.error){let y=`订阅remarks:[${c.remarks}] 时发生异常: ${c.error.msg||"..."}`;return r.error(y,{duration:1e4}),console.warn(y),c.pluginsList}return c.pluginsList.length||(r.warning(`订阅remarks:[${c.remarks}] 订阅列表为空`),console.warn(`订阅remarks:[${c.remarks}] 订阅列表为空`)),l.push(c.classification),c.pluginsList});if(k.value=(a||[]).flat(),!k.value.length){A.value="未在订阅链接中获取到任何插件...";return}let p=[];l.flat().map(c=>{p.push({label:c,key:c})}),M.value.devClassification=p,fe(n.data||[])})}const J=m([]);function fe(n){return D(this,null,function*(){const l=[...n];for(const a of l){const p={type:a.type,remarks:a.remarks,subUrl:a.subUrl,classification:a.classification,pluginsIds:[],subMD5:a.subMD5};for(const g of a.pluginsList)"error"in g||p.pluginsIds.push(g.id);J.value.push(p)}})}const me=Ke(()=>{const n=(F.value-1)*20,l=Math.min(n+20,k.value.length);let a=k.value;if(M.value.inputValue){const p=M.value.inputValue||"";a=k.value.filter(w=>Object.keys(w).some(c=>{const I={适配器:"isAdapter",模块:"isMod",需认证:"isAuthentication",加密:"isEncPlugin",服务:"isService"};if(Object.keys(I).includes(p||""))return w[I[p]];if(["fileDir","isAdapter","isMod","isService","isAuthentication","isInstall","isUpdate","id","isEncPlugin"].includes(c))return!1;const y=w[c];return typeof y=="string"?y.toLowerCase().includes(p.toLowerCase()):!1}))}return ie.value=a.length,a.slice(n,l)});function Q(n,l="安装插件"){return D(this,null,function*(){r.success(`${l}: ${n.filename}`,{showIcon:!1});const a={type:"sub",remarks:"",subUrl:""};for(const c of J.value)if(c.subMD5===n.subMD5){a.remarks=c.remarks,a.subUrl=c.subUrl,a.type=c.type;break}if(!a.remarks||!a.subUrl){r.error(`${l}失败,意外情况导致失败, -1301.01`);return}const p=yield vt(se(ne({},a),{id:n.id}));if(p.error)return;const g=p.data||"";(yield bt({content:g,noBncrDataPath:n.fileDir,errMsg:`${l}:${n.filename} 失败!`,okMsg:`${l}:${n.filename} 成功!`})).error||(r.success(`${l}:${n.filename} 成功!`),n.isUpdate=!1,n.isInstall=!0)})}function ge(n){Q(n,"更新插件")}function he(n){return D(this,null,function*(){(yield ft({path:n.fileDir,noBncrDataPath:n.fileDir})).error||(r.success(n.filename+" 卸载成功"),n.isInstall=!1)})}const W=m(),j=m(500);return Ee(W,n=>{const l=n[0],{width:a,height:p}=l.contentRect;j.value=p}),Ge(()=>{ce()}),(n,l)=>{const a=qe,p=Oe,g=Xe,w=_t,c=Je,I=Qe,y=We,ye=Ye,ve=Pt,H=Ze,be=Mt,xe=Ut,N=et,ke=tt,we=nt,Ce=st,Ue=lt,$e=at,ze=ot,Se=it,De=rt,Y=ut,R=ct,Ae=pt,Me=dt;return o(),h("div",null,[s(N,{ref_key:"NcardDom",ref:W,size:"small",bordered:!1,class:"h-full rounded-8px shadow-sm"},{default:e(()=>[s(g,{style:{display:"flex","justify-content":"space-between"}},{default:e(()=>[le(L)?(o(),h("span",jt)):(o(),h("span",Ft,"插件市场")),i("div",Ht,[k.value.length?(o(),_(a,{key:0,placeholder:"搜索",value:P.value,"onUpdate:value":l[0]||(l[0]=t=>P.value=t),style:{"max-width":"150px"},onKeydown:ae(X,["enter"])},null,8,["value","onKeydown"])):b("",!0),s(p,{ghost:"",type:"success",style:{"margin-left":"5px"},onClick:l[1]||(l[1]=t=>$.value=!0)},{icon:e(()=>[]),default:e(()=>[v(" 订阅 ")]),_:1})])]),_:1}),k.value.length?(o(),_(N,{key:0,bordered:!0,style:q({height:j.value*.9+"px","margin-top":"5px"}),"content-style":"padding: 0px 0px 0px 0px; height:100% ;"},{default:e(()=>[s(ze,{"has-sider":"",style:{height:"100%"},"content-style":"height: 100%"},{default:e(()=>[le(L)?b("",!0):(o(),_(I,{key:0,class:"scripts-container","content-style":"display: flex; flex-direction: column; height: 100%; padding: .8em 0 0 .8em",width:"150",bordered:"","collapsed-width":0,"show-trigger":"arrow-circle","show-collapsed-content":!1,"native-scrollbar":!1,collapsed:B.value,"on-update:collapsed":()=>{B.value=!B.value}},{default:e(()=>[s(g,null,{default:e(()=>[k.value.length?(o(),_(a,{key:0,placeholder:"搜索",value:P.value,"onUpdate:value":l[2]||(l[2]=t=>P.value=t),style:{"max-width":"98%"},onKeydown:ae(X,["enter"])},{prefix:e(()=>[s(w)]),_:1},8,["value","onKeydown"])):b("",!0),s(c,{vertical:"",size:[0,10]},{default:e(()=>[Nt,(o(!0),h(G,null,T(M.value.systemClassification,t=>(o(),h("div",{key:t.key,style:{width:"130px",display:"flex","justify-content":"space-between"}},[s(p,{text:"",type:"tertiary",class:"text-15px",onClick:C=>void 0},{default:e(()=>[i("span",Rt,U(t.label),1)]),_:2},1032,["onClick"]),Kt]))),128))]),_:1}),s(c,{vertical:"",class:"mt-20px",size:[0,10]},{default:e(()=>[Et,M.value.devClassification.length?(o(!0),h(G,{key:0},T(M.value.devClassification,t=>(o(),h("div",{key:t.key,style:{width:"130px",display:"flex","justify-content":"space-between"}},[s(p,{text:"",type:"tertiary",class:"text-15px",onClick:C=>void 0},{default:e(()=>[i("span",Gt,U(t.label),1)]),_:2},1032,["onClick"]),Tt]))),128)):(o(),h("div",qt,Xt))]),_:1})]),_:1})]),_:1},8,["collapsed","on-update:collapsed"])),s($e,{"native-scrollbar":!1},{default:e(()=>[s(Ce,{ref_key:"scrollbarRef",ref:O},{default:e(()=>[i("div",{style:q([{height:j.value*.83+"px"},{"margin-top":"5px",padding:"5px 10px 10px 10px"}])},[s(we,{"x-gap":"16","y-gap":"16",cols:"12","item-responsive":!0},{default:e(()=>[(o(!0),h(G,null,T(me.value,(t,C)=>(o(),_(ke,{key:C,span:"0:12 400:6 640:4 1100:3"},{default:e(()=>[s(N,{size:"small",embedded:!0,bordered:"",class:"h-180px rounded-15px shadow-sm"},{default:e(()=>[i("span",Jt,U(t.filename),1),i("span",Qt,U("v"+t.version),1),s(g,{size:[1,0],inline:"",style:{position:"absolute",bottom:"8px",right:"20px","text-align":"right"}},{default:e(()=>[i("span",Wt,U(t.team),1),Yt,i("span",Zt,U(t.author),1)]),_:2},1024),s(g,{size:[4,0],style:{position:"absolute",left:"20px",bottom:"10px"}},{default:e(()=>[t.isChatPlugin?(o(),_(y,{key:0,bordered:!1,type:"success",size:"small"},{default:e(()=>[v("会话")]),_:1})):b("",!0),t.isMod?(o(),_(y,{key:1,bordered:!1,type:"warning",size:"small"},{default:e(()=>[v("模块")]),_:1})):b("",!0),t.isAdapter?(o(),_(y,{key:2,bordered:!1,color:{color:"#3B2E7E",textColor:"#F8F8F8",borderColor:"#0677B1"},size:"small"},{default:e(()=>[v("适配器")]),_:1})):b("",!0),t.isService?(o(),_(y,{key:3,bordered:!1,type:"info",size:"small"},{default:e(()=>[v("服务")]),_:1})):b("",!0),t.isEncPlugin?(o(),_(y,{key:4,bordered:!1,type:"error",size:"small"},{default:e(()=>[v("加密")]),_:1})):b("",!0),t.isAuthentication?(o(),_(y,{key:5,bordered:!1,color:{color:"#CB73F8",textColor:"#F8F8F8",borderColor:"#992DCF"},size:"small"},{default:e(()=>[v("需认证")]),_:1})):b("",!0)]),_:2},1024),s(g,{style:{"font-size":"12px","margin-top":"8px"}},{default:e(()=>[s(ye,{"line-clamp":3,style:{"max-width":"100%"}},{tooltip:e(()=>[i("div",en,U(t.description),1)]),default:e(()=>[v(U(t.description||"这个作者很懒,啥说明都没写!")+" ",1)]),_:2},1024)]),_:2},1024),i("div",tn,[t.isUpdate?(o(),_(H,{key:0,trigger:"hover"},{trigger:e(()=>[s(p,{strong:"",secondary:"",circle:"",size:"small",type:"error",onClick:Z=>ge(t)},{default:e(()=>[s(ve,{class:"text-15px"})]),_:2},1032,["onClick"])]),default:e(()=>[nn]),_:2},1024)):t.isInstall?(o(),_(H,{key:2,trigger:"hover"},{trigger:e(()=>[s(p,{strong:"",tertiary:"",circle:"",size:"small",type:"default",onClick:Z=>oe(t)},{default:e(()=>[s(xe,{class:"text-15px"})]),_:2},1032,["onClick"])]),default:e(()=>[ln]),_:2},1024)):(o(),_(H,{key:1,trigger:"hover"},{trigger:e(()=>[s(p,{strong:"",secondary:"",circle:"",size:"small",type:"primary",onClick:Z=>Q(t)},{default:e(()=>[s(be,{class:"text-15px"})]),_:2},1032,["onClick"])]),default:e(()=>[sn]),_:2},1024))])]),_:2},1024)]),_:2},1024))),128))]),_:1})],4)]),_:1},512),s(Ue,{page:F.value,"onUpdate:page":[l[3]||(l[3]=t=>F.value=t),re],"page-count":Math.ceil(k.value.length/20),"page-slot":7,style:{"margin-top":"8px",display:"flex","justify-content":"center"}},null,8,["page","page-count"])]),_:1})]),_:1})]),_:1},8,["style"])):(o(),_(N,{key:1,bordered:!0,style:q({height:j.value*.9+"px","margin-top":"5px"}),"content-style":"padding: 0px 0px 0px 0px; height:100% ;"},{default:e(()=>[i("div",an,[i("p",on,U(A.value),1)])]),_:1},8,["style"]))]),_:1},512),s(Me,{show:$.value,"onUpdate:show":l[10]||(l[10]=t=>$.value=t),preset:"dialog","mask-closable":!1,title:"订阅插件",style:{width:"760px"}},{action:e(()=>[s(p,{type:"default",onClick:l[9]||(l[9]=t=>$.value=!1)},{default:e(()=>[v(" 取消 ")]),_:1}),s(H,{trigger:"hover"},{trigger:e(()=>[z.value==="生成订阅链接"?(o(),_(p,{key:0,type:"primary",onClick:pe,disabled:!f.value.url||!f.value.team||!f.value.author},{default:e(()=>[v(" 生成订阅链接 ")]),_:1},8,["disabled"])):z.value==="订阅链接"?(o(),_(p,{key:1,type:"primary",onClick:ue,disabled:!S.value.length||E.value},{default:e(()=>[v(" 保存修改 ")]),_:1},8,["disabled"])):b("",!0)]),default:e(()=>[!f.value.url||!f.value.team||!f.value.author?(o(),h("span",_n," 请完善必填项 ")):(o(),h("span",fn," 保存修改项 "))]),_:1})]),default:e(()=>[s(Ae,{size:"large","justify-content":"space-evenly","default-value":z.value,"onUpdate:value":x},{default:e(()=>[s(Y,{name:"订阅链接",tab:"订阅链接"},{default:e(()=>[s(De,{value:S.value,"onUpdate:value":l[4]||(l[4]=t=>S.value=t),"on-create":de},{"create-button-default":e(()=>[v(" 添加订阅数据 ")]),default:e(({value:t})=>[i("div",rn,[s(Se,{style:{"max-width":"80px"},"consistent-menu-width":!1,value:t.type,"onUpdate:value":C=>t.type=C,placeholder:"订阅模式",options:[{label:"sub",value:"sub"},{label:"github",value:"github"}]},null,8,["value","onUpdate:value"]),s(a,{type:"textarea",autosize:{minRows:1,maxRows:1},placeholder:"订阅链接",value:t.subUrl,"onUpdate:value":C=>t.subUrl=C,style:{"margin-right":"5px","margin-left":"5px"}},null,8,["value","onUpdate:value"]),s(a,{placeholder:"备注信息",value:t.remarks,"onUpdate:value":C=>t.remarks=C,style:{"margin-right":"5px","max-width":"100px"}},null,8,["value","onUpdate:value"])])]),_:1},8,["value"])]),_:1}),s(Y,{name:"生成订阅链接",tab:"生成订阅链接"},{default:e(()=>[un,s(R,{label:"无界地址*"},{default:e(()=>[s(a,{value:f.value.url,"onUpdate:value":l[5]||(l[5]=t=>f.value.url=t)},null,8,["value"])]),_:1}),s(R,{label:"团队/组织名*"},{default:e(()=>[s(a,{value:f.value.team,"onUpdate:value":l[6]||(l[6]=t=>f.value.team=t)},null,8,["value"])]),_:1}),s(R,{label:"作者名*"},{default:e(()=>[s(a,{value:f.value.author,"onUpdate:value":l[7]||(l[7]=t=>f.value.author=t)},null,8,["value"])]),_:1}),s(R,{label:"订阅链接"},{default:e(()=>[s(a,{value:f.value.subscriptionUrl,"onUpdate:value":l[8]||(l[8]=t=>f.value.subscriptionUrl=t),placeholder:"创建成功后订阅链接会显示在此处",disabled:!1,type:"textarea"},null,8,["value"])]),_:1}),cn,pn,dn]),_:1})]),_:1},8,["default-value"])]),_:1},8,["show"])])}}});export{bn as default};
