var h=Object.defineProperty;var V=(i,e,s)=>e in i?h(i,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[e]=s;var c=(i,e,s)=>(V(i,typeof e!="symbol"?e+"":e,s),s);var T=(i,e,s)=>new Promise((r,t)=>{var a=n=>{try{o(s.next(n))}catch(l){t(l)}},_=n=>{try{o(s.throw(n))}catch(l){t(l)}},o=n=>n.done?r(n.value):Promise.resolve(n.value).then(a,_);o((s=s.apply(i,e)).next())});import{K as d,aB as u,aC as C,aD as P}from"./index-b380aaed.js";let I=!1;const E=class E{constructor(){c(this,"ws",null);c(this,"callBackMapping",{});c(this,"connected",d(!1));c(this,"sendRetryCount",0);c(this,"connectRetryCount",0);c(this,"maxRetries",60)}static get Instance(){return this.instance||(this.instance=new E,this.instance.connect()),this.instance}connect(){if(!window.WebSocket)return console.log("您的浏览器不支持WebSocket");let{ws:e}=u({VITE_BASE_URL:"/admin",VITE_APP_NAME:"BncrAdmin",VITE_APP_TITLE:"BncrAdmin",VITE_APP_DESC:"基于nodejs的会话式bot框架，它拥有无限可能，我愿称之为《无界》",VITE_AUTH_ROUTE_MODE:"static",VITE_ROUTE_HOME_PATH:"/dashboard",VITE_ICON_PREFIX:"icon",VITE_ICON_LOCAL_PREFIX:"icon-local",VITE_VISUALIZER:"N",VITE_COMPRESS:"N",VITE_COMPRESS_TYPE:"gzip",VITE_PWA:"N",VITE_PROD_MOCK:"N",VITE_SERVICE_ENV:"prod",BASE_URL:"./",MODE:"production",DEV:!1,PROD:!0,SSR:!1});const{VITE_SERVICE_ENV:s="dev"}={VITE_BASE_URL:"/admin",VITE_APP_NAME:"BncrAdmin",VITE_APP_TITLE:"BncrAdmin",VITE_APP_DESC:"基于nodejs的会话式bot框架，它拥有无限可能，我愿称之为《无界》",VITE_AUTH_ROUTE_MODE:"static",VITE_ROUTE_HOME_PATH:"/dashboard",VITE_ICON_PREFIX:"icon",VITE_ICON_LOCAL_PREFIX:"icon-local",VITE_VISUALIZER:"N",VITE_COMPRESS:"N",VITE_COMPRESS_TYPE:"gzip",VITE_PWA:"N",VITE_PROD_MOCK:"N",VITE_SERVICE_ENV:"prod",BASE_URL:"./",MODE:"production",DEV:!1,PROD:!0,SSR:!1};s==="prod"&&(e=`${window.location.protocol==="https:"?"wss":"ws"}://${window.location.host}${e}`),console.log("链接:",e),this.ws=new WebSocket(e),this.ws.onopen=()=>{console.log("WebSocket 链接成功"),this.connected.value=!0,this.connectRetryCount=0},this.ws.onclose=()=>{console.log("断开服务端链接"),this.connected.value=!1,this.ws=null,this.connectRetryCount<this.maxRetries?(this.connectRetryCount++,console.log(`尝试重连次数: ${this.connectRetryCount}`),this.connect()):(E.instance=null,console.log("重连失败次数达到上限，停止重连"))},this.ws.onmessage=r=>T(this,null,function*(){var a,_,o,n;let t;try{t=JSON.parse(r.data)}catch(l){return console.error("消息解析失败",r.data,l)}if(!(t!=null&&t.stats)){console.log("ws获取数据错误:"+t.message),(_=(a=window==null?void 0:window.$message)==null?void 0:a.error)==null||_.call(a,"ws获取数据错误:"+t.message),t.code===3001&&!I&&(I=!0,(yield C({}))&&(console.log("已刷新token"),(n=(o=window==null?void 0:window.$message)==null?void 0:o.success)==null||n.call(o,"已刷新token")),I=!1);return}t!=null&&t.type&&this.callBackMapping[t.type]?this.callBackMapping[t.type](t):console.log(t,"未识别的服务端消息")})}registerCallBack(e,s){this.callBackMapping[e]=s}unRegisterCallBack(e){delete this.callBackMapping[e]}send(e){if(this.ws&&this.connected.value){this.sendRetryCount=0;try{return e.token=P.get("token"),this.ws.send(JSON.stringify(e)),!0}catch(s){return console.error("发送消息失败,数据结构有误!",s),!1}}else this.sendRetryCount++,this.sendRetryCount<30&&setTimeout(()=>{this.send(e)},this.sendRetryCount*200)}closeWs(){this.ws&&this.ws.close(),console.log("ws已关闭..")}};c(E,"instance");let R=E;export{R as S};
