var N=(B,i,d)=>new Promise((f,h)=>{var l=m=>{try{b(d.next(m))}catch(c){h(c)}},w=m=>{try{b(d.throw(m))}catch(c){h(c)}},b=m=>m.done?f(m.value):Promise.resolve(m.value).then(l,w);b((d=d.apply(B,i)).next())});import{_ as Q}from"./content-save-check-58cc70d2.js";import{V as W}from"./vue3-form-naive.esm-2d045f5d.js";import{d as S,K as D,ak as q,M,o as k,p as T,w as r,e as n,a,b as X,h as _,c as z,l as G,f as U,F as H,an as F,aG as y,I as P,ar as O,i as E,j,H as A,bh as K,z as R,A as J,q as L,B as Y,C as Z,aq as ee,$ as te,bo as ne}from"./index-b380aaed.js";const oe={style:{"text-align":"center"}},ae=a("th",null,"平台",-1),le=a("th",null,"群聊ID",-1),se={style:{"white-space":"pre-wrap"}},re=a("th",null,"备注",-1),ue=a("th",null,"操作",-1),ie={style:{"text-align":"center"}},ce=S({__name:"listenGroupList",setup(B){const i=F.isMobile,d=D([{label:"qq",value:"qq"},{label:"tg",value:"tg"},{label:"HumanTG",value:"HumanTG"}]),f=D([]),h=(o,e)=>{y.post("/delListenServiceData",e).then(t=>{var u;t.data?(l.success("删除成功"),f.value.splice(o,1)):l.error("删除失败,"+((u=t.error)==null?void 0:u.msg)||"")})},l=q();function w(){y.get("/getAdapterInfo").then(o=>{var e;if(o.data){let t=[];for(const u of o.data.name)t.push({label:u,value:u});d.value=t}else l.error(((e=o.error)==null?void 0:e.msg)||"错误")}),y.get("/getListenServiceData").then(o=>{var e;o.data?f.value=o.data:l.error(((e=o.error)==null?void 0:e.msg)||"错误")})}function b(o){y.post("/setListenServiceData",o).then(e=>{var t;e.data?l.success("保存成功"):l.error("保存失败,"+((t=e.error)==null?void 0:t.msg))})}function m(){return{platform:null,groupID:null,reply:!0,listen:!0,remarks:null}}function c(o,e){}return M(()=>{w()}),(o,e)=>{const t=P,u=O,C=E,x=j,$=A,V=K,I=R,p=J;return k(),T(p,{cols:24,"item-responsive":""},{default:r(()=>[n(I,{span:"0:24 400:24 600:15 800:15"},{default:r(()=>[n(V,{bordered:!0,size:"small",striped:""},{default:r(()=>[a("thead",oe,[a("tr",null,[ae,le,a("th",se,X(_(i)?"监听回复":"监听 | 回复"),1),re,ue])]),a("tbody",ie,[(k(!0),z(H,null,G(f.value,(s,g)=>(k(),z("tr",{key:g},[a("td",null,[n(t,{size:_(i)?"tiny":"medium",placeholder:"选择平台",clearable:"",value:s.platform,"onUpdate:value":v=>s.platform=v,options:d.value,"consistent-menu-width":!1,filterable:""},null,8,["size","value","onUpdate:value","options"])]),a("td",null,[n(u,{size:_(i)?"tiny":"medium",value:s.groupID,"onUpdate:value":v=>s.groupID=v,type:"text",placeholder:"输入群聊id"},null,8,["size","value","onUpdate:value"])]),a("td",null,[n(x,{size:[5,1],justify:"center"},{default:r(()=>[n(C,{size:_(i)?"small":"medium",value:s.listen,"onUpdate:value":[v=>s.listen=v,v=>(s.listen,void 0)]},null,8,["size","value","onUpdate:value"]),n(C,{size:_(i)?"small":"medium",value:s.reply,"onUpdate:value":v=>s.reply=v,disabled:!s.listen},null,8,["size","value","onUpdate:value","disabled"])]),_:2},1024)]),a("td",null,[n(u,{size:_(i)?"tiny":"medium",value:s.remarks,"onUpdate:value":v=>s.remarks=v,type:"text",placeholder:"输入备注信息"},null,8,["size","value","onUpdate:value"])]),a("td",null,[n(x,{size:[5,1],justify:"center"},{default:r(()=>[n($,{type:"error",text:"",size:_(i)?"tiny":"small",ghost:"",round:"",onClick:v=>h(g,s)},{default:r(()=>[U(" 删除 ")]),_:2},1032,["size","onClick"]),n($,{type:"default",text:"",size:_(i)?"tiny":"small",ghost:"",round:"",onClick:v=>b(s)},{default:r(()=>[U(" 保存 ")]),_:2},1032,["size","onClick"])]),_:2},1024)])]))),128))])]),_:1}),n($,{type:"primary",dashed:"",class:"w-full mt-20px",onClick:e[0]||(e[0]=s=>f.value.push(m()))},{default:r(()=>[U(" + 添加数据 ")]),_:1})]),_:1})]),_:1})}}}),_e=a("thead",{style:{"text-align":"center"}},[a("tr",null,[a("th",null,"平台"),a("th",null,"个人ID"),a("th",null,"拉黑"),a("th",null,"备注"),a("th",null,"操作")])],-1),de={style:{"text-align":"center"}},pe=S({__name:"userBlackList",setup(B){const i=F.isMobile,d=D([{label:"qq",value:"qq"},{label:"tg",value:"tg"},{label:"HumanTG",value:"HumanTG"}]),f=D([]),h=(c,o)=>{y.post("/delUserBlackList",o).then(e=>{var t;e.data?(l.success("删除成功"),f.value.splice(c,1)):l.error("删除失败,"+((t=e.error)==null?void 0:t.msg)||"")})},l=q();function w(){y.get("/getAdapterInfo").then(c=>{var o;if(c.data){let e=[];for(const t of c.data.name)e.push({label:t,value:t});d.value=e}else l.error(((o=c.error)==null?void 0:o.msg)||"错误")}),y.get("/getUserBlackList").then(c=>{var o;c.data?f.value=c.data:l.error(((o=c.error)==null?void 0:o.msg)||"错误")})}function b(c){y.post("/setUserBlackList",c).then(o=>{var e;o.data?l.success("保存成功"):l.error("保存失败,"+((e=o.error)==null?void 0:e.msg))})}function m(){return{platform:null,userID:null,isBlack:!0,remarks:null}}return M(()=>{w()}),(c,o)=>{const e=P,t=O,u=E,C=j,x=A,$=K,V=R,I=J;return k(),T(I,{cols:24,"item-responsive":""},{default:r(()=>[n(V,{span:"0:24 400:24 600:15 800:15"},{default:r(()=>[n($,{bordered:!0,size:"small",striped:""},{default:r(()=>[_e,a("tbody",de,[(k(!0),z(H,null,G(f.value,(p,s)=>(k(),z("tr",{key:s},[a("td",null,[n(e,{size:_(i)?"tiny":"medium",placeholder:"选择平台",clearable:"",value:p.platform,"onUpdate:value":g=>p.platform=g,options:d.value,"consistent-menu-width":!1,filterable:""},null,8,["size","value","onUpdate:value","options"])]),a("td",null,[n(t,{size:_(i)?"tiny":"medium",value:p.userID,"onUpdate:value":g=>p.userID=g,type:"text",placeholder:"输入个人账户id"},null,8,["size","value","onUpdate:value"])]),a("td",null,[n(C,{size:[5,1],justify:"center"},{default:r(()=>[n(u,{size:_(i)?"small":"medium",value:p.isBlack,"onUpdate:value":g=>p.isBlack=g},null,8,["size","value","onUpdate:value"])]),_:2},1024)]),a("td",null,[n(t,{size:_(i)?"tiny":"medium",value:p.remarks,"onUpdate:value":g=>p.remarks=g,type:"text",placeholder:"输入备注信息"},null,8,["size","value","onUpdate:value"])]),a("td",null,[n(C,{size:[5,1],justify:"center"},{default:r(()=>[n(x,{type:"error",text:"",size:_(i)?"tiny":"small",ghost:"",round:"",onClick:g=>h(s,p)},{default:r(()=>[U(" 删除 ")]),_:2},1032,["size","onClick"]),n(x,{type:"default",text:"",size:_(i)?"tiny":"small",ghost:"",round:"",onClick:g=>b(p)},{default:r(()=>[U(" 保存 ")]),_:2},1032,["size","onClick"])]),_:2},1024)])]))),128))])]),_:1}),n(x,{type:"primary",dashed:"",class:"w-full mt-20px",onClick:o[0]||(o[0]=p=>f.value.push(m()))},{default:r(()=>[U(" + 添加数据 ")]),_:1})]),_:1})]),_:1})}}}),me={key:0},fe={key:0},he={key:0},ge=a("br",null,null,-1),ve=a("br",null,null,-1);const ze=S({__name:"index",setup(B){const i=F.isMobile,d=q(),f=te,h=D("系统配置"),l=D({jsonSchema:{type:"object",properties:{ToverifyUrl:{type:"string",title:"鉴权url",description:"设置自定义的鉴权url,默认系统自带",default:"http://wj.yanyuwangluo.cn:1201"},token:{type:"string",title:"鉴权token",description:"请输入从( https://t.me/red_Lights_Districts_Bot )获取的token:",default:""},sysLogOpen:{type:"boolean",title:"系统日志开关",description:"如果设置为关，则不显示例如插件匹配、运行等日志系统运行消息",default:!0},msgLogOpen:{type:"number",title:"控制台消息日志等级",description:"0:不显示任何收到消息的日志,1:显示全部消息日志,2:只显示未屏蔽的消息",default:1},developerMode:{type:"boolean",title:"开发者模式",description:"设置为关则不会实时重载插件的修改,开则监听插件改动,实时重载插件,如果不调试插件请关闭该功能进一步节省内存(可能也就能释放几M吧)",default:!1}}},nowData:{}});function w(e){switch(e){case"系统配置":y.get("/getSystemConfig").then(t=>{var u;t.data?l.value.nowData=t.data:d.error(((u=t.error)==null?void 0:u.msg)||"错误")});break;case"监听群列表":y.get("/getAll").then(t=>{var u;t.data?l.value.nowData=t.data:d.error(((u=t.error)==null?void 0:u.msg)||"错误")});break}}const b=D();function m(){return N(this,null,function*(){yield b.value.$$uiFormRef.validate(),y.post("/setSystemConfig",l.value.nowData).then(e=>{var t;e.data?d.success("保存成功,重启后生效改动"):d.error("保存失败,"+((t=e.error)==null?void 0:t.msg))})})}function c(e){h.value=e}const o={show:!0,okBtn:"保存",okBtnProps:{type:"primary"},cancelBtn:"取消",formItemAttrs:{"label-style":{backgroundColor:"#F8F8F8"}}};return M(()=>{w("系统配置")}),(e,t)=>{const u=Y,C=Z,x=ee,$=Q,V=A,I=ne,p=j;return k(),z("div",null,[n(p,{vertical:""},{default:r(()=>[n(x,{bordered:!1,class:"rounded-8px shadow-sm"},{default:r(()=>[n(C,{type:"line","default-value":h.value,animated:"","onUpdate:value":c},{default:r(()=>[n(u,{name:"系统配置",tab:"系统配置"},{default:r(()=>[h.value==="系统配置"?(k(),z("div",me,[n(_(W),{ref_key:"myForm",ref:b,style:{padding:"5px"},modelValue:l.value.nowData,"onUpdate:modelValue":t[0]||(t[0]=s=>l.value.nowData=s),schema:l.value.jsonSchema,formFooter:o,formProps:{layoutColumn:_(i)?1:2},onSubmit:m},null,8,["modelValue","schema","formProps"])])):L("",!0)]),_:1}),n(u,{name:"监听群列表",tab:"监听群列表"},{default:r(()=>[h.value==="监听群列表"?(k(),z("div",fe,[n(ce)])):L("",!0)]),_:1}),n(u,{name:"黑名单用户",tab:"黑名单用户"},{default:r(()=>[h.value==="黑名单用户"?(k(),z("div",he,[n(pe)])):L("",!0)]),_:1})]),_:1},8,["default-value"]),ge,ve]),_:1}),n(x,{bordered:!1,class:"rounded-8px shadow-sm"}),L("",!0)]),_:1})])}}});export{ze as default};
