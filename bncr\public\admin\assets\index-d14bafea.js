var g=(v,d,n)=>new Promise((p,l)=>{var y=a=>{try{o(n.next(a))}catch(s){l(s)}},x=a=>{try{o(n.throw(a))}catch(s){l(s)}},o=a=>a.done?p(a.value):Promise.resolve(a.value).then(y,x);o((n=n.apply(v,d)).next())});import{d as w,R as P,a9 as b,aQ as z,L as B,aR as I,o as _,c as N,e as u,w as e,a as c,f as t,b as S,h as i,P as f,p as r,q as h,aS as V,y as C,I as D,H as U,j as q,aq as A}from"./index-b380aaed.js";const j={class:"h-full"},E={class:"pb-12px"},H={class:"py-12px"},L={class:"py-12px"},T=w({__name:"index",setup(v){const d=P(),n=b(),{hasPermission:p}=z(),l=V;return B(()=>n.userInfo.userRole,()=>g(this,null,function*(){d.reloadPage()})),(y,x)=>{const o=C,a=D,s=U,R=q,k=A,m=I("permission");return _(),N("div",j,[u(k,{title:"权限切换",bordered:!1,class:"h-full rounded-8px shadow-sm"},{default:e(()=>[c("div",E,[u(o,{type:"primary",size:20},{default:e(()=>[t("当前用户的权限："+S(i(n).userInfo.userRole),1)]),_:1})]),u(a,{value:i(n).userInfo.userRole,class:"w-120px",size:"small",options:i(l),"onUpdate:value":i(n).updateUserRole},null,8,["value","options","onUpdate:value"]),c("div",H,[u(o,{type:"primary",size:20},{default:e(()=>[t("权限指令 v-permission")]),_:1})]),c("div",null,[f((_(),r(s,{class:"mr-12px"},{default:e(()=>[t("super可见")]),_:1})),[[m,"super"]]),f((_(),r(s,{class:"mr-12px"},{default:e(()=>[t("admin可见")]),_:1})),[[m,"admin"]]),f((_(),r(s,null,{default:e(()=>[t("admin和test可见")]),_:1})),[[m,["admin","user"]]])]),c("div",L,[u(o,{type:"primary",size:20},{default:e(()=>[t("权限函数 hasPermission")]),_:1})]),u(R,null,{default:e(()=>[i(p)("super")?(_(),r(s,{key:0},{default:e(()=>[t("super可见")]),_:1})):h("",!0),i(p)("admin")?(_(),r(s,{key:1},{default:e(()=>[t("admin可见")]),_:1})):h("",!0),i(p)(["admin","user"])?(_(),r(s,{key:2},{default:e(()=>[t("admin和user可见")]),_:1})):h("",!0)]),_:1})]),_:1})])}}});export{T as default};
