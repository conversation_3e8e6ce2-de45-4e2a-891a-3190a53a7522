import{d,o as a,p,w as e,a as y,aq as b,e as n,f as v,b as g,h as c,ao as S,aN as h,aO as f,c as k,l as E,F as x,aP as T,M as w,j as N}from"./index-b380aaed.js";const C=y("p",{class:"leading-24px"}," Soybean Admin 是一个基于 Vue3、Vite、Naive UI、TypeScript 的中后台解决方案，它使用了最新的前端技术栈，并提炼了典型的业务模型，页面，包括二次封装组件、动态菜单、权限校验、粒子化权限控制等功能，它可以帮助你快速搭建企业级中后台项目，相信不管是从新技术使用还是其他方面，都能帮助到你。 ",-1),D=d({name:"ProjectIntroduction",__name:"project-introduction",setup(t){return(s,r)=>{const l=b;return a(),p(l,{title:"关于",bordered:!1,size:"large",class:"rounded-8px shadow-sm"},{default:e(()=>[C]),_:1})}}}),z="bncr-admin",U="0.10.4",R="A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite3、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。",A={name:"Soybean",email:"<EMAIL>",url:"https://github.com/soybeanjs"},B="MIT",$="https://github.com/honghuangdc/soybean-admin",O={url:"https://github.com/honghuangdc/soybean-admin.git"},P={url:"https://github.com/honghuangdc/soybean-admin/issues"},q=["Vue","Vue3","admin","admin-template","vue-admin","vue-admin-template","Vite3","Vite","vite-admin","TypeScript","TS","NaiveUI","naive-ui","naive-admin","NaiveUI-Admin","naive-ui-admin","UnoCSS"],L={dev:"cross-env VITE_SERVICE_ENV=dev vite","dev:test":"cross-env VITE_SERVICE_ENV=test vite","dev:prod":"cross-env VITE_SERVICE_ENV=prod vite",build:"npm run typecheck && cross-env VITE_SERVICE_ENV=prod vite build","build:dev":"npm run typecheck && cross-env VITE_SERVICE_ENV=dev vite build","build:test":"npm run typecheck && cross-env VITE_SERVICE_ENV=test vite build","build:vercel":"cross-env VITE_HASH_ROUTE=Y VITE_VERCEL=Y vite build",preview:"vite preview",typecheck:"vue-tsc --noEmit --skipLibCheck",lint:"eslint . --fix",format:"soy prettier-write",commit:"soy git-commit",cleanup:"soy cleanup","update-pkg":"soy ncu",release:"soy release",tsx:"tsx",logo:"tsx ./scripts/logo.ts",prepare:"soy init-simple-git-hooks"},M={"@antv/data-set":"0.11.8","@antv/g2":"4.2.10","@better-scroll/core":"2.5.1","@form-create/naive-ui":"^3.1.25","@lljj/vue3-form-naive":"^1.19.1","@soybeanjs/vue-materials":"0.2.0","@vueuse/core":"10.5.0",axios:"1.5.1",clipboard:"2.0.11",colord:"2.9.3","copy-to-clipboard":"^3.3.3","crypto-js":"4.1.1",dayjs:"1.11.10",echarts:"5.4.3","form-data":"4.0.0","json-editor-vue3":"^1.0.8","lodash-es":"4.17.21",moment:"^2.29.4","monaco-editor":"^0.44.0","naive-ui":"2.37.3",pinia:"2.1.6","print-js":"1.6.0",qs:"6.11.2","socket.io-client":"4.7.2",swiper:"10.3.1","tdesign-vue-next":"^1.6.5","ua-parser-js":"1.0.36","unplugin-auto-import":"^0.17.2",vditor:"3.9.6","vite-plugin-html":"^3.2.0","vite-plugin-monaco-editor":"^1.1.0",vue:"3.3.4","vue-i18n":"9.5.0","vue-native-websocket":"^2.0.15","vue-router":"4.2.5","vue-types":"^5.1.1",vuedraggable:"4.1.0",wangeditor:"4.7.15",xgplayer:"3.0.9"},F={"@amap/amap-jsapi-types":"0.0.13","@iconify/json":"2.2.128","@iconify/vue":"4.1.1","@soybeanjs/cli":"0.7.4","@soybeanjs/vite-plugin-vue-page-route":"0.0.10","@types/bmapgl":"0.0.7","@types/crypto-js":"4.1.2","@types/node":"20.8.4","@types/qs":"6.9.8","@types/ua-parser-js":"0.7.37","@unocss/preset-uno":"0.56.5","@unocss/transformer-directives":"0.56.5","@unocss/vite":"0.56.5","@vitejs/plugin-vue":"4.4.0","@vitejs/plugin-vue-jsx":"3.0.2","cross-env":"7.0.3",eslint:"8.51.0","eslint-config-soybeanjs":"0.5.7",mockjs:"1.1.0","rollup-plugin-visualizer":"5.9.2",sass:"1.69.3","simple-git-hooks":"2.9.0",tsx:"3.13.0",typescript:"5.2.2","unplugin-icons":"0.17.0","unplugin-vue-components":"0.25.2",vite:"4.4.11","vite-plugin-compression":"0.5.1","vite-plugin-mock":"2.9.8","vite-plugin-progress":"0.0.7","vite-plugin-pwa":"0.16.5","vite-plugin-svg-icons":"2.0.1","vite-plugin-vue-devtools":"1.0.0-rc.5","vue-tsc":"1.8.19"},G={patchedDependencies:{"mockjs@1.1.0":"patches/<EMAIL>","@lljj/vue3-form-naive@1.19.1":"patches/@<EMAIL>"}},H={useSoybeanToken:!0},Y={name:z,version:U,description:R,author:A,license:B,homepage:$,repository:O,bugs:P,keywords:q,scripts:L,dependencies:M,devDependencies:F,pnpm:G,"#simple-git-hooks":{"##commit-msg":"pnpm soy git-commit-verify","##pre-commit":"pnpm typecheck && pnpm soy lint-staged"},soybean:H},_=Y;function V(t){const[s,r]=t;return{name:s,version:r}}const j={name:_.name,version:_.version,dependencies:Object.entries(_.dependencies).map(t=>V(t)),devDependencies:Object.entries(_.devDependencies).map(t=>V(t))},J=y("a",{class:"text-primary",href:"https://github.com/Anmours/Bncr",target:"_blank"},"Github地址",-1),W=y("a",{class:"text-primary",href:"https://admin.soybeanjs.cn",target:"_blank"},"预览地址",-1),K=d({name:"ProjectInfo",__name:"project-info",setup(t){const{version:s}=j,r="2024-11-24 15:44:24";return(l,m)=>{const u=S,i=h,o=f,I=b;return a(),p(I,{title:"项目信息",bordered:!1,size:"small",class:"rounded-8px shadow-sm"},{default:e(()=>[n(o,{"label-placement":"left",bordered:"",size:"small",column:2},{default:e(()=>[n(i,{label:"版本"},{default:e(()=>[n(u,{type:"primary"},{default:e(()=>[v(g(c(s)),1)]),_:1})]),_:1}),n(i,{label:"最后编译时间"},{default:e(()=>[n(u,{type:"primary"},{default:e(()=>[v(g(c(r)),1)]),_:1})]),_:1}),n(i,{label:"Github地址"},{default:e(()=>[J]),_:1}),n(i,{label:"预览地址"},{default:e(()=>[W]),_:1})]),_:1})]),_:1})}}}),Q=d({name:"ProDependency",__name:"pro-dependency",setup(t){const{dependencies:s}=j;return(r,l)=>{const m=h,u=f,i=b;return a(),p(i,{title:"生产环境依赖",bordered:!1,size:"small",class:"rounded-8px shadow-sm"},{default:e(()=>[n(u,{"label-placement":"left",bordered:"",size:"small"},{default:e(()=>[(a(!0),k(x,null,E(c(s),o=>(a(),p(m,{key:o.name,label:o.name},{default:e(()=>[v(g(o.version),1)]),_:2},1032,["label"]))),128))]),_:1})]),_:1})}}}),X=d({name:"DevDependency",__name:"dev-dependency",setup(t){const{devDependencies:s}=j;return(r,l)=>{const m=h,u=f,i=b;return a(),p(i,{title:"开发环境依赖",bordered:!1,size:"small",class:"rounded-8px shadow-sm"},{default:e(()=>[n(u,{"label-placement":"left",bordered:"",size:"small"},{default:e(()=>[(a(!0),k(x,null,E(c(s),o=>(a(),p(m,{key:o.name,label:o.name},{default:e(()=>[v(g(o.version),1)]),_:2},1032,["label"]))),128))]),_:1})]),_:1})}}}),ee=d({__name:"index",setup(t){return T(()=>{console.log("about page activated")}),w(()=>{console.log("about page mounted")}),(s,r)=>{const l=N;return a(),p(l,{vertical:!0,size:16},{default:e(()=>[n(c(D)),n(c(K)),n(c(Q)),n(c(X))]),_:1})}}});export{ee as default};
