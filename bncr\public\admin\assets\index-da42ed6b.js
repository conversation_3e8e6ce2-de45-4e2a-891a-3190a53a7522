import{d as i,Z as d,a1 as p,o as m,p as f,w as e,e as n,a as t,b as s,h as _,f as h,aE as b,H as v,j as x,aq as T}from"./index-b380aaed.js";const g=t("div",null,"当前路由的描述数据(meta)：",-1),N=t("div",null,"当前路由的查询数据(query)：",-1),z=i({__name:"index",setup(k){const a=d(),{routerPush:u}=p();function c(){u({name:b("function_tab")})}return(q,w)=>{const l=v,o=x,r=T;return m(),f(o,{vertical:!0,size:16},{default:e(()=>[n(r,{title:"Tab Detail",bordered:!1,size:"small",class:"rounded-8px shadow-sm"},{default:e(()=>[n(o,{vertical:!0,size:12},{default:e(()=>[g,t("div",null,s(_(a).meta),1),N,t("div",null,s(_(a).query),1),n(l,{onClick:c},{default:e(()=>[h("返回Tab")]),_:1})]),_:1})]),_:1})]),_:1})}}});export{z as default};
