var F=(s,o,e)=>new Promise((T,I)=>{var k=y=>{try{w(e.next(y))}catch(b){I(b)}},R=y=>{try{w(e.throw(y))}catch(b){I(b)}},w=y=>y.done?T(y.value):Promise.resolve(y.value).then(k,R);w((e=e.apply(s,o)).next())});import{_ as Oe,a as Ie,b as Ve,c as Le,d as Pe,e as qe,f as Ae,g as Je,h as Ue,i as He,j as Ke,k as We,l as Ye,m as Ge,n as Xe,o as Ze,p as Qe,q as en,r as nn,s as tn,t as on,u as an,v as ln,w as sn,x as cn,y as _n,z as rn,A as un,B as dn}from"./file-csv-0a1956f1.js";import{_ as pn}from"./search2-line-b583daf2.js";import{_ as fn}from"./content-save-check-58cc70d2.js";import{o as n,p as t,w as c,e as u,be as M,d as mn,ak as gn,u as vn,dr as yn,K as a,al as hn,M as bn,c as ue,h,j as K,H as W,az as de,q as z,a as B,b as N,f as xn,x as $n,an as kn,$ as wn,b2 as f,N as Cn,bm as Sn,bo as Dn,ar as jn,bp as Fn,a3 as Mn,a8 as Tn,bq as Rn,aw as zn,bs as Bn,bt as En,bu as Nn,aq as On}from"./index-b380aaed.js";import{_ as i}from"./_plugin-vue_export-helper-c27b6911.js";import{m as In,d as Vn,n as Ln}from"./file-b561a0a1.js";import{V as Pn}from"./vue3-form-naive.esm-2d045f5d.js";const qn={};function An(s,o){const e=Oe,T=M;return n(),t(T,null,{default:c(()=>[u(e)]),_:1})}const Jn=i(qn,[["render",An]]),Un={};function Hn(s,o){const e=Ie;return n(),t(e,{style:{"vertical-align":"baseline"}})}const O=i(Un,[["render",Hn]]),Kn={};function Wn(s,o){const e=Ve;return n(),t(e,{style:{"vertical-align":"baseline"}})}const Yn=i(Kn,[["render",Wn]]),Gn={};function Xn(s,o){const e=Le;return n(),t(e,{style:{"vertical-align":"baseline"}})}const pe=i(Gn,[["render",Xn]]),Zn={};function Qn(s,o){const e=Pe;return n(),t(e,{style:{"vertical-align":"baseline"}})}const et=i(Zn,[["render",Qn]]),nt={};function tt(s,o){const e=qe;return n(),t(e,{style:{"vertical-align":"baseline"}})}const ot=i(nt,[["render",tt]]),at={};function lt(s,o){const e=Ae;return n(),t(e,{style:{"vertical-align":"baseline"}})}const st=i(at,[["render",lt]]),ct={};function it(s,o){const e=Je;return n(),t(e,{style:{"vertical-align":"baseline"}})}const _t=i(ct,[["render",it]]),rt={};function ut(s,o){const e=Ue;return n(),t(e,{style:{"vertical-align":"baseline"}})}const dt=i(rt,[["render",ut]]),pt={};function ft(s,o){const e=He;return n(),t(e,{style:{"vertical-align":"baseline"}})}const mt=i(pt,[["render",ft]]),gt={};function vt(s,o){const e=Ke;return n(),t(e,{style:{"vertical-align":"baseline"}})}const yt=i(gt,[["render",vt]]),ht={};function bt(s,o){const e=We;return n(),t(e,{style:{"vertical-align":"baseline"}})}const xt=i(ht,[["render",bt]]),$t={};function kt(s,o){const e=Ye;return n(),t(e,{style:{"vertical-align":"baseline"}})}const wt=i($t,[["render",kt]]),Ct={};function St(s,o){const e=Ge;return n(),t(e,{style:{"vertical-align":"baseline"}})}const Dt=i(Ct,[["render",St]]),jt={};function Ft(s,o){const e=Xe;return n(),t(e,{style:{"vertical-align":"baseline"}})}const fe=i(jt,[["render",Ft]]),Mt={};function Tt(s,o){const e=Ze;return n(),t(e,{style:{"vertical-align":"baseline"}})}const me=i(Mt,[["render",Tt]]),Rt={};function zt(s,o){const e=Qe;return n(),t(e,{style:{"vertical-align":"baseline"}})}const Bt=i(Rt,[["render",zt]]),Et={};function Nt(s,o){const e=en;return n(),t(e,{style:{"vertical-align":"baseline"}})}const Ot=i(Et,[["render",Nt]]),It={};function Vt(s,o){const e=nn;return n(),t(e,{style:{"vertical-align":"baseline"}})}const Lt=i(It,[["render",Vt]]),Pt={};function qt(s,o){const e=tn;return n(),t(e,{style:{"vertical-align":"baseline"}})}const At=i(Pt,[["render",qt]]),Jt={};function Ut(s,o){const e=on;return n(),t(e,{style:{"vertical-align":"baseline"}})}const Ht=i(Jt,[["render",Ut]]),Kt={};function Wt(s,o){const e=an;return n(),t(e,{style:{"vertical-align":"baseline"}})}const Yt=i(Kt,[["render",Wt]]),Gt={};function Xt(s,o){const e=ln;return n(),t(e,{style:{"vertical-align":"baseline"}})}const Y=i(Gt,[["render",Xt]]),Zt={};function Qt(s,o){const e=sn;return n(),t(e,{style:{"vertical-align":"baseline"}})}const G=i(Zt,[["render",Qt]]),eo={};function no(s,o){const e=cn;return n(),t(e,{style:{"vertical-align":"baseline"}})}const to=i(eo,[["render",no]]),oo={txt:{language:"plaintext",icon:Yt},log:{language:"plaintext",icon:Dt},sh:{language:"shell",icon:st,canRun:!0},py:{language:"python",icon:_t,canRun:!0},js:{language:"javascript",icon:dt,canRun:!0},ts:{language:"typescript",icon:mt,canRun:!0},json:{language:"json",icon:yt},md:{language:"markdown",icon:xt},go:{language:"go",icon:wt},yml:{language:"yaml",icon:fe},yaml:{language:"yaml",icon:fe},xml:{language:"xml",icon:me},html:{language:"html",icon:me},css:{language:"css",icon:Bt},java:{language:"java",icon:Ot},c:{language:"c",icon:Lt},cpp:{language:"cpp",icon:At},cs:{language:"csharp",icon:Ht},zip:{icon:Y,disabled:!0},"7z":{icon:Y,disabled:!0},rar:{icon:Y,disabled:!0},png:{icon:G,disabled:!0},jpeg:{icon:G,disabled:!0},gif:{icon:G,disabled:!0},csv:{icon:to,disabled:!0},db:{icon:O,disabled:!0},mdb:{icon:O,disabled:!0},accdb:{icon:O,disabled:!0},sql:{icon:O,disabled:!0}},ao={key:0,class:"font-bold text-20px"},lo={class:"font-bold text-15px"},so={class:"pb-1 mr-3"},co={class:"pb-1 mr-3"},io=mn({__name:"index",setup(s){var le;const o=kn.isMobile,e=gn(),T=vn();yn().warning({title:"插件配置编辑器",content:"该功能需要插件开发者适配2.0规范,未适配完成的插件不会在此列表中列出.",duration:6e3,closable:!0});const k=wn,R=a([""]),w=a(),y=a(500),b=a("配置文件编辑器"),V=a([]),C=a(""),ge=a([]),ve=a(0),ye=a(0),he=a(),x=a({nowData:null,jsonSchema:null}),X=a(""),be=a(""),Z=a(!o.value),Q=a(o.value?"on":"off"),ee=a(!1),S=a(!1),L=a(!1),P=a(!1),m=a(void 0),q=a(!1),ne=a(!0),A=a(!1),J=a();if(T.darkMode&&(le=J.value)!=null&&le.$$uiFormRef){const _=document.querySelectorAll(".arrayOrderList");_.length&&_.forEach(l=>{l.style.backgroundColor="#282828"})}const D=a(),xe=a(null),$e=a(null),ke=a(""),we=a(null),te=a({type:1,name:null,dir:null});function Ce(){JSON.stringify(x.value.nowData,null,4)}const Se={show:!1,okBtn:"保存",okBtnProps:{type:"primary"},cancelBtn:"取消",formItemAttrs:{"label-style":{backgroundColor:"#F8F8F8"}}};function De(_){return f(Cn,{placement:"right",delay:800,duration:500,style:{width:"max-content",fontSize:"12px"}},{trigger:()=>_.option.label,default:()=>f(K,{style:"gap: 4px"},{default:()=>[_.option.key,f(W,{text:!0,type:"primary",focusable:!1,onClick:()=>je(_.option.key)},{default:()=>f(Jn)})]})})}function je(_){_n(_)?e.success(k("common.copyOk")):e.error(k("common.copyFail"))}function oe(_,l,d){~(()=>F(this,null,function*(){const r=d.node;if(d.action==="select"){if(!r.isDir&&!r.disabled){if(o.value&&(m.value=!0),!r.key||!r.language||(yield Fe(r.key,r.language))===-3e3)return;D.value=r,b.value=r.label}}else r.isDir||(g("title"),g("code"),g("content"),g("contextMenu"))}))()}function Fe(_,l){return F(this,null,function*(){var d;be.value=l,A.value=!0;try{const r=yield In({path:encodeURI(_)});if(A.value=!1,(d=r.error)!=null&&d.msg)throw new Error("错误");r.data?(x.value=r.data,S.value=!0):(X.value="",x.value={nowData:null,jsonSchema:null},e.info("配置文件出错"),S.value=!1)}catch(r){return g("content"),g("contextMenu"),-3e3}finally{A.value=!1}})}function ae(_,l,d){if(d.node)switch(d.action){case"expand":d.node.prefix=()=>f(M,null,{default:()=>f(et)});break;case"collapse":d.node.prefix=()=>f(M,null,{default:()=>f(pe)});break}}function E(_){return F(this,null,function*(){switch(_){case"findBox":ee.value=!ee.value,P.value=!1;break;case"wordWrap":Q.value=Q.value==="on"?"off":"on",P.value=!1;break;case"minimap":Z.value=!Z.value,P.value=!1;break;case"showEditorShortcutsInfo":he.value.showShortCuts=!0;break;case"changeToEditor":S.value=!1,S.value?(e.info(k("editor.switch.changeToEditorMsg"),{duration:1e3}),m.value=!0):(g("title"),g("code"),g("content"),g("contextMenu"),g("treeSelect"));break;case"siderCollapse":m.value=!m.value;break;case"save":q.value=!0;try{yield J.value.$$uiFormRef.validate();const l={database:"pluginConfig",form:"PluginConfig",key:D.value.key,val:x.value.nowData};(yield Vn(l)).data?e.success("数据修改成功"):e.error("数据修改失败")}catch(l){e.error("数据填写有误,请检查!"),console.error("数据填写有误,请检查!",l)}finally{q.value=!1}break;case"clickContextMenu":L.value=!1;break;case"exitContextMenu":L.value=!1;break}})}function g(_){switch(_){case"title":b.value="插件配置";break;case"code":X.value="",x.value={nowData:null,jsonSchema:null};break;case"content":D.value=null,o.value&&(m.value=!1);break;case"contextMenu":xe.value=null;break;case"renameInput":ke.value="";break;case"treeSelect":$e.value=null,te.value.dir=null;break;case"fileAttributeData":we.value=null;break;case"fileCreate":te.value={type:1,name:null,dir:null};break}}function Me(_,l){return F(this,null,function*(){const d=r=>{r.length&&r.forEach(p=>{if(p.path=p.path.split("BncrData")[1],p.type===0)Object.assign(p,{key:p.path,label:p.title,isDir:!0,prefix:()=>f(M,null,{default:()=>f(pe)})}),d(p.children),p.children.length||(Object.assign(p,{children:null,disabled:!l}),p.prefix=()=>f(M,null,{default:()=>f(ot)}));else{const j=p.name;if(!j)return;const U=j.substring(j.lastIndexOf(".")+1),$=oo[U]||{};Object.assign(p,{key:p.path,label:j,isDir:!1,language:$.language||"plaintext",canRun:$.canRun||!1,disabled:$.disabled||!$.language,prefix:()=>f(M,null,{default:()=>f($.icon||Yn)})})}})};return d(_),_})}function Te(){return F(this,null,function*(){ne.value=!0;const _=yield Me(yield Ln());R.value=[_[0].key],V.value=_,ne.value=!1})}return hn(w,_=>{const l=_[0],{width:d,height:r}=l.contentRect;y.value=r}),bn(()=>{Te()}),(_,l)=>{const d=rn,r=un,p=Sn,j=fn,U=Dn,$=pn,se=jn,ce=Fn,Re=Mn,H=Tn,ie=Rn,ze=zn,_e=Bn,Be=dn,Ee=En,Ne=Nn,re=On;return n(),ue("div",null,[u(re,{ref_key:"NcardDom",ref:w,bordered:!1,class:"h-full rounded-8px shadow-sm"},{default:c(()=>[u(U,null,{title:c(()=>[u(h(K),null,{default:c(()=>[h(o)&&D.value?(n(),t(de,{key:0,name:"fade-slide",mode:"in-out",appear:""},{default:c(()=>[u(h(W),{strong:"",secondary:"",size:"tiny",style:{height:"29px",padding:"0 8px"},focusable:!1,onClick:l[0]||(l[0]=v=>E("siderCollapse"))},{default:c(()=>[m.value?(n(),t(d,{key:0,style:{"vertical-align":"baseline"}})):(n(),t(r,{key:1,style:{"vertical-align":"baseline"}}))]),_:1})]),_:1})):z("",!0),B("div",null,[h(o)?(n(),t(p,{key:1,style:{height:"100%","max-width":"100px"}},{tooltip:c(()=>[B("span",null,N(b.value),1)]),default:c(()=>[B("span",lo,N(b.value),1)]),_:1})):(n(),ue("span",ao,N(b.value),1))])]),_:1})]),subtitle:c(()=>[]),extra:c(()=>[D.value?(n(),t(de,{key:0,name:"fade-slide",mode:"out-in",appear:""},{default:c(()=>[S.value?(n(),t(h(K),{key:0},{default:c(()=>[u(h(W),{strong:"",type:"primary",size:"small",loading:q.value,focusable:!1,onClick:l[1]||(l[1]=v=>E("save"))},{icon:c(()=>[u(j)]),default:c(()=>[xn(" "+N(h(k)("editor.main.saveButton")),1)]),_:1},8,["loading"])]),_:1})):z("",!0)]),_:1})):z("",!0)]),_:1}),u(re,{bordered:!0,style:$n({height:y.value*.88+"px","margin-top":"10px"}),"content-style":"padding: 0; height: 90% ;"},{default:c(()=>[u(Ne,{"has-sider":"",style:{height:"100%"},"content-style":"height: 100%"},{default:c(()=>[h(o)?(n(),t(ie,{key:1,class:"scripts-container",width:"100%","content-style":"display: flex; flex-direction: column; height: 100%; padding: .8em 0 0 .8em","trigger-style":"margin-right: .75em","collapsed-trigger-style":"margin-right: -0.75em","collapsed-width":0,bordered:!1,"show-trigger":!1,"show-collapsed-content":!1,collapsed:m.value,"on-update:collapsed":v=>m.value=v,"native-scrollbar":!1},{default:c(()=>[B("div",co,[u(se,{value:C.value,"onUpdate:value":l[5]||(l[5]=v=>C.value=v),clearable:"",size:"small",style:{"margin-bottom":".5em"},placeholder:"请输入目录或文件名"},{prefix:c(()=>[u($)]),_:1},8,["value"])]),u(H,{"x-scrollable":"",class:"flex-1 pr-3"},{default:c(()=>[u(ce,{"expand-on-click":"","block-line":"",style:{height:"100%","font-size":"15px"},"show-irrelevant-nodes":!1,pattern:C.value,data:V.value,"on-update:expanded-keys":ae,"on-update:selected-keys":oe,"default-expanded-keys":R.value},null,8,["pattern","data","default-expanded-keys"]),u(ze,{right:40,bottom:80})]),_:1})]),_:1},8,["collapsed","on-update:collapsed"])):(n(),t(ie,{key:0,class:"scripts-container","content-style":"display: flex; flex-direction: column; height: 100%; padding: .8em 0 0 .8em",width:"280",bordered:"","collapsed-width":0,"show-trigger":"bar","show-collapsed-content":!1,"native-scrollbar":!1,collapsed:m.value,"on-update:collapsed":m.value=void 0},{default:c(()=>[B("div",so,[u(se,{value:C.value,"onUpdate:value":l[2]||(l[2]=v=>C.value=v),clearable:"",style:{"margin-bottom":".5em"},placeholder:"请输入目录或文件名"},{prefix:c(()=>[u($)]),_:1},8,["value"])]),u(H,{"x-scrollable":"",class:"flex-1 pr-3"},{default:c(()=>[u(ce,{"expand-on-click":"","block-line":"","show-line":"",style:{height:"100%"},"show-irrelevant-nodes":!1,pattern:C.value,data:V.value,"render-label":De,"on-update:expanded-keys":ae,"on-update:selected-keys":oe,"default-expanded-keys":R.value},null,8,["pattern","data","default-expanded-keys"]),u(Re,{trigger:"manual",placement:"bottom-start",show:L.value,options:ge.value,x:ve.value,y:ye.value,onSelect:l[3]||(l[3]=v=>E("clickContextMenu")),onClickoutside:l[4]||(l[4]=v=>E("exitContextMenu"))},null,8,["show","options","x","y"])]),_:1})]),_:1},8,["collapsed","on-update:collapsed"])),D.value?(n(),t(_e,{key:2},{default:c(()=>[S.value?(n(),t(H,{key:0,"x-scrollable":""},{default:c(()=>[u(h(Pn),{ref_key:"myForm",ref:J,style:{padding:"25px"},modelValue:x.value.nowData,"onUpdate:modelValue":l[6]||(l[6]=v=>x.value.nowData=v),schema:x.value.jsonSchema,formFooter:Se,formProps:{layoutColumn:h(o)?1:2},onSubmit:Ce},null,8,["modelValue","schema","formProps"])]),_:1})):z("",!0)]),_:1})):(n(),t(_e,{key:3},{default:c(()=>[m.value!==!0?(n(),t(Ee,{key:0,size:"huge",description:"请选择一个文件",class:"h-full flex-x-center lex-y-center"},{icon:c(()=>[u(Be)]),_:1})):z("",!0)]),_:1}))]),_:1})]),_:1},8,["style"])]),_:1},512)])}}});const yo=i(io,[["__scopeId","data-v-db5680e2"]]);export{yo as default};
