import{d as T,K as y,L as U,b1 as N,b5 as P,b6 as $,h as D,o as i,c as m,e,w as t,f as b,b as n,q as C,a as s,F as z,l as L,b7 as M,aq as q,z as E,b8 as F,A as G,b9 as J,J as K,ba as O,p as Q,j as W}from"./index-b380aaed.js";import{S as X}from"./index-b7461bb8.js";const Y={key:0},Z=s("p",{class:"mt-15px"},"CPU占用(整机)",-1),ee=s("p",{class:"mt-15px"},"内存占用率(整机)",-1),te=s("p",{class:"mt-1px"},"已用内存(整机)",-1),se=s("p",{class:"mt-1px"},"剩余内存(整机)",-1),ae=s("p",{class:"mt-1px"},"Bncr占用内存",-1),le=s("p",{class:"mt-1px"},"Bncr使用内存",-1),ne=s("p",{class:"text-20px font-bold"},"适配器使用情况",-1),oe=s("p",{class:"text-15px font-bold"},"(启动至今)",-1),ue={class:"w-full py-1px"},de=s("p",{class:"text-20px font-bold"},"系统信息",-1),ce={class:"w-full py-1px"},re=s("p",{class:"text-20px font-bold"},"运行信息",-1),pe={class:"w-full py-1px"},ie=s("p",{class:"text-20px font-bold"},"数据库",-1),_e=s("p",{class:"text-20px font-bold"},"设置",-1),me={class:"w-full h-full mt-5px",style:{display:"flex","flex-direction":"column","align-items":"center"}},fe={class:"mt-10px"},xe={key:1},ye={class:"text-primary",style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}},ve={key:0},he={key:1},ge=T({__name:"index",setup(H){const r=y({systemInfoData:!1,systemRunData:!1,HistogramData:!1}),u=X.Instance;u.registerCallBack("systemInfoData",o=>{l.value=o.body,r.value.systemInfoData=!0}),u.registerCallBack("systemRunData",o=>{p.value=o.body,l.value&&(l.value.runTime=o.body.runTime,l.value.systemTime=o.body.systemTime),r.value.systemRunData=!0}),u.registerCallBack("HistogramData",o=>{f.value.yAxis.data=o.body.yAxis.data,f.value.series[0].data=o.body.series[0].data,f.value.series[1].data=o.body.series[1].data,f.value.series[2].data=o.body.series[2].data,r.value.HistogramData=!0});let x=!1;function k(){x&&w(),l.value||u.send({type:"systemInfoData"}),u.send({type:"systemRunData"}),u.send({type:"HistogramData"})}const _=y(5);U(()=>_.value,o=>{w()});let v=null;const w=()=>{let o;u.connected.value?(o=_.value,x=!1):(o=.5,x=!0),B(),v=setInterval(k,o*1e3)},B=()=>{v&&(clearInterval(v),v=null)},l=y(null),p=y(null),f=y({title:{text:""},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{},grid:{left:"1%",right:"4%",bottom:"5%",containLabel:!0},xAxis:{type:"value",boundaryGap:[0,.01]},yAxis:{type:"category",data:[]},series:[{name:"接收",type:"bar",data:[]},{name:"处理",type:"bar",data:[]},{name:"发送",type:"bar",data:[]}]}),{domRef:j}=N(f);return P(()=>{k(),w()}),$(()=>{B(),u.unRegisterCallBack("systemInfoData"),u.unRegisterCallBack("dashboardData")}),(o,h)=>{const I=M,d=q,c=E,g=F,R=G,V=J,S=K,A=O;return r.value.HistogramData&&r.value.systemInfoData&&r.value.systemRunData&&D(u).connected.value&&p.value&&l.value?(i(),m("div",Y,[e(R,{"x-gap":16,"y-gap":16,cols:"24","item-responsive":!0},{default:t(()=>[e(c,{span:"0:12 640:8 1200:4"},{default:t(()=>[e(d,{bordered:!1,class:"rounded-8px shadow-sm h-full","content-style":{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center"}},{default:t(()=>{var a;return[e(I,{type:"circle",percentage:+((a=p.value)==null?void 0:a.cpuPercentage)},null,8,["percentage"]),Z]}),_:1})]),_:1}),e(c,{span:"0:12 640:8 1200:4"},{default:t(()=>[e(d,{bordered:!1,class:"rounded-8px shadow-sm h-full","content-style":{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center"}},{default:t(()=>{var a;return[e(I,{type:"circle",percentage:+((a=p.value)==null?void 0:a.mem.percentage)},null,8,["percentage"]),ee]}),_:1})]),_:1}),e(c,{span:"0:12 640:8 1200:4"},{default:t(()=>[e(d,{bordered:!1,class:"rounded-8px shadow-sm h-full","content-style":{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center"}},{default:t(()=>[e(g,null,{default:t(()=>{var a;return[b(n((a=p.value)==null?void 0:a.mem.used),1)]}),_:1}),te]),_:1})]),_:1}),e(c,{span:"0:12 640:8 1200:4"},{default:t(()=>[e(d,{bordered:!1,class:"rounded-8px shadow-sm h-full","content-style":{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center"}},{default:t(()=>[e(g,null,{default:t(()=>{var a;return[b(n((a=p.value)==null?void 0:a.mem.free),1)]}),_:1}),se]),_:1})]),_:1}),e(c,{span:"0:12 640:8 1200:4"},{default:t(()=>[e(d,{bordered:!1,class:"rounded-8px shadow-sm h-full","content-style":{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center"}},{default:t(()=>[e(g,null,{default:t(()=>{var a;return[b(n((a=p.value)==null?void 0:a.bncrMem.rss),1)]}),_:1}),ae]),_:1})]),_:1}),e(c,{span:"0:12 640:8 1200:4"},{default:t(()=>[e(d,{bordered:!1,class:"rounded-8px shadow-sm h-full","content-style":{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center"}},{default:t(()=>[e(g,null,{default:t(()=>{var a;return[b(n((a=p.value)==null?void 0:a.bncrMem.heapUsed),1)]}),_:1}),le]),_:1})]),_:1})]),_:1}),e(R,{"x-gap":16,"y-gap":16,cols:"24","item-responsive":!0,class:"py-18px"},{default:t(()=>[e(c,{span:"0:24 640:24 1024:24"},{default:t(()=>[e(d,{bordered:!1,class:"rounded-8px shadow-sm"},{default:t(()=>[ne,oe,f.value.yAxis.data.length>0?(i(),m("div",{key:0,ref_key:"HistogramRef",ref:j,class:"w-full h-460px"},null,512)):C("",!0)]),_:1})]),_:1}),e(c,{span:"0:24 640:24 1024:6"},{default:t(()=>[e(d,{bordered:!1,class:"rounded-8px shadow-sm max-h-300px"},{default:t(()=>[s("div",ue,[de,s("p",null,"平台: "+n(l.value.platform),1),s("p",null,"系统: "+n(l.value.distro),1),s("p",null,"CPU: "+n(l.value.cpu),1),s("p",null,"频率: "+n(l.value.cpuSpeed),1),s("p",null,"核心: "+n(l.value.cpuCores),1),s("p",null,"内存: "+n(l.value.memTotal),1)])]),_:1})]),_:1}),e(c,{span:"0:24 640:24 1024:6"},{default:t(()=>[e(d,{bordered:!1,class:"rounded-8px shadow-sm h-full"},{default:t(()=>[s("div",ce,[re,s("p",null,"Bncr版本: "+n(l.value.bncrVersion),1),s("p",null,"nodejs版本: "+n(l.value.nodeVersion),1),s("p",null,"npm版本: "+n(l.value.npmVersion),1),s("p",null,"运行时间: "+n(l.value.runTime),1),s("p",null,"启动时间: "+n(l.value.startTime),1),s("p",null,"系统时间: "+n(l.value.systemTime),1)])]),_:1})]),_:1}),e(c,{span:"0:24 640:24 1024:6"},{default:t(()=>[e(d,{bordered:!1,class:"rounded-8px shadow-sm h-full"},{default:t(()=>[s("div",pe,[ie,(i(!0),m(z,null,L(l.value.dataSize,a=>(i(),m("p",null,n(a),1))),256))])]),_:1})]),_:1}),e(c,{span:"0:24 640:24 1024:6"},{default:t(()=>[e(d,{bordered:!1,class:"rounded-8px shadow-sm h-full"},{default:t(()=>[_e,s("div",me,[e(V,{class:"max-w-200px mt-15px",value:_.value,"onUpdate:value":h[0]||(h[0]=a=>_.value=a),step:1},null,8,["value"]),e(S,{class:"max-w-200px mt-15px",value:_.value,"onUpdate:value":h[1]||(h[1]=a=>_.value=a)},null,8,["value"]),s("p",fe,"更新数据间隔:"+n(_.value)+"s",1)])]),_:1})]),_:1})]),_:1})])):(i(),m("div",xe,[e(d,{bordered:!1,class:"rounded-8px shadow-sm",style:{height:"calc(100vh - 150px)"}},{default:t(()=>[s("div",ye,[D(u).connected.value?!r.value.HistogramData||!r.value.systemInfoData||!r.value.systemRunData?(i(),m("p",he," 取得数据中.. ")):C("",!0):(i(),m("p",ve," 已断开与服务器的链接.. "))]),e(A,{height:"100%",width:"100%",sharp:!1})]),_:1})]))}}}),De=T({__name:"index",setup(H){return(r,u)=>{const x=W;return i(),Q(x,{vertical:!0,size:16},{default:t(()=>[e(D(ge))]),_:1})}}});export{De as default};
