import{d as S,K as d,k as p,V as X,o as f,c as h,e as n,w as o,f as i,h as v,a as l,x as m,aE as Y,y as P,H as A,aq as V}from"./index-b380aaed.js";const W={class:"wh-full flex-col-center"},$={class:"flex-center py-12px"},z={class:"flex-center"},B={class:"w-full h-full"},F=["cx","cy","rx","ry"],I=60,q=S({__name:"index",setup(L){const y=Y("root"),e={cX:202,cY:102,rX:200,rY:100,strokeWidth:2},g=(()=>{const{rX:r,rY:c,strokeWidth:s}=e,t=(r+s)*2,u=(c+s)*2;return`width:${t}px;height:${u}px;`})(),_=d(0),k=p(()=>{const{rX:r,rY:c}=e,s=r*Math.sin(_.value),t=c*Math.cos(_.value);return`transform: translate3d(${s}px,${t}px,0px)`}),w=d(2),C=2*Math.PI,b=p(()=>C/w.value/I),a=d(null),M=p(()=>a.value!==null),x=()=>{_.value+=b.value,a.value=window.requestAnimationFrame(x)},E=()=>{a.value!==null&&(window.cancelAnimationFrame(a.value),a.value=null)};return(r,c)=>{const s=P,t=A,u=X("router-link"),N=V;return f(),h("div",W,[n(s,{class:"mb-24px",type:"primary",size:28},{default:o(()=>[i("Custom Constant Page")]),_:1}),n(u,{to:{name:v(y)}},{default:o(()=>[n(t,{type:"primary"},{default:o(()=>[i("回到首页")]),_:1})]),_:1},8,["to"]),n(N,{bordered:!1,size:"small",class:"mt-24px rounded-8px shadow-sm"},{default:o(()=>[l("div",$,[n(t,{type:"primary",class:"mr-24px",disabled:M.value,onClick:x},{default:o(()=>[i("开始")]),_:1},8,["disabled"]),n(t,{type:"error",onClick:E},{default:o(()=>[i("暂停")]),_:1})]),l("div",z,[l("div",{class:"relative bg-primary_active",style:m(v(g))},[(f(),h("svg",B,[l("ellipse",{cx:e.cX,cy:e.cY,rx:e.rX,ry:e.rY,style:m({strokeWidth:e.strokeWidth+"px"}),class:"fill-none stroke-primary"},null,12,F)])),l("div",{class:"absolute left-182px top-82px w-40px h-40px bg-red rounded-20px",style:m(k.value)},null,4)],4)])]),_:1})])}}});export{q as default};
