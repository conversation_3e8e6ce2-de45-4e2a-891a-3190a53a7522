var R=Object.defineProperty,I=Object.defineProperties;var N=Object.getOwnPropertyDescriptors;var S=Object.getOwnPropertySymbols;var G=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var q=(f,a,n)=>a in f?R(f,a,{enumerable:!0,configurable:!0,writable:!0,value:n}):f[a]=n,j=(f,a)=>{for(var n in a||(a={}))G.call(a,n)&&q(f,n,a[n]);if(S)for(var n of S(a))O.call(a,n)&&q(f,n,a[n]);return f},_=(f,a)=>I(f,N(a));import{o as u,c as i,a as d,d as M,p as z,w as t,e,b as m,h as l,f as o,n as J,$ as P,ao as U,bh as X,D as K,u as Q,bi as Y,K as T,bj as k,an as ee,M as oe,b6 as re,L as C}from"./index-b380aaed.js";const te={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},ne=d("g",{id:"feTrash0",fill:"none","fill-rule":"evenodd",stroke:"none","stroke-width":"1"},[d("g",{id:"feTrash1",fill:"currentColor","fill-rule":"nonzero"},[d("path",{id:"feTrash2",d:"M4 5h3V4a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1h3a1 1 0 0 1 0 2h-1v13a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V7H4a1 1 0 1 1 0-2Zm3 2v13h10V7H7Zm2-2h6V4H9v1Zm0 4h2v9H9V9Zm4 0h2v9h-2V9Z"})])],-1),ae=[ne];function de(f,a){return u(),i("svg",te,ae)}const so={name:"fe-trash",render:de},ue={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},ie=d("path",{fill:"currentColor",d:"M5 21q-.825 0-1.413-.588T3 19V5q0-.825.588-1.413T5 3h6v2H5v14h14v-6h2v6q0 .825-.588 1.413T19 21H5Zm11-10V8h-3V6h3V3h2v3h3v2h-3v3h-2Z"},null,-1),se=[ie];function fe(f,a){return u(),i("svg",ue,se)}const fo={name:"material-symbols-new-window",render:fe},ge={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},ce=d("path",{fill:"currentColor",d:"M3 4h6.11l7.04 14H21v2h-6.12L7.84 6H3V4m11 0h7v2h-7V4Z"},null,-1),ke=[ce];function pe(f,a){return u(),i("svg",ge,ke)}const le={name:"mdi-apple-keyboard-option",render:pe},me={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},ye=d("path",{fill:"currentColor",d:"M21 18q-.425 0-.713-.288T20 17V7q0-.425.288-.713T21 6q.425 0 .713.288T22 7v10q0 .425-.288.713T21 18Zm-6.825-5H3q-.425 0-.713-.288T2 12q0-.425.288-.713T3 11h11.175L11.3 8.1q-.275-.275-.288-.688T11.3 6.7q.275-.275.7-.275t.7.275l4.6 4.6q.15.15.213.325t.062.375q0 .2-.063.375t-.212.325l-4.6 4.6q-.275.275-.687.275T11.3 17.3q-.3-.3-.3-.713t.3-.712L14.175 13Z"},null,-1),be=[ye];function Be(f,a){return u(),i("svg",me,be)}const H={name:"material-symbols-keyboard-tab-rounded",render:Be},he={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Ae=d("path",{fill:"currentColor",d:"M15 18v-6h2.17L12 6.83L6.83 12H9v6h6M12 4l10 10h-5v6H7v-6H2L12 4Z"},null,-1),ve=[Ae];function we(f,a){return u(),i("svg",he,ve)}const Ce={name:"mdi-apple-keyboard-shift",render:we},xe={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},je=d("path",{fill:"currentColor",d:"M6 2a4 4 0 0 1 4 4v2h4V6a4 4 0 0 1 4-4a4 4 0 0 1 4 4a4 4 0 0 1-4 4h-2v4h2a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4v-2h-4v2a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4h2v-4H6a4 4 0 0 1-4-4a4 4 0 0 1 4-4m10 16a2 2 0 0 0 2 2a2 2 0 0 0 2-2a2 2 0 0 0-2-2h-2v2m-2-8h-4v4h4v-4m-8 6a2 2 0 0 0-2 2a2 2 0 0 0 2 2a2 2 0 0 0 2-2v-2H6M8 6a2 2 0 0 0-2-2a2 2 0 0 0-2 2a2 2 0 0 0 2 2h2V6m10 2a2 2 0 0 0 2-2a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2h2Z"},null,-1),_e=[je];function Fe(f,a){return u(),i("svg",xe,_e)}const Ee={name:"mdi-apple-keyboard-command",render:Fe},De={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Se=d("path",{fill:"currentColor",d:"M4 19q-.825 0-1.413-.588T2 17V7q0-.825.588-1.413T4 5h16q.825 0 1.413.588T22 7v10q0 .825-.588 1.413T20 19H4Zm5-3h6q.425 0 .713-.288T16 15q0-.425-.288-.713T15 14H9q-.425 0-.713.288T8 15q0 .425.288.713T9 16Zm-4-3h2v-2H5v2Zm3 0h2v-2H8v2Zm3 0h2v-2h-2v2Zm3 0h2v-2h-2v2Zm3 0h2v-2h-2v2ZM5 10h2V8H5v2Zm3 0h2V8H8v2Zm3 0h2V8h-2v2Zm3 0h2V8h-2v2Zm3 0h2V8h-2v2Z"},null,-1),qe=[Se];function Te(f,a){return u(),i("svg",De,qe)}const He={name:"material-symbols-keyboard-rounded",render:Te},Ve={key:0},Le={key:1},Me={key:0},$e={key:1},Ze={key:0},We={key:1},Re={key:0},Ie={key:1},Ne={key:0},Ge={key:1},Oe={key:0},ze={key:1},Je={key:0},Pe={key:1},Ue={key:0},Xe={key:1},Ke={key:0},Qe={key:1},Ye={key:0},eo={key:1},oo={key:0},ro={key:1},to=M({__name:"ShortcutsModal",setup(f){const a=P;function n(){return!1}return(F,E)=>{const b=He,c=Ee,r=U,v=H,B=Ce,w=H,x=le,y=X,h=K;return u(),z(h,{class:J({"apple-modal":n()}),preset:"dialog",title:l(a)("editor.shortcuts.title"),style:{"max-width":"480px"},"auto-focus":!1},{icon:t(()=>[e(b,{style:{"vertical-align":"0"}})]),default:t(()=>[e(y,{"single-column":"",bordered:!1,"bottom-bordered":!1},{default:t(()=>[d("tbody",null,[d("tr",null,[d("td",null,m(l(a)("editor.shortcuts.indentLine")),1),n()?(u(),i("td",Ve,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(c)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" ] ")]),_:1}),o("  /  "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(v)]),_:1})])):(u(),i("td",Le,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" CTRL ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" ] ")]),_:1}),o("  /  "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" TAB ")]),_:1})]))]),d("tr",null,[d("td",null,m(l(a)("editor.shortcuts.outdentLine")),1),n()?(u(),i("td",Me,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(c)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" [ ")]),_:1}),o("  /  "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(B)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(w)]),_:1})])):(u(),i("td",$e,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" CTRL ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" [ ")]),_:1}),o("  /  "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" SHIFT ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" TAB ")]),_:1})]))]),d("tr",null,[d("td",null,m(l(a)("editor.shortcuts.toggleLineComment")),1),n()?(u(),i("td",Ze,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(c)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" / ")]),_:1})])):(u(),i("td",We,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" CTRL ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" / ")]),_:1})]))]),d("tr",null,[d("td",null,m(l(a)("editor.shortcuts.find")),1),n()?(u(),i("td",Re,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(c)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" F ")]),_:1})])):(u(),i("td",Ie,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" CTRL ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" F ")]),_:1})]))]),d("tr",null,[d("td",null,m(l(a)("editor.shortcuts.replace")),1),n()?(u(),i("td",Ne,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(x)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(c)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" F ")]),_:1})])):(u(),i("td",Ge,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" CTRL ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" H ")]),_:1})]))]),d("tr",null,[d("td",null,m(l(a)("editor.shortcuts.goToLine")),1),n()?(u(),i("td",Oe,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(c)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" G ")]),_:1})])):(u(),i("td",ze,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" CTRL ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" G ")]),_:1})]))]),d("tr",null,[d("td",null,m(l(a)("editor.shortcuts.cut")),1),n()?(u(),i("td",Je,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(c)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" X ")]),_:1})])):(u(),i("td",Pe,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" CTRL ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" X ")]),_:1})]))]),d("tr",null,[d("td",null,m(l(a)("editor.shortcuts.copy")),1),n()?(u(),i("td",Ue,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(c)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" C ")]),_:1})])):(u(),i("td",Xe,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" CTRL ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" C ")]),_:1})]))]),d("tr",null,[d("td",null,m(l(a)("editor.shortcuts.paste")),1),n()?(u(),i("td",Ke,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(c)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" V ")]),_:1})])):(u(),i("td",Qe,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" CTRL ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" V ")]),_:1})]))]),d("tr",null,[d("td",null,m(l(a)("editor.shortcuts.revoke")),1),n()?(u(),i("td",Ye,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(c)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" Z ")]),_:1})])):(u(),i("td",eo,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" CTRL ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" Z ")]),_:1})]))]),d("tr",null,[d("td",null,m(l(a)("editor.shortcuts.recover")),1),n()?(u(),i("td",oo,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(B)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[e(c)]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" Z ")]),_:1})])):(u(),i("td",ro,[e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" CTRL ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" SHIFT ")]),_:1}),o(" + "),e(r,{type:"primary",style:{"font-weight":"550"},bordered:!1},{default:t(()=>[o(" Z ")]),_:1})]))])])]),_:1})]),_:1},8,["class","title"])}}}),V="#2080f0",no={inherit:!0,base:"vs",colors:{"activityBar.background":"#fafafa","activityBar.foreground":"#121417","activityBarBadge.background":"#526fff","activityBarBadge.foreground":"#ffffff","badge.background":"#526fff","badge.foreground":"#ffffff","button.background":"#5871ef","button.foreground":"#ffffff","button.hoverBackground":"#6b83ed","diffEditor.insertedTextBackground":"#00809b33","dropdown.background":"#ffffff","dropdown.border":"#dbdbdc","editor.background":"#ffffff","editor.findMatchHighlightBackground":"#526fff33","editor.foreground":"#383a42","editor.lineHighlightBackground":"#383a420c","editor.selectionBackground":"#e5e5e6","editorCursor.background":"#ffffffc9","editorCursor.foreground":V,"editorGroup.border":"#dbdbdc","editorGroupHeader.tabsBackground":"#eaeaeb","editorHoverWidget.background":"#eaeaeb","editorHoverWidget.border":"#dbdbdc","editorIndentGuide.activeBackground":"#626772","editorIndentGuide.background":"#383a4233","editorInlayHint.background":"#f5f5f5","editorInlayHint.foreground":"#afb2bb","editorLineNumber.activeForeground":V,"editorLineNumber.foreground":"#c0c0c0","editorRuler.foreground":"#383a4233","editorSuggestWidget.background":"#eaeaeb","editorSuggestWidget.border":"#dbdbdc","editorSuggestWidget.selectedBackground":"#ffffff","editorWhitespace.foreground":"#383a4233","editorWidget.background":"#eaeaeb","editorWidget.border":"#e5e5e6","extensionButton.prominentBackground":"#3bba54","extensionButton.prominentHoverBackground":"#4cc263",focusBorder:"#526fff","input.background":"#ffffff","input.border":"#dbdbdc","list.activeSelectionBackground":"#dbdbdc","list.activeSelectionForeground":"#232324","list.focusBackground":"#dbdbdc","list.highlightForeground":"#121417","list.hoverBackground":"#dbdbdc66","list.inactiveSelectionBackground":"#dbdbdc","list.inactiveSelectionForeground":"#232324","notebook.cellEditorBackground":"#f5f5f5","peekView.border":"#526fff","peekViewEditor.background":"#ffffff","peekViewResult.background":"#eaeaeb","peekViewResult.selectionBackground":"#dbdbdc","peekViewTitle.background":"#ffffff","pickerGroup.border":"#526fff","scrollbarSlider.activeBackground":"#747d9180","scrollbarSlider.background":"#4e566680","scrollbarSlider.hoverBackground":"#5a637580","sideBar.background":"#eaeaeb","sideBarSectionHeader.background":"#fafafa","statusBar.background":"#eaeaeb","statusBar.debuggingForeground":"#ffffff","statusBar.foreground":"#424243","statusBar.noFolderBackground":"#eaeaeb","statusBarItem.hoverBackground":"#dbdbdc","tab.activeBackground":"#fafafa","tab.activeForeground":"#121417","tab.border":"#dbdbdc","tab.inactiveBackground":"#eaeaeb","titleBar.activeBackground":"#eaeaeb","titleBar.activeForeground":"#424243","titleBar.inactiveBackground":"#eaeaeb","titleBar.inactiveForeground":"#424243"},rules:[{foreground:"#a6a6a6",fontStyle:"italic",token:"comment"},{foreground:"#A0A1A7",token:"comment markup.link"},{foreground:"#C18401",token:"entity.name.type"},{foreground:"#C18401",token:"entity.other.inherited-class"},{foreground:"#A626A4",token:"keyword"},{foreground:"#A626A4",token:"keyword.control"},{foreground:"#383A42",token:"keyword.operator"},{foreground:"#4078F2",token:"keyword.other.special-method"},{foreground:"#986801",token:"keyword.other.unit"},{foreground:"#A626A4",token:"storage"},{foreground:"#A626A4",token:"storage.type.annotation"},{foreground:"#A626A4",token:"storage.type.primitive"},{foreground:"#383A42",token:"storage.modifier.package"},{foreground:"#383A42",token:"storage.modifier.import"},{foreground:"#986801",token:"constant"},{foreground:"#986801",token:"constant.variable"},{foreground:"#0184BC",token:"constant.character.escape"},{foreground:"#986801",token:"constant.numeric"},{foreground:"#0184BC",token:"constant.other.color"},{foreground:"#0184BC",token:"constant.other.symbol"},{foreground:"#E45649",token:"variable"},{foreground:"#CA1243",token:"variable.interpolation"},{foreground:"#383A42",token:"variable.parameter"},{foreground:"#50A14F",token:"string"},{foreground:"#383A42",token:"string > source"},{foreground:"#383A42",token:"string embedded"},{foreground:"#0184BC",token:"string.regexp"},{foreground:"#C18401",token:"string.regexp source.ruby.embedded"},{foreground:"#E45649",token:"string.other.link"},{foreground:"#A0A1A7",token:"punctuation.definition.comment"},{foreground:"#383A42",token:"punctuation.definition.method-parameters"},{foreground:"#383A42",token:"punctuation.definition.function-parameters"},{foreground:"#383A42",token:"punctuation.definition.parameters"},{foreground:"#383A42",token:"punctuation.definition.separator"},{foreground:"#383A42",token:"punctuation.definition.seperator"},{foreground:"#383A42",token:"punctuation.definition.array"},{foreground:"#4078F2",token:"punctuation.definition.heading"},{foreground:"#4078F2",token:"punctuation.definition.identity"},{foreground:"#C18401",fontStyle:"bold",token:"punctuation.definition.bold"},{foreground:"#A626A4",fontStyle:"italic",token:"punctuation.definition.italic"},{foreground:"#CA1243",token:"punctuation.section.embedded"},{foreground:"#383A42",token:"punctuation.section.method"},{foreground:"#383A42",token:"punctuation.section.class"},{foreground:"#383A42",token:"punctuation.section.inner-class"},{foreground:"#C18401",token:"support.class"},{foreground:"#0184BC",token:"support.type"},{foreground:"#0184BC",token:"support.function"},{foreground:"#4078F2",token:"support.function.any-method"},{foreground:"#4078F2",token:"entity.name.function"},{foreground:"#C18401",token:"entity.name.class"},{foreground:"#C18401",token:"entity.name.type.class"},{foreground:"#4078F2",token:"entity.name.section"},{foreground:"#E45649",token:"entity.name.tag"},{foreground:"#986801",token:"entity.other.attribute-name"},{foreground:"#4078F2",token:"entity.other.attribute-name.id"},{foreground:"#C18401",token:"meta.class"},{foreground:"#383A42",token:"meta.class.body"},{foreground:"#383A42",token:"meta.method-call"},{foreground:"#383A42",token:"meta.method"},{foreground:"#E45649",token:"meta.definition.variable"},{foreground:"#986801",token:"meta.link"},{foreground:"#4078F2",token:"meta.require"},{foreground:"#A626A4",token:"meta.selector"},{foreground:"#383A42",token:"meta.separator"},{foreground:"#383A42",token:"meta.tag"},{token:"underline"},{foreground:"#383A42",token:"none"},{foreground:"#000000",background:"#F2A60D",token:"invalid.deprecated"},{background:"#FF1414",token:"invalid.illegal"},{foreground:"#986801",fontStyle:"bold",token:"markup.bold"},{foreground:"#A626A4",token:"markup.changed"},{foreground:"#E45649",token:"markup.deleted"},{foreground:"#A626A4",fontStyle:"italic",token:"markup.italic"},{foreground:"#E45649",token:"markup.heading"},{foreground:"#4078F2",token:"markup.heading punctuation.definition.heading"},{foreground:"#0184BC",token:"markup.link"},{foreground:"#50A14F",token:"markup.inserted"},{foreground:"#986801",token:"markup.quote"},{foreground:"#50A14F",token:"markup.raw"},{foreground:"#A626A4",token:"source.c keyword.operator"},{foreground:"#A626A4",token:"source.cpp keyword.operator"},{foreground:"#A626A4",token:"source.cs keyword.operator"},{foreground:"#696C77",token:"source.css property-name"},{foreground:"#696C77",token:"source.css property-value"},{foreground:"#383A42",token:"source.css property-name.support"},{foreground:"#383A42",token:"source.css property-value.support"},{foreground:"#383A42",token:"source.elixir source.embedded.source"},{foreground:"#4078F2",token:"source.elixir constant.language"},{foreground:"#4078F2",token:"source.elixir constant.numeric"},{foreground:"#4078F2",token:"source.elixir constant.definition"},{foreground:"#A626A4",token:"source.elixir variable.definition"},{foreground:"#A626A4",token:"source.elixir variable.anonymous"},{foreground:"#986801",fontStyle:"italic",token:"source.elixir parameter.variable.function"},{foreground:"#50A14F",token:"source.elixir quoted"},{foreground:"#E45649",token:"source.elixir keyword.special-method"},{foreground:"#E45649",token:"source.elixir embedded.section"},{foreground:"#E45649",token:"source.elixir embedded.source.empty"},{foreground:"#E45649",token:"source.elixir readwrite.module punctuation"},{foreground:"#CA1243",token:"source.elixir regexp.section"},{foreground:"#CA1243",token:"source.elixir regexp.string"},{foreground:"#986801",token:"source.elixir separator"},{foreground:"#986801",token:"source.elixir keyword.operator"},{foreground:"#C18401",token:"source.elixir variable.constant"},{foreground:"#696C77",token:"source.elixir array"},{foreground:"#696C77",token:"source.elixir scope"},{foreground:"#696C77",token:"source.elixir section"},{token:"source.gfm markup"},{foreground:"#4078F2",token:"source.gfm link entity"},{foreground:"#A626A4",token:"source.go storage.type.string"},{foreground:"#E45649",token:"source.ini keyword.other.definition.ini"},{foreground:"#C18401",token:"source.java storage.modifier.import"},{foreground:"#C18401",token:"source.java storage.type"},{foreground:"#A626A4",token:"source.java keyword.operator.instanceof"},{foreground:"#E45649",token:"source.java-properties meta.key-pair"},{foreground:"#383A42",token:"source.java-properties meta.key-pair > punctuation"},{foreground:"#0184BC",token:"source.js keyword.operator"},{foreground:"#A626A4",token:"source.js keyword.operator.delete"},{foreground:"#A626A4",token:"source.js keyword.operator.in"},{foreground:"#A626A4",token:"source.js keyword.operator.of"},{foreground:"#A626A4",token:"source.js keyword.operator.instanceof"},{foreground:"#A626A4",token:"source.js keyword.operator.new"},{foreground:"#A626A4",token:"source.js keyword.operator.typeof"},{foreground:"#A626A4",token:"source.js keyword.operator.void"},{foreground:"#0184BC",token:"source.ts keyword.operator"},{foreground:"#0184BC",token:"source.flow keyword.operator"},{foreground:"#E45649",token:"source.json meta.structure.dictionary.json > string.quoted.json"},{foreground:"#E45649",token:"source.json meta.structure.dictionary.json > string.quoted.json > punctuation.string"},{foreground:"#50A14F",token:"source.json meta.structure.dictionary.json > value.json > string.quoted.json"},{foreground:"#50A14F",token:"source.json meta.structure.array.json > value.json > string.quoted.json"},{foreground:"#50A14F",token:"source.json meta.structure.dictionary.json > value.json > string.quoted.json > punctuation"},{foreground:"#50A14F",token:"source.json meta.structure.array.json > value.json > string.quoted.json > punctuation"},{foreground:"#0184BC",token:"source.json meta.structure.dictionary.json > constant.language.json"},{foreground:"#0184BC",token:"source.json meta.structure.array.json > constant.language.json"},{foreground:"#E45649",token:"ng.interpolation"},{foreground:"#4078F2",token:"ng.interpolation.begin"},{foreground:"#4078F2",token:"ng.interpolation.end"},{foreground:"#E45649",token:"ng.interpolation function"},{foreground:"#4078F2",token:"ng.interpolation function.begin"},{foreground:"#4078F2",token:"ng.interpolation function.end"},{foreground:"#986801",token:"ng.interpolation bool"},{foreground:"#383A42",token:"ng.interpolation bracket"},{foreground:"#383A42",token:"ng.pipe"},{foreground:"#383A42",token:"ng.operator"},{foreground:"#0184BC",token:"ng.tag"},{foreground:"#C18401",token:"ng.attribute-with-value attribute-name"},{foreground:"#A626A4",token:"ng.attribute-with-value string"},{foreground:"#383A42",token:"ng.attribute-with-value string.begin"},{foreground:"#383A42",token:"ng.attribute-with-value string.end"},{token:"source.ruby constant.other.symbol > punctuation"},{foreground:"#383A42",token:"source.php class.bracket"},{foreground:"#A626A4",token:"source.python keyword.operator.logical.python"},{foreground:"#986801",token:"source.python variable.parameter"},{foreground:"#383A42",token:"customrule"},{foreground:"#383A42",token:"support.type.property-name"},{foreground:"#50A14F",token:"string.quoted.double punctuation"},{foreground:"#986801",token:"support.constant"},{foreground:"#E45649",token:"support.type.property-name.json"},{foreground:"#E45649",token:"support.type.property-name.json punctuation"},{foreground:"#0184BC",token:"punctuation.separator.key-value.ts"},{foreground:"#0184BC",token:"punctuation.separator.key-value.js"},{foreground:"#0184BC",token:"punctuation.separator.key-value.tsx"},{foreground:"#0184BC",token:"source.js.embedded.html keyword.operator"},{foreground:"#0184BC",token:"source.ts.embedded.html keyword.operator"},{foreground:"#383A42",token:"variable.other.readwrite.js"},{foreground:"#383A42",token:"variable.other.readwrite.ts"},{foreground:"#383A42",token:"variable.other.readwrite.tsx"},{foreground:"#E45649",token:"support.variable.dom.js"},{foreground:"#E45649",token:"support.variable.dom.ts"},{foreground:"#E45649",token:"support.variable.property.dom.js"},{foreground:"#E45649",token:"support.variable.property.dom.ts"},{foreground:"#CA1243",token:"meta.template.expression.js punctuation.definition"},{foreground:"#CA1243",token:"meta.template.expression.ts punctuation.definition"},{foreground:"#383A42",token:"source.ts punctuation.definition.typeparameters"},{foreground:"#383A42",token:"source.js punctuation.definition.typeparameters"},{foreground:"#383A42",token:"source.tsx punctuation.definition.typeparameters"},{foreground:"#383A42",token:"source.ts punctuation.definition.block"},{foreground:"#383A42",token:"source.js punctuation.definition.block"},{foreground:"#383A42",token:"source.tsx punctuation.definition.block"},{foreground:"#383A42",token:"source.ts punctuation.separator.comma"},{foreground:"#383A42",token:"source.js punctuation.separator.comma"},{foreground:"#383A42",token:"source.tsx punctuation.separator.comma"},{foreground:"#E45649",token:"support.variable.property.js"},{foreground:"#E45649",token:"support.variable.property.ts"},{foreground:"#E45649",token:"support.variable.property.tsx"},{foreground:"#E45649",token:"keyword.control.default.js"},{foreground:"#E45649",token:"keyword.control.default.ts"},{foreground:"#E45649",token:"keyword.control.default.tsx"},{foreground:"#A626A4",token:"keyword.operator.expression.instanceof.js"},{foreground:"#A626A4",token:"keyword.operator.expression.instanceof.ts"},{foreground:"#A626A4",token:"keyword.operator.expression.instanceof.tsx"},{foreground:"#A626A4",token:"keyword.operator.expression.of.js"},{foreground:"#A626A4",token:"keyword.operator.expression.of.ts"},{foreground:"#A626A4",token:"keyword.operator.expression.of.tsx"},{foreground:"#383A42",token:"meta.brace.round.js"},{foreground:"#383A42",token:"meta.array-binding-pattern-variable.js"},{foreground:"#383A42",token:"meta.brace.square.js"},{foreground:"#383A42",token:"meta.brace.round.ts"},{foreground:"#383A42",token:"meta.array-binding-pattern-variable.ts"},{foreground:"#383A42",token:"meta.brace.square.ts"},{foreground:"#383A42",token:"meta.brace.round.tsx"},{foreground:"#383A42",token:"meta.array-binding-pattern-variable.tsx"},{foreground:"#383A42",token:"meta.brace.square.tsx"},{foreground:"#383A42",token:"source.js punctuation.accessor"},{foreground:"#383A42",token:"source.ts punctuation.accessor"},{foreground:"#383A42",token:"source.tsx punctuation.accessor"},{foreground:"#383A42",token:"punctuation.terminator.statement.js"},{foreground:"#383A42",token:"punctuation.terminator.statement.ts"},{foreground:"#383A42",token:"punctuation.terminator.statement.tsx"},{foreground:"#986801",token:"meta.array-binding-pattern-variable.js variable.other.readwrite.js"},{foreground:"#986801",token:"meta.array-binding-pattern-variable.ts variable.other.readwrite.ts"},{foreground:"#986801",token:"meta.array-binding-pattern-variable.tsx variable.other.readwrite.tsx"},{foreground:"#E45649",token:"source.js support.variable"},{foreground:"#E45649",token:"source.ts support.variable"},{foreground:"#E45649",token:"source.tsx support.variable"},{foreground:"#986801",token:"variable.other.constant.property.js"},{foreground:"#986801",token:"variable.other.constant.property.ts"},{foreground:"#986801",token:"variable.other.constant.property.tsx"},{foreground:"#A626A4",token:"keyword.operator.new.ts"},{foreground:"#A626A4",token:"keyword.operator.new.j"},{foreground:"#A626A4",token:"keyword.operator.new.tsx"},{foreground:"#0184BC",token:"source.ts keyword.operator"},{foreground:"#0184BC",token:"source.tsx keyword.operator"},{foreground:"#383A42",token:"punctuation.separator.parameter.js"},{foreground:"#383A42",token:"punctuation.separator.parameter.ts"},{foreground:"#383A42",token:"punctuation.separator.parameter.tsx"},{foreground:"#E45649",token:"constant.language.import-export-all.js"},{foreground:"#E45649",token:"constant.language.import-export-all.ts"},{foreground:"#0184BC",token:"constant.language.import-export-all.jsx"},{foreground:"#0184BC",token:"constant.language.import-export-all.tsx"},{foreground:"#383A42",token:"keyword.control.as.js"},{foreground:"#383A42",token:"keyword.control.as.ts"},{foreground:"#383A42",token:"keyword.control.as.jsx"},{foreground:"#383A42",token:"keyword.control.as.tsx"},{foreground:"#E45649",token:"variable.other.readwrite.alias.js"},{foreground:"#E45649",token:"variable.other.readwrite.alias.ts"},{foreground:"#E45649",token:"variable.other.readwrite.alias.jsx"},{foreground:"#E45649",token:"variable.other.readwrite.alias.tsx"},{foreground:"#986801",token:"variable.other.constant.js"},{foreground:"#986801",token:"variable.other.constant.ts"},{foreground:"#986801",token:"variable.other.constant.jsx"},{foreground:"#986801",token:"variable.other.constant.tsx"},{foreground:"#E45649",token:"meta.export.default.js variable.other.readwrite.js"},{foreground:"#E45649",token:"meta.export.default.ts variable.other.readwrite.ts"},{foreground:"#50A14F",token:"source.js meta.template.expression.js punctuation.accessor"},{foreground:"#50A14F",token:"source.ts meta.template.expression.ts punctuation.accessor"},{foreground:"#50A14F",token:"source.tsx meta.template.expression.tsx punctuation.accessor"},{foreground:"#383A42",token:"source.js meta.import-equals.external.js keyword.operator"},{foreground:"#383A42",token:"source.jsx meta.import-equals.external.jsx keyword.operator"},{foreground:"#383A42",token:"source.ts meta.import-equals.external.ts keyword.operator"},{foreground:"#383A42",token:"source.tsx meta.import-equals.external.tsx keyword.operator"},{foreground:"#50A14F",token:"entity.name.type.module.js"},{foreground:"#50A14F",token:"entity.name.type.module.ts"},{foreground:"#50A14F",token:"entity.name.type.module.jsx"},{foreground:"#50A14F",token:"entity.name.type.module.tsx"},{foreground:"#383A42",token:"meta.class.js"},{foreground:"#383A42",token:"meta.class.ts"},{foreground:"#383A42",token:"meta.class.jsx"},{foreground:"#383A42",token:"meta.class.tsx"},{foreground:"#383A42",token:"meta.definition.property.js variable"},{foreground:"#383A42",token:"meta.definition.property.ts variable"},{foreground:"#383A42",token:"meta.definition.property.jsx variable"},{foreground:"#383A42",token:"meta.definition.property.tsx variable"},{foreground:"#383A42",token:"meta.type.parameters.js support.type"},{foreground:"#383A42",token:"meta.type.parameters.jsx support.type"},{foreground:"#383A42",token:"meta.type.parameters.ts support.type"},{foreground:"#383A42",token:"meta.type.parameters.tsx support.type"},{foreground:"#383A42",token:"source.js meta.tag.js keyword.operator"},{foreground:"#383A42",token:"source.jsx meta.tag.jsx keyword.operator"},{foreground:"#383A42",token:"source.ts meta.tag.ts keyword.operator"},{foreground:"#383A42",token:"source.tsx meta.tag.tsx keyword.operator"},{foreground:"#383A42",token:"meta.tag.js punctuation.section.embedded"},{foreground:"#383A42",token:"meta.tag.jsx punctuation.section.embedded"},{foreground:"#383A42",token:"meta.tag.ts punctuation.section.embedded"},{foreground:"#383A42",token:"meta.tag.tsx punctuation.section.embedded"},{foreground:"#C18401",token:"meta.array.literal.js variable"},{foreground:"#C18401",token:"meta.array.literal.jsx variable"},{foreground:"#C18401",token:"meta.array.literal.ts variable"},{foreground:"#C18401",token:"meta.array.literal.tsx variable"},{foreground:"#E45649",token:"support.type.object.module.js"},{foreground:"#E45649",token:"support.type.object.module.jsx"},{foreground:"#E45649",token:"support.type.object.module.ts"},{foreground:"#E45649",token:"support.type.object.module.tsx"},{foreground:"#0184BC",token:"constant.language.json"},{foreground:"#986801",token:"variable.other.constant.object.js"},{foreground:"#986801",token:"variable.other.constant.object.jsx"},{foreground:"#986801",token:"variable.other.constant.object.ts"},{foreground:"#986801",token:"variable.other.constant.object.tsx"},{foreground:"#0184BC",token:"storage.type.property.js"},{foreground:"#0184BC",token:"storage.type.property.jsx"},{foreground:"#0184BC",token:"storage.type.property.ts"},{foreground:"#0184BC",token:"storage.type.property.tsx"},{foreground:"#50A14F",token:"meta.template.expression.js string.quoted punctuation.definition"},{foreground:"#50A14F",token:"meta.template.expression.jsx string.quoted punctuation.definition"},{foreground:"#50A14F",token:"meta.template.expression.ts string.quoted punctuation.definition"},{foreground:"#50A14F",token:"meta.template.expression.tsx string.quoted punctuation.definition"},{foreground:"#50A14F",token:"meta.template.expression.js string.template punctuation.definition.string.template"},{foreground:"#50A14F",token:"meta.template.expression.jsx string.template punctuation.definition.string.template"},{foreground:"#50A14F",token:"meta.template.expression.ts string.template punctuation.definition.string.template"},{foreground:"#50A14F",token:"meta.template.expression.tsx string.template punctuation.definition.string.template"},{foreground:"#A626A4",token:"keyword.operator.expression.in.js"},{foreground:"#A626A4",token:"keyword.operator.expression.in.jsx"},{foreground:"#A626A4",token:"keyword.operator.expression.in.ts"},{foreground:"#A626A4",token:"keyword.operator.expression.in.tsx"},{foreground:"#383A42",token:"variable.other.object.js"},{foreground:"#383A42",token:"variable.other.object.ts"},{foreground:"#E45649",token:"meta.object-literal.key.js"},{foreground:"#E45649",token:"meta.object-literal.key.ts"},{foreground:"#383A42",token:"source.python constant.other"},{foreground:"#986801",token:"source.python constant"},{foreground:"#986801",token:"constant.character.format.placeholder.other.python storage"},{foreground:"#E45649",token:"support.variable.magic.python"},{foreground:"#986801",token:"meta.function.parameters.python"},{foreground:"#383A42",token:"punctuation.separator.annotation.python"},{foreground:"#383A42",token:"punctuation.separator.parameters.python"},{foreground:"#E45649",token:"entity.name.variable.field.cs"},{foreground:"#383A42",token:"source.cs keyword.operator"},{foreground:"#383A42",token:"variable.other.readwrite.cs"},{foreground:"#383A42",token:"variable.other.object.cs"},{foreground:"#383A42",token:"variable.other.object.property.cs"},{foreground:"#4078F2",token:"entity.name.variable.property.cs"},{foreground:"#C18401",token:"storage.type.cs"},{foreground:"#A626A4",token:"keyword.other.unsafe.rust"},{foreground:"#0184BC",token:"entity.name.type.rust"},{foreground:"#383A42",token:"storage.modifier.lifetime.rust"},{foreground:"#986801",token:"entity.name.lifetime.rust"},{foreground:"#0184BC",token:"storage.type.core.rust"},{foreground:"#986801",token:"meta.attribute.rust"},{foreground:"#0184BC",token:"storage.class.std.rust"},{foreground:"#383A42",token:"markup.raw.block.markdown"},{foreground:"#E45649",token:"punctuation.definition.variable.shell"},{foreground:"#383A42",token:"support.constant.property-value.css"},{foreground:"#986801",token:"punctuation.definition.constant.css"},{foreground:"#E45649",token:"punctuation.separator.key-value.scss"},{foreground:"#986801",token:"punctuation.definition.constant.scss"},{foreground:"#383A42",token:"meta.property-list.scss punctuation.separator.key-value.scss"},{foreground:"#C18401",token:"storage.type.primitive.array.java"},{foreground:"#E45649",token:"entity.name.section.markdown"},{foreground:"#E45649",token:"punctuation.definition.heading.markdown"},{foreground:"#383A42",token:"markup.heading.setext"},{foreground:"#986801",token:"punctuation.definition.bold.markdown"},{foreground:"#50A14F",token:"markup.inline.raw.markdown"},{foreground:"#E45649",token:"beginning.punctuation.definition.list.markdown"},{foreground:"#A0A1A7",fontStyle:"italic",token:"markup.quote.markdown"},{foreground:"#383A42",token:"punctuation.definition.string.begin.markdown"},{foreground:"#383A42",token:"punctuation.definition.string.end.markdown"},{foreground:"#383A42",token:"punctuation.definition.metadata.markdown"},{foreground:"#A626A4",token:"punctuation.definition.metadata.markdown"},{foreground:"#A626A4",token:"markup.underline.link.markdown"},{foreground:"#A626A4",token:"markup.underline.link.image.markdown"},{foreground:"#4078F2",token:"string.other.link.title.markdown"},{foreground:"#4078F2",token:"string.other.link.description.markdown"},{foreground:"#E45649",token:"punctuation.separator.variable.ruby"},{foreground:"#986801",token:"variable.other.constant.ruby"},{foreground:"#50A14F",token:"keyword.operator.other.ruby"},{foreground:"#E45649",token:"punctuation.definition.variable.php"},{foreground:"#383A42",token:"meta.class.php"},{foreground:"#316BCD",token:"token.info-token"},{foreground:"#CD9731",token:"token.warn-token"},{foreground:"#CD3131",token:"token.error-token"},{foreground:"#800080",token:"token.debug-token"},{token:"",foreground:"#383a42"}],encodedTokensColors:[]},L="#56b8f5",ao={inherit:!0,base:"vs-dark",colors:{"activityBar.background":"#333842","activityBar.foreground":"#d7dae0","activityBarBadge.background":"#528bff","activityBarBadge.foreground":"#d7dae0","menu.background":"#1d1c20","badge.background":"#528bff","badge.foreground":"#d7dae0","button.background":"#4d78cc","button.foreground":"#ffffff","button.hoverBackground":"#6087cf","diffEditor.insertedTextBackground":"#00809b33","dropdown.background":"#353b45","dropdown.border":"#181a1f","editor.background":"#18181c","editor.findMatchHighlightBackground":"#528bff3d","editor.foreground":"#abb2bf","editor.lineHighlightBackground":"#99bbff0a","editor.selectionBackground":"#3e4451","editorCursor.foreground":L,"editorGroup.border":"#181a1f","editorGroupHeader.tabsBackground":"#1d1c20","editorHoverWidget.background":"#1d1c20","editorHoverWidget.border":"#181a1f","editorIndentGuide.activeBackground":"#626772","editorIndentGuide.background":"#abb2bf26","editorInlayHint.background":"#ffffff17","editorInlayHint.foreground":"#636e83","editorLineNumber.activeForeground":L,"editorLineNumber.foreground":"#575757","editorRuler.foreground":"#abb2bf26","editorSuggestWidget.background":"#1d1c20","editorSuggestWidget.border":"#181a1f","editorSuggestWidget.selectedBackground":"#ffffff17","editorWhitespace.foreground":"#abb2bf26","editorWidget.background":"#1d1c20","editorWidget.border":"#3a3f4b","extensionButton.prominentBackground":"#2ba143","extensionButton.prominentHoverBackground":"#37af4e",focusBorder:"#528bff","input.background":"#ffffff14","input.border":"#181a1f","list.activeSelectionBackground":"#ffffff17","list.activeSelectionForeground":"#d7dae0","list.focusBackground":"#ffffff17","list.highlightForeground":"#d7dae0","list.hoverBackground":"#ffffff17","list.inactiveSelectionBackground":"#ffffff17","list.inactiveSelectionForeground":"#d7dae0","notebook.cellEditorBackground":"#ffffff17","peekView.border":"#528bff","peekViewEditor.background":"#1b1d23","peekViewResult.background":"#1d1c20","peekViewResult.selectionBackground":"#ffffff17","peekViewTitle.background":"#1b1d23","pickerGroup.border":"#528bff","scrollbarSlider.activeBackground":"#747d9180","scrollbarSlider.background":"#4e566680","scrollbarSlider.hoverBackground":"#5a637580","sideBar.background":"#1d1c20","sideBarSectionHeader.background":"#333842","statusBar.background":"#1d1c20","statusBar.debuggingForeground":"#ffffff","statusBar.foreground":"#9da5b4","statusBar.noFolderBackground":"#1d1c20","statusBarItem.hoverBackground":"#ffffff17","tab.activeBackground":"#282c34","tab.activeForeground":"#d7dae0","tab.border":"#181a1f","tab.inactiveBackground":"#1d1c20","titleBar.activeBackground":"#1d1c20","titleBar.activeForeground":"#9da5b4","titleBar.inactiveBackground":"#1d1c20","titleBar.inactiveForeground":"#9da5b4"},rules:[{foreground:"#7b7b7b",fontStyle:"italic",token:"comment"},{foreground:"#5C6370",token:"comment markup.link"},{foreground:"#5C6370",token:"markup.changed.diff"},{foreground:"#E5C07B",token:"entity.name.type"},{foreground:"#E5C07B",token:"entity.other.inherited-class"},{foreground:"#C678DD",token:"keyword"},{foreground:"#C678DD",token:"keyword.control"},{foreground:"#C678DD",token:"keyword.operator"},{foreground:"#61AFEF",token:"keyword.other.special-method"},{foreground:"#D19A66",token:"keyword.other.unit"},{foreground:"#C678DD",token:"storage"},{foreground:"#C678DD",token:"storage.type.annotation"},{foreground:"#C678DD",token:"storage.type.primitive"},{foreground:"#ABB2BF",token:"storage.modifier.package"},{foreground:"#ABB2BF",token:"storage.modifier.import"},{foreground:"#D19A66",token:"constant"},{foreground:"#D19A66",token:"constant.variable"},{foreground:"#56B6C2",token:"constant.character.escape"},{foreground:"#D19A66",token:"constant.numeric"},{foreground:"#56B6C2",token:"constant.other.color"},{foreground:"#56B6C2",token:"constant.other.symbol"},{foreground:"#E06C75",token:"variable"},{foreground:"#BE5046",token:"variable.interpolation"},{foreground:"#ABB2BF",token:"variable.parameter"},{foreground:"#98C379",token:"string"},{foreground:"#ABB2BF",token:"string > source"},{foreground:"#ABB2BF",token:"string embedded"},{foreground:"#56B6C2",token:"string.regexp"},{foreground:"#E5C07B",token:"string.regexp source.ruby.embedded"},{foreground:"#E06C75",token:"string.other.link"},{foreground:"#5C6370",token:"punctuation.definition.comment"},{foreground:"#ABB2BF",token:"punctuation.definition.method-parameters"},{foreground:"#ABB2BF",token:"punctuation.definition.function-parameters"},{foreground:"#ABB2BF",token:"punctuation.definition.parameters"},{foreground:"#ABB2BF",token:"punctuation.definition.separator"},{foreground:"#ABB2BF",token:"punctuation.definition.seperator"},{foreground:"#ABB2BF",token:"punctuation.definition.array"},{foreground:"#61AFEF",token:"punctuation.definition.heading"},{foreground:"#61AFEF",token:"punctuation.definition.identity"},{foreground:"#E5C07B",fontStyle:"bold",token:"punctuation.definition.bold"},{foreground:"#C678DD",fontStyle:"italic",token:"punctuation.definition.italic"},{foreground:"#BE5046",token:"punctuation.section.embedded"},{foreground:"#ABB2BF",token:"punctuation.section.method"},{foreground:"#ABB2BF",token:"punctuation.section.class"},{foreground:"#ABB2BF",token:"punctuation.section.inner-class"},{foreground:"#E5C07B",token:"support.class"},{foreground:"#56B6C2",token:"support.type"},{foreground:"#56B6C2",token:"support.function"},{foreground:"#61AFEF",token:"support.function.any-method"},{foreground:"#61AFEF",token:"entity.name.function"},{foreground:"#E5C07B",token:"entity.name.class"},{foreground:"#E5C07B",token:"entity.name.type.class"},{foreground:"#61AFEF",token:"entity.name.section"},{foreground:"#E06C75",token:"entity.name.tag"},{foreground:"#D19A66",token:"entity.other.attribute-name"},{foreground:"#61AFEF",token:"entity.other.attribute-name.id"},{foreground:"#E5C07B",token:"meta.class"},{foreground:"#ABB2BF",token:"meta.class.body"},{foreground:"#ABB2BF",token:"meta.method-call"},{foreground:"#ABB2BF",token:"meta.method"},{foreground:"#E06C75",token:"meta.definition.variable"},{foreground:"#D19A66",token:"meta.link"},{foreground:"#61AFEF",token:"meta.require"},{foreground:"#C678DD",token:"meta.selector"},{foreground:"#ABB2BF",token:"meta.separator"},{foreground:"#ABB2BF",token:"meta.tag"},{token:"underline"},{foreground:"#ABB2BF",token:"none"},{foreground:"#523D14",background:"#E0C285",token:"invalid.deprecated"},{background:"#E05252",token:"invalid.illegal"},{foreground:"#D19A66",fontStyle:"bold",token:"markup.bold"},{foreground:"#C678DD",token:"markup.changed"},{foreground:"#E06C75",token:"markup.deleted"},{foreground:"#C678DD",fontStyle:"italic",token:"markup.italic"},{foreground:"#E06C75",token:"markup.heading"},{foreground:"#61AFEF",token:"markup.heading punctuation.definition.heading"},{foreground:"#56B6C2",token:"markup.link"},{foreground:"#98C379",token:"markup.inserted"},{foreground:"#D19A66",token:"markup.quote"},{foreground:"#98C379",token:"markup.raw"},{foreground:"#C678DD",token:"source.c keyword.operator"},{foreground:"#C678DD",token:"source.cpp keyword.operator"},{foreground:"#C678DD",token:"source.cs keyword.operator"},{foreground:"#828997",token:"source.css property-name"},{foreground:"#828997",token:"source.css property-value"},{foreground:"#ABB2BF",token:"source.css property-name.support"},{foreground:"#ABB2BF",token:"source.css property-value.support"},{foreground:"#ABB2BF",token:"source.elixir source.embedded.source"},{foreground:"#61AFEF",token:"source.elixir constant.language"},{foreground:"#61AFEF",token:"source.elixir constant.numeric"},{foreground:"#61AFEF",token:"source.elixir constant.definition"},{foreground:"#C678DD",token:"source.elixir variable.definition"},{foreground:"#C678DD",token:"source.elixir variable.anonymous"},{foreground:"#D19A66",fontStyle:"italic",token:"source.elixir parameter.variable.function"},{foreground:"#98C379",token:"source.elixir quoted"},{foreground:"#E06C75",token:"source.elixir keyword.special-method"},{foreground:"#E06C75",token:"source.elixir embedded.section"},{foreground:"#E06C75",token:"source.elixir embedded.source.empty"},{foreground:"#E06C75",token:"source.elixir readwrite.module punctuation"},{foreground:"#BE5046",token:"source.elixir regexp.section"},{foreground:"#BE5046",token:"source.elixir regexp.string"},{foreground:"#D19A66",token:"source.elixir separator"},{foreground:"#D19A66",token:"source.elixir keyword.operator"},{foreground:"#E5C07B",token:"source.elixir variable.constant"},{foreground:"#828997",token:"source.elixir array"},{foreground:"#828997",token:"source.elixir scope"},{foreground:"#828997",token:"source.elixir section"},{token:"source.gfm markup"},{foreground:"#61AFEF",token:"source.gfm link entity"},{foreground:"#C678DD",token:"source.go storage.type.string"},{foreground:"#E06C75",token:"source.ini keyword.other.definition.ini"},{foreground:"#E5C07B",token:"source.java storage.modifier.import"},{foreground:"#E5C07B",token:"source.java storage.type"},{foreground:"#C678DD",token:"source.java keyword.operator.instanceof"},{foreground:"#E06C75",token:"source.java-properties meta.key-pair"},{foreground:"#ABB2BF",token:"source.java-properties meta.key-pair > punctuation"},{foreground:"#56B6C2",token:"source.js keyword.operator"},{foreground:"#C678DD",token:"source.js keyword.operator.delete"},{foreground:"#C678DD",token:"source.js keyword.operator.in"},{foreground:"#C678DD",token:"source.js keyword.operator.of"},{foreground:"#C678DD",token:"source.js keyword.operator.instanceof"},{foreground:"#C678DD",token:"source.js keyword.operator.new"},{foreground:"#C678DD",token:"source.js keyword.operator.typeof"},{foreground:"#C678DD",token:"source.js keyword.operator.void"},{foreground:"#56B6C2",token:"source.ts keyword.operator"},{foreground:"#56B6C2",token:"source.flow keyword.operator"},{foreground:"#E06C75",token:"source.json meta.structure.dictionary.json > string.quoted.json"},{foreground:"#E06C75",token:"source.json meta.structure.dictionary.json > string.quoted.json > punctuation.string"},{foreground:"#98C379",token:"source.json meta.structure.dictionary.json > value.json > string.quoted.json"},{foreground:"#98C379",token:"source.json meta.structure.array.json > value.json > string.quoted.json"},{foreground:"#98C379",token:"source.json meta.structure.dictionary.json > value.json > string.quoted.json > punctuation"},{foreground:"#98C379",token:"source.json meta.structure.array.json > value.json > string.quoted.json > punctuation"},{foreground:"#56B6C2",token:"source.json meta.structure.dictionary.json > constant.language.json"},{foreground:"#56B6C2",token:"source.json meta.structure.array.json > constant.language.json"},{foreground:"#E06C75",token:"ng.interpolation"},{foreground:"#61AFEF",token:"ng.interpolation.begin"},{foreground:"#61AFEF",token:"ng.interpolation.end"},{foreground:"#E06C75",token:"ng.interpolation function"},{foreground:"#61AFEF",token:"ng.interpolation function.begin"},{foreground:"#61AFEF",token:"ng.interpolation function.end"},{foreground:"#D19A66",token:"ng.interpolation bool"},{foreground:"#ABB2BF",token:"ng.interpolation bracket"},{foreground:"#ABB2BF",token:"ng.pipe"},{foreground:"#ABB2BF",token:"ng.operator"},{foreground:"#56B6C2",token:"ng.tag"},{foreground:"#E5C07B",token:"ng.attribute-with-value attribute-name"},{foreground:"#C678DD",token:"ng.attribute-with-value string"},{foreground:"#ABB2BF",token:"ng.attribute-with-value string.begin"},{foreground:"#ABB2BF",token:"ng.attribute-with-value string.end"},{token:"source.ruby constant.other.symbol > punctuation"},{foreground:"#ABB2BF",token:"source.php class.bracket"},{foreground:"#C678DD",token:"source.python keyword.operator.logical.python"},{foreground:"#D19A66",token:"source.python variable.parameter"},{foreground:"#ABB2BF",token:"customrule"},{foreground:"#ABB2BF",token:"support.type.property-name"},{foreground:"#98C379",token:"string.quoted.double punctuation"},{foreground:"#D19A66",token:"support.constant"},{foreground:"#E06C75",token:"support.type.property-name.json"},{foreground:"#E06C75",token:"support.type.property-name.json punctuation"},{foreground:"#56B6C2",token:"punctuation.separator.key-value.ts"},{foreground:"#56B6C2",token:"punctuation.separator.key-value.js"},{foreground:"#56B6C2",token:"punctuation.separator.key-value.tsx"},{foreground:"#56B6C2",token:"source.js.embedded.html keyword.operator"},{foreground:"#56B6C2",token:"source.ts.embedded.html keyword.operator"},{foreground:"#ABB2BF",token:"variable.other.readwrite.js"},{foreground:"#ABB2BF",token:"variable.other.readwrite.ts"},{foreground:"#ABB2BF",token:"variable.other.readwrite.tsx"},{foreground:"#E06C75",token:"support.variable.dom.js"},{foreground:"#E06C75",token:"support.variable.dom.ts"},{foreground:"#E06C75",token:"support.variable.property.dom.js"},{foreground:"#E06C75",token:"support.variable.property.dom.ts"},{foreground:"#BE5046",token:"meta.template.expression.js punctuation.definition"},{foreground:"#BE5046",token:"meta.template.expression.ts punctuation.definition"},{foreground:"#ABB2BF",token:"source.ts punctuation.definition.typeparameters"},{foreground:"#ABB2BF",token:"source.js punctuation.definition.typeparameters"},{foreground:"#ABB2BF",token:"source.tsx punctuation.definition.typeparameters"},{foreground:"#ABB2BF",token:"source.ts punctuation.definition.block"},{foreground:"#ABB2BF",token:"source.js punctuation.definition.block"},{foreground:"#ABB2BF",token:"source.tsx punctuation.definition.block"},{foreground:"#ABB2BF",token:"source.ts punctuation.separator.comma"},{foreground:"#ABB2BF",token:"source.js punctuation.separator.comma"},{foreground:"#ABB2BF",token:"source.tsx punctuation.separator.comma"},{foreground:"#E06C75",token:"support.variable.property.js"},{foreground:"#E06C75",token:"support.variable.property.ts"},{foreground:"#E06C75",token:"support.variable.property.tsx"},{foreground:"#E06C75",token:"keyword.control.default.js"},{foreground:"#E06C75",token:"keyword.control.default.ts"},{foreground:"#E06C75",token:"keyword.control.default.tsx"},{foreground:"#C678DD",token:"keyword.operator.expression.instanceof.js"},{foreground:"#C678DD",token:"keyword.operator.expression.instanceof.ts"},{foreground:"#C678DD",token:"keyword.operator.expression.instanceof.tsx"},{foreground:"#C678DD",token:"keyword.operator.expression.of.js"},{foreground:"#C678DD",token:"keyword.operator.expression.of.ts"},{foreground:"#C678DD",token:"keyword.operator.expression.of.tsx"},{foreground:"#ABB2BF",token:"meta.brace.round.js"},{foreground:"#ABB2BF",token:"meta.array-binding-pattern-variable.js"},{foreground:"#ABB2BF",token:"meta.brace.square.js"},{foreground:"#ABB2BF",token:"meta.brace.round.ts"},{foreground:"#ABB2BF",token:"meta.array-binding-pattern-variable.ts"},{foreground:"#ABB2BF",token:"meta.brace.square.ts"},{foreground:"#ABB2BF",token:"meta.brace.round.tsx"},{foreground:"#ABB2BF",token:"meta.array-binding-pattern-variable.tsx"},{foreground:"#ABB2BF",token:"meta.brace.square.tsx"},{foreground:"#ABB2BF",token:"source.js punctuation.accessor"},{foreground:"#ABB2BF",token:"source.ts punctuation.accessor"},{foreground:"#ABB2BF",token:"source.tsx punctuation.accessor"},{foreground:"#ABB2BF",token:"punctuation.terminator.statement.js"},{foreground:"#ABB2BF",token:"punctuation.terminator.statement.ts"},{foreground:"#ABB2BF",token:"punctuation.terminator.statement.tsx"},{foreground:"#D19A66",token:"meta.array-binding-pattern-variable.js variable.other.readwrite.js"},{foreground:"#D19A66",token:"meta.array-binding-pattern-variable.ts variable.other.readwrite.ts"},{foreground:"#D19A66",token:"meta.array-binding-pattern-variable.tsx variable.other.readwrite.tsx"},{foreground:"#E06C75",token:"source.js support.variable"},{foreground:"#E06C75",token:"source.ts support.variable"},{foreground:"#E06C75",token:"source.tsx support.variable"},{foreground:"#D19A66",token:"variable.other.constant.property.js"},{foreground:"#D19A66",token:"variable.other.constant.property.ts"},{foreground:"#D19A66",token:"variable.other.constant.property.tsx"},{foreground:"#C678DD",token:"keyword.operator.new.ts"},{foreground:"#C678DD",token:"keyword.operator.new.j"},{foreground:"#C678DD",token:"keyword.operator.new.tsx"},{foreground:"#56B6C2",token:"source.ts keyword.operator"},{foreground:"#56B6C2",token:"source.tsx keyword.operator"},{foreground:"#ABB2BF",token:"punctuation.separator.parameter.js"},{foreground:"#ABB2BF",token:"punctuation.separator.parameter.ts"},{foreground:"#ABB2BF",token:"punctuation.separator.parameter.tsx"},{foreground:"#E06C75",token:"constant.language.import-export-all.js"},{foreground:"#E06C75",token:"constant.language.import-export-all.ts"},{foreground:"#56B6C2",token:"constant.language.import-export-all.jsx"},{foreground:"#56B6C2",token:"constant.language.import-export-all.tsx"},{foreground:"#ABB2BF",token:"keyword.control.as.js"},{foreground:"#ABB2BF",token:"keyword.control.as.ts"},{foreground:"#ABB2BF",token:"keyword.control.as.jsx"},{foreground:"#ABB2BF",token:"keyword.control.as.tsx"},{foreground:"#E06C75",token:"variable.other.readwrite.alias.js"},{foreground:"#E06C75",token:"variable.other.readwrite.alias.ts"},{foreground:"#E06C75",token:"variable.other.readwrite.alias.jsx"},{foreground:"#E06C75",token:"variable.other.readwrite.alias.tsx"},{foreground:"#D19A66",token:"variable.other.constant.js"},{foreground:"#D19A66",token:"variable.other.constant.ts"},{foreground:"#D19A66",token:"variable.other.constant.jsx"},{foreground:"#D19A66",token:"variable.other.constant.tsx"},{foreground:"#E06C75",token:"meta.export.default.js variable.other.readwrite.js"},{foreground:"#E06C75",token:"meta.export.default.ts variable.other.readwrite.ts"},{foreground:"#98C379",token:"source.js meta.template.expression.js punctuation.accessor"},{foreground:"#98C379",token:"source.ts meta.template.expression.ts punctuation.accessor"},{foreground:"#98C379",token:"source.tsx meta.template.expression.tsx punctuation.accessor"},{foreground:"#ABB2BF",token:"source.js meta.import-equals.external.js keyword.operator"},{foreground:"#ABB2BF",token:"source.jsx meta.import-equals.external.jsx keyword.operator"},{foreground:"#ABB2BF",token:"source.ts meta.import-equals.external.ts keyword.operator"},{foreground:"#ABB2BF",token:"source.tsx meta.import-equals.external.tsx keyword.operator"},{foreground:"#98C379",token:"entity.name.type.module.js"},{foreground:"#98C379",token:"entity.name.type.module.ts"},{foreground:"#98C379",token:"entity.name.type.module.jsx"},{foreground:"#98C379",token:"entity.name.type.module.tsx"},{foreground:"#ABB2BF",token:"meta.class.js"},{foreground:"#ABB2BF",token:"meta.class.ts"},{foreground:"#ABB2BF",token:"meta.class.jsx"},{foreground:"#ABB2BF",token:"meta.class.tsx"},{foreground:"#ABB2BF",token:"meta.definition.property.js variable"},{foreground:"#ABB2BF",token:"meta.definition.property.ts variable"},{foreground:"#ABB2BF",token:"meta.definition.property.jsx variable"},{foreground:"#ABB2BF",token:"meta.definition.property.tsx variable"},{foreground:"#ABB2BF",token:"meta.type.parameters.js support.type"},{foreground:"#ABB2BF",token:"meta.type.parameters.jsx support.type"},{foreground:"#ABB2BF",token:"meta.type.parameters.ts support.type"},{foreground:"#ABB2BF",token:"meta.type.parameters.tsx support.type"},{foreground:"#ABB2BF",token:"source.js meta.tag.js keyword.operator"},{foreground:"#ABB2BF",token:"source.jsx meta.tag.jsx keyword.operator"},{foreground:"#ABB2BF",token:"source.ts meta.tag.ts keyword.operator"},{foreground:"#ABB2BF",token:"source.tsx meta.tag.tsx keyword.operator"},{foreground:"#ABB2BF",token:"meta.tag.js punctuation.section.embedded"},{foreground:"#ABB2BF",token:"meta.tag.jsx punctuation.section.embedded"},{foreground:"#ABB2BF",token:"meta.tag.ts punctuation.section.embedded"},{foreground:"#ABB2BF",token:"meta.tag.tsx punctuation.section.embedded"},{foreground:"#E5C07B",token:"meta.array.literal.js variable"},{foreground:"#E5C07B",token:"meta.array.literal.jsx variable"},{foreground:"#E5C07B",token:"meta.array.literal.ts variable"},{foreground:"#E5C07B",token:"meta.array.literal.tsx variable"},{foreground:"#E06C75",token:"support.type.object.module.js"},{foreground:"#E06C75",token:"support.type.object.module.jsx"},{foreground:"#E06C75",token:"support.type.object.module.ts"},{foreground:"#E06C75",token:"support.type.object.module.tsx"},{foreground:"#56B6C2",token:"constant.language.json"},{foreground:"#D19A66",token:"variable.other.constant.object.js"},{foreground:"#D19A66",token:"variable.other.constant.object.jsx"},{foreground:"#D19A66",token:"variable.other.constant.object.ts"},{foreground:"#D19A66",token:"variable.other.constant.object.tsx"},{foreground:"#56B6C2",token:"storage.type.property.js"},{foreground:"#56B6C2",token:"storage.type.property.jsx"},{foreground:"#56B6C2",token:"storage.type.property.ts"},{foreground:"#56B6C2",token:"storage.type.property.tsx"},{foreground:"#98C379",token:"meta.template.expression.js string.quoted punctuation.definition"},{foreground:"#98C379",token:"meta.template.expression.jsx string.quoted punctuation.definition"},{foreground:"#98C379",token:"meta.template.expression.ts string.quoted punctuation.definition"},{foreground:"#98C379",token:"meta.template.expression.tsx string.quoted punctuation.definition"},{foreground:"#98C379",token:"meta.template.expression.js string.template punctuation.definition.string.template"},{foreground:"#98C379",token:"meta.template.expression.jsx string.template punctuation.definition.string.template"},{foreground:"#98C379",token:"meta.template.expression.ts string.template punctuation.definition.string.template"},{foreground:"#98C379",token:"meta.template.expression.tsx string.template punctuation.definition.string.template"},{foreground:"#C678DD",token:"keyword.operator.expression.in.js"},{foreground:"#C678DD",token:"keyword.operator.expression.in.jsx"},{foreground:"#C678DD",token:"keyword.operator.expression.in.ts"},{foreground:"#C678DD",token:"keyword.operator.expression.in.tsx"},{foreground:"#ABB2BF",token:"variable.other.object.js"},{foreground:"#ABB2BF",token:"variable.other.object.ts"},{foreground:"#E06C75",token:"meta.object-literal.key.js"},{foreground:"#E06C75",token:"meta.object-literal.key.ts"},{foreground:"#ABB2BF",token:"source.python constant.other"},{foreground:"#D19A66",token:"source.python constant"},{foreground:"#D19A66",token:"constant.character.format.placeholder.other.python storage"},{foreground:"#E06C75",token:"support.variable.magic.python"},{foreground:"#D19A66",token:"meta.function.parameters.python"},{foreground:"#ABB2BF",token:"punctuation.separator.annotation.python"},{foreground:"#ABB2BF",token:"punctuation.separator.parameters.python"},{foreground:"#E06C75",token:"entity.name.variable.field.cs"},{foreground:"#ABB2BF",token:"source.cs keyword.operator"},{foreground:"#ABB2BF",token:"variable.other.readwrite.cs"},{foreground:"#ABB2BF",token:"variable.other.object.cs"},{foreground:"#ABB2BF",token:"variable.other.object.property.cs"},{foreground:"#61AFEF",token:"entity.name.variable.property.cs"},{foreground:"#E5C07B",token:"storage.type.cs"},{foreground:"#C678DD",token:"keyword.other.unsafe.rust"},{foreground:"#56B6C2",token:"entity.name.type.rust"},{foreground:"#ABB2BF",token:"storage.modifier.lifetime.rust"},{foreground:"#D19A66",token:"entity.name.lifetime.rust"},{foreground:"#56B6C2",token:"storage.type.core.rust"},{foreground:"#D19A66",token:"meta.attribute.rust"},{foreground:"#56B6C2",token:"storage.class.std.rust"},{foreground:"#ABB2BF",token:"markup.raw.block.markdown"},{foreground:"#E06C75",token:"punctuation.definition.variable.shell"},{foreground:"#ABB2BF",token:"support.constant.property-value.css"},{foreground:"#D19A66",token:"punctuation.definition.constant.css"},{foreground:"#E06C75",token:"punctuation.separator.key-value.scss"},{foreground:"#D19A66",token:"punctuation.definition.constant.scss"},{foreground:"#ABB2BF",token:"meta.property-list.scss punctuation.separator.key-value.scss"},{foreground:"#E5C07B",token:"storage.type.primitive.array.java"},{foreground:"#E06C75",token:"entity.name.section.markdown"},{foreground:"#E06C75",token:"punctuation.definition.heading.markdown"},{foreground:"#ABB2BF",token:"markup.heading.setext"},{foreground:"#D19A66",token:"punctuation.definition.bold.markdown"},{foreground:"#98C379",token:"markup.inline.raw.markdown"},{foreground:"#E06C75",token:"beginning.punctuation.definition.list.markdown"},{foreground:"#5C6370",fontStyle:"italic",token:"markup.quote.markdown"},{foreground:"#ABB2BF",token:"punctuation.definition.string.begin.markdown"},{foreground:"#ABB2BF",token:"punctuation.definition.string.end.markdown"},{foreground:"#ABB2BF",token:"punctuation.definition.metadata.markdown"},{foreground:"#C678DD",token:"punctuation.definition.metadata.markdown"},{foreground:"#C678DD",token:"markup.underline.link.markdown"},{foreground:"#C678DD",token:"markup.underline.link.image.markdown"},{foreground:"#61AFEF",token:"string.other.link.title.markdown"},{foreground:"#61AFEF",token:"string.other.link.description.markdown"},{foreground:"#E06C75",token:"punctuation.separator.variable.ruby"},{foreground:"#D19A66",token:"variable.other.constant.ruby"},{foreground:"#98C379",token:"keyword.operator.other.ruby"},{foreground:"#E06C75",token:"punctuation.definition.variable.php"},{foreground:"#ABB2BF",token:"meta.class.php"},{foreground:"#6796E6",token:"token.info-token"},{foreground:"#CD9731",token:"token.warn-token"},{foreground:"#F44747",token:"token.error-token"},{foreground:"#B267E6",token:"token.debug-token"},{token:"",foreground:"#ffffff"}],encodedTokensColors:[]},go=M({__name:"index",props:{cardSize:{default:500},value:{default:{content:"",language:"plaintext"}},originValue:{default:""},diffMode:{default:!1},wordWrap:{default:"off"},renderSideBySide:{default:!0},fontSize:{default:0},readOnly:{default:!1},minimap:{default:!0},findBox:{default:!1}},setup(f,{expose:a}){const n=f,F=Q(),{originValue:E,diffMode:b,wordWrap:c,renderSideBySide:r,fontSize:v,readOnly:B,minimap:w,findBox:x}=Y(n),y=T(),h=T(!1);let s=null;k.defineTheme("vs",JSON.parse(JSON.stringify(no))),k.defineTheme("vs-dark",JSON.parse(JSON.stringify(ao)));const D={theme:F.darkMode?"vs-dark":"vs",fontFamily:'"SF Mono", "JetBrains Mono", Consolas, "Courier New", monospace',fontSize:v.value||13,fontLigatures:!0,lineNumbers:"on",lineNumbersMinChars:1,padding:{top:20},cursorStyle:"line",cursorBlinking:"smooth",cursorSmoothCaretAnimation:!1,lineHeight:1.5,automaticLayout:!0,autoIndent:"full",quickSuggestionsDelay:200,roundedSelection:!0,glyphMargin:!0,smoothScrolling:!0,wordWrap:c.value,folding:!0,colorDecorators:!0,occurrencesHighlight:!1,unicodeHighlight:{ambiguousCharacters:!1},inlineSuggest:{enabled:!0,showToolbar:"onHover"},minimap:{enabled:w.value,autohide:!ee.isMobile.value,maxColumn:120},stickyScroll:{enabled:!0,maxLineCount:5},guides:{highlightActiveBracketPair:!0,bracketPairs:"active",bracketPairsHorizontal:"active"},readOnly:B.value};oe(()=>{y.value&&(b.value?(s=k.createDiffEditor(y.value,_(j({},D),{hideUnchangedRegions:{contextLineCount:3,enabled:!0,minimumLineCount:3,revealLineCount:20},diffWordWrap:c.value,useInlineViewWhenSpaceIsLimited:!1,lineDecorationsWidth:11,ignoreTrimWhitespace:!1,renderSideBySide:r.value})),s.setModel({original:k.createModel(n.originValue,n.value.language),modified:k.createModel(n.value.content,n.value.language)})):(s=k.create(y.value,_(j({},D),{value:n.value.content,language:n.value.language,lineDecorationsWidth:0})),s.getModel().setEOL(k.EndOfLineSequence.LF)))}),re(()=>{s&&(s.dispose(),k.getModels().forEach(g=>g.dispose()))});function $(){return s?b.value?s.getModifiedEditor().getValue():s.getValue():""}return C(()=>n.value,g=>{if(s)if(b.value){const p=s==null?void 0:s.getModel().modified;p&&p.getLanguageId()!==g.language&&k.setModelLanguage(p,g.language),s.setModel({original:k.createModel(n.originValue,g.language),modified:k.createModel(g.content,g.language)})}else{const p=s==null?void 0:s.getModel();p&&p.getLanguageId()!==g.language&&k.setModelLanguage(p,g.language),s.setValue(g.content)}}),C(E,g=>{s&&s.setModel({original:k.createModel(g,n.value.language),modified:k.createModel(n.value.content,n.value.language)})}),C([c,r,v,B,w],([g,p,A,Z,W])=>{s&&s.updateOptions({wordWrap:g,renderSideBySide:p,fontSize:A,readOnly:Z,minimap:{enabled:W}})}),C(x,()=>{s&&s.getAction("actions.find").run()}),a({getValue:$,showShortCuts:h}),(g,p)=>(u(),i("div",null,[d("div",{ref_key:"codeEditBox",ref:y,style:{height:"100%"}},null,512),e(to,{show:h.value,"onUpdate:show":p[0]||(p[0]=A=>h.value=A)},null,8,["show"])]))}});export{so as _,fo as a,go as b};
