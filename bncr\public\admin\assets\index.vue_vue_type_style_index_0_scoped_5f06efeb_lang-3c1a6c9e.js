import{ax as jt,ay as Ut,d as Vt,R as $t,u as qt,a0 as Gt,V as Zt,o as tt,p as et,w as pt,e as Jt,az as Qt,h as L,aA as te,a2 as ee,n as oe,q as re}from"./index-b380aaed.js";var wt={exports:{}};/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */(function(o,t){(function(r,n){o.exports=n()})(jt,function(){return function(){var e={686:function(i,a,s){s.d(a,{default:function(){return Wt}});var l=s(279),c=s.n(l),h=s(370),f=s.n(h),g=s(817),S=s.n(g);function v(m){try{return document.execCommand(m)}catch(p){return!1}}var b=function(p){var u=S()(p);return v("cut"),u},k=b;function P(m){var p=document.documentElement.getAttribute("dir")==="rtl",u=document.createElement("textarea");u.style.fontSize="12pt",u.style.border="0",u.style.padding="0",u.style.margin="0",u.style.position="absolute",u.style[p?"right":"left"]="-9999px";var d=window.pageYOffset||document.documentElement.scrollTop;return u.style.top="".concat(d,"px"),u.setAttribute("readonly",""),u.value=m,u}var ht=function(p,u){var d=P(p);u.container.appendChild(d);var y=S()(d);return v("copy"),d.remove(),y},Ot=function(p){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body},d="";return typeof p=="string"?d=ht(p,u):p instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(p==null?void 0:p.type)?d=ht(p.value,u):(d=S()(p),v("copy")),d},Z=Ot;function H(m){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?H=function(u){return typeof u}:H=function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},H(m)}var Xt=function(){var p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=p.action,d=u===void 0?"copy":u,y=p.container,T=p.target,w=p.text;if(d!=="copy"&&d!=="cut")throw new Error('Invalid "action" value, use either "copy" or "cut"');if(T!==void 0)if(T&&H(T)==="object"&&T.nodeType===1){if(d==="copy"&&T.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if(d==="cut"&&(T.hasAttribute("readonly")||T.hasAttribute("disabled")))throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`)}else throw new Error('Invalid "target" value, use a valid Element');if(w)return Z(w,{container:y});if(T)return d==="cut"?k(T):Z(T,{container:y})},Yt=Xt;function Y(m){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Y=function(u){return typeof u}:Y=function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},Y(m)}function Lt(m,p){if(!(m instanceof p))throw new TypeError("Cannot call a class as a function")}function ut(m,p){for(var u=0;u<p.length;u++){var d=p[u];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(m,d.key,d)}}function Rt(m,p,u){return p&&ut(m.prototype,p),u&&ut(m,u),m}function Ft(m,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function");m.prototype=Object.create(p&&p.prototype,{constructor:{value:m,writable:!0,configurable:!0}}),p&&J(m,p)}function J(m,p){return J=Object.setPrototypeOf||function(d,y){return d.__proto__=y,d},J(m,p)}function Ht(m){var p=Nt();return function(){var d=z(m),y;if(p){var T=z(this).constructor;y=Reflect.construct(d,arguments,T)}else y=d.apply(this,arguments);return zt(this,y)}}function zt(m,p){return p&&(Y(p)==="object"||typeof p=="function")?p:Kt(m)}function Kt(m){if(m===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m}function Nt(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(m){return!1}}function z(m){return z=Object.setPrototypeOf?Object.getPrototypeOf:function(u){return u.__proto__||Object.getPrototypeOf(u)},z(m)}function Q(m,p){var u="data-clipboard-".concat(m);if(p.hasAttribute(u))return p.getAttribute(u)}var It=function(m){Ft(u,m);var p=Ht(u);function u(d,y){var T;return Lt(this,u),T=p.call(this),T.resolveOptions(y),T.listenClick(d),T}return Rt(u,[{key:"resolveOptions",value:function(){var y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.action=typeof y.action=="function"?y.action:this.defaultAction,this.target=typeof y.target=="function"?y.target:this.defaultTarget,this.text=typeof y.text=="function"?y.text:this.defaultText,this.container=Y(y.container)==="object"?y.container:document.body}},{key:"listenClick",value:function(y){var T=this;this.listener=f()(y,"click",function(w){return T.onClick(w)})}},{key:"onClick",value:function(y){var T=y.delegateTarget||y.currentTarget,w=this.action(T)||"copy",K=Yt({action:w,container:this.container,target:this.target(T),text:this.text(T)});this.emit(K?"success":"error",{action:w,text:K,trigger:T,clearSelection:function(){T&&T.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(y){return Q("action",y)}},{key:"defaultTarget",value:function(y){var T=Q("target",y);if(T)return document.querySelector(T)}},{key:"defaultText",value:function(y){return Q("text",y)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(y){var T=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body};return Z(y,T)}},{key:"cut",value:function(y){return k(y)}},{key:"isSupported",value:function(){var y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["copy","cut"],T=typeof y=="string"?[y]:y,w=!!document.queryCommandSupported;return T.forEach(function(K){w=w&&!!document.queryCommandSupported(K)}),w}}]),u}(c()),Wt=It},828:function(i){var a=9;if(typeof Element!="undefined"&&!Element.prototype.matches){var s=Element.prototype;s.matches=s.matchesSelector||s.mozMatchesSelector||s.msMatchesSelector||s.oMatchesSelector||s.webkitMatchesSelector}function l(c,h){for(;c&&c.nodeType!==a;){if(typeof c.matches=="function"&&c.matches(h))return c;c=c.parentNode}}i.exports=l},438:function(i,a,s){var l=s(828);function c(g,S,v,b,k){var P=f.apply(this,arguments);return g.addEventListener(v,P,k),{destroy:function(){g.removeEventListener(v,P,k)}}}function h(g,S,v,b,k){return typeof g.addEventListener=="function"?c.apply(null,arguments):typeof v=="function"?c.bind(null,document).apply(null,arguments):(typeof g=="string"&&(g=document.querySelectorAll(g)),Array.prototype.map.call(g,function(P){return c(P,S,v,b,k)}))}function f(g,S,v,b){return function(k){k.delegateTarget=l(k.target,S),k.delegateTarget&&b.call(g,k)}}i.exports=h},879:function(i,a){a.node=function(s){return s!==void 0&&s instanceof HTMLElement&&s.nodeType===1},a.nodeList=function(s){var l=Object.prototype.toString.call(s);return s!==void 0&&(l==="[object NodeList]"||l==="[object HTMLCollection]")&&"length"in s&&(s.length===0||a.node(s[0]))},a.string=function(s){return typeof s=="string"||s instanceof String},a.fn=function(s){var l=Object.prototype.toString.call(s);return l==="[object Function]"}},370:function(i,a,s){var l=s(879),c=s(438);function h(v,b,k){if(!v&&!b&&!k)throw new Error("Missing required arguments");if(!l.string(b))throw new TypeError("Second argument must be a String");if(!l.fn(k))throw new TypeError("Third argument must be a Function");if(l.node(v))return f(v,b,k);if(l.nodeList(v))return g(v,b,k);if(l.string(v))return S(v,b,k);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function f(v,b,k){return v.addEventListener(b,k),{destroy:function(){v.removeEventListener(b,k)}}}function g(v,b,k){return Array.prototype.forEach.call(v,function(P){P.addEventListener(b,k)}),{destroy:function(){Array.prototype.forEach.call(v,function(P){P.removeEventListener(b,k)})}}}function S(v,b,k){return c(document.body,v,b,k)}i.exports=h},817:function(i){function a(s){var l;if(s.nodeName==="SELECT")s.focus(),l=s.value;else if(s.nodeName==="INPUT"||s.nodeName==="TEXTAREA"){var c=s.hasAttribute("readonly");c||s.setAttribute("readonly",""),s.select(),s.setSelectionRange(0,s.value.length),c||s.removeAttribute("readonly"),l=s.value}else{s.hasAttribute("contenteditable")&&s.focus();var h=window.getSelection(),f=document.createRange();f.selectNodeContents(s),h.removeAllRanges(),h.addRange(f),l=h.toString()}return l}i.exports=a},279:function(i){function a(){}a.prototype={on:function(s,l,c){var h=this.e||(this.e={});return(h[s]||(h[s]=[])).push({fn:l,ctx:c}),this},once:function(s,l,c){var h=this;function f(){h.off(s,f),l.apply(c,arguments)}return f._=l,this.on(s,f,c)},emit:function(s){var l=[].slice.call(arguments,1),c=((this.e||(this.e={}))[s]||[]).slice(),h=0,f=c.length;for(h;h<f;h++)c[h].fn.apply(c[h].ctx,l);return this},off:function(s,l){var c=this.e||(this.e={}),h=c[s],f=[];if(h&&l)for(var g=0,S=h.length;g<S;g++)h[g].fn!==l&&h[g].fn._!==l&&f.push(h[g]);return f.length?c[s]=f:delete c[s],this}},i.exports=a,i.exports.TinyEmitter=a}},r={};function n(i){if(r[i])return r[i].exports;var a=r[i]={exports:{}};return e[i](a,a.exports,n),a.exports}return function(){n.n=function(i){var a=i&&i.__esModule?function(){return i.default}:function(){return i};return n.d(a,{a}),a}}(),function(){n.d=function(i,a){for(var s in a)n.o(a,s)&&!n.o(i,s)&&Object.defineProperty(i,s,{enumerable:!0,get:a[s]})}}(),function(){n.o=function(i,a){return Object.prototype.hasOwnProperty.call(i,a)}}(),n(686)}().default})})(wt);var ne=wt.exports;const He=Ut(ne);/*!
 * better-scroll / core
 * (c) 2016-2023 ustbhuangyi
 * Released under the MIT License.
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var it=function(o,t){return it=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])},it(o,t)};function $(o,t){it(o,t);function e(){this.constructor=o}o.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var R=function(){return R=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++){e=arguments[r];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},R.apply(this,arguments)};function Pt(){for(var o=0,t=0,e=arguments.length;t<e;t++)o+=arguments[t].length;for(var r=Array(o),n=0,t=0;t<e;t++)for(var i=arguments[t],a=0,s=i.length;a<s;a++,n++)r[n]=i[a];return r}var ie=[{sourceKey:"scroller.scrollBehaviorX.currentPos",key:"x"},{sourceKey:"scroller.scrollBehaviorY.currentPos",key:"y"},{sourceKey:"scroller.scrollBehaviorX.hasScroll",key:"hasHorizontalScroll"},{sourceKey:"scroller.scrollBehaviorY.hasScroll",key:"hasVerticalScroll"},{sourceKey:"scroller.scrollBehaviorX.contentSize",key:"scrollerWidth"},{sourceKey:"scroller.scrollBehaviorY.contentSize",key:"scrollerHeight"},{sourceKey:"scroller.scrollBehaviorX.maxScrollPos",key:"maxScrollX"},{sourceKey:"scroller.scrollBehaviorY.maxScrollPos",key:"maxScrollY"},{sourceKey:"scroller.scrollBehaviorX.minScrollPos",key:"minScrollX"},{sourceKey:"scroller.scrollBehaviorY.minScrollPos",key:"minScrollY"},{sourceKey:"scroller.scrollBehaviorX.movingDirection",key:"movingDirectionX"},{sourceKey:"scroller.scrollBehaviorY.movingDirection",key:"movingDirectionY"},{sourceKey:"scroller.scrollBehaviorX.direction",key:"directionX"},{sourceKey:"scroller.scrollBehaviorY.direction",key:"directionY"},{sourceKey:"scroller.actions.enabled",key:"enabled"},{sourceKey:"scroller.animater.pending",key:"pending"},{sourceKey:"scroller.animater.stop",key:"stop"},{sourceKey:"scroller.scrollTo",key:"scrollTo"},{sourceKey:"scroller.scrollBy",key:"scrollBy"},{sourceKey:"scroller.scrollToElement",key:"scrollToElement"},{sourceKey:"scroller.resetPosition",key:"resetPosition"}];function U(o){console.error("[BScroll warn]: "+o)}var E=typeof window!="undefined",X=E&&navigator.userAgent.toLowerCase(),se=!!(X&&/wechatdevtools/.test(X)),ae=X&&X.indexOf("android")>0,ce=function(){if(typeof X=="string"){var o=/os (\d\d?_\d(_\d)?)/,t=o.exec(X);if(!t)return!1;var e=t[1].split("_").map(function(r){return parseInt(r,10)});return e[0]===13&&e[1]>=4}return!1}(),Et=!1;if(E){var le="test-passive";try{var ft={};Object.defineProperty(ft,"passive",{get:function(){Et=!0}}),window.addEventListener(le,function(){},ft)}catch(o){}}function A(){return window.performance&&window.performance.now&&window.performance.timing?window.performance.now()+window.performance.timing.navigationStart:+new Date}var st=function(o,t){for(var e in t)o[e]=t[e];return o};function at(o){return o==null}function vt(o,t,e){return o<t?t:o>e?e:o}var lt=E&&document.createElement("div").style,O=function(){if(!E)return!1;for(var o=[{key:"standard",value:"transform"},{key:"webkit",value:"webkitTransform"},{key:"Moz",value:"MozTransform"},{key:"O",value:"OTransform"},{key:"ms",value:"msTransform"}],t=0,e=o;t<e.length;t++){var r=e[t];if(lt[r.value]!==void 0)return r.key}return!1}();function _(o){return O===!1?o:O==="standard"?o==="transitionEnd"?"transitionend":o:O+o.charAt(0).toUpperCase()+o.substr(1)}function _t(o){return typeof o=="string"?document.querySelector(o):o}function he(o,t,e,r){var n=Et?{passive:!1,capture:!!r}:!!r;o.addEventListener(t,e,n)}function ue(o,t,e,r){o.removeEventListener(t,e,{capture:!!r})}function Ct(o){o.cancelable&&o.preventDefault()}function dt(o){for(var t=0,e=0;o;)t-=o.offsetLeft,e-=o.offsetTop,o=o.offsetParent;return{left:t,top:e}}O&&O!=="standard"&&""+O.toLowerCase();var pe=_("transform"),xt=_("transition"),fe=E&&_("perspective")in lt,yt=E&&("ontouchstart"in window||se),ve=E&&xt in lt,D={transform:pe,transition:xt,transitionTimingFunction:_("transitionTimingFunction"),transitionDuration:_("transitionDuration"),transitionDelay:_("transitionDelay"),transformOrigin:_("transformOrigin"),transitionEnd:_("transitionEnd"),transitionProperty:_("transitionProperty")},ot={touchstart:1,touchmove:1,touchend:1,touchcancel:1,mousedown:2,mousemove:2,mouseup:2};function mt(o){if(o instanceof window.SVGElement){var t=o.getBoundingClientRect();return{top:t.top,left:t.left,width:t.width,height:t.height}}else return{top:o.offsetTop,left:o.offsetLeft,width:o.offsetWidth,height:o.offsetHeight}}function F(o,t){for(var e in t)if(t[e].test(o[e]))return!0;return!1}var de=F;function ye(o,t){var e=document.createEvent("Event");e.initEvent(t,!0,!0),e.pageX=o.pageX,e.pageY=o.pageY,o.target.dispatchEvent(e)}function Bt(o,t){t===void 0&&(t="click");var e;o.type==="mouseup"?e=o:(o.type==="touchend"||o.type==="touchcancel")&&(e=o.changedTouches[0]);var r={};e&&(r.screenX=e.screenX||0,r.screenY=e.screenY||0,r.clientX=e.clientX||0,r.clientY=e.clientY||0);var n,i=!0,a=!0,s=o.ctrlKey,l=o.shiftKey,c=o.altKey,h=o.metaKey,f={ctrlKey:s,shiftKey:l,altKey:c,metaKey:h};if(typeof MouseEvent!="undefined")try{n=new MouseEvent(t,st(R({bubbles:i,cancelable:a},f),r))}catch(S){g()}else g();function g(){n=document.createEvent("Event"),n.initEvent(t,i,a),st(n,r)}n.forwardedTouchEvent=!0,n._constructed=!0,o.target.dispatchEvent(n)}function me(o){Bt(o,"dblclick")}var B={swipe:{style:"cubic-bezier(0.23, 1, 0.32, 1)",fn:function(o){return 1+--o*o*o*o*o}},swipeBounce:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(o){return o*(2-o)}},bounce:{style:"cubic-bezier(0.165, 0.84, 0.44, 1)",fn:function(o){return 1- --o*o*o*o}}},ge=1e3/60,C=E&&window;function Dt(){}var At=function(){return E?C.requestAnimationFrame||C.webkitRequestAnimationFrame||C.mozRequestAnimationFrame||C.oRequestAnimationFrame||function(o){return window.setTimeout(o,o.interval||ge)}:Dt}(),M=function(){return E?C.cancelAnimationFrame||C.webkitCancelAnimationFrame||C.mozCancelAnimationFrame||C.oCancelAnimationFrame||function(o){window.clearTimeout(o)}:Dt}(),gt=function(o){},rt={enumerable:!0,configurable:!0,get:gt,set:gt},Te=function(o,t){for(var e=t.split("."),r=0;r<e.length-1;r++)if(o=o[e[r]],typeof o!="object"||!o)return;var n=e.pop();return typeof o[n]=="function"?function(){return o[n].apply(o,arguments)}:o[n]},ke=function(o,t,e){for(var r=t.split("."),n,i=0;i<r.length-1;i++)n=r[i],o[n]||(o[n]={}),o=o[n];o[r.pop()]=e};function be(o,t,e){rt.get=function(){return Te(this,t)},rt.set=function(n){ke(this,t,n)},Object.defineProperty(o,e,rt)}var x=function(){function o(t){this.events={},this.eventTypes={},this.registerType(t)}return o.prototype.on=function(t,e,r){return r===void 0&&(r=this),this.hasType(t),this.events[t]||(this.events[t]=[]),this.events[t].push([e,r]),this},o.prototype.once=function(t,e,r){var n=this;r===void 0&&(r=this),this.hasType(t);var i=function(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];n.off(t,i);var l=e.apply(r,a);if(l===!0)return l};return i.fn=e,this.on(t,i),this},o.prototype.off=function(t,e){if(!t&&!e)return this.events={},this;if(t){if(this.hasType(t),!e)return this.events[t]=[],this;var r=this.events[t];if(!r)return this;for(var n=r.length;n--;)(r[n][0]===e||r[n][0]&&r[n][0].fn===e)&&r.splice(n,1);return this}},o.prototype.trigger=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];this.hasType(t);var n=this.events[t];if(n)for(var i=n.length,a=Pt(n),s,l=0;l<i;l++){var c=a[l],h=c[0],f=c[1];if(h&&(s=h.apply(f,e),s===!0))return s}},o.prototype.registerType=function(t){var e=this;t.forEach(function(r){e.eventTypes[r]=r})},o.prototype.destroy=function(){this.events={},this.eventTypes={}},o.prototype.hasType=function(t){var e=this.eventTypes,r=e[t]===t;r||U('EventEmitter has used unknown event type: "'+t+'", should be oneof ['+(""+Object.keys(e).map(function(n){return JSON.stringify(n)}))+"]")},o}(),V=function(){function o(t,e){this.wrapper=t,this.events=e,this.addDOMEvents()}return o.prototype.destroy=function(){this.removeDOMEvents(),this.events=[]},o.prototype.addDOMEvents=function(){this.handleDOMEvents(he)},o.prototype.removeDOMEvents=function(){this.handleDOMEvents(ue)},o.prototype.handleDOMEvents=function(t){var e=this,r=this.wrapper;this.events.forEach(function(n){t(r,n.name,e,!!n.capture)})},o.prototype.handleEvent=function(t){var e=t.type;this.events.some(function(r){return r.name===e?(r.handler(t),!0):!1})},o}(),Se=function(){function o(){}return o}(),we=function(o){$(t,o);function t(){var e=o.call(this)||this;return e.startX=0,e.startY=0,e.scrollX=!1,e.scrollY=!0,e.freeScroll=!1,e.directionLockThreshold=0,e.eventPassthrough="",e.click=!1,e.dblclick=!1,e.tap="",e.bounce={top:!0,bottom:!0,left:!0,right:!0},e.bounceTime=800,e.momentum=!0,e.momentumLimitTime=300,e.momentumLimitDistance=15,e.swipeTime=2500,e.swipeBounceTime=500,e.deceleration=.0015,e.flickLimitTime=200,e.flickLimitDistance=100,e.resizePolling=60,e.probeType=0,e.stopPropagation=!1,e.preventDefault=!0,e.preventDefaultException={tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT|AUDIO)$/},e.tagException={tagName:/^TEXTAREA$/},e.HWCompositing=!0,e.useTransition=!0,e.bindToWrapper=!1,e.bindToTarget=!1,e.disableMouse=yt,e.disableTouch=!yt,e.autoBlur=!0,e.autoEndDistance=5,e.outOfBoundaryDampingFactor=1/3,e.specifiedIndexAsContent=0,e.quadrant=1,e}return t.prototype.merge=function(e){if(!e)return this;for(var r in e){if(r==="bounce"){this.bounce=this.resolveBounce(e[r]);continue}this[r]=e[r]}return this},t.prototype.process=function(){return this.translateZ=this.HWCompositing&&fe?" translateZ(1px)":"",this.useTransition=this.useTransition&&ve,this.preventDefault=!this.eventPassthrough&&this.preventDefault,this.scrollX=this.eventPassthrough==="horizontal"?!1:this.scrollX,this.scrollY=this.eventPassthrough==="vertical"?!1:this.scrollY,this.freeScroll=this.freeScroll&&!this.eventPassthrough,this.scrollX=this.freeScroll?!0:this.scrollX,this.scrollY=this.freeScroll?!0:this.scrollY,this.directionLockThreshold=this.eventPassthrough?0:this.directionLockThreshold,this},t.prototype.resolveBounce=function(e){var r={top:!0,right:!0,bottom:!0,left:!0},n={top:!1,right:!1,bottom:!1,left:!1},i;return typeof e=="object"?i=st(r,e):i=e?r:n,i},t}(Se),Pe=function(){function o(t,e){this.wrapper=t,this.options=e,this.hooks=new x(["beforeStart","start","move","end","click"]),this.handleDOMEvents()}return o.prototype.handleDOMEvents=function(){var t=this.options,e=t.bindToWrapper,r=t.disableMouse,n=t.disableTouch,i=t.click,a=this.wrapper,s=e?a:window,l=[],c=[],h=!n,f=!r;i&&l.push({name:"click",handler:this.click.bind(this),capture:!0}),h&&(l.push({name:"touchstart",handler:this.start.bind(this)}),c.push({name:"touchmove",handler:this.move.bind(this)},{name:"touchend",handler:this.end.bind(this)},{name:"touchcancel",handler:this.end.bind(this)})),f&&(l.push({name:"mousedown",handler:this.start.bind(this)}),c.push({name:"mousemove",handler:this.move.bind(this)},{name:"mouseup",handler:this.end.bind(this)})),this.wrapperEventRegister=new V(a,l),this.targetEventRegister=new V(s,c)},o.prototype.beforeHandler=function(t,e){var r=this.options,n=r.preventDefault,i=r.stopPropagation,a=r.preventDefaultException,s={start:function(){return n&&!F(t.target,a)},end:function(){return n&&!F(t.target,a)},move:function(){return n}};s[e]()&&t.preventDefault(),i&&t.stopPropagation()},o.prototype.setInitiated=function(t){t===void 0&&(t=0),this.initiated=t},o.prototype.start=function(t){var e=ot[t.type];if(!(this.initiated&&this.initiated!==e)){if(this.setInitiated(e),de(t.target,this.options.tagException)){this.setInitiated();return}if(!(e===2&&t.button!==0)&&!this.hooks.trigger(this.hooks.eventTypes.beforeStart,t)){this.beforeHandler(t,"start");var r=t.touches?t.touches[0]:t;this.pointX=r.pageX,this.pointY=r.pageY,this.hooks.trigger(this.hooks.eventTypes.start,t)}}},o.prototype.move=function(t){if(ot[t.type]===this.initiated){this.beforeHandler(t,"move");var e=t.touches?t.touches[0]:t,r=e.pageX-this.pointX,n=e.pageY-this.pointY;if(this.pointX=e.pageX,this.pointY=e.pageY,!this.hooks.trigger(this.hooks.eventTypes.move,{deltaX:r,deltaY:n,e:t})){var i=document.documentElement.scrollLeft||window.pageXOffset||document.body.scrollLeft,a=document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop,s=this.pointX-i,l=this.pointY-a,c=this.options.autoEndDistance;(s>document.documentElement.clientWidth-c||l>document.documentElement.clientHeight-c||s<c||l<c)&&this.end(t)}}},o.prototype.end=function(t){ot[t.type]===this.initiated&&(this.setInitiated(),this.beforeHandler(t,"end"),this.hooks.trigger(this.hooks.eventTypes.end,t))},o.prototype.click=function(t){this.hooks.trigger(this.hooks.eventTypes.click,t)},o.prototype.setContent=function(t){t!==this.wrapper&&(this.wrapper=t,this.rebindDOMEvents())},o.prototype.rebindDOMEvents=function(){this.wrapperEventRegister.destroy(),this.targetEventRegister.destroy(),this.handleDOMEvents()},o.prototype.destroy=function(){this.wrapperEventRegister.destroy(),this.targetEventRegister.destroy(),this.hooks.destroy()},o}(),nt={x:["translateX","px"],y:["translateY","px"]},Ee=function(){function o(t){this.setContent(t),this.hooks=new x(["beforeTranslate","translate"])}return o.prototype.getComputedPosition=function(){var t=window.getComputedStyle(this.content,null),e=t[D.transform].split(")")[0].split(", "),r=+(e[12]||e[4])||0,n=+(e[13]||e[5])||0;return{x:r,y:n}},o.prototype.translate=function(t){var e=[];Object.keys(t).forEach(function(r){if(nt[r]){var n=nt[r][0];if(n){var i=nt[r][1],a=t[r];e.push(n+"("+a+i+")")}}}),this.hooks.trigger(this.hooks.eventTypes.beforeTranslate,e,t),this.style[D.transform]=e.join(" "),this.hooks.trigger(this.hooks.eventTypes.translate,t)},o.prototype.setContent=function(t){this.content!==t&&(this.content=t,this.style=t.style)},o.prototype.destroy=function(){this.hooks.destroy()},o}(),Mt=function(){function o(t,e,r){this.translater=e,this.options=r,this.timer=0,this.hooks=new x(["move","end","beforeForceStop","forceStop","callStop","time","timeFunction"]),this.setContent(t)}return o.prototype.translate=function(t){this.translater.translate(t)},o.prototype.setPending=function(t){this.pending=t},o.prototype.setForceStopped=function(t){this.forceStopped=t},o.prototype.setCallStop=function(t){this.callStopWhenPending=t},o.prototype.setContent=function(t){this.content!==t&&(this.content=t,this.style=t.style,this.stop())},o.prototype.clearTimer=function(){this.timer&&(M(this.timer),this.timer=0)},o.prototype.destroy=function(){this.hooks.destroy(),M(this.timer)},o}(),_e=function(o,t,e,r){var n=function(c,h){var f=c-h,g=f>0?-1:f<0?1:0;return g},i=n(t.x,o.x),a=n(t.y,o.y),s=e.x-r.x,l=e.y-r.y;return i*s<=0&&a*l<=0},Ce=function(o){$(t,o);function t(){return o!==null&&o.apply(this,arguments)||this}return t.prototype.startProbe=function(e,r){var n=this,i=e,a=function(){var s=n.translater.getComputedPosition();_e(e,r,s,i)&&n.hooks.trigger(n.hooks.eventTypes.move,s),n.pending||(n.callStopWhenPending?n.callStopWhenPending=!1:n.hooks.trigger(n.hooks.eventTypes.end,s)),i=s,n.pending&&(n.timer=At(a))};this.callStopWhenPending&&this.setCallStop(!1),M(this.timer),a()},t.prototype.transitionTime=function(e){e===void 0&&(e=0),this.style[D.transitionDuration]=e+"ms",this.hooks.trigger(this.hooks.eventTypes.time,e)},t.prototype.transitionTimingFunction=function(e){this.style[D.transitionTimingFunction]=e,this.hooks.trigger(this.hooks.eventTypes.timeFunction,e)},t.prototype.transitionProperty=function(){this.style[D.transitionProperty]=D.transform},t.prototype.move=function(e,r,n,i){this.setPending(n>0),this.transitionTimingFunction(i),this.transitionProperty(),this.transitionTime(n),this.translate(r);var a=this.options.probeType===3;n&&a&&this.startProbe(e,r),n||(this._reflow=this.content.offsetHeight,a&&this.hooks.trigger(this.hooks.eventTypes.move,r),this.hooks.trigger(this.hooks.eventTypes.end,r))},t.prototype.doStop=function(){var e=this.pending;if(this.setForceStopped(!1),this.setCallStop(!1),e){this.setPending(!1),M(this.timer);var r=this.translater.getComputedPosition(),n=r.x,i=r.y;this.transitionTime(),this.translate({x:n,y:i}),this.setForceStopped(!0),this.setCallStop(!0),this.hooks.trigger(this.hooks.eventTypes.forceStop,{x:n,y:i})}return e},t.prototype.stop=function(){var e=this.doStop();e&&this.hooks.trigger(this.hooks.eventTypes.callStop)},t}(Mt),xe=function(o){$(t,o);function t(){return o!==null&&o.apply(this,arguments)||this}return t.prototype.move=function(e,r,n,i){if(!n){this.translate(r),this.options.probeType===3&&this.hooks.trigger(this.hooks.eventTypes.move,r),this.hooks.trigger(this.hooks.eventTypes.end,r);return}this.animate(e,r,n,i)},t.prototype.animate=function(e,r,n,i){var a=this,s=A(),l=s+n,c=this.options.probeType===3,h=function(){var f=A();if(f>=l){a.translate(r),c&&a.hooks.trigger(a.hooks.eventTypes.move,r),a.hooks.trigger(a.hooks.eventTypes.end,r);return}f=(f-s)/n;var g=i(f),S={};Object.keys(r).forEach(function(v){var b=e[v],k=r[v];S[v]=(k-b)*g+b}),a.translate(S),c&&a.hooks.trigger(a.hooks.eventTypes.move,S),a.pending&&(a.timer=At(h)),a.pending||(a.callStopWhenPending?a.callStopWhenPending=!1:a.hooks.trigger(a.hooks.eventTypes.end,r))};this.setPending(!0),this.callStopWhenPending&&this.setCallStop(!1),M(this.timer),h()},t.prototype.doStop=function(){var e=this.pending;if(this.setForceStopped(!1),this.setCallStop(!1),e){this.setPending(!1),M(this.timer);var r=this.translater.getComputedPosition();this.setForceStopped(!0),this.setCallStop(!0),this.hooks.trigger(this.hooks.eventTypes.forceStop,r)}return e},t.prototype.stop=function(){var e=this.doStop();e&&this.hooks.trigger(this.hooks.eventTypes.callStop)},t}(Mt);function Be(o,t,e){var r=e.useTransition,n={};return Object.defineProperty(n,"probeType",{enumerable:!0,configurable:!1,get:function(){return e.probeType}}),r?new Ce(o,t,n):new xe(o,t,n)}var Tt=function(){function o(t,e,r){this.wrapper=t,this.options=r,this.hooks=new x(["beforeComputeBoundary","computeBoundary","momentum","end","ignoreHasScroll"]),this.refresh(e)}return o.prototype.start=function(){this.dist=0,this.setMovingDirection(0),this.setDirection(0)},o.prototype.move=function(t){return t=this.hasScroll?t:0,this.setMovingDirection(t),this.performDampingAlgorithm(t,this.options.outOfBoundaryDampingFactor)},o.prototype.setMovingDirection=function(t){this.movingDirection=t>0?-1:t<0?1:0},o.prototype.setDirection=function(t){this.direction=t>0?-1:t<0?1:0},o.prototype.performDampingAlgorithm=function(t,e){var r=this.currentPos+t;return(r>this.minScrollPos||r<this.maxScrollPos)&&(r>this.minScrollPos&&this.options.bounces[0]||r<this.maxScrollPos&&this.options.bounces[1]?r=this.currentPos+t*e:r=r>this.minScrollPos?this.minScrollPos:this.maxScrollPos),r},o.prototype.end=function(t){var e={duration:0},r=Math.abs(this.currentPos-this.startPos);if(this.options.momentum&&t<this.options.momentumLimitTime&&r>this.options.momentumLimitDistance){var n=this.direction===-1&&this.options.bounces[0]||this.direction===1&&this.options.bounces[1]?this.wrapperSize:0;e=this.hasScroll?this.momentum(this.currentPos,this.startPos,t,this.maxScrollPos,this.minScrollPos,n,this.options):{destination:this.currentPos,duration:0}}else this.hooks.trigger(this.hooks.eventTypes.end,e);return e},o.prototype.momentum=function(t,e,r,n,i,a,s){s===void 0&&(s=this.options);var l=t-e,c=Math.abs(l)/r,h=s.deceleration,f=s.swipeBounceTime,g=s.swipeTime,S=Math.min(g,c*2/h),v={destination:t+c*c/h*(l<0?-1:1),duration:S,rate:15};return this.hooks.trigger(this.hooks.eventTypes.momentum,v,l),v.destination<n?(v.destination=a?Math.max(n-a/4,n-a/v.rate*c):n,v.duration=f):v.destination>i&&(v.destination=a?Math.min(i+a/4,i+a/v.rate*c):i,v.duration=f),v.destination=Math.round(v.destination),v},o.prototype.updateDirection=function(){var t=this.currentPos-this.absStartPos;this.setDirection(t)},o.prototype.refresh=function(t){var e=this.options.rect,r=e.size,n=e.position,i=window.getComputedStyle(this.wrapper,null).position==="static",a=mt(this.wrapper);this.wrapperSize=this.wrapper[r==="width"?"clientWidth":"clientHeight"],this.setContent(t);var s=mt(this.content);this.contentSize=s[r],this.relativeOffset=s[n],i&&(this.relativeOffset-=a[n]),this.computeBoundary(),this.setDirection(0)},o.prototype.setContent=function(t){t!==this.content&&(this.content=t,this.resetState())},o.prototype.resetState=function(){this.currentPos=0,this.startPos=0,this.dist=0,this.setDirection(0),this.setMovingDirection(0),this.resetStartPos()},o.prototype.computeBoundary=function(){this.hooks.trigger(this.hooks.eventTypes.beforeComputeBoundary);var t={minScrollPos:0,maxScrollPos:this.wrapperSize-this.contentSize};t.maxScrollPos<0&&(t.maxScrollPos-=this.relativeOffset,this.options.specifiedIndexAsContent===0&&(t.minScrollPos=-this.relativeOffset)),this.hooks.trigger(this.hooks.eventTypes.computeBoundary,t),this.minScrollPos=t.minScrollPos,this.maxScrollPos=t.maxScrollPos,this.hasScroll=this.options.scrollable&&this.maxScrollPos<this.minScrollPos,!this.hasScroll&&this.minScrollPos<this.maxScrollPos&&(this.maxScrollPos=this.minScrollPos,this.contentSize=this.wrapperSize)},o.prototype.updatePosition=function(t){this.currentPos=t},o.prototype.getCurrentPos=function(){return this.currentPos},o.prototype.checkInBoundary=function(){var t=this.adjustPosition(this.currentPos),e=t===this.getCurrentPos();return{position:t,inBoundary:e}},o.prototype.adjustPosition=function(t){return!this.hasScroll&&!this.hooks.trigger(this.hooks.eventTypes.ignoreHasScroll)?t=this.minScrollPos:t>this.minScrollPos?t=this.minScrollPos:t<this.maxScrollPos&&(t=this.maxScrollPos),t},o.prototype.updateStartPos=function(){this.startPos=this.currentPos},o.prototype.updateAbsStartPos=function(){this.absStartPos=this.currentPos},o.prototype.resetStartPos=function(){this.updateStartPos(),this.updateAbsStartPos()},o.prototype.getAbsDist=function(t){return this.dist+=t,Math.abs(this.dist)},o.prototype.destroy=function(){this.hooks.destroy()},o}(),N,I,W,j,kt=(N={},N.yes=function(o){return!0},N.no=function(o){return Ct(o),!1},N),De=(I={},I.horizontal=(W={},W.yes="horizontal",W.no="vertical",W),I.vertical=(j={},j.yes="vertical",j.no="horizontal",j),I),Ae=function(){function o(t,e,r){this.directionLockThreshold=t,this.freeScroll=e,this.eventPassthrough=r,this.reset()}return o.prototype.reset=function(){this.directionLocked=""},o.prototype.checkMovingDirection=function(t,e,r){return this.computeDirectionLock(t,e),this.handleEventPassthrough(r)},o.prototype.adjustDelta=function(t,e){return this.directionLocked==="horizontal"?e=0:this.directionLocked==="vertical"&&(t=0),{deltaX:t,deltaY:e}},o.prototype.computeDirectionLock=function(t,e){this.directionLocked===""&&!this.freeScroll&&(t>e+this.directionLockThreshold?this.directionLocked="horizontal":e>=t+this.directionLockThreshold?this.directionLocked="vertical":this.directionLocked="none")},o.prototype.handleEventPassthrough=function(t){var e=De[this.directionLocked];if(e){if(this.eventPassthrough===e.yes)return kt.yes(t);if(this.eventPassthrough===e.no)return kt.no(t)}return!1},o}(),Me=function(o,t,e){return e===2?[t,-o]:e===3?[-o,-t]:e===4?[-t,o]:[o,t]},Oe=function(){function o(t,e,r,n,i){this.hooks=new x(["start","beforeMove","scrollStart","scroll","beforeEnd","end","scrollEnd","contentNotMoved","detectMovingDirection","coordinateTransformation"]),this.scrollBehaviorX=t,this.scrollBehaviorY=e,this.actionsHandler=r,this.animater=n,this.options=i,this.directionLockAction=new Ae(i.directionLockThreshold,i.freeScroll,i.eventPassthrough),this.enabled=!0,this.bindActionsHandler()}return o.prototype.bindActionsHandler=function(){var t=this;this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.start,function(e){return t.enabled?t.handleStart(e):!0}),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.move,function(e){var r=e.deltaX,n=e.deltaY,i=e.e;if(!t.enabled)return!0;var a=Me(r,n,t.options.quadrant),s=a[0],l=a[1],c={deltaX:s,deltaY:l};return t.hooks.trigger(t.hooks.eventTypes.coordinateTransformation,c),t.handleMove(c.deltaX,c.deltaY,i)}),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.end,function(e){return t.enabled?t.handleEnd(e):!0}),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.click,function(e){t.enabled&&!e._constructed&&t.handleClick(e)})},o.prototype.handleStart=function(t){var e=A();this.fingerMoved=!1,this.contentMoved=!1,this.startTime=e,this.directionLockAction.reset(),this.scrollBehaviorX.start(),this.scrollBehaviorY.start(),this.animater.doStop(),this.scrollBehaviorX.resetStartPos(),this.scrollBehaviorY.resetStartPos(),this.hooks.trigger(this.hooks.eventTypes.start,t)},o.prototype.handleMove=function(t,e,r){if(!this.hooks.trigger(this.hooks.eventTypes.beforeMove,r)){var n=this.scrollBehaviorX.getAbsDist(t),i=this.scrollBehaviorY.getAbsDist(e),a=A();if(this.checkMomentum(n,i,a))return!0;if(this.directionLockAction.checkMovingDirection(n,i,r))return this.actionsHandler.setInitiated(),!0;var s=this.directionLockAction.adjustDelta(t,e),l=this.scrollBehaviorX.getCurrentPos(),c=this.scrollBehaviorX.move(s.deltaX),h=this.scrollBehaviorY.getCurrentPos(),f=this.scrollBehaviorY.move(s.deltaY);if(!this.hooks.trigger(this.hooks.eventTypes.detectMovingDirection)){this.fingerMoved||(this.fingerMoved=!0);var g=c!==l||f!==h;!this.contentMoved&&!g&&this.hooks.trigger(this.hooks.eventTypes.contentNotMoved),!this.contentMoved&&g&&(this.contentMoved=!0,this.hooks.trigger(this.hooks.eventTypes.scrollStart)),this.contentMoved&&g&&(this.animater.translate({x:c,y:f}),this.dispatchScroll(a))}}},o.prototype.dispatchScroll=function(t){t-this.startTime>this.options.momentumLimitTime&&(this.startTime=t,this.scrollBehaviorX.updateStartPos(),this.scrollBehaviorY.updateStartPos(),this.options.probeType===1&&this.hooks.trigger(this.hooks.eventTypes.scroll,this.getCurrentPos())),this.options.probeType>1&&this.hooks.trigger(this.hooks.eventTypes.scroll,this.getCurrentPos())},o.prototype.checkMomentum=function(t,e,r){return r-this.endTime>this.options.momentumLimitTime&&e<this.options.momentumLimitDistance&&t<this.options.momentumLimitDistance},o.prototype.handleEnd=function(t){if(!this.hooks.trigger(this.hooks.eventTypes.beforeEnd,t)){var e=this.getCurrentPos();if(this.scrollBehaviorX.updateDirection(),this.scrollBehaviorY.updateDirection(),this.hooks.trigger(this.hooks.eventTypes.end,t,e))return!0;e=this.ensureIntegerPos(e),this.animater.translate(e),this.endTime=A();var r=this.endTime-this.startTime;this.hooks.trigger(this.hooks.eventTypes.scrollEnd,e,r)}},o.prototype.ensureIntegerPos=function(t){this.ensuringInteger=!0;var e=t.x,r=t.y,n=this.scrollBehaviorX,i=n.minScrollPos,a=n.maxScrollPos,s=this.scrollBehaviorY,l=s.minScrollPos,c=s.maxScrollPos;return e=e>0?Math.ceil(e):Math.floor(e),r=r>0?Math.ceil(r):Math.floor(r),e=vt(e,a,i),r=vt(r,c,l),{x:e,y:r}},o.prototype.handleClick=function(t){F(t.target,this.options.preventDefaultException)||(Ct(t),t.stopPropagation())},o.prototype.getCurrentPos=function(){return{x:this.scrollBehaviorX.getCurrentPos(),y:this.scrollBehaviorY.getCurrentPos()}},o.prototype.refresh=function(){this.endTime=0},o.prototype.destroy=function(){this.hooks.destroy()},o}();function Xe(o){var t=["click","bindToWrapper","disableMouse","disableTouch","preventDefault","stopPropagation","tagException","preventDefaultException","autoEndDistance"].reduce(function(e,r){return e[r]=o[r],e},{});return t}function bt(o,t,e,r){var n=["momentum","momentumLimitTime","momentumLimitDistance","deceleration","swipeBounceTime","swipeTime","outOfBoundaryDampingFactor","specifiedIndexAsContent"].reduce(function(i,a){return i[a]=o[a],i},{});return n.scrollable=!!o[t],n.bounces=e,n.rect=r,n}function ct(o,t,e){e.forEach(function(r){var n,i;typeof r=="string"?n=i=r:(n=r.source,i=r.target),o.on(n,function(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];return t.trigger.apply(t,Pt([i],a))})})}function Ye(o,t){for(var e=Object.keys(o),r=0,n=e;r<n.length;r++){var i=n[r];if(o[i]!==t[i])return!1}return!0}var St=1,Le=function(){function o(t,e,r){this.wrapper=t,this.content=e,this.resizeTimeout=0,this.hooks=new x(["beforeStart","beforeMove","beforeScrollStart","scrollStart","scroll","beforeEnd","scrollEnd","resize","touchEnd","end","flick","scrollCancel","momentum","scrollTo","minDistanceScroll","scrollToElement","beforeRefresh"]),this.options=r;var n=this.options.bounce,i=n.left,a=n.right,s=n.top,l=n.bottom;this.scrollBehaviorX=new Tt(t,e,bt(r,"scrollX",[i,a],{size:"width",position:"left"})),this.scrollBehaviorY=new Tt(t,e,bt(r,"scrollY",[s,l],{size:"height",position:"top"})),this.translater=new Ee(this.content),this.animater=Be(this.content,this.translater,this.options),this.actionsHandler=new Pe(this.options.bindToTarget?this.content:t,Xe(this.options)),this.actions=new Oe(this.scrollBehaviorX,this.scrollBehaviorY,this.actionsHandler,this.animater,this.options);var c=this.resize.bind(this);this.resizeRegister=new V(window,[{name:"orientationchange",handler:c},{name:"resize",handler:c}]),this.registerTransitionEnd(),this.init()}return o.prototype.init=function(){var t=this;this.bindTranslater(),this.bindAnimater(),this.bindActions(),this.hooks.on(this.hooks.eventTypes.scrollEnd,function(){t.togglePointerEvents(!0)})},o.prototype.registerTransitionEnd=function(){this.transitionEndRegister=new V(this.content,[{name:D.transitionEnd,handler:this.transitionEnd.bind(this)}])},o.prototype.bindTranslater=function(){var t=this,e=this.translater.hooks;e.on(e.eventTypes.beforeTranslate,function(r){t.options.translateZ&&r.push(t.options.translateZ)}),e.on(e.eventTypes.translate,function(r){var n=t.getCurrentPos();if(t.updatePositions(r),t.actions.ensuringInteger===!0){t.actions.ensuringInteger=!1;return}(r.x!==n.x||r.y!==n.y)&&t.togglePointerEvents(!1)})},o.prototype.bindAnimater=function(){var t=this;this.animater.hooks.on(this.animater.hooks.eventTypes.end,function(e){t.resetPosition(t.options.bounceTime)||(t.animater.setPending(!1),t.hooks.trigger(t.hooks.eventTypes.scrollEnd,e))}),ct(this.animater.hooks,this.hooks,[{source:this.animater.hooks.eventTypes.move,target:this.hooks.eventTypes.scroll},{source:this.animater.hooks.eventTypes.forceStop,target:this.hooks.eventTypes.scrollEnd}])},o.prototype.bindActions=function(){var t=this,e=this.actions;ct(e.hooks,this.hooks,[{source:e.hooks.eventTypes.start,target:this.hooks.eventTypes.beforeStart},{source:e.hooks.eventTypes.start,target:this.hooks.eventTypes.beforeScrollStart},{source:e.hooks.eventTypes.beforeMove,target:this.hooks.eventTypes.beforeMove},{source:e.hooks.eventTypes.scrollStart,target:this.hooks.eventTypes.scrollStart},{source:e.hooks.eventTypes.scroll,target:this.hooks.eventTypes.scroll},{source:e.hooks.eventTypes.beforeEnd,target:this.hooks.eventTypes.beforeEnd}]),e.hooks.on(e.hooks.eventTypes.end,function(r,n){if(t.hooks.trigger(t.hooks.eventTypes.touchEnd,n),t.hooks.trigger(t.hooks.eventTypes.end,n)||!e.fingerMoved&&(t.hooks.trigger(t.hooks.eventTypes.scrollCancel),t.checkClick(r)))return!0;if(t.resetPosition(t.options.bounceTime,B.bounce))return t.animater.setForceStopped(!1),!0}),e.hooks.on(e.hooks.eventTypes.scrollEnd,function(r,n){var i=Math.abs(r.x-t.scrollBehaviorX.startPos),a=Math.abs(r.y-t.scrollBehaviorY.startPos);if(t.checkFlick(n,i,a)){t.animater.setForceStopped(!1),t.hooks.trigger(t.hooks.eventTypes.flick);return}if(t.momentum(r,n)){t.animater.setForceStopped(!1);return}e.contentMoved&&t.hooks.trigger(t.hooks.eventTypes.scrollEnd,r),t.animater.forceStopped&&t.animater.setForceStopped(!1)})},o.prototype.checkFlick=function(t,e,r){var n=1;if(this.hooks.events.flick.length>1&&t<this.options.flickLimitTime&&e<this.options.flickLimitDistance&&r<this.options.flickLimitDistance&&(r>n||e>n))return!0},o.prototype.momentum=function(t,e){var r={time:0,easing:B.swiper,newX:t.x,newY:t.y},n=this.scrollBehaviorX.end(e),i=this.scrollBehaviorY.end(e);if(r.newX=at(n.destination)?r.newX:n.destination,r.newY=at(i.destination)?r.newY:i.destination,r.time=Math.max(n.duration,i.duration),this.hooks.trigger(this.hooks.eventTypes.momentum,r,this),r.newX!==t.x||r.newY!==t.y)return(r.newX>this.scrollBehaviorX.minScrollPos||r.newX<this.scrollBehaviorX.maxScrollPos||r.newY>this.scrollBehaviorY.minScrollPos||r.newY<this.scrollBehaviorY.maxScrollPos)&&(r.easing=B.swipeBounce),this.scrollTo(r.newX,r.newY,r.time,r.easing),!0},o.prototype.checkClick=function(t){var e={preventClick:this.animater.forceStopped};if(this.hooks.trigger(this.hooks.eventTypes.checkClick))return this.animater.setForceStopped(!1),!0;if(!e.preventClick){var r=this.options.dblclick,n=!1;if(r&&this.lastClickTime){var i=r.delay,a=i===void 0?300:i;A()-this.lastClickTime<a&&(n=!0,me(t))}return this.options.tap&&ye(t,this.options.tap),this.options.click&&!F(t.target,this.options.preventDefaultException)&&Bt(t),this.lastClickTime=n?null:A(),!0}return!1},o.prototype.resize=function(){var t=this;this.actions.enabled&&(ae&&(this.wrapper.scrollTop=0),clearTimeout(this.resizeTimeout),this.resizeTimeout=window.setTimeout(function(){t.hooks.trigger(t.hooks.eventTypes.resize)},this.options.resizePolling))},o.prototype.transitionEnd=function(t){if(!(t.target!==this.content||!this.animater.pending)){var e=this.animater;e.transitionTime(),this.resetPosition(this.options.bounceTime,B.bounce)||(this.animater.setPending(!1),this.options.probeType!==3&&this.hooks.trigger(this.hooks.eventTypes.scrollEnd,this.getCurrentPos()))}},o.prototype.togglePointerEvents=function(t){t===void 0&&(t=!0);for(var e=this.content.children.length?this.content.children:[this.content],r=t?"auto":"none",n=0;n<e.length;n++){var i=e[n];i.isBScrollContainer||(i.style.pointerEvents=r)}},o.prototype.refresh=function(t){var e=this.setContent(t);this.hooks.trigger(this.hooks.eventTypes.beforeRefresh),this.scrollBehaviorX.refresh(t),this.scrollBehaviorY.refresh(t),e&&(this.translater.setContent(t),this.animater.setContent(t),this.transitionEndRegister.destroy(),this.registerTransitionEnd(),this.options.bindToTarget&&this.actionsHandler.setContent(t)),this.actions.refresh(),this.wrapperOffset=dt(this.wrapper)},o.prototype.setContent=function(t){var e=t!==this.content;return e&&(this.content=t),e},o.prototype.scrollBy=function(t,e,r,n){r===void 0&&(r=0);var i=this.getCurrentPos(),a=i.x,s=i.y;n=n||B.bounce,t+=a,e+=s,this.scrollTo(t,e,r,n)},o.prototype.scrollTo=function(t,e,r,n,i){r===void 0&&(r=0),n===void 0&&(n=B.bounce),i===void 0&&(i={start:{},end:{}});var a=this.options.useTransition?n.style:n.fn,s=this.getCurrentPos(),l=R({x:s.x,y:s.y},i.start),c=R({x:t,y:e},i.end);if(this.hooks.trigger(this.hooks.eventTypes.scrollTo,c),!Ye(l,c)){var h=Math.abs(c.x-l.x),f=Math.abs(c.y-l.y);h<St&&f<St&&(r=0,this.hooks.trigger(this.hooks.eventTypes.minDistanceScroll)),this.animater.move(l,c,r,a)}},o.prototype.scrollToElement=function(t,e,r,n,i){var a=_t(t),s=dt(a),l=function(h,f,g){return typeof h=="number"?h:h?Math.round(f/2-g/2):0};r=l(r,a.offsetWidth,this.wrapper.offsetWidth),n=l(n,a.offsetHeight,this.wrapper.offsetHeight);var c=function(h,f,g,S){return h-=f,h=S.adjustPosition(h-g),h};s.left=c(s.left,this.wrapperOffset.left,r,this.scrollBehaviorX),s.top=c(s.top,this.wrapperOffset.top,n,this.scrollBehaviorY),!this.hooks.trigger(this.hooks.eventTypes.scrollToElement,a,s)&&this.scrollTo(s.left,s.top,e,i)},o.prototype.resetPosition=function(t,e){t===void 0&&(t=0),e===void 0&&(e=B.bounce);var r=this.scrollBehaviorX.checkInBoundary(),n=r.position,i=r.inBoundary,a=this.scrollBehaviorY.checkInBoundary(),s=a.position,l=a.inBoundary;return i&&l?!1:(ce&&this.reflow(),this.scrollTo(n,s,t,e),!0)},o.prototype.reflow=function(){this._reflow=this.content.offsetHeight},o.prototype.updatePositions=function(t){this.scrollBehaviorX.updatePosition(t.x),this.scrollBehaviorY.updatePosition(t.y)},o.prototype.getCurrentPos=function(){return this.actions.getCurrentPos()},o.prototype.enable=function(){this.actions.enabled=!0},o.prototype.disable=function(){M(this.animater.timer),this.actions.enabled=!1},o.prototype.destroy=function(){var t=this,e=["resizeRegister","transitionEndRegister","actionsHandler","actions","hooks","animater","translater","scrollBehaviorX","scrollBehaviorY"];e.forEach(function(r){return t[r].destroy()})},o}(),q=function(o){$(t,o);function t(e,r){var n=o.call(this,["refresh","contentChanged","enable","disable","beforeScrollStart","scrollStart","scroll","scrollEnd","scrollCancel","touchEnd","flick","destroy"])||this,i=_t(e);return i?(n.plugins={},n.options=new we().merge(r).process(),n.setContent(i).valid&&(n.hooks=new x(["refresh","enable","disable","destroy","beforeInitialScrollTo","contentChanged"]),n.init(i)),n):(U("Can not resolve the wrapper DOM."),n)}return t.use=function(e){var r=e.pluginName,n=t.plugins.some(function(i){return e===i.ctor});return n?t:at(r)?(U("Plugin Class must specify plugin's name in static property by 'pluginName' field."),t):(t.pluginsMap[r]=!0,t.plugins.push({name:r,applyOrder:e.applyOrder,ctor:e}),t)},t.prototype.setContent=function(e){var r=!1,n=!0,i=e.children[this.options.specifiedIndexAsContent];return i?(r=this.content!==i,r&&(this.content=i)):(U("The wrapper need at least one child element to be content element to scroll."),n=!1),{valid:n,contentChanged:r}},t.prototype.init=function(e){var r=this;this.wrapper=e,e.isBScrollContainer=!0,this.scroller=new Le(e,this.content,this.options),this.scroller.hooks.on(this.scroller.hooks.eventTypes.resize,function(){r.refresh()}),this.eventBubbling(),this.handleAutoBlur(),this.enable(),this.proxy(ie),this.applyPlugins(),this.refreshWithoutReset(this.content);var n=this.options,i=n.startX,a=n.startY,s={x:i,y:a};this.hooks.trigger(this.hooks.eventTypes.beforeInitialScrollTo,s)||this.scroller.scrollTo(s.x,s.y)},t.prototype.applyPlugins=function(){var e=this,r=this.options;t.plugins.sort(function(n,i){var a,s=(a={},a.pre=-1,a.post=1,a),l=n.applyOrder?s[n.applyOrder]:0,c=i.applyOrder?s[i.applyOrder]:0;return l-c}).forEach(function(n){var i=n.ctor;r[n.name]&&typeof i=="function"&&(e.plugins[n.name]=new i(e))})},t.prototype.handleAutoBlur=function(){this.options.autoBlur&&this.on(this.eventTypes.beforeScrollStart,function(){var e=document.activeElement;e&&(e.tagName==="INPUT"||e.tagName==="TEXTAREA")&&e.blur()})},t.prototype.eventBubbling=function(){ct(this.scroller.hooks,this,[this.eventTypes.beforeScrollStart,this.eventTypes.scrollStart,this.eventTypes.scroll,this.eventTypes.scrollEnd,this.eventTypes.scrollCancel,this.eventTypes.touchEnd,this.eventTypes.flick])},t.prototype.refreshWithoutReset=function(e){this.scroller.refresh(e),this.hooks.trigger(this.hooks.eventTypes.refresh,e),this.trigger(this.eventTypes.refresh,e)},t.prototype.proxy=function(e){var r=this;e.forEach(function(n){var i=n.key,a=n.sourceKey;be(r,a,i)})},t.prototype.refresh=function(){var e=this.setContent(this.wrapper),r=e.contentChanged,n=e.valid;if(n){var i=this.content;this.refreshWithoutReset(i),r&&(this.hooks.trigger(this.hooks.eventTypes.contentChanged,i),this.trigger(this.eventTypes.contentChanged,i)),this.scroller.resetPosition()}},t.prototype.enable=function(){this.scroller.enable(),this.hooks.trigger(this.hooks.eventTypes.enable),this.trigger(this.eventTypes.enable)},t.prototype.disable=function(){this.scroller.disable(),this.hooks.trigger(this.hooks.eventTypes.disable),this.trigger(this.eventTypes.disable)},t.prototype.destroy=function(){this.hooks.trigger(this.hooks.eventTypes.destroy),this.trigger(this.eventTypes.destroy),this.scroller.destroy()},t.prototype.eventRegister=function(e){this.registerType(e)},t.plugins=[],t.pluginsMap={},t}(x);function G(o,t){var e=new q(o,t);return e}G.use=q.use;G.plugins=q.plugins;G.pluginsMap=q.pluginsMap;var ze=G;const Ke=Vt({name:"GlobalContent",__name:"index",props:{showPadding:{type:Boolean,default:!0}},setup(o){const t=$t(),e=qt(),r=Gt();return(n,i)=>{const a=Zt("router-view");return tt(),et(a,null,{default:pt(({Component:s,route:l})=>[Jt(Qt,{name:L(e).pageAnimateMode,mode:"out-in",appear:!0,onBeforeLeave:i[0]||(i[0]=c=>L(t).setDisableMainXScroll(!0)),onAfterEnter:i[1]||(i[1]=c=>L(t).setDisableMainXScroll(!1))},{default:pt(()=>[(tt(),et(te,{include:L(r).cacheRoutes},[L(t).reloadFlag?(tt(),et(ee(s),{key:l.fullPath,class:oe([{"p-16px":n.showPadding},"flex-grow bg-#f6f9f8 dark:bg-#101014 transition duration-300 ease-in-out"])},null,8,["class"])):re("",!0)],1032,["include"]))]),_:2},1032,["name"])]),_:1})}}});export{ze as B,He as C,Ke as _};
