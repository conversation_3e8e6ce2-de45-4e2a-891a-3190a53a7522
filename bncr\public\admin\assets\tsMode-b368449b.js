var V=Object.defineProperty,W=Object.defineProperties;var j=Object.getOwnPropertyDescriptors;var P=Object.getOwnPropertySymbols;var B=Object.prototype.hasOwnProperty,U=Object.prototype.propertyIsEnumerable;var F=(e,t,r)=>t in e?V(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,O=(e,t)=>{for(var r in t||(t={}))B.call(t,r)&&F(e,r,t[r]);if(P)for(var r of P(t))U.call(t,r)&&F(e,r,t[r]);return e},I=(e,t)=>W(e,j(t));var w=(e,t,r)=>(F(e,typeof t!="symbol"?t+"":t,r),r);var m=(e,t,r)=>new Promise((l,i)=>{var u=n=>{try{o(r.next(n))}catch(d){i(d)}},g=n=>{try{o(r.throw(n))}catch(d){i(d)}},o=n=>n.done?l(n.value):Promise.resolve(n.value).then(u,g);o((r=r.apply(e,t)).next())});import{t as $,m as z}from"./index-b380aaed.js";/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/var M=Object.defineProperty,G=Object.getOwnPropertyDescriptor,J=Object.getOwnPropertyNames,Q=Object.prototype.hasOwnProperty,q=(e,t,r)=>t in e?M(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,N=(e,t,r,l)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of J(t))!Q.call(e,i)&&i!==r&&M(e,i,{get:()=>t[i],enumerable:!(l=G(t,i))||l.enumerable});return e},X=(e,t,r)=>(N(e,t,"default"),r&&N(r,t,"default")),b=(e,t,r)=>(q(e,typeof t!="symbol"?t+"":t,r),r),a={};X(a,z);var Y=class{constructor(e,t){w(this,"_configChangeListener");w(this,"_updateExtraLibsToken");w(this,"_extraLibsChangeListener");w(this,"_worker");w(this,"_client");this._modeId=e,this._defaults=t,this._worker=null,this._client=null,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker()),this._updateExtraLibsToken=0,this._extraLibsChangeListener=this._defaults.onDidExtraLibsChange(()=>this._updateExtraLibs())}dispose(){this._configChangeListener.dispose(),this._extraLibsChangeListener.dispose(),this._stopWorker()}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}_updateExtraLibs(){return m(this,null,function*(){if(!this._worker)return;const e=++this._updateExtraLibsToken,t=yield this._worker.getProxy();this._updateExtraLibsToken===e&&t.updateExtraLibs(this._defaults.getExtraLibs())})}_getClient(){return this._client||(this._client=(()=>m(this,null,function*(){return this._worker=a.editor.createWebWorker({moduleId:"vs/language/typescript/tsWorker",label:this._modeId,keepIdleModels:!0,createData:{compilerOptions:this._defaults.getCompilerOptions(),extraLibs:this._defaults.getExtraLibs(),customWorkerPath:this._defaults.workerOptions.customWorkerPath,inlayHintsOptions:this._defaults.inlayHintsOptions}}),this._defaults.getEagerModelSync()?yield this._worker.withSyncedResources(a.editor.getModels().filter(e=>e.getLanguageId()===this._modeId).map(e=>e.uri)):yield this._worker.getProxy()}))()),this._client}getLanguageServiceWorker(...e){return m(this,null,function*(){const t=yield this._getClient();return this._worker&&(yield this._worker.withSyncedResources(e)),t})}},s={};s["lib.d.ts"]=!0;s["lib.decorators.d.ts"]=!0;s["lib.decorators.legacy.d.ts"]=!0;s["lib.dom.d.ts"]=!0;s["lib.dom.iterable.d.ts"]=!0;s["lib.es2015.collection.d.ts"]=!0;s["lib.es2015.core.d.ts"]=!0;s["lib.es2015.d.ts"]=!0;s["lib.es2015.generator.d.ts"]=!0;s["lib.es2015.iterable.d.ts"]=!0;s["lib.es2015.promise.d.ts"]=!0;s["lib.es2015.proxy.d.ts"]=!0;s["lib.es2015.reflect.d.ts"]=!0;s["lib.es2015.symbol.d.ts"]=!0;s["lib.es2015.symbol.wellknown.d.ts"]=!0;s["lib.es2016.array.include.d.ts"]=!0;s["lib.es2016.d.ts"]=!0;s["lib.es2016.full.d.ts"]=!0;s["lib.es2017.d.ts"]=!0;s["lib.es2017.full.d.ts"]=!0;s["lib.es2017.intl.d.ts"]=!0;s["lib.es2017.object.d.ts"]=!0;s["lib.es2017.sharedmemory.d.ts"]=!0;s["lib.es2017.string.d.ts"]=!0;s["lib.es2017.typedarrays.d.ts"]=!0;s["lib.es2018.asyncgenerator.d.ts"]=!0;s["lib.es2018.asynciterable.d.ts"]=!0;s["lib.es2018.d.ts"]=!0;s["lib.es2018.full.d.ts"]=!0;s["lib.es2018.intl.d.ts"]=!0;s["lib.es2018.promise.d.ts"]=!0;s["lib.es2018.regexp.d.ts"]=!0;s["lib.es2019.array.d.ts"]=!0;s["lib.es2019.d.ts"]=!0;s["lib.es2019.full.d.ts"]=!0;s["lib.es2019.intl.d.ts"]=!0;s["lib.es2019.object.d.ts"]=!0;s["lib.es2019.string.d.ts"]=!0;s["lib.es2019.symbol.d.ts"]=!0;s["lib.es2020.bigint.d.ts"]=!0;s["lib.es2020.d.ts"]=!0;s["lib.es2020.date.d.ts"]=!0;s["lib.es2020.full.d.ts"]=!0;s["lib.es2020.intl.d.ts"]=!0;s["lib.es2020.number.d.ts"]=!0;s["lib.es2020.promise.d.ts"]=!0;s["lib.es2020.sharedmemory.d.ts"]=!0;s["lib.es2020.string.d.ts"]=!0;s["lib.es2020.symbol.wellknown.d.ts"]=!0;s["lib.es2021.d.ts"]=!0;s["lib.es2021.full.d.ts"]=!0;s["lib.es2021.intl.d.ts"]=!0;s["lib.es2021.promise.d.ts"]=!0;s["lib.es2021.string.d.ts"]=!0;s["lib.es2021.weakref.d.ts"]=!0;s["lib.es2022.array.d.ts"]=!0;s["lib.es2022.d.ts"]=!0;s["lib.es2022.error.d.ts"]=!0;s["lib.es2022.full.d.ts"]=!0;s["lib.es2022.intl.d.ts"]=!0;s["lib.es2022.object.d.ts"]=!0;s["lib.es2022.regexp.d.ts"]=!0;s["lib.es2022.sharedmemory.d.ts"]=!0;s["lib.es2022.string.d.ts"]=!0;s["lib.es2023.array.d.ts"]=!0;s["lib.es2023.d.ts"]=!0;s["lib.es2023.full.d.ts"]=!0;s["lib.es5.d.ts"]=!0;s["lib.es6.d.ts"]=!0;s["lib.esnext.d.ts"]=!0;s["lib.esnext.full.d.ts"]=!0;s["lib.esnext.intl.d.ts"]=!0;s["lib.scripthost.d.ts"]=!0;s["lib.webworker.d.ts"]=!0;s["lib.webworker.importscripts.d.ts"]=!0;s["lib.webworker.iterable.d.ts"]=!0;function A(e,t,r=0){if(typeof e=="string")return e;if(e===void 0)return"";let l="";if(r){l+=t;for(let i=0;i<r;i++)l+="  "}if(l+=e.messageText,r++,e.next)for(const i of e.next)l+=A(i,t,r);return l}function S(e){return e?e.map(t=>t.text).join(""):""}var y=class{constructor(e){this._worker=e}_textSpanToRange(e,t){let r=e.getPositionAt(t.start),l=e.getPositionAt(t.start+t.length),{lineNumber:i,column:u}=r,{lineNumber:g,column:o}=l;return{startLineNumber:i,startColumn:u,endLineNumber:g,endColumn:o}}},Z=class{constructor(e){w(this,"_libFiles");w(this,"_hasFetchedLibFiles");w(this,"_fetchLibFilesPromise");this._worker=e,this._libFiles={},this._hasFetchedLibFiles=!1,this._fetchLibFilesPromise=null}isLibFile(e){return e&&e.path.indexOf("/lib.")===0?!!s[e.path.slice(1)]:!1}getOrCreateModel(e){const t=a.Uri.parse(e),r=a.editor.getModel(t);if(r)return r;if(this.isLibFile(t)&&this._hasFetchedLibFiles)return a.editor.createModel(this._libFiles[t.path.slice(1)],"typescript",t);const l=$.getExtraLibs()[e];return l?a.editor.createModel(l.content,"typescript",t):null}_containsLibFile(e){for(let t of e)if(this.isLibFile(t))return!0;return!1}fetchLibFilesIfNecessary(e){return m(this,null,function*(){this._containsLibFile(e)&&(yield this._fetchLibFiles())})}_fetchLibFiles(){return this._fetchLibFilesPromise||(this._fetchLibFilesPromise=this._worker().then(e=>e.getLibFiles()).then(e=>{this._hasFetchedLibFiles=!0,this._libFiles=e})),this._fetchLibFilesPromise}},ee=class extends y{constructor(t,r,l,i){super(i);w(this,"_disposables",[]);w(this,"_listener",Object.create(null));this._libFiles=t,this._defaults=r,this._selector=l;const u=n=>{if(n.getLanguageId()!==l)return;const d=()=>{const{onlyVisible:k}=this._defaults.getDiagnosticsOptions();k?n.isAttachedToEditor()&&this._doValidate(n):this._doValidate(n)};let p;const f=n.onDidChangeContent(()=>{clearTimeout(p),p=window.setTimeout(d,500)}),h=n.onDidChangeAttached(()=>{const{onlyVisible:k}=this._defaults.getDiagnosticsOptions();k&&(n.isAttachedToEditor()?d():a.editor.setModelMarkers(n,this._selector,[]))});this._listener[n.uri.toString()]={dispose(){f.dispose(),h.dispose(),clearTimeout(p)}},d()},g=n=>{a.editor.setModelMarkers(n,this._selector,[]);const d=n.uri.toString();this._listener[d]&&(this._listener[d].dispose(),delete this._listener[d])};this._disposables.push(a.editor.onDidCreateModel(n=>u(n))),this._disposables.push(a.editor.onWillDisposeModel(g)),this._disposables.push(a.editor.onDidChangeModelLanguage(n=>{g(n.model),u(n.model)})),this._disposables.push({dispose(){for(const n of a.editor.getModels())g(n)}});const o=()=>{for(const n of a.editor.getModels())g(n),u(n)};this._disposables.push(this._defaults.onDidChange(o)),this._disposables.push(this._defaults.onDidExtraLibsChange(o)),a.editor.getModels().forEach(n=>u(n))}dispose(){this._disposables.forEach(t=>t&&t.dispose()),this._disposables=[]}_doValidate(t){return m(this,null,function*(){const r=yield this._worker(t.uri);if(t.isDisposed())return;const l=[],{noSyntaxValidation:i,noSemanticValidation:u,noSuggestionDiagnostics:g}=this._defaults.getDiagnosticsOptions();i||l.push(r.getSyntacticDiagnostics(t.uri.toString())),u||l.push(r.getSemanticDiagnostics(t.uri.toString())),g||l.push(r.getSuggestionDiagnostics(t.uri.toString()));const o=yield Promise.all(l);if(!o||t.isDisposed())return;const n=o.reduce((p,f)=>f.concat(p),[]).filter(p=>(this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore||[]).indexOf(p.code)===-1),d=n.map(p=>p.relatedInformation||[]).reduce((p,f)=>f.concat(p),[]).map(p=>p.file?a.Uri.parse(p.file.fileName):null);yield this._libFiles.fetchLibFilesIfNecessary(d),!t.isDisposed()&&a.editor.setModelMarkers(t,this._selector,n.map(p=>this._convertDiagnostics(t,p)))})}_convertDiagnostics(t,r){const l=r.start||0,i=r.length||1,{lineNumber:u,column:g}=t.getPositionAt(l),{lineNumber:o,column:n}=t.getPositionAt(l+i),d=[];return r.reportsUnnecessary&&d.push(a.MarkerTag.Unnecessary),r.reportsDeprecated&&d.push(a.MarkerTag.Deprecated),{severity:this._tsDiagnosticCategoryToMarkerSeverity(r.category),startLineNumber:u,startColumn:g,endLineNumber:o,endColumn:n,message:A(r.messageText,`
`),code:r.code.toString(),tags:d,relatedInformation:this._convertRelatedInformation(t,r.relatedInformation)}}_convertRelatedInformation(t,r){if(!r)return[];const l=[];return r.forEach(i=>{let u=t;if(i.file&&(u=this._libFiles.getOrCreateModel(i.file.fileName)),!u)return;const g=i.start||0,o=i.length||1,{lineNumber:n,column:d}=u.getPositionAt(g),{lineNumber:p,column:f}=u.getPositionAt(g+o);l.push({resource:u.uri,startLineNumber:n,startColumn:d,endLineNumber:p,endColumn:f,message:A(i.messageText,`
`)})}),l}_tsDiagnosticCategoryToMarkerSeverity(t){switch(t){case 1:return a.MarkerSeverity.Error;case 3:return a.MarkerSeverity.Info;case 0:return a.MarkerSeverity.Warning;case 2:return a.MarkerSeverity.Hint}return a.MarkerSeverity.Info}},D=class extends y{get triggerCharacters(){return["."]}provideCompletionItems(e,t,r,l){return m(this,null,function*(){const i=e.getWordUntilPosition(t),u=new a.Range(t.lineNumber,i.startColumn,t.lineNumber,i.endColumn),g=e.uri,o=e.getOffsetAt(t),n=yield this._worker(g);if(e.isDisposed())return;const d=yield n.getCompletionsAtPosition(g.toString(),o);return!d||e.isDisposed()?void 0:{suggestions:d.entries.map(f=>{let h=u;if(f.replacementSpan){const C=e.getPositionAt(f.replacementSpan.start),x=e.getPositionAt(f.replacementSpan.start+f.replacementSpan.length);h=new a.Range(C.lineNumber,C.column,x.lineNumber,x.column)}const k=[];return f.kindModifiers!==void 0&&f.kindModifiers.indexOf("deprecated")!==-1&&k.push(a.languages.CompletionItemTag.Deprecated),{uri:g,position:t,offset:o,range:h,label:f.name,insertText:f.name,sortText:f.sortText,kind:D.convertKind(f.kind),tags:k}})}})}resolveCompletionItem(e,t){return m(this,null,function*(){const r=e,l=r.uri,i=r.position,u=r.offset,o=yield(yield this._worker(l)).getCompletionEntryDetails(l.toString(),u,r.label);return o?{uri:l,position:i,label:o.name,kind:D.convertKind(o.kind),detail:S(o.displayParts),documentation:{value:D.createDocumentationString(o)}}:r})}static convertKind(e){switch(e){case c.primitiveType:case c.keyword:return a.languages.CompletionItemKind.Keyword;case c.variable:case c.localVariable:return a.languages.CompletionItemKind.Variable;case c.memberVariable:case c.memberGetAccessor:case c.memberSetAccessor:return a.languages.CompletionItemKind.Field;case c.function:case c.memberFunction:case c.constructSignature:case c.callSignature:case c.indexSignature:return a.languages.CompletionItemKind.Function;case c.enum:return a.languages.CompletionItemKind.Enum;case c.module:return a.languages.CompletionItemKind.Module;case c.class:return a.languages.CompletionItemKind.Class;case c.interface:return a.languages.CompletionItemKind.Interface;case c.warning:return a.languages.CompletionItemKind.File}return a.languages.CompletionItemKind.Property}static createDocumentationString(e){let t=S(e.documentation);if(e.tags)for(const r of e.tags)t+=`

${R(r)}`;return t}};function R(e){let t=`*@${e.name}*`;if(e.name==="param"&&e.text){const[r,...l]=e.text;t+=`\`${r.text}\``,l.length>0&&(t+=` — ${l.map(i=>i.text).join(" ")}`)}else Array.isArray(e.text)?t+=` — ${e.text.map(r=>r.text).join(" ")}`:e.text&&(t+=` — ${e.text}`);return t}var K=class extends y{constructor(){super(...arguments);w(this,"signatureHelpTriggerCharacters",["(",","])}static _toSignatureHelpTriggerReason(t){switch(t.triggerKind){case a.languages.SignatureHelpTriggerKind.TriggerCharacter:return t.triggerCharacter?t.isRetrigger?{kind:"retrigger",triggerCharacter:t.triggerCharacter}:{kind:"characterTyped",triggerCharacter:t.triggerCharacter}:{kind:"invoked"};case a.languages.SignatureHelpTriggerKind.ContentChange:return t.isRetrigger?{kind:"retrigger"}:{kind:"invoked"};case a.languages.SignatureHelpTriggerKind.Invoke:default:return{kind:"invoked"}}}provideSignatureHelp(t,r,l,i){return m(this,null,function*(){const u=t.uri,g=t.getOffsetAt(r),o=yield this._worker(u);if(t.isDisposed())return;const n=yield o.getSignatureHelpItems(u.toString(),g,{triggerReason:K._toSignatureHelpTriggerReason(i)});if(!n||t.isDisposed())return;const d={activeSignature:n.selectedItemIndex,activeParameter:n.argumentIndex,signatures:[]};return n.items.forEach(p=>{const f={label:"",parameters:[]};f.documentation={value:S(p.documentation)},f.label+=S(p.prefixDisplayParts),p.parameters.forEach((h,k,C)=>{const x=S(h.displayParts),H={label:x,documentation:{value:S(h.documentation)}};f.label+=x,f.parameters.push(H),k<C.length-1&&(f.label+=S(p.separatorDisplayParts))}),f.label+=S(p.suffixDisplayParts),d.signatures.push(f)}),{value:d,dispose(){}}})}},te=class extends y{provideHover(e,t,r){return m(this,null,function*(){const l=e.uri,i=e.getOffsetAt(t),u=yield this._worker(l);if(e.isDisposed())return;const g=yield u.getQuickInfoAtPosition(l.toString(),i);if(!g||e.isDisposed())return;const o=S(g.documentation),n=g.tags?g.tags.map(p=>R(p)).join(`  

`):"",d=S(g.displayParts);return{range:this._textSpanToRange(e,g.textSpan),contents:[{value:"```typescript\n"+d+"\n```\n"},{value:o+(n?`

`+n:"")}]}})}},re=class extends y{provideDocumentHighlights(e,t,r){return m(this,null,function*(){const l=e.uri,i=e.getOffsetAt(t),u=yield this._worker(l);if(e.isDisposed())return;const g=yield u.getDocumentHighlights(l.toString(),i,[l.toString()]);if(!(!g||e.isDisposed()))return g.flatMap(o=>o.highlightSpans.map(n=>({range:this._textSpanToRange(e,n.textSpan),kind:n.kind==="writtenReference"?a.languages.DocumentHighlightKind.Write:a.languages.DocumentHighlightKind.Text})))})}},se=class extends y{constructor(e,t){super(t),this._libFiles=e}provideDefinition(e,t,r){return m(this,null,function*(){const l=e.uri,i=e.getOffsetAt(t),u=yield this._worker(l);if(e.isDisposed())return;const g=yield u.getDefinitionAtPosition(l.toString(),i);if(!g||e.isDisposed()||(yield this._libFiles.fetchLibFilesIfNecessary(g.map(n=>a.Uri.parse(n.fileName))),e.isDisposed()))return;const o=[];for(let n of g){const d=this._libFiles.getOrCreateModel(n.fileName);d&&o.push({uri:d.uri,range:this._textSpanToRange(d,n.textSpan)})}return o})}},ie=class extends y{constructor(e,t){super(t),this._libFiles=e}provideReferences(e,t,r,l){return m(this,null,function*(){const i=e.uri,u=e.getOffsetAt(t),g=yield this._worker(i);if(e.isDisposed())return;const o=yield g.getReferencesAtPosition(i.toString(),u);if(!o||e.isDisposed()||(yield this._libFiles.fetchLibFilesIfNecessary(o.map(d=>a.Uri.parse(d.fileName))),e.isDisposed()))return;const n=[];for(let d of o){const p=this._libFiles.getOrCreateModel(d.fileName);p&&n.push({uri:p.uri,range:this._textSpanToRange(p,d.textSpan)})}return n})}},ne=class extends y{provideDocumentSymbols(e,t){return m(this,null,function*(){const r=e.uri,l=yield this._worker(r);if(e.isDisposed())return;const i=yield l.getNavigationTree(r.toString());if(!i||e.isDisposed())return;const u=(o,n)=>{var p;return{name:o.text,detail:"",kind:_[o.kind]||a.languages.SymbolKind.Variable,range:this._textSpanToRange(e,o.spans[0]),selectionRange:this._textSpanToRange(e,o.spans[0]),tags:[],children:(p=o.childItems)==null?void 0:p.map(f=>u(f,o.text)),containerName:n}};return i.childItems?i.childItems.map(o=>u(o)):[]})}},c=class{};b(c,"unknown","");b(c,"keyword","keyword");b(c,"script","script");b(c,"module","module");b(c,"class","class");b(c,"interface","interface");b(c,"type","type");b(c,"enum","enum");b(c,"variable","var");b(c,"localVariable","local var");b(c,"function","function");b(c,"localFunction","local function");b(c,"memberFunction","method");b(c,"memberGetAccessor","getter");b(c,"memberSetAccessor","setter");b(c,"memberVariable","property");b(c,"constructorImplementation","constructor");b(c,"callSignature","call");b(c,"indexSignature","index");b(c,"constructSignature","construct");b(c,"parameter","parameter");b(c,"typeParameter","type parameter");b(c,"primitiveType","primitive type");b(c,"label","label");b(c,"alias","alias");b(c,"const","const");b(c,"let","let");b(c,"warning","warning");var _=Object.create(null);_[c.module]=a.languages.SymbolKind.Module;_[c.class]=a.languages.SymbolKind.Class;_[c.enum]=a.languages.SymbolKind.Enum;_[c.interface]=a.languages.SymbolKind.Interface;_[c.memberFunction]=a.languages.SymbolKind.Method;_[c.memberVariable]=a.languages.SymbolKind.Property;_[c.memberGetAccessor]=a.languages.SymbolKind.Property;_[c.memberSetAccessor]=a.languages.SymbolKind.Property;_[c.variable]=a.languages.SymbolKind.Variable;_[c.const]=a.languages.SymbolKind.Variable;_[c.localVariable]=a.languages.SymbolKind.Variable;_[c.variable]=a.languages.SymbolKind.Variable;_[c.function]=a.languages.SymbolKind.Function;_[c.localFunction]=a.languages.SymbolKind.Function;var v=class extends y{static _convertOptions(e){return{ConvertTabsToSpaces:e.insertSpaces,TabSize:e.tabSize,IndentSize:e.tabSize,IndentStyle:2,NewLineCharacter:`
`,InsertSpaceAfterCommaDelimiter:!0,InsertSpaceAfterSemicolonInForStatements:!0,InsertSpaceBeforeAndAfterBinaryOperators:!0,InsertSpaceAfterKeywordsInControlFlowStatements:!0,InsertSpaceAfterFunctionKeywordForAnonymousFunctions:!0,InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis:!1,InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets:!1,InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces:!1,PlaceOpenBraceOnNewLineForControlBlocks:!1,PlaceOpenBraceOnNewLineForFunctions:!1}}_convertTextChanges(e,t){return{text:t.newText,range:this._textSpanToRange(e,t.span)}}},ae=class extends v{constructor(){super(...arguments);w(this,"canFormatMultipleRanges",!1)}provideDocumentRangeFormattingEdits(t,r,l,i){return m(this,null,function*(){const u=t.uri,g=t.getOffsetAt({lineNumber:r.startLineNumber,column:r.startColumn}),o=t.getOffsetAt({lineNumber:r.endLineNumber,column:r.endColumn}),n=yield this._worker(u);if(t.isDisposed())return;const d=yield n.getFormattingEditsForRange(u.toString(),g,o,v._convertOptions(l));if(!(!d||t.isDisposed()))return d.map(p=>this._convertTextChanges(t,p))})}},oe=class extends v{get autoFormatTriggerCharacters(){return[";","}",`
`]}provideOnTypeFormattingEdits(e,t,r,l,i){return m(this,null,function*(){const u=e.uri,g=e.getOffsetAt(t),o=yield this._worker(u);if(e.isDisposed())return;const n=yield o.getFormattingEditsAfterKeystroke(u.toString(),g,r,v._convertOptions(l));if(!(!n||e.isDisposed()))return n.map(d=>this._convertTextChanges(e,d))})}},le=class extends v{provideCodeActions(e,t,r,l){return m(this,null,function*(){const i=e.uri,u=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),g=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),o=v._convertOptions(e.getOptions()),n=r.markers.filter(h=>h.code).map(h=>h.code).map(Number),d=yield this._worker(i);if(e.isDisposed())return;const p=yield d.getCodeFixesAtPosition(i.toString(),u,g,n,o);return!p||e.isDisposed()?{actions:[],dispose:()=>{}}:{actions:p.filter(h=>h.changes.filter(k=>k.isNewFile).length===0).map(h=>this._tsCodeFixActionToMonacoCodeAction(e,r,h)),dispose:()=>{}}})}_tsCodeFixActionToMonacoCodeAction(e,t,r){const l=[];for(const u of r.changes)for(const g of u.textChanges)l.push({resource:e.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(e,g.span),text:g.newText}});return{title:r.description,edit:{edits:l},diagnostics:t.markers,kind:"quickfix"}}},ce=class extends y{constructor(e,t){super(t),this._libFiles=e}provideRenameEdits(e,t,r,l){return m(this,null,function*(){const i=e.uri,u=i.toString(),g=e.getOffsetAt(t),o=yield this._worker(i);if(e.isDisposed())return;const n=yield o.getRenameInfo(u,g,{allowRenameOfImportPath:!1});if(n.canRename===!1)return{edits:[],rejectReason:n.localizedErrorMessage};if(n.fileToRename!==void 0)throw new Error("Renaming files is not supported.");const d=yield o.findRenameLocations(u,g,!1,!1,!1);if(!d||e.isDisposed())return;const p=[];for(const f of d){const h=this._libFiles.getOrCreateModel(f.fileName);if(h)p.push({resource:h.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(h,f.textSpan),text:r}});else throw new Error(`Unknown file ${f.fileName}.`)}return{edits:p}})}},ue=class extends y{provideInlayHints(e,t,r){return m(this,null,function*(){const l=e.uri,i=l.toString(),u=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),g=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),o=yield this._worker(l);return e.isDisposed()?null:{hints:(yield o.provideInlayHints(i,u,g)).map(p=>I(O({},p),{label:p.text,position:e.getPositionAt(p.position),kind:this._convertHintKind(p.kind)})),dispose:()=>{}}})}_convertHintKind(e){switch(e){case"Parameter":return a.languages.InlayHintKind.Parameter;case"Type":return a.languages.InlayHintKind.Type;default:return a.languages.InlayHintKind.Type}}},T,L;function fe(e){L=E(e,"typescript")}function be(e){T=E(e,"javascript")}function he(){return new Promise((e,t)=>{if(!T)return t("JavaScript not registered!");e(T)})}function me(){return new Promise((e,t)=>{if(!L)return t("TypeScript not registered!");e(L)})}function E(e,t){const r=[],l=new Y(t,e),i=(...o)=>l.getLanguageServiceWorker(...o),u=new Z(i);function g(){const{modeConfiguration:o}=e;ge(r),o.completionItems&&r.push(a.languages.registerCompletionItemProvider(t,new D(i))),o.signatureHelp&&r.push(a.languages.registerSignatureHelpProvider(t,new K(i))),o.hovers&&r.push(a.languages.registerHoverProvider(t,new te(i))),o.documentHighlights&&r.push(a.languages.registerDocumentHighlightProvider(t,new re(i))),o.definitions&&r.push(a.languages.registerDefinitionProvider(t,new se(u,i))),o.references&&r.push(a.languages.registerReferenceProvider(t,new ie(u,i))),o.documentSymbols&&r.push(a.languages.registerDocumentSymbolProvider(t,new ne(i))),o.rename&&r.push(a.languages.registerRenameProvider(t,new ce(u,i))),o.documentRangeFormattingEdits&&r.push(a.languages.registerDocumentRangeFormattingEditProvider(t,new ae(i))),o.onTypeFormattingEdits&&r.push(a.languages.registerOnTypeFormattingEditProvider(t,new oe(i))),o.codeActions&&r.push(a.languages.registerCodeActionProvider(t,new le(i))),o.inlayHints&&r.push(a.languages.registerInlayHintsProvider(t,new ue(i))),o.diagnostics&&r.push(new ee(u,e,t,i))}return g(),i}function ge(e){for(;e.length;)e.pop().dispose()}export{y as Adapter,le as CodeActionAdaptor,se as DefinitionAdapter,ee as DiagnosticsAdapter,re as DocumentHighlightAdapter,ae as FormatAdapter,v as FormatHelper,oe as FormatOnTypeAdapter,ue as InlayHintsAdapter,c as Kind,Z as LibFiles,ne as OutlineAdapter,te as QuickInfoAdapter,ie as ReferenceAdapter,ce as RenameAdapter,K as SignatureHelpAdapter,D as SuggestAdapter,Y as WorkerManager,A as flattenDiagnosticMessageText,he as getJavaScriptWorker,me as getTypeScriptWorker,be as setupJavaScript,fe as setupTypeScript};
