import{b2 as N,K as dr,L as ur,V as pn,d as Ir,ci as gn,k as Le,ca as lt,o as Qe,p as He,b as yn,q as za,e as or,r as Pn,cV as qa,cW as bn,cC as En,M as Sn}from"./index-b380aaed.js";/** @license @lljj/vue3-form-naive (c) 2020-2023 Liu.Jun License: Apache-2.0 */function Ke(a){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ke=function(e){return typeof e}:Ke=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ke(a)}function G(a,e,t){return e in a?Object.defineProperty(a,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):a[e]=t,a}function Zr(){return Zr=Object.assign||function(a){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(a[n]=t[n])}return a},Zr.apply(this,arguments)}function Wa(a,e){var t=Object.keys(a);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(a);e&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(a,r).enumerable})),t.push.apply(t,n)}return t}function _(a){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Wa(Object(t),!0).forEach(function(n){G(a,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(t)):Wa(Object(t)).forEach(function(n){Object.defineProperty(a,n,Object.getOwnPropertyDescriptor(t,n))})}return a}function wn(a,e){if(a==null)return{};var t={},n=Object.keys(a),r,o;for(o=0;o<n.length;o++)r=n[o],!(e.indexOf(r)>=0)&&(t[r]=a[r]);return t}function we(a,e){if(a==null)return{};var t=wn(a,e),n,r;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(a);for(r=0;r<o.length;r++)n=o[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(a,n)&&(t[n]=a[n])}return t}function Je(a,e){return On(a)||_n(a,e)||ut(a,e)||An()}function _e(a){return Fn(a)||Dn(a)||ut(a)||xn()}function Fn(a){if(Array.isArray(a))return Yr(a)}function On(a){if(Array.isArray(a))return a}function Dn(a){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(a))return Array.from(a)}function _n(a,e){if(!(typeof Symbol=="undefined"||!(Symbol.iterator in Object(a)))){var t=[],n=!0,r=!1,o=void 0;try{for(var i=a[Symbol.iterator](),s;!(n=(s=i.next()).done)&&(t.push(s.value),!(e&&t.length===e));n=!0);}catch(c){r=!0,o=c}finally{try{!n&&i.return!=null&&i.return()}finally{if(r)throw o}}return t}}function ut(a,e){if(a){if(typeof a=="string")return Yr(a,e);var t=Object.prototype.toString.call(a).slice(8,-1);if(t==="Object"&&a.constructor&&(t=a.constructor.name),t==="Map"||t==="Set")return Array.from(a);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Yr(a,e)}}function Yr(a,e){(e==null||e>a.length)&&(e=a.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=a[t];return n}function xn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function An(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function $n(a,e){if(typeof a!="object"||a===null)return a;var t=a[Symbol.toPrimitive];if(t!==void 0){var n=t.call(a,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(a)}function In(a){var e=$n(a,"string");return typeof e=="symbol"?e:String(e)}var ma=".";function ft(a){var e="__pathRoot";return a?"".concat(e,".").concat(a).replace(/\./g,"_"):e}function Cn(a){return a===""}function Nr(a,e){return a===""?e:[a,e].join(ma)}function Ue(a,e){for(var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,n=e.split(ma),r=0;r<n.length-t;r+=1){if(a===void 0)return;a=n[r]===""?a:a[n[r]]}return a}function Rn(a,e){delete a[e]}function jr(a,e,t){for(var n=e.split(ma),r=0;r<n.length;r+=1){if(n.length-r<2){a[n[n.length-1]]=t;break}a=a[n[r]]}}function ee(a){return typeof a=="string"?pn(a):a}var $e=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.model,r=n===void 0?"value":n;return Ir({inheritAttrs:!1,setup:function(i,s){var c=s.attrs,d=s.slots;return function(){var l,f=c.modelValue,p=c["onUpdate:modelValue"],u=we(c,["modelValue","onUpdate:modelValue"]);return N(ee(e),_((l={},G(l,r,f),G(l,"onUpdate:".concat(r),p),l),u),d)}}})};function J(a){return Object.prototype.toString.call(a)==="[object Object]"}function xr(a){return Object.prototype.toString.call(a)==="[object Arguments]"}var Nn=function(e){return Array.isArray(e)?"array":typeof e=="string"?"string":e==null?"null":typeof e=="boolean"?"boolean":isNaN(e)?Ke(e)==="object"?"object":"string":"number"};function Cr(a,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=Object.assign({},a);return J(e)?Object.keys(e).reduce(function(r,o){var i=a?a[o]:{},s=e[o];return a&&a.hasOwnProperty(o)&&J(s)?r[o]=Cr(i,s,t):t&&Array.isArray(i)&&Array.isArray(s)?r[o]=i.concat(s):r[o]=s,r},n):n}function ha(a){var e=a.type;return!e&&a.const?Nn(a.const):!e&&a.enum?"string":!e&&a.items?"array":!e&&(a.properties||a.additionalProperties)?"object":e instanceof Array&&e.length===2&&e.includes("null")?e.find(function(t){return t!=="null"}):e}function fr(a,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[],n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:[];if(a===e||typeof a=="function"||typeof e=="function")return!0;if(Ke(a)!=="object"||Ke(e)!=="object"||a===null||e===null)return!1;if(a instanceof Date&&e instanceof Date)return a.getTime()===e.getTime();if(a instanceof RegExp&&e instanceof RegExp)return a.source===e.source&&a.global===e.global&&a.multiline===e.multiline&&a.lastIndex===e.lastIndex&&a.ignoreCase===e.ignoreCase;if(xr(a)||xr(e)){if(!(xr(a)&&xr(e)))return!1;var r=Array.prototype.slice;return fr(r.call(a),r.call(e),t,n)}if(a.constructor!==e.constructor)return!1;var o=Object.keys(a),i=Object.keys(e);if(o.length===0&&i.length===0)return!0;if(o.length!==i.length)return!1;for(var s=t.length;s--;)if(t[s]===a)return n[s]===e;t.push(a),n.push(e),o.sort(),i.sort();for(var c=o.length-1;c>=0;c--)if(o[c]!==i[c])return!1;for(var d,l=o.length-1;l>=0;l--)if(d=o[l],!fr(a[d],e[d],t,n))return!1;return t.pop(),n.pop(),!0}var Pr=function(){var e="".concat(+new Date),t=0;return function(){var n="".concat(+new Date);return n===e?t+=1:t=0,e=n,"".concat(e,"x").concat(t)}}();function Ba(a){if(!a)return!0;for(var e in a)if(Object.prototype.hasOwnProperty.call(a,e))return!1;return!0}function Qa(a,e){return Object.entries(a).reduce(function(t,n){var r=Je(n,2),o=r[0],i=r[1],s=e(o,i);return s!==void 0&&(t[s]=i),t},{})}function Rr(a){return a===void 0?a:String(a).replace(/^./,function(e){return e.toLocaleLowerCase()})}function ct(a,e){return e===0?a:ct(e,a%e)}function jn(a,e){return a*e/ct(a,e)}function Ln(a,e){for(var t=e.split("/"),n=0;n<t.length;n+=1){if(a===void 0)return;a=t[n]===""?a:a[t[n]]}return a}function va(a){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=a;if(a.startsWith("#"))a=decodeURIComponent(a.substring(1));else throw new Error("Could not find a definition for ".concat(t,"."));var n=Ln(e,a);if(n===void 0)throw new Error("Could not find a definition for ".concat(t,"."));return n.hasOwnProperty("$ref")?va(n.$ref,e):n}var Tn=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function kn(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}function pa(a,e){return e={exports:{}},a(e,e.exports),e.exports}function dt(a){return a&&a.default||a}var mr=pa(function(a,e){/** @license URI.js v4.4.1 (c) 2011 Gary Court. License: http://github.com/garycourt/uri-js */(function(t,n){n(e)})(Tn,function(t){function n(){for(var y=arguments.length,m=Array(y),P=0;P<y;P++)m[P]=arguments[P];if(m.length>1){m[0]=m[0].slice(0,-1);for(var O=m.length-1,w=1;w<O;++w)m[w]=m[w].slice(1,-1);return m[O]=m[O].slice(1),m.join("")}else return m[0]}function r(y){return"(?:"+y+")"}function o(y){return y===void 0?"undefined":y===null?"null":Object.prototype.toString.call(y).split(" ").pop().split("]").shift().toLowerCase()}function i(y){return y.toUpperCase()}function s(y){return y!=null?y instanceof Array?y:typeof y.length!="number"||y.split||y.setInterval||y.call?[y]:Array.prototype.slice.call(y):[]}function c(y,m){var P=y;if(m)for(var O in m)P[O]=m[O];return P}function d(y){var m="[A-Za-z]",P="[0-9]",O=n(P,"[A-Fa-f]"),w=r(r("%[EFef]"+O+"%"+O+O+"%"+O+O)+"|"+r("%[89A-Fa-f]"+O+"%"+O+O)+"|"+r("%"+O+O)),k="[\\:\\/\\?\\#\\[\\]\\@]",M="[\\!\\$\\&\\'\\(\\)\\*\\+\\,\\;\\=]",Q=n(k,M),X=y?"[\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]":"[]",se=y?"[\\uE000-\\uF8FF]":"[]",B=n(m,P,"[\\-\\.\\_\\~]",X);r(m+n(m,P,"[\\+\\-\\.]")+"*"),r(r(w+"|"+n(B,M,"[\\:]"))+"*");var Y=r(r("25[0-5]")+"|"+r("2[0-4]"+P)+"|"+r("1"+P+P)+"|"+r("0?[1-9]"+P)+"|0?0?"+P),le=r(Y+"\\."+Y+"\\."+Y+"\\."+Y),z=r(O+"{1,4}"),re=r(r(z+"\\:"+z)+"|"+le),ce=r(r(z+"\\:")+"{6}"+re),ae=r("\\:\\:"+r(z+"\\:")+"{5}"+re),We=r(r(z)+"?\\:\\:"+r(z+"\\:")+"{4}"+re),Re=r(r(r(z+"\\:")+"{0,1}"+z)+"?\\:\\:"+r(z+"\\:")+"{3}"+re),Ne=r(r(r(z+"\\:")+"{0,2}"+z)+"?\\:\\:"+r(z+"\\:")+"{2}"+re),lr=r(r(r(z+"\\:")+"{0,3}"+z)+"?\\:\\:"+z+"\\:"+re),er=r(r(r(z+"\\:")+"{0,4}"+z)+"?\\:\\:"+re),De=r(r(r(z+"\\:")+"{0,5}"+z)+"?\\:\\:"+z),je=r(r(r(z+"\\:")+"{0,6}"+z)+"?\\:\\:"),rr=r([ce,ae,We,Re,Ne,lr,er,De,je].join("|")),Me=r(r(B+"|"+w)+"+");r("[vV]"+O+"+\\."+n(B,M,"[\\:]")+"+"),r(r(w+"|"+n(B,M))+"*");var gr=r(w+"|"+n(B,M,"[\\:\\@]"));return r(r(w+"|"+n(B,M,"[\\@]"))+"+"),r(r(gr+"|"+n("[\\/\\?]",se))+"*"),{NOT_SCHEME:new RegExp(n("[^]",m,P,"[\\+\\-\\.]"),"g"),NOT_USERINFO:new RegExp(n("[^\\%\\:]",B,M),"g"),NOT_HOST:new RegExp(n("[^\\%\\[\\]\\:]",B,M),"g"),NOT_PATH:new RegExp(n("[^\\%\\/\\:\\@]",B,M),"g"),NOT_PATH_NOSCHEME:new RegExp(n("[^\\%\\/\\@]",B,M),"g"),NOT_QUERY:new RegExp(n("[^\\%]",B,M,"[\\:\\@\\/\\?]",se),"g"),NOT_FRAGMENT:new RegExp(n("[^\\%]",B,M,"[\\:\\@\\/\\?]"),"g"),ESCAPE:new RegExp(n("[^]",B,M),"g"),UNRESERVED:new RegExp(B,"g"),OTHER_CHARS:new RegExp(n("[^\\%]",B,Q),"g"),PCT_ENCODED:new RegExp(w,"g"),IPV4ADDRESS:new RegExp("^("+le+")$"),IPV6ADDRESS:new RegExp("^\\[?("+rr+")"+r(r("\\%25|\\%(?!"+O+"{2})")+"("+Me+")")+"?\\]?$")}}var l=d(!1),f=d(!0),p=function(){function y(m,P){var O=[],w=!0,k=!1,M=void 0;try{for(var Q=m[Symbol.iterator](),X;!(w=(X=Q.next()).done)&&(O.push(X.value),!(P&&O.length===P));w=!0);}catch(se){k=!0,M=se}finally{try{!w&&Q.return&&Q.return()}finally{if(k)throw M}}return O}return function(m,P){if(Array.isArray(m))return m;if(Symbol.iterator in Object(m))return y(m,P);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),u=function(y){if(Array.isArray(y)){for(var m=0,P=Array(y.length);m<y.length;m++)P[m]=y[m];return P}else return Array.from(y)},v=2147483647,g=36,h=1,b=26,F=38,E=700,S=72,D=128,x="-",C=/^xn--/,I=/[^\0-\x7E]/,R=/[\x2E\u3002\uFF0E\uFF61]/g,T={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},j=g-h,L=Math.floor,$=String.fromCharCode;function A(y){throw new RangeError(T[y])}function V(y,m){for(var P=[],O=y.length;O--;)P[O]=m(y[O]);return P}function H(y,m){var P=y.split("@"),O="";P.length>1&&(O=P[0]+"@",y=P[1]),y=y.replace(R,".");var w=y.split("."),k=V(w,m).join(".");return O+k}function q(y){for(var m=[],P=0,O=y.length;P<O;){var w=y.charCodeAt(P++);if(w>=55296&&w<=56319&&P<O){var k=y.charCodeAt(P++);(k&64512)==56320?m.push(((w&1023)<<10)+(k&1023)+65536):(m.push(w),P--)}else m.push(w)}return m}var K=function(m){return String.fromCodePoint.apply(String,u(m))},W=function(m){return m-48<10?m-22:m-65<26?m-65:m-97<26?m-97:g},U=function(m,P){return m+22+75*(m<26)-((P!=0)<<5)},ue=function(m,P,O){var w=0;for(m=O?L(m/E):m>>1,m+=L(m/P);m>j*b>>1;w+=g)m=L(m/j);return L(w+(j+1)*m/(m+F))},ve=function(m){var P=[],O=m.length,w=0,k=D,M=S,Q=m.lastIndexOf(x);Q<0&&(Q=0);for(var X=0;X<Q;++X)m.charCodeAt(X)>=128&&A("not-basic"),P.push(m.charCodeAt(X));for(var se=Q>0?Q+1:0;se<O;){for(var B=w,Y=1,le=g;;le+=g){se>=O&&A("invalid-input");var z=W(m.charCodeAt(se++));(z>=g||z>L((v-w)/Y))&&A("overflow"),w+=z*Y;var re=le<=M?h:le>=M+b?b:le-M;if(z<re)break;var ce=g-re;Y>L(v/ce)&&A("overflow"),Y*=ce}var ae=P.length+1;M=ue(w-B,ae,B==0),L(w/ae)>v-k&&A("overflow"),k+=L(w/ae),w%=ae,P.splice(w++,0,k)}return String.fromCodePoint.apply(String,P)},ge=function(m){var P=[];m=q(m);var O=m.length,w=D,k=0,M=S,Q=!0,X=!1,se=void 0;try{for(var B=m[Symbol.iterator](),Y;!(Q=(Y=B.next()).done);Q=!0){var le=Y.value;le<128&&P.push($(le))}}catch(yr){X=!0,se=yr}finally{try{!Q&&B.return&&B.return()}finally{if(X)throw se}}var z=P.length,re=z;for(z&&P.push(x);re<O;){var ce=v,ae=!0,We=!1,Re=void 0;try{for(var Ne=m[Symbol.iterator](),lr;!(ae=(lr=Ne.next()).done);ae=!0){var er=lr.value;er>=w&&er<ce&&(ce=er)}}catch(yr){We=!0,Re=yr}finally{try{!ae&&Ne.return&&Ne.return()}finally{if(We)throw Re}}var De=re+1;ce-w>L((v-k)/De)&&A("overflow"),k+=(ce-w)*De,w=ce;var je=!0,rr=!1,Me=void 0;try{for(var gr=m[Symbol.iterator](),ka;!(je=(ka=gr.next()).done);je=!0){var Va=ka.value;if(Va<w&&++k>v&&A("overflow"),Va==w){for(var Or=k,Dr=g;;Dr+=g){var _r=Dr<=M?h:Dr>=M+b?b:Dr-M;if(Or<_r)break;var Ma=Or-_r,Ua=g-_r;P.push($(U(_r+Ma%Ua,0))),Or=L(Ma/Ua)}P.push($(U(Or,0))),M=ue(k,De,re==z),k=0,++re}}}catch(yr){rr=!0,Me=yr}finally{try{!je&&gr.return&&gr.return()}finally{if(rr)throw Me}}++k,++w}return P.join("")},Z=function(m){return H(m,function(P){return C.test(P)?ve(P.slice(4).toLowerCase()):P})},Te=function(m){return H(m,function(P){return I.test(P)?"xn--"+ge(P):P})},be={version:"2.1.0",ucs2:{decode:q,encode:K},decode:ve,encode:ge,toASCII:Te,toUnicode:Z},ie={};function Pe(y){var m=y.charCodeAt(0),P=void 0;return m<16?P="%0"+m.toString(16).toUpperCase():m<128?P="%"+m.toString(16).toUpperCase():m<2048?P="%"+(m>>6|192).toString(16).toUpperCase()+"%"+(m&63|128).toString(16).toUpperCase():P="%"+(m>>12|224).toString(16).toUpperCase()+"%"+(m>>6&63|128).toString(16).toUpperCase()+"%"+(m&63|128).toString(16).toUpperCase(),P}function Oe(y){for(var m="",P=0,O=y.length;P<O;){var w=parseInt(y.substr(P+1,2),16);if(w<128)m+=String.fromCharCode(w),P+=3;else if(w>=194&&w<224){if(O-P>=6){var k=parseInt(y.substr(P+4,2),16);m+=String.fromCharCode((w&31)<<6|k&63)}else m+=y.substr(P,6);P+=6}else if(w>=224){if(O-P>=9){var M=parseInt(y.substr(P+4,2),16),Q=parseInt(y.substr(P+7,2),16);m+=String.fromCharCode((w&15)<<12|(M&63)<<6|Q&63)}else m+=y.substr(P,9);P+=9}else m+=y.substr(P,3),P+=3}return m}function fe(y,m){function P(O){var w=Oe(O);return w.match(m.UNRESERVED)?w:O}return y.scheme&&(y.scheme=String(y.scheme).replace(m.PCT_ENCODED,P).toLowerCase().replace(m.NOT_SCHEME,"")),y.userinfo!==void 0&&(y.userinfo=String(y.userinfo).replace(m.PCT_ENCODED,P).replace(m.NOT_USERINFO,Pe).replace(m.PCT_ENCODED,i)),y.host!==void 0&&(y.host=String(y.host).replace(m.PCT_ENCODED,P).toLowerCase().replace(m.NOT_HOST,Pe).replace(m.PCT_ENCODED,i)),y.path!==void 0&&(y.path=String(y.path).replace(m.PCT_ENCODED,P).replace(y.scheme?m.NOT_PATH:m.NOT_PATH_NOSCHEME,Pe).replace(m.PCT_ENCODED,i)),y.query!==void 0&&(y.query=String(y.query).replace(m.PCT_ENCODED,P).replace(m.NOT_QUERY,Pe).replace(m.PCT_ENCODED,i)),y.fragment!==void 0&&(y.fragment=String(y.fragment).replace(m.PCT_ENCODED,P).replace(m.NOT_FRAGMENT,Pe).replace(m.PCT_ENCODED,i)),y}function te(y){return y.replace(/^0*(.*)/,"$1")||"0"}function ke(y,m){var P=y.match(m.IPV4ADDRESS)||[],O=p(P,2),w=O[1];return w?w.split(".").map(te).join("."):y}function pe(y,m){var P=y.match(m.IPV6ADDRESS)||[],O=p(P,3),w=O[1],k=O[2];if(w){for(var M=w.toLowerCase().split("::").reverse(),Q=p(M,2),X=Q[0],se=Q[1],B=se?se.split(":").map(te):[],Y=X.split(":").map(te),le=m.IPV4ADDRESS.test(Y[Y.length-1]),z=le?7:8,re=Y.length-z,ce=Array(z),ae=0;ae<z;++ae)ce[ae]=B[ae]||Y[re+ae]||"";le&&(ce[z-1]=ke(ce[z-1],m));var We=ce.reduce(function(De,je,rr){if(!je||je==="0"){var Me=De[De.length-1];Me&&Me.index+Me.length===rr?Me.length++:De.push({index:rr,length:1})}return De},[]),Re=We.sort(function(De,je){return je.length-De.length})[0],Ne=void 0;if(Re&&Re.length>1){var lr=ce.slice(0,Re.index),er=ce.slice(Re.index+Re.length);Ne=lr.join(":")+"::"+er.join(":")}else Ne=ce.join(":");return k&&(Ne+="%"+k),Ne}else return y}var ne=/^(?:([^:\/?#]+):)?(?:\/\/((?:([^\/?#@]*)@)?(\[[^\/?#\]]+\]|[^\/?#:]*)(?:\:(\d*))?))?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n|\r)*))?/i,ze="".match(/(){0}/)[1]===void 0;function ye(y){var m=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},P={},O=m.iri!==!1?f:l;m.reference==="suffix"&&(y=(m.scheme?m.scheme+":":"")+"//"+y);var w=y.match(ne);if(w){ze?(P.scheme=w[1],P.userinfo=w[3],P.host=w[4],P.port=parseInt(w[5],10),P.path=w[6]||"",P.query=w[7],P.fragment=w[8],isNaN(P.port)&&(P.port=w[5])):(P.scheme=w[1]||void 0,P.userinfo=y.indexOf("@")!==-1?w[3]:void 0,P.host=y.indexOf("//")!==-1?w[4]:void 0,P.port=parseInt(w[5],10),P.path=w[6]||"",P.query=y.indexOf("?")!==-1?w[7]:void 0,P.fragment=y.indexOf("#")!==-1?w[8]:void 0,isNaN(P.port)&&(P.port=y.match(/\/\/(?:.|\n)*\:(?:\/|\?|\#|$)/)?w[4]:void 0)),P.host&&(P.host=pe(ke(P.host,O),O)),P.scheme===void 0&&P.userinfo===void 0&&P.host===void 0&&P.port===void 0&&!P.path&&P.query===void 0?P.reference="same-document":P.scheme===void 0?P.reference="relative":P.fragment===void 0?P.reference="absolute":P.reference="uri",m.reference&&m.reference!=="suffix"&&m.reference!==P.reference&&(P.error=P.error||"URI is not a "+m.reference+" reference.");var k=ie[(m.scheme||P.scheme||"").toLowerCase()];if(!m.unicodeSupport&&(!k||!k.unicodeSupport)){if(P.host&&(m.domainHost||k&&k.domainHost))try{P.host=be.toASCII(P.host.replace(O.PCT_ENCODED,Oe).toLowerCase())}catch(M){P.error=P.error||"Host's domain name can not be converted to ASCII via punycode: "+M}fe(P,l)}else fe(P,O);k&&k.parse&&k.parse(P,m)}else P.error=P.error||"URI can not be parsed.";return P}function ir(y,m){var P=m.iri!==!1?f:l,O=[];return y.userinfo!==void 0&&(O.push(y.userinfo),O.push("@")),y.host!==void 0&&O.push(pe(ke(String(y.host),P),P).replace(P.IPV6ADDRESS,function(w,k,M){return"["+k+(M?"%25"+M:"")+"]"})),(typeof y.port=="number"||typeof y.port=="string")&&(O.push(":"),O.push(String(y.port))),O.length?O.join(""):void 0}var Xe=/^\.\.?\//,he=/^\/\.(\/|$)/,qe=/^\/\.\.(\/|$)/,hr=/^\/?(?:.|\n)*?(?=\/|$)/;function de(y){for(var m=[];y.length;)if(y.match(Xe))y=y.replace(Xe,"");else if(y.match(he))y=y.replace(he,"/");else if(y.match(qe))y=y.replace(qe,"/"),m.pop();else if(y==="."||y==="..")y="";else{var P=y.match(hr);if(P){var O=P[0];y=y.slice(O.length),m.push(O)}else throw new Error("Unexpected dot segment condition")}return m.join("")}function me(y){var m=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},P=m.iri?f:l,O=[],w=ie[(m.scheme||y.scheme||"").toLowerCase()];if(w&&w.serialize&&w.serialize(y,m),y.host&&!P.IPV6ADDRESS.test(y.host)){if(m.domainHost||w&&w.domainHost)try{y.host=m.iri?be.toUnicode(y.host):be.toASCII(y.host.replace(P.PCT_ENCODED,Oe).toLowerCase())}catch(Q){y.error=y.error||"Host's domain name can not be converted to "+(m.iri?"Unicode":"ASCII")+" via punycode: "+Q}}fe(y,P),m.reference!=="suffix"&&y.scheme&&(O.push(y.scheme),O.push(":"));var k=ir(y,m);if(k!==void 0&&(m.reference!=="suffix"&&O.push("//"),O.push(k),y.path&&y.path.charAt(0)!=="/"&&O.push("/")),y.path!==void 0){var M=y.path;!m.absolutePath&&(!w||!w.absolutePath)&&(M=de(M)),k===void 0&&(M=M.replace(/^\/\//,"/%2F")),O.push(M)}return y.query!==void 0&&(O.push("?"),O.push(y.query)),y.fragment!==void 0&&(O.push("#"),O.push(y.fragment)),O.join("")}function Ee(y,m){var P=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},O=arguments[3],w={};return O||(y=ye(me(y,P),P),m=ye(me(m,P),P)),P=P||{},!P.tolerant&&m.scheme?(w.scheme=m.scheme,w.userinfo=m.userinfo,w.host=m.host,w.port=m.port,w.path=de(m.path||""),w.query=m.query):(m.userinfo!==void 0||m.host!==void 0||m.port!==void 0?(w.userinfo=m.userinfo,w.host=m.host,w.port=m.port,w.path=de(m.path||""),w.query=m.query):(m.path?(m.path.charAt(0)==="/"?w.path=de(m.path):((y.userinfo!==void 0||y.host!==void 0||y.port!==void 0)&&!y.path?w.path="/"+m.path:y.path?w.path=y.path.slice(0,y.path.lastIndexOf("/")+1)+m.path:w.path=m.path,w.path=de(w.path)),w.query=m.query):(w.path=y.path,m.query!==void 0?w.query=m.query:w.query=y.query),w.userinfo=y.userinfo,w.host=y.host,w.port=y.port),w.scheme=y.scheme),w.fragment=m.fragment,w}function Fr(y,m,P){var O=c({scheme:"null"},P);return me(Ee(ye(y,O),ye(m,O),O,!0),O)}function Br(y,m){return typeof y=="string"?y=me(ye(y,m),m):o(y)==="object"&&(y=ye(me(y,m),m)),y}function Qr(y,m,P){return typeof y=="string"?y=me(ye(y,P),P):o(y)==="object"&&(y=me(y,P)),typeof m=="string"?m=me(ye(m,P),P):o(m)==="object"&&(m=me(m,P)),y===m}function tn(y,m){return y&&y.toString().replace(!m||!m.iri?l.ESCAPE:f.ESCAPE,Pe)}function Ve(y,m){return y&&y.toString().replace(!m||!m.iri?l.PCT_ENCODED:f.PCT_ENCODED,Oe)}var vr={scheme:"http",domainHost:!0,parse:function(m,P){return m.host||(m.error=m.error||"HTTP URIs must have a host."),m},serialize:function(m,P){var O=String(m.scheme).toLowerCase()==="https";return(m.port===(O?443:80)||m.port==="")&&(m.port=void 0),m.path||(m.path="/"),m}},$a={scheme:"https",domainHost:vr.domainHost,parse:vr.parse,serialize:vr.serialize};function Ia(y){return typeof y.secure=="boolean"?y.secure:String(y.scheme).toLowerCase()==="wss"}var pr={scheme:"ws",domainHost:!0,parse:function(m,P){var O=m;return O.secure=Ia(O),O.resourceName=(O.path||"/")+(O.query?"?"+O.query:""),O.path=void 0,O.query=void 0,O},serialize:function(m,P){if((m.port===(Ia(m)?443:80)||m.port==="")&&(m.port=void 0),typeof m.secure=="boolean"&&(m.scheme=m.secure?"wss":"ws",m.secure=void 0),m.resourceName){var O=m.resourceName.split("?"),w=p(O,2),k=w[0],M=w[1];m.path=k&&k!=="/"?k:void 0,m.query=M,m.resourceName=void 0}return m.fragment=void 0,m}},Ca={scheme:"wss",domainHost:pr.domainHost,parse:pr.parse,serialize:pr.serialize},nn={},Ra="[A-Za-z0-9\\-\\.\\_\\~\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]",Ce="[0-9A-Fa-f]",on=r(r("%[EFef]"+Ce+"%"+Ce+Ce+"%"+Ce+Ce)+"|"+r("%[89A-Fa-f]"+Ce+"%"+Ce+Ce)+"|"+r("%"+Ce+Ce)),sn="[A-Za-z0-9\\!\\$\\%\\'\\*\\+\\-\\^\\_\\`\\{\\|\\}\\~]",ln="[\\!\\$\\%\\'\\(\\)\\*\\+\\,\\-\\.0-9\\<\\>A-Z\\x5E-\\x7E]",un=n(ln,'[\\"\\\\]'),fn="[\\!\\$\\'\\(\\)\\*\\+\\,\\;\\:\\@]",cn=new RegExp(Ra,"g"),sr=new RegExp(on,"g"),dn=new RegExp(n("[^]",sn,"[\\.]",'[\\"]',un),"g"),Na=new RegExp(n("[^]",Ra,fn),"g"),mn=Na;function Hr(y){var m=Oe(y);return m.match(cn)?m:y}var ja={scheme:"mailto",parse:function(m,P){var O=m,w=O.to=O.path?O.path.split(","):[];if(O.path=void 0,O.query){for(var k=!1,M={},Q=O.query.split("&"),X=0,se=Q.length;X<se;++X){var B=Q[X].split("=");switch(B[0]){case"to":for(var Y=B[1].split(","),le=0,z=Y.length;le<z;++le)w.push(Y[le]);break;case"subject":O.subject=Ve(B[1],P);break;case"body":O.body=Ve(B[1],P);break;default:k=!0,M[Ve(B[0],P)]=Ve(B[1],P);break}}k&&(O.headers=M)}O.query=void 0;for(var re=0,ce=w.length;re<ce;++re){var ae=w[re].split("@");if(ae[0]=Ve(ae[0]),P.unicodeSupport)ae[1]=Ve(ae[1],P).toLowerCase();else try{ae[1]=be.toASCII(Ve(ae[1],P).toLowerCase())}catch(We){O.error=O.error||"Email address's domain name can not be converted to ASCII via punycode: "+We}w[re]=ae.join("@")}return O},serialize:function(m,P){var O=m,w=s(m.to);if(w){for(var k=0,M=w.length;k<M;++k){var Q=String(w[k]),X=Q.lastIndexOf("@"),se=Q.slice(0,X).replace(sr,Hr).replace(sr,i).replace(dn,Pe),B=Q.slice(X+1);try{B=P.iri?be.toUnicode(B):be.toASCII(Ve(B,P).toLowerCase())}catch(re){O.error=O.error||"Email address's domain name can not be converted to "+(P.iri?"Unicode":"ASCII")+" via punycode: "+re}w[k]=se+"@"+B}O.path=w.join(",")}var Y=m.headers=m.headers||{};m.subject&&(Y.subject=m.subject),m.body&&(Y.body=m.body);var le=[];for(var z in Y)Y[z]!==nn[z]&&le.push(z.replace(sr,Hr).replace(sr,i).replace(Na,Pe)+"="+Y[z].replace(sr,Hr).replace(sr,i).replace(mn,Pe));return le.length&&(O.query=le.join("&")),O}},hn=/^([^\:]+)\:(.*)/,La={scheme:"urn",parse:function(m,P){var O=m.path&&m.path.match(hn),w=m;if(O){var k=P.scheme||w.scheme||"urn",M=O[1].toLowerCase(),Q=O[2],X=k+":"+(P.nid||M),se=ie[X];w.nid=M,w.nss=Q,w.path=void 0,se&&(w=se.parse(w,P))}else w.error=w.error||"URN can not be parsed.";return w},serialize:function(m,P){var O=P.scheme||m.scheme||"urn",w=m.nid,k=O+":"+(P.nid||w),M=ie[k];M&&(m=M.serialize(m,P));var Q=m,X=m.nss;return Q.path=(w||P.nid)+":"+X,Q}},vn=/^[0-9A-Fa-f]{8}(?:\-[0-9A-Fa-f]{4}){3}\-[0-9A-Fa-f]{12}$/,Ta={scheme:"urn:uuid",parse:function(m,P){var O=m;return O.uuid=O.nss,O.nss=void 0,!P.tolerant&&(!O.uuid||!O.uuid.match(vn))&&(O.error=O.error||"UUID is not valid."),O},serialize:function(m,P){var O=m;return O.nss=(m.uuid||"").toLowerCase(),O}};ie[vr.scheme]=vr,ie[$a.scheme]=$a,ie[pr.scheme]=pr,ie[Ca.scheme]=Ca,ie[ja.scheme]=ja,ie[La.scheme]=La,ie[Ta.scheme]=Ta,t.SCHEMES=ie,t.pctEncChar=Pe,t.pctDecChars=Oe,t.parse=ye,t.removeDotSegments=de,t.serialize=me,t.resolveComponents=Ee,t.resolve=Fr,t.normalize=Br,t.equal=Qr,t.escapeComponent=tn,t.unescapeComponent=Ve,Object.defineProperty(t,"__esModule",{value:!0})})});kn(mr);var Lr=function a(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;var n,r,o;if(Array.isArray(e)){if(n=e.length,n!=t.length)return!1;for(r=n;r--!==0;)if(!a(e[r],t[r]))return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!Object.prototype.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){var i=o[r];if(!a(e[i],t[i]))return!1}return!0}return e!==e&&t!==t},Vn=function(e){for(var t=0,n=e.length,r=0,o;r<n;)t++,o=e.charCodeAt(r++),o>=55296&&o<=56319&&r<n&&(o=e.charCodeAt(r),(o&64512)==56320&&r++);return t},Fe={copy:Mn,checkDataType:Xr,checkDataTypes:Un,coerceToTypes:zn,toHash:ga,getProperty:ya,escapeQuotes:Pa,equal:Lr,ucs2length:Vn,varOccurences:Bn,varReplace:Qn,schemaHasRules:Hn,schemaHasRulesExcept:Kn,schemaUnknownRules:Gn,toQuotedString:ea,getPathExpr:Jn,getPath:Zn,getData:eo,unescapeFragment:ro,unescapeJsonPointer:Ea,escapeFragment:ao,escapeJsonPointer:ba};function Mn(a,e){e=e||{};for(var t in a)e[t]=a[t];return e}function Xr(a,e,t,n){var r=n?" !== ":" === ",o=n?" || ":" && ",i=n?"!":"",s=n?"":"!";switch(a){case"null":return e+r+"null";case"array":return i+"Array.isArray("+e+")";case"object":return"("+i+e+o+"typeof "+e+r+'"object"'+o+s+"Array.isArray("+e+"))";case"integer":return"(typeof "+e+r+'"number"'+o+s+"("+e+" % 1)"+o+e+r+e+(t?o+i+"isFinite("+e+")":"")+")";case"number":return"(typeof "+e+r+'"'+a+'"'+(t?o+i+"isFinite("+e+")":"")+")";default:return"typeof "+e+r+'"'+a+'"'}}function Un(a,e,t){switch(a.length){case 1:return Xr(a[0],e,t,!0);default:var n="",r=ga(a);r.array&&r.object&&(n=r.null?"(":"(!"+e+" || ",n+="typeof "+e+' !== "object")',delete r.null,delete r.array,delete r.object),r.number&&delete r.integer;for(var o in r)n+=(n?" && ":"")+Xr(o,e,t,!0);return n}}var Ha=ga(["string","number","integer","boolean","null"]);function zn(a,e){if(Array.isArray(e)){for(var t=[],n=0;n<e.length;n++){var r=e[n];(Ha[r]||a==="array"&&r==="array")&&(t[t.length]=r)}if(t.length)return t}else{if(Ha[e])return[e];if(a==="array"&&e==="array")return["array"]}}function ga(a){for(var e={},t=0;t<a.length;t++)e[a[t]]=!0;return e}var qn=/^[a-z$_][a-z$_0-9]*$/i,Wn=/'|\\/g;function ya(a){return typeof a=="number"?"["+a+"]":qn.test(a)?"."+a:"['"+Pa(a)+"']"}function Pa(a){return a.replace(Wn,"\\$&").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\f/g,"\\f").replace(/\t/g,"\\t")}function Bn(a,e){e+="[^0-9]";var t=a.match(new RegExp(e,"g"));return t?t.length:0}function Qn(a,e,t){return e+="([^0-9])",t=t.replace(/\$/g,"$$$$"),a.replace(new RegExp(e,"g"),t+"$1")}function Hn(a,e){if(typeof a=="boolean")return!a;for(var t in a)if(e[t])return!0}function Kn(a,e,t){if(typeof a=="boolean")return!a&&t!="not";for(var n in a)if(n!=t&&e[n])return!0}function Gn(a,e){if(typeof a!="boolean"){for(var t in a)if(!e[t])return t}}function ea(a){return"'"+Pa(a)+"'"}function Jn(a,e,t,n){var r=t?"'/' + "+e+(n?"":".replace(/~/g, '~0').replace(/\\//g, '~1')"):n?"'[' + "+e+" + ']'":"'[\\'' + "+e+" + '\\']'";return mt(a,r)}function Zn(a,e,t){var n=ea(t?"/"+ba(e):ya(e));return mt(a,n)}var Yn=/^\/(?:[^~]|~0|~1)*$/,Xn=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function eo(a,e,t){var n,r,o,i;if(a==="")return"rootData";if(a[0]=="/"){if(!Yn.test(a))throw new Error("Invalid JSON-pointer: "+a);r=a,o="rootData"}else{if(i=a.match(Xn),!i)throw new Error("Invalid JSON-pointer: "+a);if(n=+i[1],r=i[2],r=="#"){if(n>=e)throw new Error("Cannot access property/index "+n+" levels up, current level is "+e);return t[e-n]}if(n>e)throw new Error("Cannot access data "+n+" levels up, current level is "+e);if(o="data"+(e-n||""),!r)return o}for(var s=o,c=r.split("/"),d=0;d<c.length;d++){var l=c[d];l&&(o+=ya(Ea(l)),s+=" && "+o)}return s}function mt(a,e){return a=='""'?e:(a+" + "+e).replace(/([^\\])' \+ '/g,"$1")}function ro(a){return Ea(decodeURIComponent(a))}function ao(a){return encodeURIComponent(ba(a))}function ba(a){return a.replace(/~/g,"~0").replace(/\//g,"~1")}function Ea(a){return a.replace(/~1/g,"/").replace(/~0/g,"~")}var cr=to;function to(a){Fe.copy(a,this)}var no=pa(function(a){var e=a.exports=function(r,o,i){typeof o=="function"&&(i=o,o={}),i=o.cb||i;var s=typeof i=="function"?i:i.pre||function(){},c=i.post||function(){};t(o,s,c,r,"",r)};e.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0},e.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0},e.propsKeywords={definitions:!0,properties:!0,patternProperties:!0,dependencies:!0},e.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0};function t(r,o,i,s,c,d,l,f,p,u){if(s&&typeof s=="object"&&!Array.isArray(s)){o(s,c,d,l,f,p,u);for(var v in s){var g=s[v];if(Array.isArray(g)){if(v in e.arrayKeywords)for(var h=0;h<g.length;h++)t(r,o,i,g[h],c+"/"+v+"/"+h,d,c,v,s,h)}else if(v in e.propsKeywords){if(g&&typeof g=="object")for(var b in g)t(r,o,i,g[b],c+"/"+v+"/"+n(b),d,c,v,s,b)}else(v in e.keywords||r.allKeys&&!(v in e.skipKeywords))&&t(r,o,i,g,c+"/"+v,d,c,v,s)}i(s,c,d,l,f,p,u)}}function n(r){return r.replace(/~/g,"~0").replace(/\//g,"~1")}}),Se=Ye;Ye.normalizeId=Ge;Ye.fullPath=Tr;Ye.url=kr;Ye.ids=uo;Ye.inlineRef=ra;Ye.schema=zr;function Ye(a,e,t){var n=this._refs[t];if(typeof n=="string")if(this._refs[n])n=this._refs[n];else return Ye.call(this,a,e,n);if(n=n||this._schemas[t],n instanceof cr)return ra(n.schema,this._opts.inlineRefs)?n.schema:n.validate||this._compile(n);var r=zr.call(this,e,t),o,i,s;return r&&(o=r.schema,e=r.root,s=r.baseId),o instanceof cr?i=o.validate||a.call(this,o.schema,e,void 0,s):o!==void 0&&(i=ra(o,this._opts.inlineRefs)?o:a.call(this,o,e,void 0,s)),i}function zr(a,e){var t=mr.parse(e),n=vt(t),r=Tr(this._getId(a.schema));if(Object.keys(a.schema).length===0||n!==r){var o=Ge(n),i=this._refs[o];if(typeof i=="string")return oo.call(this,a,i,t);if(i instanceof cr)i.validate||this._compile(i),a=i;else if(i=this._schemas[o],i instanceof cr){if(i.validate||this._compile(i),o==Ge(e))return{schema:i,root:a,baseId:r};a=i}else return;if(!a.schema)return;r=Tr(this._getId(a.schema))}return ht.call(this,t,r,a.schema,a)}function oo(a,e,t){var n=zr.call(this,a,e);if(n){var r=n.schema,o=n.baseId;a=n.root;var i=this._getId(r);return i&&(o=kr(o,i)),ht.call(this,t,o,r,a)}}var io=Fe.toHash(["properties","patternProperties","enum","dependencies","definitions"]);function ht(a,e,t,n){if(a.fragment=a.fragment||"",a.fragment.slice(0,1)=="/"){for(var r=a.fragment.split("/"),o=1;o<r.length;o++){var i=r[o];if(i){if(i=Fe.unescapeFragment(i),t=t[i],t===void 0)break;var s;if(!io[i]&&(s=this._getId(t),s&&(e=kr(e,s)),t.$ref)){var c=kr(e,t.$ref),d=zr.call(this,n,c);d&&(t=d.schema,n=d.root,e=d.baseId)}}}if(t!==void 0&&t!==n.schema)return{schema:t,root:n,baseId:e}}}var so=Fe.toHash(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum"]);function ra(a,e){if(e===!1)return!1;if(e===void 0||e===!0)return aa(a);if(e)return ta(a)<=e}function aa(a){var e;if(Array.isArray(a)){for(var t=0;t<a.length;t++)if(e=a[t],typeof e=="object"&&!aa(e))return!1}else for(var n in a)if(n=="$ref"||(e=a[n],typeof e=="object"&&!aa(e)))return!1;return!0}function ta(a){var e=0,t;if(Array.isArray(a)){for(var n=0;n<a.length;n++)if(t=a[n],typeof t=="object"&&(e+=ta(t)),e==1/0)return 1/0}else for(var r in a){if(r=="$ref")return 1/0;if(so[r])e++;else if(t=a[r],typeof t=="object"&&(e+=ta(t)+1),e==1/0)return 1/0}return e}function Tr(a,e){e!==!1&&(a=Ge(a));var t=mr.parse(a);return vt(t)}function vt(a){return mr.serialize(a).split("#")[0]+"#"}var lo=/#\/?$/;function Ge(a){return a?a.replace(lo,""):""}function kr(a,e){return e=Ge(e),mr.resolve(a,e)}function uo(a){var e=Ge(this._getId(a)),t={"":e},n={"":Tr(e,!1)},r={},o=this;return no(a,{allKeys:!0},function(i,s,c,d,l,f,p){if(s!==""){var u=o._getId(i),v=t[d],g=n[d]+"/"+l;if(p!==void 0&&(g+="/"+(typeof p=="number"?p:Fe.escapeFragment(p))),typeof u=="string"){u=v=Ge(v?mr.resolve(v,u):u);var h=o._refs[u];if(typeof h=="string"&&(h=o._refs[h]),h&&h.schema){if(!Lr(i,h.schema))throw new Error('id "'+u+'" resolves to more than one schema')}else if(u!=Ge(g))if(u[0]=="#"){if(r[u]&&!Lr(i,r[u]))throw new Error('id "'+u+'" resolves to more than one schema');r[u]=i}else o._refs[u]=g}t[s]=v,n[s]=g}}),r}var Er={Validation:Ka(fo),MissingRef:Ka(Sa)};function fo(a){this.message="validation failed",this.errors=a,this.ajv=this.validation=!0}Sa.message=function(a,e){return"can't resolve reference "+e+" from id "+a};function Sa(a,e,t){this.message=t||Sa.message(a,e),this.missingRef=Se.url(a,e),this.missingSchema=Se.normalizeId(Se.fullPath(this.missingRef))}function Ka(a){return a.prototype=Object.create(Error.prototype),a.prototype.constructor=a,a}var pt=function(a,e){e||(e={}),typeof e=="function"&&(e={cmp:e});var t=typeof e.cycles=="boolean"?e.cycles:!1,n=e.cmp&&function(o){return function(i){return function(s,c){var d={key:s,value:i[s]},l={key:c,value:i[c]};return o(d,l)}}}(e.cmp),r=[];return function o(i){if(i&&i.toJSON&&typeof i.toJSON=="function"&&(i=i.toJSON()),i!==void 0){if(typeof i=="number")return isFinite(i)?""+i:"null";if(typeof i!="object")return JSON.stringify(i);var s,c;if(Array.isArray(i)){for(c="[",s=0;s<i.length;s++)s&&(c+=","),c+=o(i[s])||"null";return c+"]"}if(i===null)return"null";if(r.indexOf(i)!==-1){if(t)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var d=r.push(i)-1,l=Object.keys(i).sort(n&&n(i));for(c="",s=0;s<l.length;s++){var f=l[s],p=o(i[f]);p&&(c&&(c+=","),c+=JSON.stringify(f)+":"+p)}return r.splice(d,1),"{"+c+"}"}}(a)},na=function(e,t,n){var r="",o=e.schema.$async===!0,i=e.util.schemaHasRulesExcept(e.schema,e.RULES.all,"$ref"),s=e.self._getId(e.schema);if(e.opts.strictKeywords){var c=e.util.schemaUnknownRules(e.schema,e.RULES.keywords);if(c){var d="unknown keyword: "+c;if(e.opts.strictKeywords==="log")e.logger.warn(d);else throw new Error(d)}}if(e.isTop&&(r+=" var validate = ",o&&(e.async=!0,r+="async "),r+="function(data, dataPath, parentData, parentDataProperty, rootData) { 'use strict'; ",s&&(e.opts.sourceCode||e.opts.processCode)&&(r+=" "+("/*# sourceURL="+s+" */")+" ")),typeof e.schema=="boolean"||!(i||e.schema.$ref)){var t="false schema",l=e.level,f=e.dataLevel,p=e.schema[t],u=e.schemaPath+e.util.getProperty(t),v=e.errSchemaPath+"/"+t,D=!e.opts.allErrors,I,g="data"+(f||""),S="valid"+l;if(e.schema===!1){e.isTop?D=!0:r+=" var "+S+" = false; ";var h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(I||"false schema")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(v)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: 'boolean schema is false' "),e.opts.verbose&&(r+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+g+" "),r+=" } "):r+=" {} ";var b=r;r=h.pop(),!e.compositeRule&&D?e.async?r+=" throw new ValidationError(["+b+"]); ":r+=" validate.errors = ["+b+"]; return false; ":r+=" var err = "+b+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}else e.isTop?o?r+=" return data; ":r+=" validate.errors = null; return true; ":r+=" var "+S+" = true; ";return e.isTop&&(r+=" }; return validate; "),r}if(e.isTop){var F=e.isTop,l=e.level=0,f=e.dataLevel=0,g="data";if(e.rootId=e.resolve.fullPath(e.self._getId(e.root.schema)),e.baseId=e.baseId||e.rootId,delete e.isTop,e.dataPathArr=[""],e.schema.default!==void 0&&e.opts.useDefaults&&e.opts.strictDefaults){var E="default is ignored in the schema root";if(e.opts.strictDefaults==="log")e.logger.warn(E);else throw new Error(E)}r+=" var vErrors = null; ",r+=" var errors = 0;     ",r+=" if (rootData === undefined) rootData = data; "}else{var l=e.level,f=e.dataLevel,g="data"+(f||"");if(s&&(e.baseId=e.resolve.url(e.baseId,s)),o&&!e.async)throw new Error("async schema in sync schema");r+=" var errs_"+l+" = errors;"}var S="valid"+l,D=!e.opts.allErrors,x="",C="",I,R=e.schema.type,T=Array.isArray(R);if(R&&e.opts.nullable&&e.schema.nullable===!0&&(T?R.indexOf("null")==-1&&(R=R.concat("null")):R!="null"&&(R=[R,"null"],T=!0)),T&&R.length==1&&(R=R[0],T=!1),e.schema.$ref&&i){if(e.opts.extendRefs=="fail")throw new Error('$ref: validation keywords used in schema at path "'+e.errSchemaPath+'" (see option extendRefs)');e.opts.extendRefs!==!0&&(i=!1,e.logger.warn('$ref: keywords ignored in schema at path "'+e.errSchemaPath+'"'))}if(e.schema.$comment&&e.opts.$comment&&(r+=" "+e.RULES.all.$comment.code(e,"$comment")),R){if(e.opts.coerceTypes)var j=e.util.coerceToTypes(e.opts.coerceTypes,R);var L=e.RULES.types[R];if(j||T||L===!0||L&&!he(L)){var u=e.schemaPath+".type",v=e.errSchemaPath+"/type",u=e.schemaPath+".type",v=e.errSchemaPath+"/type",$=T?"checkDataTypes":"checkDataType";if(r+=" if ("+e.util[$](R,g,e.opts.strictNumbers,!0)+") { ",j){var A="dataType"+l,V="coerced"+l;r+=" var "+A+" = typeof "+g+"; var "+V+" = undefined; ",e.opts.coerceTypes=="array"&&(r+=" if ("+A+" == 'object' && Array.isArray("+g+") && "+g+".length == 1) { "+g+" = "+g+"[0]; "+A+" = typeof "+g+"; if ("+e.util.checkDataType(e.schema.type,g,e.opts.strictNumbers)+") "+V+" = "+g+"; } "),r+=" if ("+V+" !== undefined) ; ";var H=j;if(H)for(var q,K=-1,W=H.length-1;K<W;)q=H[K+=1],q=="string"?r+=" else if ("+A+" == 'number' || "+A+" == 'boolean') "+V+" = '' + "+g+"; else if ("+g+" === null) "+V+" = ''; ":q=="number"||q=="integer"?(r+=" else if ("+A+" == 'boolean' || "+g+" === null || ("+A+" == 'string' && "+g+" && "+g+" == +"+g+" ",q=="integer"&&(r+=" && !("+g+" % 1)"),r+=")) "+V+" = +"+g+"; "):q=="boolean"?r+=" else if ("+g+" === 'false' || "+g+" === 0 || "+g+" === null) "+V+" = false; else if ("+g+" === 'true' || "+g+" === 1) "+V+" = true; ":q=="null"?r+=" else if ("+g+" === '' || "+g+" === 0 || "+g+" === false) "+V+" = null; ":e.opts.coerceTypes=="array"&&q=="array"&&(r+=" else if ("+A+" == 'string' || "+A+" == 'number' || "+A+" == 'boolean' || "+g+" == null) "+V+" = ["+g+"]; ");r+=" else {   ";var h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(I||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(v)+" , params: { type: '",T?r+=""+R.join(","):r+=""+R,r+="' } ",e.opts.messages!==!1&&(r+=" , message: 'should be ",T?r+=""+R.join(","):r+=""+R,r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+u+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+g+" "),r+=" } "):r+=" {} ";var b=r;r=h.pop(),!e.compositeRule&&D?e.async?r+=" throw new ValidationError(["+b+"]); ":r+=" validate.errors = ["+b+"]; return false; ":r+=" var err = "+b+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } if ("+V+" !== undefined) {  ";var U=f?"data"+(f-1||""):"parentData",ue=f?e.dataPathArr[f]:"parentDataProperty";r+=" "+g+" = "+V+"; ",f||(r+="if ("+U+" !== undefined)"),r+=" "+U+"["+ue+"] = "+V+"; } "}else{var h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(I||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(v)+" , params: { type: '",T?r+=""+R.join(","):r+=""+R,r+="' } ",e.opts.messages!==!1&&(r+=" , message: 'should be ",T?r+=""+R.join(","):r+=""+R,r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+u+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+g+" "),r+=" } "):r+=" {} ";var b=r;r=h.pop(),!e.compositeRule&&D?e.async?r+=" throw new ValidationError(["+b+"]); ":r+=" validate.errors = ["+b+"]; return false; ":r+=" var err = "+b+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}r+=" } "}}if(e.schema.$ref&&!i)r+=" "+e.RULES.all.$ref.code(e,"$ref")+" ",D&&(r+=" } if (errors === ",F?r+="0":r+="errs_"+l,r+=") { ",C+="}");else{var ve=e.RULES;if(ve){for(var L,ge=-1,Z=ve.length-1;ge<Z;)if(L=ve[ge+=1],he(L)){if(L.type&&(r+=" if ("+e.util.checkDataType(L.type,g,e.opts.strictNumbers)+") { "),e.opts.useDefaults){if(L.type=="object"&&e.schema.properties){var p=e.schema.properties,Te=Object.keys(p),be=Te;if(be)for(var ie,Pe=-1,Oe=be.length-1;Pe<Oe;){ie=be[Pe+=1];var fe=p[ie];if(fe.default!==void 0){var te=g+e.util.getProperty(ie);if(e.compositeRule){if(e.opts.strictDefaults){var E="default is ignored for: "+te;if(e.opts.strictDefaults==="log")e.logger.warn(E);else throw new Error(E)}}else r+=" if ("+te+" === undefined ",e.opts.useDefaults=="empty"&&(r+=" || "+te+" === null || "+te+" === '' "),r+=" ) "+te+" = ",e.opts.useDefaults=="shared"?r+=" "+e.useDefault(fe.default)+" ":r+=" "+JSON.stringify(fe.default)+" ",r+="; "}}}else if(L.type=="array"&&Array.isArray(e.schema.items)){var ke=e.schema.items;if(ke){for(var fe,K=-1,pe=ke.length-1;K<pe;)if(fe=ke[K+=1],fe.default!==void 0){var te=g+"["+K+"]";if(e.compositeRule){if(e.opts.strictDefaults){var E="default is ignored for: "+te;if(e.opts.strictDefaults==="log")e.logger.warn(E);else throw new Error(E)}}else r+=" if ("+te+" === undefined ",e.opts.useDefaults=="empty"&&(r+=" || "+te+" === null || "+te+" === '' "),r+=" ) "+te+" = ",e.opts.useDefaults=="shared"?r+=" "+e.useDefault(fe.default)+" ":r+=" "+JSON.stringify(fe.default)+" ",r+="; "}}}}var ne=L.rules;if(ne){for(var ze,ye=-1,ir=ne.length-1;ye<ir;)if(ze=ne[ye+=1],qe(ze)){var Xe=ze.code(e,ze.keyword,L.type);Xe&&(r+=" "+Xe+" ",D&&(x+="}"))}}if(D&&(r+=" "+x+" ",x=""),L.type&&(r+=" } ",R&&R===L.type&&!j)){r+=" else { ";var u=e.schemaPath+".type",v=e.errSchemaPath+"/type",h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(I||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(v)+" , params: { type: '",T?r+=""+R.join(","):r+=""+R,r+="' } ",e.opts.messages!==!1&&(r+=" , message: 'should be ",T?r+=""+R.join(","):r+=""+R,r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+u+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+g+" "),r+=" } "):r+=" {} ";var b=r;r=h.pop(),!e.compositeRule&&D?e.async?r+=" throw new ValidationError(["+b+"]); ":r+=" validate.errors = ["+b+"]; return false; ":r+=" var err = "+b+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } "}D&&(r+=" if (errors === ",F?r+="0":r+="errs_"+l,r+=") { ",C+="}")}}}D&&(r+=" "+C+" "),F?(o?(r+=" if (errors === 0) return data;           ",r+=" else throw new ValidationError(vErrors); "):(r+=" validate.errors = vErrors; ",r+=" return errors === 0;       "),r+=" }; return validate;"):r+=" var "+S+" = errors === errs_"+l+";";function he(de){for(var me=de.rules,Ee=0;Ee<me.length;Ee++)if(qe(me[Ee]))return!0}function qe(de){return e.schema[de.keyword]!==void 0||de.implements&&hr(de)}function hr(de){for(var me=de.implements,Ee=0;Ee<me.length;Ee++)if(e.schema[me[Ee]]!==void 0)return!0}return r},co=Fe.ucs2length,mo=Er.Validation,gt=oa;function oa(a,e,t,n){var r=this,o=this._opts,i=[void 0],s={},c=[],d={},l=[],f={},p=[];e=e||{schema:a,refVal:i,refs:s};var u=ho.call(this,a,e,n),v=this._compilations[u.index];if(u.compiling)return v.callValidate=E;var g=this._formats,h=this.RULES;try{var b=S(a,e,t,n);v.validate=b;var F=v.callValidate;return F&&(F.schema=b.schema,F.errors=null,F.refs=b.refs,F.refVal=b.refVal,F.root=b.root,F.$async=b.$async,o.sourceCode&&(F.source=b.source)),b}finally{vo.call(this,a,e,n)}function E(){var $=v.validate,A=$.apply(this,arguments);return E.errors=$.errors,A}function S($,A,V,H){var q=!A||A&&A.schema==$;if(A.schema!=e.schema)return oa.call(r,$,A,V,H);var K=$.$async===!0,W=na({isTop:!0,schema:$,isRoot:q,baseId:H,root:A,schemaPath:"",errSchemaPath:"#",errorPath:'""',MissingRefError:Er.MissingRef,RULES:h,validate:na,util:Fe,resolve:Se,resolveRef:D,usePattern:T,useDefault:j,useCustomRule:L,opts:o,formats:g,logger:r.logger,self:r});W=Ar(i,yo)+Ar(c,po)+Ar(l,go)+Ar(p,Po)+W,o.processCode&&(W=o.processCode(W,$));var U;try{var ue=new Function("self","RULES","formats","root","refVal","defaults","customRules","equal","ucs2length","ValidationError",W);U=ue(r,h,g,e,i,l,p,Lr,co,mo),i[0]=U}catch(ve){throw r.logger.error("Error compiling schema, function code:",W),ve}return U.schema=$,U.errors=null,U.refs=s,U.refVal=i,U.root=q?U:A,K&&(U.$async=!0),o.sourceCode===!0&&(U.source={code:W,patterns:c,defaults:l}),U}function D($,A,V){A=Se.url($,A);var H=s[A],q,K;if(H!==void 0)return q=i[H],K="refVal["+H+"]",R(q,K);if(!V&&e.refs){var W=e.refs[A];if(W!==void 0)return q=e.refVal[W],K=x(A,q),R(q,K)}K=x(A);var U=Se.call(r,S,e,A);if(U===void 0){var ue=t&&t[A];ue&&(U=Se.inlineRef(ue,o.inlineRefs)?ue:oa.call(r,ue,e,t,$))}if(U===void 0)C(A);else return I(A,U),R(U,K)}function x($,A){var V=i.length;return i[V]=A,s[$]=V,"refVal"+V}function C($){delete s[$]}function I($,A){var V=s[$];i[V]=A}function R($,A){return typeof $=="object"||typeof $=="boolean"?{code:A,schema:$,inline:!0}:{code:A,$async:$&&!!$.$async}}function T($){var A=d[$];return A===void 0&&(A=d[$]=c.length,c[A]=$),"pattern"+A}function j($){switch(typeof $){case"boolean":case"number":return""+$;case"string":return Fe.toQuotedString($);case"object":if($===null)return"null";var A=pt($),V=f[A];return V===void 0&&(V=f[A]=l.length,l[V]=$),"default"+V}}function L($,A,V,H){if(r._opts.validateSchema!==!1){var q=$.definition.dependencies;if(q&&!q.every(function(be){return Object.prototype.hasOwnProperty.call(V,be)}))throw new Error("parent schema must have all required keywords: "+q.join(","));var K=$.definition.validateSchema;if(K){var W=K(A);if(!W){var U="keyword schema is invalid: "+r.errorsText(K.errors);if(r._opts.validateSchema=="log")r.logger.error(U);else throw new Error(U)}}}var ue=$.definition.compile,ve=$.definition.inline,ge=$.definition.macro,Z;if(ue)Z=ue.call(r,A,V,H);else if(ge)Z=ge.call(r,A,V,H),o.validateSchema!==!1&&r.validateSchema(Z,!0);else if(ve)Z=ve.call(r,H,$.keyword,A,V);else if(Z=$.definition.validate,!Z)return;if(Z===void 0)throw new Error('custom keyword "'+$.keyword+'"failed to compile');var Te=p.length;return p[Te]=Z,{code:"customRule"+Te,validate:Z}}}function ho(a,e,t){var n=yt.call(this,a,e,t);return n>=0?{index:n,compiling:!0}:(n=this._compilations.length,this._compilations[n]={schema:a,root:e,baseId:t},{index:n,compiling:!1})}function vo(a,e,t){var n=yt.call(this,a,e,t);n>=0&&this._compilations.splice(n,1)}function yt(a,e,t){for(var n=0;n<this._compilations.length;n++){var r=this._compilations[n];if(r.schema==a&&r.root==e&&r.baseId==t)return n}return-1}function po(a,e){return"var pattern"+a+" = new RegExp("+Fe.toQuotedString(e[a])+");"}function go(a){return"var default"+a+" = defaults["+a+"];"}function yo(a,e){return e[a]===void 0?"":"var refVal"+a+" = refVal["+a+"];"}function Po(a){return"var customRule"+a+" = customRules["+a+"];"}function Ar(a,e){if(!a.length)return"";for(var t="",n=0;n<a.length;n++)t+=e(n,a);return t}var bo=pa(function(a){var e=a.exports=function(){this._cache={}};e.prototype.put=function(n,r){this._cache[n]=r},e.prototype.get=function(n){return this._cache[n]},e.prototype.del=function(n){delete this._cache[n]},e.prototype.clear=function(){this._cache={}}}),Eo=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,So=[0,31,28,31,30,31,30,31,31,30,31,30,31],wo=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i,Pt=/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i,Fo=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,Oo=/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,bt=/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i,Et=/^(?:(?:http[s\u017F]?|ftp):\/\/)(?:(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+(?::(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?@)?(?:(?!10(?:\.[0-9]{1,3}){3})(?!127(?:\.[0-9]{1,3}){3})(?!169\.254(?:\.[0-9]{1,3}){2})(?!192\.168(?:\.[0-9]{1,3}){2})(?!172\.(?:1[6-9]|2[0-9]|3[01])(?:\.[0-9]{1,3}){2})(?:[1-9][0-9]?|1[0-9][0-9]|2[01][0-9]|22[0-3])(?:\.(?:1?[0-9]{1,2}|2[0-4][0-9]|25[0-5])){2}(?:\.(?:[1-9][0-9]?|1[0-9][0-9]|2[0-4][0-9]|25[0-4]))|(?:(?:(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+-)*(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)(?:\.(?:(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+-)*(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)*(?:\.(?:(?:[a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]){2,})))(?::[0-9]{2,5})?(?:\/(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?$/i,St=/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,wt=/^(?:\/(?:[^~/]|~0|~1)*)*$/,Ft=/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,Ot=/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/,Do=qr;function qr(a){return a=a=="full"?"full":"fast",Fe.copy(qr[a])}qr.fast={date:/^\d\d\d\d-[0-1]\d-[0-3]\d$/,time:/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i,"date-time":/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i,uri:/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/)?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+\-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i,"uri-template":bt,url:Et,email:/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i,hostname:Pt,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:xt,uuid:St,"json-pointer":wt,"json-pointer-uri-fragment":Ft,"relative-json-pointer":Ot};qr.full={date:Dt,time:_t,"date-time":Ao,uri:Io,"uri-reference":Oo,"uri-template":bt,url:Et,email:/^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:Pt,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:xt,uuid:St,"json-pointer":wt,"json-pointer-uri-fragment":Ft,"relative-json-pointer":Ot};function _o(a){return a%4===0&&(a%100!==0||a%400===0)}function Dt(a){var e=a.match(Eo);if(!e)return!1;var t=+e[1],n=+e[2],r=+e[3];return n>=1&&n<=12&&r>=1&&r<=(n==2&&_o(t)?29:So[n])}function _t(a,e){var t=a.match(wo);if(!t)return!1;var n=t[1],r=t[2],o=t[3],i=t[5];return(n<=23&&r<=59&&o<=59||n==23&&r==59&&o==60)&&(!e||i)}var xo=/t|\s/i;function Ao(a){var e=a.split(xo);return e.length==2&&Dt(e[0])&&_t(e[1],!0)}var $o=/\/|:/;function Io(a){return $o.test(a)&&Fo.test(a)}var Co=/[^\\]\\Z/;function xt(a){if(Co.test(a))return!1;try{return new RegExp(a),!0}catch(e){return!1}}var Ro=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.errSchemaPath+"/"+t,d=!e.opts.allErrors,l="data"+(i||""),f="valid"+o,p,u;if(s=="#"||s=="#/")e.isRoot?(p=e.async,u="validate"):(p=e.root.schema.$async===!0,u="root.refVal[0]");else{var v=e.resolveRef(e.baseId,s,e.isRoot);if(v===void 0){var g=e.MissingRefError.message(e.baseId,s);if(e.opts.missingRefs=="fail"){e.logger.error(g);var h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '$ref' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { ref: '"+e.util.escapeQuotes(s)+"' } ",e.opts.messages!==!1&&(r+=" , message: 'can\\'t resolve reference "+e.util.escapeQuotes(s)+"' "),e.opts.verbose&&(r+=" , schema: "+e.util.toQuotedString(s)+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+l+" "),r+=" } "):r+=" {} ";var b=r;r=h.pop(),!e.compositeRule&&d?e.async?r+=" throw new ValidationError(["+b+"]); ":r+=" validate.errors = ["+b+"]; return false; ":r+=" var err = "+b+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",d&&(r+=" if (false) { ")}else if(e.opts.missingRefs=="ignore")e.logger.warn(g),d&&(r+=" if (true) { ");else throw new e.MissingRefError(e.baseId,s,g)}else if(v.inline){var F=e.util.copy(e);F.level++;var E="valid"+F.level;F.schema=v.schema,F.schemaPath="",F.errSchemaPath=s;var S=e.validate(F).replace(/validate\.schema/g,v.code);r+=" "+S+" ",d&&(r+=" if ("+E+") { ")}else p=v.$async===!0||e.async&&v.$async!==!1,u=v.code}if(u){var h=h||[];h.push(r),r="",e.opts.passContext?r+=" "+u+".call(this, ":r+=" "+u+"( ",r+=" "+l+", (dataPath || '')",e.errorPath!='""'&&(r+=" + "+e.errorPath);var D=i?"data"+(i-1||""):"parentData",x=i?e.dataPathArr[i]:"parentDataProperty";r+=" , "+D+" , "+x+", rootData)  ";var C=r;if(r=h.pop(),p){if(!e.async)throw new Error("async schema referenced by sync schema");d&&(r+=" var "+f+"; "),r+=" try { await "+C+"; ",d&&(r+=" "+f+" = true; "),r+=" } catch (e) { if (!(e instanceof ValidationError)) throw e; if (vErrors === null) vErrors = e.errors; else vErrors = vErrors.concat(e.errors); errors = vErrors.length; ",d&&(r+=" "+f+" = false; "),r+=" } ",d&&(r+=" if ("+f+") { ")}else r+=" if (!"+C+") { if (vErrors === null) vErrors = "+u+".errors; else vErrors = vErrors.concat("+u+".errors); errors = vErrors.length; } ",d&&(r+=" else { ")}return r},No=function(e,t,n){var r=" ",o=e.schema[t],i=e.schemaPath+e.util.getProperty(t),s=e.errSchemaPath+"/"+t,c=!e.opts.allErrors,d=e.util.copy(e),l="";d.level++;var f="valid"+d.level,p=d.baseId,u=!0,v=o;if(v)for(var g,h=-1,b=v.length-1;h<b;)g=v[h+=1],(e.opts.strictKeywords?typeof g=="object"&&Object.keys(g).length>0||g===!1:e.util.schemaHasRules(g,e.RULES.all))&&(u=!1,d.schema=g,d.schemaPath=i+"["+h+"]",d.errSchemaPath=s+"/"+h,r+="  "+e.validate(d)+" ",d.baseId=p,c&&(r+=" if ("+f+") { ",l+="}"));return c&&(u?r+=" if (true) { ":r+=" "+l.slice(0,-1)+" "),r},jo=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="valid"+o,u="errs__"+o,v=e.util.copy(e),g="";v.level++;var h="valid"+v.level,b=s.every(function(I){return e.opts.strictKeywords?typeof I=="object"&&Object.keys(I).length>0||I===!1:e.util.schemaHasRules(I,e.RULES.all)});if(b){var F=v.baseId;r+=" var "+u+" = errors; var "+p+" = false;  ";var E=e.compositeRule;e.compositeRule=v.compositeRule=!0;var S=s;if(S)for(var D,x=-1,C=S.length-1;x<C;)D=S[x+=1],v.schema=D,v.schemaPath=c+"["+x+"]",v.errSchemaPath=d+"/"+x,r+="  "+e.validate(v)+" ",v.baseId=F,r+=" "+p+" = "+p+" || "+h+"; if (!"+p+") { ",g+="}";e.compositeRule=v.compositeRule=E,r+=" "+g+" if (!"+p+") {   var err =   ",e.createErrors!==!1?(r+=" { keyword: 'anyOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: 'should match some schema in anyOf' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&l&&(e.async?r+=" throw new ValidationError(vErrors); ":r+=" validate.errors = vErrors; return false; "),r+=" } else {  errors = "+u+"; if (vErrors !== null) { if ("+u+") vErrors.length = "+u+"; else vErrors = null; } ",e.opts.allErrors&&(r+=" } ")}else l&&(r+=" if (true) { ");return r},Lo=function(e,t,n){var r=" ",o=e.schema[t],i=e.errSchemaPath+"/"+t;e.opts.allErrors;var s=e.util.toQuotedString(o);return e.opts.$comment===!0?r+=" console.log("+s+");":typeof e.opts.$comment=="function"&&(r+=" self._opts.$comment("+s+", "+e.util.toQuotedString(i)+", validate.root.schema);"),r},To=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="valid"+o,u=e.opts.$data&&s&&s.$data;u&&(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; "),u||(r+=" var schema"+o+" = validate.schema"+c+";"),r+="var "+p+" = equal("+f+", schema"+o+"); if (!"+p+") {   ";var v=v||[];v.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'const' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { allowedValue: schema"+o+" } ",e.opts.messages!==!1&&(r+=" , message: 'should be equal to constant' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var g=r;return r=v.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+g+"]); ":r+=" validate.errors = ["+g+"]; return false; ":r+=" var err = "+g+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" }",l&&(r+=" else { "),r},ko=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="valid"+o,u="errs__"+o,v=e.util.copy(e),g="";v.level++;var h="valid"+v.level,b="i"+o,F=v.dataLevel=e.dataLevel+1,E="data"+F,S=e.baseId,D=e.opts.strictKeywords?typeof s=="object"&&Object.keys(s).length>0||s===!1:e.util.schemaHasRules(s,e.RULES.all);if(r+="var "+u+" = errors;var "+p+";",D){var x=e.compositeRule;e.compositeRule=v.compositeRule=!0,v.schema=s,v.schemaPath=c,v.errSchemaPath=d,r+=" var "+h+" = false; for (var "+b+" = 0; "+b+" < "+f+".length; "+b+"++) { ",v.errorPath=e.util.getPathExpr(e.errorPath,b,e.opts.jsonPointers,!0);var C=f+"["+b+"]";v.dataPathArr[F]=b;var I=e.validate(v);v.baseId=S,e.util.varOccurences(I,E)<2?r+=" "+e.util.varReplace(I,E,C)+" ":r+=" var "+E+" = "+C+"; "+I+" ",r+=" if ("+h+") break; }  ",e.compositeRule=v.compositeRule=x,r+=" "+g+" if (!"+h+") {"}else r+=" if ("+f+".length == 0) {";var R=R||[];R.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'contains' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: 'should contain a valid item' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var T=r;return r=R.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+T+"]); ":r+=" validate.errors = ["+T+"]; return false; ":r+=" var err = "+T+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } else { ",D&&(r+="  errors = "+u+"; if (vErrors !== null) { if ("+u+") vErrors.length = "+u+"; else vErrors = null; } "),e.opts.allErrors&&(r+=" } "),r},Vo=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="errs__"+o,u=e.util.copy(e),v="";u.level++;var g="valid"+u.level,h={},b={},F=e.opts.ownProperties;for(x in s)if(x!="__proto__"){var E=s[x],S=Array.isArray(E)?b:h;S[x]=E}r+="var "+p+" = errors;";var D=e.errorPath;r+="var missing"+o+";";for(var x in b)if(S=b[x],S.length){if(r+=" if ( "+f+e.util.getProperty(x)+" !== undefined ",F&&(r+=" && Object.prototype.hasOwnProperty.call("+f+", '"+e.util.escapeQuotes(x)+"') "),l){r+=" && ( ";var C=S;if(C)for(var I,R=-1,T=C.length-1;R<T;){I=C[R+=1],R&&(r+=" || ");var j=e.util.getProperty(I),L=f+j;r+=" ( ( "+L+" === undefined ",F&&(r+=" || ! Object.prototype.hasOwnProperty.call("+f+", '"+e.util.escapeQuotes(I)+"') "),r+=") && (missing"+o+" = "+e.util.toQuotedString(e.opts.jsonPointers?I:j)+") ) "}r+=")) {  ";var $="missing"+o,A="' + "+$+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.opts.jsonPointers?e.util.getPathExpr(D,$,!0):D+" + "+$);var V=V||[];V.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'dependencies' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { property: '"+e.util.escapeQuotes(x)+"', missingProperty: '"+A+"', depsCount: "+S.length+", deps: '"+e.util.escapeQuotes(S.length==1?S[0]:S.join(", "))+"' } ",e.opts.messages!==!1&&(r+=" , message: 'should have ",S.length==1?r+="property "+e.util.escapeQuotes(S[0]):r+="properties "+e.util.escapeQuotes(S.join(", ")),r+=" when property "+e.util.escapeQuotes(x)+" is present' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var H=r;r=V.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+H+"]); ":r+=" validate.errors = ["+H+"]; return false; ":r+=" var err = "+H+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}else{r+=" ) { ";var q=S;if(q)for(var I,K=-1,W=q.length-1;K<W;){I=q[K+=1];var j=e.util.getProperty(I),A=e.util.escapeQuotes(I),L=f+j;e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(D,I,e.opts.jsonPointers)),r+=" if ( "+L+" === undefined ",F&&(r+=" || ! Object.prototype.hasOwnProperty.call("+f+", '"+e.util.escapeQuotes(I)+"') "),r+=") {  var err =   ",e.createErrors!==!1?(r+=" { keyword: 'dependencies' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { property: '"+e.util.escapeQuotes(x)+"', missingProperty: '"+A+"', depsCount: "+S.length+", deps: '"+e.util.escapeQuotes(S.length==1?S[0]:S.join(", "))+"' } ",e.opts.messages!==!1&&(r+=" , message: 'should have ",S.length==1?r+="property "+e.util.escapeQuotes(S[0]):r+="properties "+e.util.escapeQuotes(S.join(", ")),r+=" when property "+e.util.escapeQuotes(x)+" is present' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}r+=" }   ",l&&(v+="}",r+=" else { ")}e.errorPath=D;var U=u.baseId;for(var x in h){var E=h[x];(e.opts.strictKeywords?typeof E=="object"&&Object.keys(E).length>0||E===!1:e.util.schemaHasRules(E,e.RULES.all))&&(r+=" "+g+" = true; if ( "+f+e.util.getProperty(x)+" !== undefined ",F&&(r+=" && Object.prototype.hasOwnProperty.call("+f+", '"+e.util.escapeQuotes(x)+"') "),r+=") { ",u.schema=E,u.schemaPath=c+e.util.getProperty(x),u.errSchemaPath=d+"/"+e.util.escapeFragment(x),r+="  "+e.validate(u)+" ",u.baseId=U,r+=" }  ",l&&(r+=" if ("+g+") { ",v+="}"))}return l&&(r+="   "+v+" if ("+p+" == errors) {"),r},Mo=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="valid"+o,u=e.opts.$data&&s&&s.$data;u&&(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; ");var v="i"+o,g="schema"+o;u||(r+=" var "+g+" = validate.schema"+c+";"),r+="var "+p+";",u&&(r+=" if (schema"+o+" === undefined) "+p+" = true; else if (!Array.isArray(schema"+o+")) "+p+" = false; else {"),r+=""+p+" = false;for (var "+v+"=0; "+v+"<"+g+".length; "+v+"++) if (equal("+f+", "+g+"["+v+"])) { "+p+" = true; break; }",u&&(r+="  }  "),r+=" if (!"+p+") {   ";var h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'enum' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { allowedValues: schema"+o+" } ",e.opts.messages!==!1&&(r+=" , message: 'should be equal to one of the allowed values' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var b=r;return r=h.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+b+"]); ":r+=" validate.errors = ["+b+"]; return false; ":r+=" var err = "+b+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" }",l&&(r+=" else { "),r},Uo=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||"");if(e.opts.format===!1)return l&&(r+=" if (true) { "),r;var p=e.opts.$data&&s&&s.$data,u;p?(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; ",u="schema"+o):u=s;var v=e.opts.unknownFormats,g=Array.isArray(v);if(p){var h="format"+o,b="isObject"+o,F="formatType"+o;r+=" var "+h+" = formats["+u+"]; var "+b+" = typeof "+h+" == 'object' && !("+h+" instanceof RegExp) && "+h+".validate; var "+F+" = "+b+" && "+h+".type || 'string'; if ("+b+") { ",e.async&&(r+=" var async"+o+" = "+h+".async; "),r+=" "+h+" = "+h+".validate; } if (  ",p&&(r+=" ("+u+" !== undefined && typeof "+u+" != 'string') || "),r+=" (",v!="ignore"&&(r+=" ("+u+" && !"+h+" ",g&&(r+=" && self._opts.unknownFormats.indexOf("+u+") == -1 "),r+=") || "),r+=" ("+h+" && "+F+" == '"+n+"' && !(typeof "+h+" == 'function' ? ",e.async?r+=" (async"+o+" ? await "+h+"("+f+") : "+h+"("+f+")) ":r+=" "+h+"("+f+") ",r+=" : "+h+".test("+f+"))))) {"}else{var h=e.formats[s];if(!h){if(v=="ignore")return e.logger.warn('unknown format "'+s+'" ignored in schema at path "'+e.errSchemaPath+'"'),l&&(r+=" if (true) { "),r;if(g&&v.indexOf(s)>=0)return l&&(r+=" if (true) { "),r;throw new Error('unknown format "'+s+'" is used in schema at path "'+e.errSchemaPath+'"')}var b=typeof h=="object"&&!(h instanceof RegExp)&&h.validate,F=b&&h.type||"string";if(b){var E=h.async===!0;h=h.validate}if(F!=n)return l&&(r+=" if (true) { "),r;if(E){if(!e.async)throw new Error("async format in sync schema");var S="formats"+e.util.getProperty(s)+".validate";r+=" if (!(await "+S+"("+f+"))) { "}else{r+=" if (! ";var S="formats"+e.util.getProperty(s);b&&(S+=".validate"),typeof h=="function"?r+=" "+S+"("+f+") ":r+=" "+S+".test("+f+") ",r+=") { "}}var D=D||[];D.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'format' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { format:  ",p?r+=""+u:r+=""+e.util.toQuotedString(s),r+="  } ",e.opts.messages!==!1&&(r+=` , message: 'should match format "`,p?r+="' + "+u+" + '":r+=""+e.util.escapeQuotes(s),r+=`"' `),e.opts.verbose&&(r+=" , schema:  ",p?r+="validate.schema"+c:r+=""+e.util.toQuotedString(s),r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var x=r;return r=D.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+x+"]); ":r+=" validate.errors = ["+x+"]; return false; ":r+=" var err = "+x+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } ",l&&(r+=" else { "),r},zo=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="valid"+o,u="errs__"+o,v=e.util.copy(e);v.level++;var g="valid"+v.level,h=e.schema.then,b=e.schema.else,F=h!==void 0&&(e.opts.strictKeywords?typeof h=="object"&&Object.keys(h).length>0||h===!1:e.util.schemaHasRules(h,e.RULES.all)),E=b!==void 0&&(e.opts.strictKeywords?typeof b=="object"&&Object.keys(b).length>0||b===!1:e.util.schemaHasRules(b,e.RULES.all)),S=v.baseId;if(F||E){var D;v.createErrors=!1,v.schema=s,v.schemaPath=c,v.errSchemaPath=d,r+=" var "+u+" = errors; var "+p+" = true;  ";var x=e.compositeRule;e.compositeRule=v.compositeRule=!0,r+="  "+e.validate(v)+" ",v.baseId=S,v.createErrors=!0,r+="  errors = "+u+"; if (vErrors !== null) { if ("+u+") vErrors.length = "+u+"; else vErrors = null; }  ",e.compositeRule=v.compositeRule=x,F?(r+=" if ("+g+") {  ",v.schema=e.schema.then,v.schemaPath=e.schemaPath+".then",v.errSchemaPath=e.errSchemaPath+"/then",r+="  "+e.validate(v)+" ",v.baseId=S,r+=" "+p+" = "+g+"; ",F&&E?(D="ifClause"+o,r+=" var "+D+" = 'then'; "):D="'then'",r+=" } ",E&&(r+=" else { ")):r+=" if (!"+g+") { ",E&&(v.schema=e.schema.else,v.schemaPath=e.schemaPath+".else",v.errSchemaPath=e.errSchemaPath+"/else",r+="  "+e.validate(v)+" ",v.baseId=S,r+=" "+p+" = "+g+"; ",F&&E?(D="ifClause"+o,r+=" var "+D+" = 'else'; "):D="'else'",r+=" } "),r+=" if (!"+p+") {   var err =   ",e.createErrors!==!1?(r+=" { keyword: 'if' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { failingKeyword: "+D+" } ",e.opts.messages!==!1&&(r+=` , message: 'should match "' + `+D+` + '" schema' `),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&l&&(e.async?r+=" throw new ValidationError(vErrors); ":r+=" validate.errors = vErrors; return false; "),r+=" }   ",l&&(r+=" else { ")}else l&&(r+=" if (true) { ");return r},qo=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="valid"+o,u="errs__"+o,v=e.util.copy(e),g="";v.level++;var h="valid"+v.level,b="i"+o,F=v.dataLevel=e.dataLevel+1,E="data"+F,S=e.baseId;if(r+="var "+u+" = errors;var "+p+";",Array.isArray(s)){var D=e.schema.additionalItems;if(D===!1){r+=" "+p+" = "+f+".length <= "+s.length+"; ";var x=d;d=e.errSchemaPath+"/additionalItems",r+="  if (!"+p+") {   ";var C=C||[];C.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'additionalItems' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { limit: "+s.length+" } ",e.opts.messages!==!1&&(r+=" , message: 'should NOT have more than "+s.length+" items' "),e.opts.verbose&&(r+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var I=r;r=C.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+I+"]); ":r+=" validate.errors = ["+I+"]; return false; ":r+=" var err = "+I+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } ",d=x,l&&(g+="}",r+=" else { ")}var R=s;if(R){for(var T,j=-1,L=R.length-1;j<L;)if(T=R[j+=1],e.opts.strictKeywords?typeof T=="object"&&Object.keys(T).length>0||T===!1:e.util.schemaHasRules(T,e.RULES.all)){r+=" "+h+" = true; if ("+f+".length > "+j+") { ";var $=f+"["+j+"]";v.schema=T,v.schemaPath=c+"["+j+"]",v.errSchemaPath=d+"/"+j,v.errorPath=e.util.getPathExpr(e.errorPath,j,e.opts.jsonPointers,!0),v.dataPathArr[F]=j;var A=e.validate(v);v.baseId=S,e.util.varOccurences(A,E)<2?r+=" "+e.util.varReplace(A,E,$)+" ":r+=" var "+E+" = "+$+"; "+A+" ",r+=" }  ",l&&(r+=" if ("+h+") { ",g+="}")}}if(typeof D=="object"&&(e.opts.strictKeywords?typeof D=="object"&&Object.keys(D).length>0||D===!1:e.util.schemaHasRules(D,e.RULES.all))){v.schema=D,v.schemaPath=e.schemaPath+".additionalItems",v.errSchemaPath=e.errSchemaPath+"/additionalItems",r+=" "+h+" = true; if ("+f+".length > "+s.length+") {  for (var "+b+" = "+s.length+"; "+b+" < "+f+".length; "+b+"++) { ",v.errorPath=e.util.getPathExpr(e.errorPath,b,e.opts.jsonPointers,!0);var $=f+"["+b+"]";v.dataPathArr[F]=b;var A=e.validate(v);v.baseId=S,e.util.varOccurences(A,E)<2?r+=" "+e.util.varReplace(A,E,$)+" ":r+=" var "+E+" = "+$+"; "+A+" ",l&&(r+=" if (!"+h+") break; "),r+=" } }  ",l&&(r+=" if ("+h+") { ",g+="}")}}else if(e.opts.strictKeywords?typeof s=="object"&&Object.keys(s).length>0||s===!1:e.util.schemaHasRules(s,e.RULES.all)){v.schema=s,v.schemaPath=c,v.errSchemaPath=d,r+="  for (var "+b+" = 0; "+b+" < "+f+".length; "+b+"++) { ",v.errorPath=e.util.getPathExpr(e.errorPath,b,e.opts.jsonPointers,!0);var $=f+"["+b+"]";v.dataPathArr[F]=b;var A=e.validate(v);v.baseId=S,e.util.varOccurences(A,E)<2?r+=" "+e.util.varReplace(A,E,$)+" ":r+=" var "+E+" = "+$+"; "+A+" ",l&&(r+=" if (!"+h+") break; "),r+=" }"}return l&&(r+=" "+g+" if ("+u+" == errors) {"),r},Ga=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,S,f="data"+(i||""),p=e.opts.$data&&s&&s.$data,u;p?(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; ",u="schema"+o):u=s;var v=t=="maximum",g=v?"exclusiveMaximum":"exclusiveMinimum",h=e.schema[g],b=e.opts.$data&&h&&h.$data,F=v?"<":">",E=v?">":"<",S=void 0;if(!(p||typeof s=="number"||s===void 0))throw new Error(t+" must be number");if(!(b||h===void 0||typeof h=="number"||typeof h=="boolean"))throw new Error(g+" must be number or boolean");if(b){var D=e.util.getData(h.$data,i,e.dataPathArr),x="exclusive"+o,C="exclType"+o,I="exclIsNumber"+o,R="op"+o,T="' + "+R+" + '";r+=" var schemaExcl"+o+" = "+D+"; ",D="schemaExcl"+o,r+=" var "+x+"; var "+C+" = typeof "+D+"; if ("+C+" != 'boolean' && "+C+" != 'undefined' && "+C+" != 'number') { ";var S=g,j=j||[];j.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(S||"_exclusiveLimit")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: '"+g+" should be boolean' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var L=r;r=j.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+L+"]); ":r+=" validate.errors = ["+L+"]; return false; ":r+=" var err = "+L+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } else if ( ",p&&(r+=" ("+u+" !== undefined && typeof "+u+" != 'number') || "),r+=" "+C+" == 'number' ? ( ("+x+" = "+u+" === undefined || "+D+" "+F+"= "+u+") ? "+f+" "+E+"= "+D+" : "+f+" "+E+" "+u+" ) : ( ("+x+" = "+D+" === true) ? "+f+" "+E+"= "+u+" : "+f+" "+E+" "+u+" ) || "+f+" !== "+f+") { var op"+o+" = "+x+" ? '"+F+"' : '"+F+"='; ",s===void 0&&(S=g,d=e.errSchemaPath+"/"+g,u=D,p=b)}else{var I=typeof h=="number",T=F;if(I&&p){var R="'"+T+"'";r+=" if ( ",p&&(r+=" ("+u+" !== undefined && typeof "+u+" != 'number') || "),r+=" ( "+u+" === undefined || "+h+" "+F+"= "+u+" ? "+f+" "+E+"= "+h+" : "+f+" "+E+" "+u+" ) || "+f+" !== "+f+") { "}else{I&&s===void 0?(x=!0,S=g,d=e.errSchemaPath+"/"+g,u=h,E+="="):(I&&(u=Math[v?"min":"max"](h,s)),h===(I?u:!0)?(x=!0,S=g,d=e.errSchemaPath+"/"+g,E+="="):(x=!1,T+="="));var R="'"+T+"'";r+=" if ( ",p&&(r+=" ("+u+" !== undefined && typeof "+u+" != 'number') || "),r+=" "+f+" "+E+" "+u+" || "+f+" !== "+f+") { "}}S=S||t;var j=j||[];j.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(S||"_limit")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { comparison: "+R+", limit: "+u+", exclusive: "+x+" } ",e.opts.messages!==!1&&(r+=" , message: 'should be "+T+" ",p?r+="' + "+u:r+=""+u+"'"),e.opts.verbose&&(r+=" , schema:  ",p?r+="validate.schema"+c:r+=""+s,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var L=r;return r=j.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+L+"]); ":r+=" validate.errors = ["+L+"]; return false; ":r+=" var err = "+L+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } ",l&&(r+=" else { "),r},Ja=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,g,f="data"+(i||""),p=e.opts.$data&&s&&s.$data,u;if(p?(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; ",u="schema"+o):u=s,!(p||typeof s=="number"))throw new Error(t+" must be number");var v=t=="maxItems"?">":"<";r+="if ( ",p&&(r+=" ("+u+" !== undefined && typeof "+u+" != 'number') || "),r+=" "+f+".length "+v+" "+u+") { ";var g=t,h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(g||"_limitItems")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { limit: "+u+" } ",e.opts.messages!==!1&&(r+=" , message: 'should NOT have ",t=="maxItems"?r+="more":r+="fewer",r+=" than ",p?r+="' + "+u+" + '":r+=""+s,r+=" items' "),e.opts.verbose&&(r+=" , schema:  ",p?r+="validate.schema"+c:r+=""+s,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var b=r;return r=h.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+b+"]); ":r+=" validate.errors = ["+b+"]; return false; ":r+=" var err = "+b+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+="} ",l&&(r+=" else { "),r},Za=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,g,f="data"+(i||""),p=e.opts.$data&&s&&s.$data,u;if(p?(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; ",u="schema"+o):u=s,!(p||typeof s=="number"))throw new Error(t+" must be number");var v=t=="maxLength"?">":"<";r+="if ( ",p&&(r+=" ("+u+" !== undefined && typeof "+u+" != 'number') || "),e.opts.unicode===!1?r+=" "+f+".length ":r+=" ucs2length("+f+") ",r+=" "+v+" "+u+") { ";var g=t,h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(g||"_limitLength")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { limit: "+u+" } ",e.opts.messages!==!1&&(r+=" , message: 'should NOT be ",t=="maxLength"?r+="longer":r+="shorter",r+=" than ",p?r+="' + "+u+" + '":r+=""+s,r+=" characters' "),e.opts.verbose&&(r+=" , schema:  ",p?r+="validate.schema"+c:r+=""+s,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var b=r;return r=h.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+b+"]); ":r+=" validate.errors = ["+b+"]; return false; ":r+=" var err = "+b+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+="} ",l&&(r+=" else { "),r},Ya=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,g,f="data"+(i||""),p=e.opts.$data&&s&&s.$data,u;if(p?(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; ",u="schema"+o):u=s,!(p||typeof s=="number"))throw new Error(t+" must be number");var v=t=="maxProperties"?">":"<";r+="if ( ",p&&(r+=" ("+u+" !== undefined && typeof "+u+" != 'number') || "),r+=" Object.keys("+f+").length "+v+" "+u+") { ";var g=t,h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(g||"_limitProperties")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { limit: "+u+" } ",e.opts.messages!==!1&&(r+=" , message: 'should NOT have ",t=="maxProperties"?r+="more":r+="fewer",r+=" than ",p?r+="' + "+u+" + '":r+=""+s,r+=" properties' "),e.opts.verbose&&(r+=" , schema:  ",p?r+="validate.schema"+c:r+=""+s,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var b=r;return r=h.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+b+"]); ":r+=" validate.errors = ["+b+"]; return false; ":r+=" var err = "+b+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+="} ",l&&(r+=" else { "),r},Wo=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p=e.opts.$data&&s&&s.$data,u;if(p?(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; ",u="schema"+o):u=s,!(p||typeof s=="number"))throw new Error(t+" must be number");r+="var division"+o+";if (",p&&(r+=" "+u+" !== undefined && ( typeof "+u+" != 'number' || "),r+=" (division"+o+" = "+f+" / "+u+", ",e.opts.multipleOfPrecision?r+=" Math.abs(Math.round(division"+o+") - division"+o+") > 1e-"+e.opts.multipleOfPrecision+" ":r+=" division"+o+" !== parseInt(division"+o+") ",r+=" ) ",p&&(r+="  )  "),r+=" ) {   ";var v=v||[];v.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'multipleOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { multipleOf: "+u+" } ",e.opts.messages!==!1&&(r+=" , message: 'should be multiple of ",p?r+="' + "+u:r+=""+u+"'"),e.opts.verbose&&(r+=" , schema:  ",p?r+="validate.schema"+c:r+=""+s,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var g=r;return r=v.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+g+"]); ":r+=" validate.errors = ["+g+"]; return false; ":r+=" var err = "+g+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+="} ",l&&(r+=" else { "),r},Bo=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="errs__"+o,u=e.util.copy(e);u.level++;var v="valid"+u.level;if(e.opts.strictKeywords?typeof s=="object"&&Object.keys(s).length>0||s===!1:e.util.schemaHasRules(s,e.RULES.all)){u.schema=s,u.schemaPath=c,u.errSchemaPath=d,r+=" var "+p+" = errors;  ";var g=e.compositeRule;e.compositeRule=u.compositeRule=!0,u.createErrors=!1;var h;u.opts.allErrors&&(h=u.opts.allErrors,u.opts.allErrors=!1),r+=" "+e.validate(u)+" ",u.createErrors=!0,h&&(u.opts.allErrors=h),e.compositeRule=u.compositeRule=g,r+=" if ("+v+") {   ";var b=b||[];b.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'not' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: 'should NOT be valid' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var F=r;r=b.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+F+"]); ":r+=" validate.errors = ["+F+"]; return false; ":r+=" var err = "+F+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } else {  errors = "+p+"; if (vErrors !== null) { if ("+p+") vErrors.length = "+p+"; else vErrors = null; } ",e.opts.allErrors&&(r+=" } ")}else r+="  var err =   ",e.createErrors!==!1?(r+=" { keyword: 'not' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: 'should NOT be valid' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",l&&(r+=" if (false) { ");return r},Qo=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="valid"+o,u="errs__"+o,v=e.util.copy(e),g="";v.level++;var h="valid"+v.level,b=v.baseId,F="prevValid"+o,E="passingSchemas"+o;r+="var "+u+" = errors , "+F+" = false , "+p+" = false , "+E+" = null; ";var S=e.compositeRule;e.compositeRule=v.compositeRule=!0;var D=s;if(D)for(var x,C=-1,I=D.length-1;C<I;)x=D[C+=1],(e.opts.strictKeywords?typeof x=="object"&&Object.keys(x).length>0||x===!1:e.util.schemaHasRules(x,e.RULES.all))?(v.schema=x,v.schemaPath=c+"["+C+"]",v.errSchemaPath=d+"/"+C,r+="  "+e.validate(v)+" ",v.baseId=b):r+=" var "+h+" = true; ",C&&(r+=" if ("+h+" && "+F+") { "+p+" = false; "+E+" = ["+E+", "+C+"]; } else { ",g+="}"),r+=" if ("+h+") { "+p+" = "+F+" = true; "+E+" = "+C+"; }";return e.compositeRule=v.compositeRule=S,r+=""+g+"if (!"+p+") {   var err =   ",e.createErrors!==!1?(r+=" { keyword: 'oneOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { passingSchemas: "+E+" } ",e.opts.messages!==!1&&(r+=" , message: 'should match exactly one schema in oneOf' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&l&&(e.async?r+=" throw new ValidationError(vErrors); ":r+=" validate.errors = vErrors; return false; "),r+="} else {  errors = "+u+"; if (vErrors !== null) { if ("+u+") vErrors.length = "+u+"; else vErrors = null; }",e.opts.allErrors&&(r+=" } "),r},Ho=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p=e.opts.$data&&s&&s.$data,u;p?(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; ",u="schema"+o):u=s;var v=p?"(new RegExp("+u+"))":e.usePattern(s);r+="if ( ",p&&(r+=" ("+u+" !== undefined && typeof "+u+" != 'string') || "),r+=" !"+v+".test("+f+") ) {   ";var g=g||[];g.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'pattern' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { pattern:  ",p?r+=""+u:r+=""+e.util.toQuotedString(s),r+="  } ",e.opts.messages!==!1&&(r+=` , message: 'should match pattern "`,p?r+="' + "+u+" + '":r+=""+e.util.escapeQuotes(s),r+=`"' `),e.opts.verbose&&(r+=" , schema:  ",p?r+="validate.schema"+c:r+=""+e.util.toQuotedString(s),r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var h=r;return r=g.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+h+"]); ":r+=" validate.errors = ["+h+"]; return false; ":r+=" var err = "+h+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+="} ",l&&(r+=" else { "),r},Ko=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="errs__"+o,u=e.util.copy(e),v="";u.level++;var g="valid"+u.level,h="key"+o,b="idx"+o,F=u.dataLevel=e.dataLevel+1,E="data"+F,S="dataProperties"+o,D=Object.keys(s||{}).filter(K),x=e.schema.patternProperties||{},C=Object.keys(x).filter(K),I=e.schema.additionalProperties,R=D.length||C.length,T=I===!1,j=typeof I=="object"&&Object.keys(I).length,L=e.opts.removeAdditional,$=T||j||L,A=e.opts.ownProperties,V=e.baseId,H=e.schema.required;if(H&&!(e.opts.$data&&H.$data)&&H.length<e.opts.loopRequired)var q=e.util.toHash(H);function K(Qr){return Qr!=="__proto__"}if(r+="var "+p+" = errors;var "+g+" = true;",A&&(r+=" var "+S+" = undefined;"),$){if(A?r+=" "+S+" = "+S+" || Object.keys("+f+"); for (var "+b+"=0; "+b+"<"+S+".length; "+b+"++) { var "+h+" = "+S+"["+b+"]; ":r+=" for (var "+h+" in "+f+") { ",R){if(r+=" var isAdditional"+o+" = !(false ",D.length)if(D.length>8)r+=" || validate.schema"+c+".hasOwnProperty("+h+") ";else{var W=D;if(W)for(var U,ue=-1,ve=W.length-1;ue<ve;)U=W[ue+=1],r+=" || "+h+" == "+e.util.toQuotedString(U)+" "}if(C.length){var ge=C;if(ge)for(var Z,Te=-1,be=ge.length-1;Te<be;)Z=ge[Te+=1],r+=" || "+e.usePattern(Z)+".test("+h+") "}r+=" ); if (isAdditional"+o+") { "}if(L=="all")r+=" delete "+f+"["+h+"]; ";else{var ie=e.errorPath,Pe="' + "+h+" + '";if(e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(e.errorPath,h,e.opts.jsonPointers)),T)if(L)r+=" delete "+f+"["+h+"]; ";else{r+=" "+g+" = false; ";var Oe=d;d=e.errSchemaPath+"/additionalProperties";var fe=fe||[];fe.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'additionalProperties' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { additionalProperty: '"+Pe+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is an invalid additional property":r+="should NOT have additional properties",r+="' "),e.opts.verbose&&(r+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var te=r;r=fe.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+te+"]); ":r+=" validate.errors = ["+te+"]; return false; ":r+=" var err = "+te+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",d=Oe,l&&(r+=" break; ")}else if(j)if(L=="failing"){r+=" var "+p+" = errors;  ";var ke=e.compositeRule;e.compositeRule=u.compositeRule=!0,u.schema=I,u.schemaPath=e.schemaPath+".additionalProperties",u.errSchemaPath=e.errSchemaPath+"/additionalProperties",u.errorPath=e.opts._errorDataPathProperty?e.errorPath:e.util.getPathExpr(e.errorPath,h,e.opts.jsonPointers);var pe=f+"["+h+"]";u.dataPathArr[F]=h;var ne=e.validate(u);u.baseId=V,e.util.varOccurences(ne,E)<2?r+=" "+e.util.varReplace(ne,E,pe)+" ":r+=" var "+E+" = "+pe+"; "+ne+" ",r+=" if (!"+g+") { errors = "+p+"; if (validate.errors !== null) { if (errors) validate.errors.length = errors; else validate.errors = null; } delete "+f+"["+h+"]; }  ",e.compositeRule=u.compositeRule=ke}else{u.schema=I,u.schemaPath=e.schemaPath+".additionalProperties",u.errSchemaPath=e.errSchemaPath+"/additionalProperties",u.errorPath=e.opts._errorDataPathProperty?e.errorPath:e.util.getPathExpr(e.errorPath,h,e.opts.jsonPointers);var pe=f+"["+h+"]";u.dataPathArr[F]=h;var ne=e.validate(u);u.baseId=V,e.util.varOccurences(ne,E)<2?r+=" "+e.util.varReplace(ne,E,pe)+" ":r+=" var "+E+" = "+pe+"; "+ne+" ",l&&(r+=" if (!"+g+") break; ")}e.errorPath=ie}R&&(r+=" } "),r+=" }  ",l&&(r+=" if ("+g+") { ",v+="}")}var ze=e.opts.useDefaults&&!e.compositeRule;if(D.length){var ye=D;if(ye)for(var U,ir=-1,Xe=ye.length-1;ir<Xe;){U=ye[ir+=1];var he=s[U];if(e.opts.strictKeywords?typeof he=="object"&&Object.keys(he).length>0||he===!1:e.util.schemaHasRules(he,e.RULES.all)){var qe=e.util.getProperty(U),pe=f+qe,hr=ze&&he.default!==void 0;u.schema=he,u.schemaPath=c+qe,u.errSchemaPath=d+"/"+e.util.escapeFragment(U),u.errorPath=e.util.getPath(e.errorPath,U,e.opts.jsonPointers),u.dataPathArr[F]=e.util.toQuotedString(U);var ne=e.validate(u);if(u.baseId=V,e.util.varOccurences(ne,E)<2){ne=e.util.varReplace(ne,E,pe);var de=pe}else{var de=E;r+=" var "+E+" = "+pe+"; "}if(hr)r+=" "+ne+" ";else{if(q&&q[U]){r+=" if ( "+de+" === undefined ",A&&(r+=" || ! Object.prototype.hasOwnProperty.call("+f+", '"+e.util.escapeQuotes(U)+"') "),r+=") { "+g+" = false; ";var ie=e.errorPath,Oe=d,me=e.util.escapeQuotes(U);e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(ie,U,e.opts.jsonPointers)),d=e.errSchemaPath+"/required";var fe=fe||[];fe.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { missingProperty: '"+me+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+me+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var te=r;r=fe.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+te+"]); ":r+=" validate.errors = ["+te+"]; return false; ":r+=" var err = "+te+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",d=Oe,e.errorPath=ie,r+=" } else { "}else l?(r+=" if ( "+de+" === undefined ",A&&(r+=" || ! Object.prototype.hasOwnProperty.call("+f+", '"+e.util.escapeQuotes(U)+"') "),r+=") { "+g+" = true; } else { "):(r+=" if ("+de+" !== undefined ",A&&(r+=" &&   Object.prototype.hasOwnProperty.call("+f+", '"+e.util.escapeQuotes(U)+"') "),r+=" ) { ");r+=" "+ne+" } "}}l&&(r+=" if ("+g+") { ",v+="}")}}if(C.length){var Ee=C;if(Ee)for(var Z,Fr=-1,Br=Ee.length-1;Fr<Br;){Z=Ee[Fr+=1];var he=x[Z];if(e.opts.strictKeywords?typeof he=="object"&&Object.keys(he).length>0||he===!1:e.util.schemaHasRules(he,e.RULES.all)){u.schema=he,u.schemaPath=e.schemaPath+".patternProperties"+e.util.getProperty(Z),u.errSchemaPath=e.errSchemaPath+"/patternProperties/"+e.util.escapeFragment(Z),A?r+=" "+S+" = "+S+" || Object.keys("+f+"); for (var "+b+"=0; "+b+"<"+S+".length; "+b+"++) { var "+h+" = "+S+"["+b+"]; ":r+=" for (var "+h+" in "+f+") { ",r+=" if ("+e.usePattern(Z)+".test("+h+")) { ",u.errorPath=e.util.getPathExpr(e.errorPath,h,e.opts.jsonPointers);var pe=f+"["+h+"]";u.dataPathArr[F]=h;var ne=e.validate(u);u.baseId=V,e.util.varOccurences(ne,E)<2?r+=" "+e.util.varReplace(ne,E,pe)+" ":r+=" var "+E+" = "+pe+"; "+ne+" ",l&&(r+=" if (!"+g+") break; "),r+=" } ",l&&(r+=" else "+g+" = true; "),r+=" }  ",l&&(r+=" if ("+g+") { ",v+="}")}}}return l&&(r+=" "+v+" if ("+p+" == errors) {"),r},Go=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="errs__"+o,u=e.util.copy(e),v="";u.level++;var g="valid"+u.level;if(r+="var "+p+" = errors;",e.opts.strictKeywords?typeof s=="object"&&Object.keys(s).length>0||s===!1:e.util.schemaHasRules(s,e.RULES.all)){u.schema=s,u.schemaPath=c,u.errSchemaPath=d;var h="key"+o,b="idx"+o,F="i"+o,E="' + "+h+" + '",S=u.dataLevel=e.dataLevel+1,D="data"+S,x="dataProperties"+o,C=e.opts.ownProperties,I=e.baseId;C&&(r+=" var "+x+" = undefined; "),C?r+=" "+x+" = "+x+" || Object.keys("+f+"); for (var "+b+"=0; "+b+"<"+x+".length; "+b+"++) { var "+h+" = "+x+"["+b+"]; ":r+=" for (var "+h+" in "+f+") { ",r+=" var startErrs"+o+" = errors; ";var R=h,T=e.compositeRule;e.compositeRule=u.compositeRule=!0;var j=e.validate(u);u.baseId=I,e.util.varOccurences(j,D)<2?r+=" "+e.util.varReplace(j,D,R)+" ":r+=" var "+D+" = "+R+"; "+j+" ",e.compositeRule=u.compositeRule=T,r+=" if (!"+g+") { for (var "+F+"=startErrs"+o+"; "+F+"<errors; "+F+"++) { vErrors["+F+"].propertyName = "+h+"; }   var err =   ",e.createErrors!==!1?(r+=" { keyword: 'propertyNames' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { propertyName: '"+E+"' } ",e.opts.messages!==!1&&(r+=" , message: 'property name \\'"+E+"\\' is invalid' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&l&&(e.async?r+=" throw new ValidationError(vErrors); ":r+=" validate.errors = vErrors; return false; "),l&&(r+=" break; "),r+=" } }"}return l&&(r+=" "+v+" if ("+p+" == errors) {"),r},Jo=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="valid"+o,u=e.opts.$data&&s&&s.$data;u&&(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; ");var v="schema"+o;if(!u)if(s.length<e.opts.loopRequired&&e.schema.properties&&Object.keys(e.schema.properties).length){var g=[],h=s;if(h)for(var b,F=-1,E=h.length-1;F<E;){b=h[F+=1];var S=e.schema.properties[b];S&&(e.opts.strictKeywords?typeof S=="object"&&Object.keys(S).length>0||S===!1:e.util.schemaHasRules(S,e.RULES.all))||(g[g.length]=b)}}else var g=s;if(u||g.length){var D=e.errorPath,x=u||g.length>=e.opts.loopRequired,C=e.opts.ownProperties;if(l)if(r+=" var missing"+o+"; ",x){u||(r+=" var "+v+" = validate.schema"+c+"; ");var I="i"+o,R="schema"+o+"["+I+"]",T="' + "+R+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(D,R,e.opts.jsonPointers)),r+=" var "+p+" = true; ",u&&(r+=" if (schema"+o+" === undefined) "+p+" = true; else if (!Array.isArray(schema"+o+")) "+p+" = false; else {"),r+=" for (var "+I+" = 0; "+I+" < "+v+".length; "+I+"++) { "+p+" = "+f+"["+v+"["+I+"]] !== undefined ",C&&(r+=" &&   Object.prototype.hasOwnProperty.call("+f+", "+v+"["+I+"]) "),r+="; if (!"+p+") break; } ",u&&(r+="  }  "),r+="  if (!"+p+") {   ";var j=j||[];j.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { missingProperty: '"+T+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+T+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var L=r;r=j.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+L+"]); ":r+=" validate.errors = ["+L+"]; return false; ":r+=" var err = "+L+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } else { "}else{r+=" if ( ";var $=g;if($)for(var A,I=-1,V=$.length-1;I<V;){A=$[I+=1],I&&(r+=" || ");var H=e.util.getProperty(A),q=f+H;r+=" ( ( "+q+" === undefined ",C&&(r+=" || ! Object.prototype.hasOwnProperty.call("+f+", '"+e.util.escapeQuotes(A)+"') "),r+=") && (missing"+o+" = "+e.util.toQuotedString(e.opts.jsonPointers?A:H)+") ) "}r+=") {  ";var R="missing"+o,T="' + "+R+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.opts.jsonPointers?e.util.getPathExpr(D,R,!0):D+" + "+R);var j=j||[];j.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { missingProperty: '"+T+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+T+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var L=r;r=j.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+L+"]); ":r+=" validate.errors = ["+L+"]; return false; ":r+=" var err = "+L+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } else { "}else if(x){u||(r+=" var "+v+" = validate.schema"+c+"; ");var I="i"+o,R="schema"+o+"["+I+"]",T="' + "+R+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(D,R,e.opts.jsonPointers)),u&&(r+=" if ("+v+" && !Array.isArray("+v+")) {  var err =   ",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { missingProperty: '"+T+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+T+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } else if ("+v+" !== undefined) { "),r+=" for (var "+I+" = 0; "+I+" < "+v+".length; "+I+"++) { if ("+f+"["+v+"["+I+"]] === undefined ",C&&(r+=" || ! Object.prototype.hasOwnProperty.call("+f+", "+v+"["+I+"]) "),r+=") {  var err =   ",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { missingProperty: '"+T+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+T+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } } ",u&&(r+="  }  ")}else{var K=g;if(K)for(var A,W=-1,U=K.length-1;W<U;){A=K[W+=1];var H=e.util.getProperty(A),T=e.util.escapeQuotes(A),q=f+H;e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(D,A,e.opts.jsonPointers)),r+=" if ( "+q+" === undefined ",C&&(r+=" || ! Object.prototype.hasOwnProperty.call("+f+", '"+e.util.escapeQuotes(A)+"') "),r+=") {  var err =   ",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { missingProperty: '"+T+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+T+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}e.errorPath=D}else l&&(r+=" if (true) {");return r},Zo=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f="data"+(i||""),p="valid"+o,u=e.opts.$data&&s&&s.$data,v;if(u?(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; ",v="schema"+o):v=s,(s||u)&&e.opts.uniqueItems!==!1){u&&(r+=" var "+p+"; if ("+v+" === false || "+v+" === undefined) "+p+" = true; else if (typeof "+v+" != 'boolean') "+p+" = false; else { "),r+=" var i = "+f+".length , "+p+" = true , j; if (i > 1) { ";var g=e.schema.items&&e.schema.items.type,h=Array.isArray(g);if(!g||g=="object"||g=="array"||h&&(g.indexOf("object")>=0||g.indexOf("array")>=0))r+=" outer: for (;i--;) { for (j = i; j--;) { if (equal("+f+"[i], "+f+"[j])) { "+p+" = false; break outer; } } } ";else{r+=" var itemIndices = {}, item; for (;i--;) { var item = "+f+"[i]; ";var b="checkDataType"+(h?"s":"");r+=" if ("+e.util[b](g,"item",e.opts.strictNumbers,!0)+") continue; ",h&&(r+=` if (typeof item == 'string') item = '"' + item; `),r+=" if (typeof itemIndices[item] == 'number') { "+p+" = false; j = itemIndices[item]; break; } itemIndices[item] = i; } "}r+=" } ",u&&(r+="  }  "),r+=" if (!"+p+") {   ";var F=F||[];F.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'uniqueItems' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { i: i, j: j } ",e.opts.messages!==!1&&(r+=" , message: 'should NOT have duplicate items (items ## ' + j + ' and ' + i + ' are identical)' "),e.opts.verbose&&(r+=" , schema:  ",u?r+="validate.schema"+c:r+=""+s,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),r+=" } "):r+=" {} ";var E=r;r=F.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+E+"]); ":r+=" validate.errors = ["+E+"]; return false; ":r+=" var err = "+E+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } ",l&&(r+=" else { ")}else l&&(r+=" if (true) { ");return r},Xa={$ref:Ro,allOf:No,anyOf:jo,$comment:Lo,const:To,contains:ko,dependencies:Vo,enum:Mo,format:Uo,if:zo,items:qo,maximum:Ga,minimum:Ga,maxItems:Ja,minItems:Ja,maxLength:Za,minLength:Za,maxProperties:Ya,minProperties:Ya,multipleOf:Wo,not:Bo,oneOf:Qo,pattern:Ho,properties:Ko,propertyNames:Go,required:Jo,uniqueItems:Zo,validate:na},Kr=Fe.toHash,Yo=function(){var e=[{type:"number",rules:[{maximum:["exclusiveMaximum"]},{minimum:["exclusiveMinimum"]},"multipleOf","format"]},{type:"string",rules:["maxLength","minLength","pattern","format"]},{type:"array",rules:["maxItems","minItems","items","contains","uniqueItems"]},{type:"object",rules:["maxProperties","minProperties","required","dependencies","propertyNames",{properties:["additionalProperties","patternProperties"]}]},{rules:["$ref","const","enum","not","anyOf","oneOf","allOf","if"]}],t=["type","$comment"],n=["$schema","$id","id","$data","$async","title","description","default","definitions","examples","readOnly","writeOnly","contentMediaType","contentEncoding","additionalItems","then","else"],r=["number","integer","string","array","object","boolean","null"];return e.all=Kr(t),e.types=Kr(r),e.forEach(function(o){o.rules=o.rules.map(function(i){var s;if(typeof i=="object"){var c=Object.keys(i)[0];s=i[c],i=c,s.forEach(function(l){t.push(l),e.all[l]=!0})}t.push(i);var d=e.all[i]={keyword:i,code:Xa[i],implements:s};return d}),e.all.$comment={keyword:"$comment",code:Xa.$comment},o.type&&(e.types[o.type]=o)}),e.keywords=Kr(t.concat(n)),e.custom={},e},et=["multipleOf","maximum","exclusiveMaximum","minimum","exclusiveMinimum","maxLength","minLength","pattern","additionalItems","maxItems","minItems","uniqueItems","maxProperties","minProperties","required","additionalProperties","enum","format","const"],At=function(a,e){for(var t=0;t<e.length;t++){a=JSON.parse(JSON.stringify(a));var n=e[t].split("/"),r=a,o;for(o=1;o<n.length;o++)r=r[n[o]];for(o=0;o<et.length;o++){var i=et[o],s=r[i];s&&(r[i]={anyOf:[s,{$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"}]})}}return a},Xo=Er.MissingRef,ei=$t;function $t(a,e,t){var n=this;if(typeof this._opts.loadSchema!="function")throw new Error("options.loadSchema should be a function");typeof e=="function"&&(t=e,e=void 0);var r=o(a).then(function(){var s=n._addSchema(a,void 0,e);return s.validate||i(s)});return t&&r.then(function(s){t(null,s)},t),r;function o(s){var c=s.$schema;return c&&!n.getSchema(c)?$t.call(n,{$ref:c},!0):Promise.resolve()}function i(s){try{return n._compile(s)}catch(d){if(d instanceof Xo)return c(d);throw d}function c(d){var l=d.missingSchema;if(u(l))throw new Error("Schema "+l+" is loaded but "+d.missingRef+" cannot be resolved");var f=n._loadingSchemas[l];return f||(f=n._loadingSchemas[l]=n._opts.loadSchema(l),f.then(p,p)),f.then(function(v){if(!u(l))return o(v).then(function(){u(l)||n.addSchema(v,l,void 0,e)})}).then(function(){return i(s)});function p(){delete n._loadingSchemas[l]}function u(v){return n._refs[v]||n._schemas[v]}}}}var ri=function(e,t,n){var r=" ",o=e.level,i=e.dataLevel,s=e.schema[t],c=e.schemaPath+e.util.getProperty(t),d=e.errSchemaPath+"/"+t,l=!e.opts.allErrors,f,p="data"+(i||""),u="valid"+o,v="errs__"+o,g=e.opts.$data&&s&&s.$data,h;g?(r+=" var schema"+o+" = "+e.util.getData(s.$data,i,e.dataPathArr)+"; ",h="schema"+o):h=s;var b=this,F="definition"+o,E=b.definition,S="",D,x,C,I,R;if(g&&E.$data){R="keywordValidate"+o;var T=E.validateSchema;r+=" var "+F+" = RULES.custom['"+t+"'].definition; var "+R+" = "+F+".validate;"}else{if(I=e.useCustomRule(b,s,e.schema,e),!I)return;h="validate.schema"+c,R=I.code,D=E.compile,x=E.inline,C=E.macro}var j=R+".errors",L="i"+o,$="ruleErr"+o,A=E.async;if(A&&!e.async)throw new Error("async keyword in sync schema");if(x||C||(r+=""+j+" = null;"),r+="var "+v+" = errors;var "+u+";",g&&E.$data&&(S+="}",r+=" if ("+h+" === undefined) { "+u+" = true; } else { ",T&&(S+="}",r+=" "+u+" = "+F+".validateSchema("+h+"); if ("+u+") { ")),x)E.statements?r+=" "+I.validate+" ":r+=" "+u+" = "+I.validate+"; ";else if(C){var V=e.util.copy(e),S="";V.level++;var H="valid"+V.level;V.schema=I.validate,V.schemaPath="";var q=e.compositeRule;e.compositeRule=V.compositeRule=!0;var K=e.validate(V).replace(/validate\.schema/g,R);e.compositeRule=V.compositeRule=q,r+=" "+K}else{var W=W||[];W.push(r),r="",r+="  "+R+".call( ",e.opts.passContext?r+="this":r+="self",D||E.schema===!1?r+=" , "+p+" ":r+=" , "+h+" , "+p+" , validate.schema"+e.schemaPath+" ",r+=" , (dataPath || '')",e.errorPath!='""'&&(r+=" + "+e.errorPath);var U=i?"data"+(i-1||""):"parentData",ue=i?e.dataPathArr[i]:"parentDataProperty";r+=" , "+U+" , "+ue+" , rootData )  ";var ve=r;r=W.pop(),E.errors===!1?(r+=" "+u+" = ",A&&(r+="await "),r+=""+ve+"; "):A?(j="customErrors"+o,r+=" var "+j+" = null; try { "+u+" = await "+ve+"; } catch (e) { "+u+" = false; if (e instanceof ValidationError) "+j+" = e.errors; else throw e; } "):r+=" "+j+" = null; "+u+" = "+ve+"; "}if(E.modifying&&(r+=" if ("+U+") "+p+" = "+U+"["+ue+"];"),r+=""+S,E.valid)l&&(r+=" if (true) { ");else{r+=" if ( ",E.valid===void 0?(r+=" !",C?r+=""+H:r+=""+u):r+=" "+!E.valid+" ",r+=") { ",f=b.keyword;var W=W||[];W.push(r),r="";var W=W||[];W.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(f||"custom")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { keyword: '"+b.keyword+"' } ",e.opts.messages!==!1&&(r+=` , message: 'should pass "`+b.keyword+`" keyword validation' `),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+p+" "),r+=" } "):r+=" {} ";var ge=r;r=W.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+ge+"]); ":r+=" validate.errors = ["+ge+"]; return false; ":r+=" var err = "+ge+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";var Z=r;r=W.pop(),x?E.errors?E.errors!="full"&&(r+="  for (var "+L+"="+v+"; "+L+"<errors; "+L+"++) { var "+$+" = vErrors["+L+"]; if ("+$+".dataPath === undefined) "+$+".dataPath = (dataPath || '') + "+e.errorPath+"; if ("+$+".schemaPath === undefined) { "+$+'.schemaPath = "'+d+'"; } ',e.opts.verbose&&(r+=" "+$+".schema = "+h+"; "+$+".data = "+p+"; "),r+=" } "):E.errors===!1?r+=" "+Z+" ":(r+=" if ("+v+" == errors) { "+Z+" } else {  for (var "+L+"="+v+"; "+L+"<errors; "+L+"++) { var "+$+" = vErrors["+L+"]; if ("+$+".dataPath === undefined) "+$+".dataPath = (dataPath || '') + "+e.errorPath+"; if ("+$+".schemaPath === undefined) { "+$+'.schemaPath = "'+d+'"; } ',e.opts.verbose&&(r+=" "+$+".schema = "+h+"; "+$+".data = "+p+"; "),r+=" } } "):C?(r+="   var err =   ",e.createErrors!==!1?(r+=" { keyword: '"+(f||"custom")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { keyword: '"+b.keyword+"' } ",e.opts.messages!==!1&&(r+=` , message: 'should pass "`+b.keyword+`" keyword validation' `),e.opts.verbose&&(r+=" , schema: validate.schema"+c+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+p+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&l&&(e.async?r+=" throw new ValidationError(vErrors); ":r+=" validate.errors = vErrors; return false; ")):E.errors===!1?r+=" "+Z+" ":(r+=" if (Array.isArray("+j+")) { if (vErrors === null) vErrors = "+j+"; else vErrors = vErrors.concat("+j+"); errors = vErrors.length;  for (var "+L+"="+v+"; "+L+"<errors; "+L+"++) { var "+$+" = vErrors["+L+"]; if ("+$+".dataPath === undefined) "+$+".dataPath = (dataPath || '') + "+e.errorPath+";  "+$+'.schemaPath = "'+d+'";  ',e.opts.verbose&&(r+=" "+$+".schema = "+h+"; "+$+".data = "+p+"; "),r+=" } } else { "+Z+" } "),r+=" } ",l&&(r+=" else { ")}return r},It="http://json-schema.org/draft-07/schema#",Ct="http://json-schema.org/draft-07/schema#",Rt="Core schema meta-schema",Nt={schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},jt=["object","boolean"],Lt={$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},ai={$schema:It,$id:Ct,title:Rt,definitions:Nt,type:jt,properties:Lt,default:!0},ti=Object.freeze({__proto__:null,$schema:It,$id:Ct,title:Rt,definitions:Nt,type:jt,properties:Lt,default:ai}),ia=dt(ti),ni={$id:"https://github.com/ajv-validator/ajv/blob/master/lib/definition_schema.js",definitions:{simpleTypes:ia.definitions.simpleTypes},type:"object",dependencies:{schema:["validate"],$data:["validate"],statements:["inline"],valid:{not:{required:["macro"]}}},properties:{type:ia.properties.type,schema:{type:"boolean"},statements:{type:"boolean"},dependencies:{type:"array",items:{type:"string"}},metaSchema:{type:"object"},modifying:{type:"boolean"},valid:{type:"boolean"},$data:{type:"boolean"},async:{type:"boolean"},errors:{anyOf:[{type:"boolean"},{const:"full"}]}}},oi=/^[a-z_$][a-z0-9_$-]*$/i,Wr={add:ii,get:si,remove:li,validate:sa};function ii(a,e){var t=this.RULES;if(t.keywords[a])throw new Error("Keyword "+a+" is already defined");if(!oi.test(a))throw new Error("Keyword "+a+" is not a valid identifier");if(e){this.validateKeyword(e,!0);var n=e.type;if(Array.isArray(n))for(var r=0;r<n.length;r++)i(a,n[r],e);else i(a,n,e);var o=e.metaSchema;o&&(e.$data&&this._opts.$data&&(o={anyOf:[o,{$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"}]}),e.validateSchema=this.compile(o,!0))}t.keywords[a]=t.all[a]=!0;function i(s,c,d){for(var l,f=0;f<t.length;f++){var p=t[f];if(p.type==c){l=p;break}}l||(l={type:c,rules:[]},t.push(l));var u={keyword:s,definition:d,custom:!0,code:ri,implements:d.implements};l.rules.push(u),t.custom[s]=u}return this}function si(a){var e=this.RULES.custom[a];return e?e.definition:this.RULES.keywords[a]||!1}function li(a){var e=this.RULES;delete e.keywords[a],delete e.all[a],delete e.custom[a];for(var t=0;t<e.length;t++)for(var n=e[t].rules,r=0;r<n.length;r++)if(n[r].keyword==a){n.splice(r,1);break}return this}function sa(a,e){sa.errors=null;var t=this._validateKeyword=this._validateKeyword||this.compile(ni,!0);if(t(a))return!0;if(sa.errors=t.errors,e)throw new Error("custom keyword definition is invalid: "+this.errorsText(t.errors));return!1}var Tt="http://json-schema.org/draft-07/schema#",kt="https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#",Vt="Meta-schema for $data reference (JSON Schema extension proposal)",Mt="object",Ut=["$data"],zt={$data:{type:"string",anyOf:[{format:"relative-json-pointer"},{format:"json-pointer"}]}},qt=!1,ui={$schema:Tt,$id:kt,description:Vt,type:Mt,required:Ut,properties:zt,additionalProperties:qt},fi=Object.freeze({__proto__:null,$schema:Tt,$id:kt,description:Vt,type:Mt,required:Ut,properties:zt,additionalProperties:qt,default:ui}),ci=dt(fi),di=oe;oe.prototype.validate=hi;oe.prototype.compile=vi;oe.prototype.addSchema=pi;oe.prototype.addMetaSchema=gi;oe.prototype.validateSchema=yi;oe.prototype.getSchema=bi;oe.prototype.removeSchema=Si;oe.prototype.addFormat=$i;oe.prototype.errorsText=Ai;oe.prototype._addSchema=wi;oe.prototype._compile=Fi;oe.prototype.compileAsync=ei;oe.prototype.addKeyword=Wr.add;oe.prototype.getKeyword=Wr.get;oe.prototype.removeKeyword=Wr.remove;oe.prototype.validateKeyword=Wr.validate;oe.ValidationError=Er.Validation;oe.MissingRefError=Er.MissingRef;oe.$dataMetaSchema=At;var Vr="http://json-schema.org/draft-07/schema",rt=["removeAdditional","useDefaults","coerceTypes","strictDefaults"],mi=["/properties"];function oe(a){if(!(this instanceof oe))return new oe(a);a=this._opts=Fe.copy(a)||{},Li(this),this._schemas={},this._refs={},this._fragments={},this._formats=Do(a.format),this._cache=a.cache||new bo,this._loadingSchemas={},this._compilations=[],this.RULES=Yo(),this._getId=Oi(a),a.loopRequired=a.loopRequired||1/0,a.errorDataPath=="property"&&(a._errorDataPathProperty=!0),a.serialize===void 0&&(a.serialize=pt),this._metaOpts=ji(this),a.formats&&Ri(this),a.keywords&&Ni(this),Ii(this),typeof a.meta=="object"&&this.addMetaSchema(a.meta),a.nullable&&this.addKeyword("nullable",{metaSchema:{type:"boolean"}}),Ci(this)}function hi(a,e){var t;if(typeof a=="string"){if(t=this.getSchema(a),!t)throw new Error('no schema with key or ref "'+a+'"')}else{var n=this._addSchema(a);t=n.validate||this._compile(n)}var r=t(e);return t.$async!==!0&&(this.errors=t.errors),r}function vi(a,e){var t=this._addSchema(a,void 0,e);return t.validate||this._compile(t)}function pi(a,e,t,n){if(Array.isArray(a)){for(var r=0;r<a.length;r++)this.addSchema(a[r],void 0,t,n);return this}var o=this._getId(a);if(o!==void 0&&typeof o!="string")throw new Error("schema id must be string");return e=Se.normalizeId(e||o),Bt(this,e),this._schemas[e]=this._addSchema(a,t,n,!0),this}function gi(a,e,t){return this.addSchema(a,e,t,!0),this}function yi(a,e){var t=a.$schema;if(t!==void 0&&typeof t!="string")throw new Error("$schema must be a string");if(t=t||this._opts.defaultMeta||Pi(this),!t)return this.logger.warn("meta-schema not available"),this.errors=null,!0;var n=this.validate(t,a);if(!n&&e){var r="schema is invalid: "+this.errorsText();if(this._opts.validateSchema=="log")this.logger.error(r);else throw new Error(r)}return n}function Pi(a){var e=a._opts.meta;return a._opts.defaultMeta=typeof e=="object"?a._getId(e)||e:a.getSchema(Vr)?Vr:void 0,a._opts.defaultMeta}function bi(a){var e=Wt(this,a);switch(typeof e){case"object":return e.validate||this._compile(e);case"string":return this.getSchema(e);case"undefined":return Ei(this,a)}}function Ei(a,e){var t=Se.schema.call(a,{schema:{}},e);if(t){var n=t.schema,r=t.root,o=t.baseId,i=gt.call(a,n,r,void 0,o);return a._fragments[e]=new cr({ref:e,fragment:!0,schema:n,root:r,baseId:o,validate:i}),i}}function Wt(a,e){return e=Se.normalizeId(e),a._schemas[e]||a._refs[e]||a._fragments[e]}function Si(a){if(a instanceof RegExp)return $r(this,this._schemas,a),$r(this,this._refs,a),this;switch(typeof a){case"undefined":return $r(this,this._schemas),$r(this,this._refs),this._cache.clear(),this;case"string":var e=Wt(this,a);return e&&this._cache.del(e.cacheKey),delete this._schemas[a],delete this._refs[a],this;case"object":var t=this._opts.serialize,n=t?t(a):a;this._cache.del(n);var r=this._getId(a);r&&(r=Se.normalizeId(r),delete this._schemas[r],delete this._refs[r])}return this}function $r(a,e,t){for(var n in e){var r=e[n];!r.meta&&(!t||t.test(n))&&(a._cache.del(r.cacheKey),delete e[n])}}function wi(a,e,t,n){if(typeof a!="object"&&typeof a!="boolean")throw new Error("schema should be object or boolean");var r=this._opts.serialize,o=r?r(a):a,i=this._cache.get(o);if(i)return i;n=n||this._opts.addUsedSchema!==!1;var s=Se.normalizeId(this._getId(a));s&&n&&Bt(this,s);var c=this._opts.validateSchema!==!1&&!e,d;c&&!(d=s&&s==Se.normalizeId(a.$schema))&&this.validateSchema(a,!0);var l=Se.ids.call(this,a),f=new cr({id:s,schema:a,localRefs:l,cacheKey:o,meta:t});return s[0]!="#"&&n&&(this._refs[s]=f),this._cache.put(o,f),c&&d&&this.validateSchema(a,!0),f}function Fi(a,e){if(a.compiling)return a.validate=r,r.schema=a.schema,r.errors=null,r.root=e||r,a.schema.$async===!0&&(r.$async=!0),r;a.compiling=!0;var t;a.meta&&(t=this._opts,this._opts=this._metaOpts);var n;try{n=gt.call(this,a.schema,e,a.localRefs)}catch(o){throw delete a.validate,o}finally{a.compiling=!1,a.meta&&(this._opts=t)}return a.validate=n,a.refs=n.refs,a.refVal=n.refVal,a.root=n.root,n;function r(){var o=a.validate,i=o.apply(this,arguments);return r.errors=o.errors,i}}function Oi(a){switch(a.schemaId){case"auto":return xi;case"id":return Di;default:return _i}}function Di(a){return a.$id&&this.logger.warn("schema $id ignored",a.$id),a.id}function _i(a){return a.id&&this.logger.warn("schema id ignored",a.id),a.$id}function xi(a){if(a.$id&&a.id&&a.$id!=a.id)throw new Error("schema $id is different from id");return a.$id||a.id}function Ai(a,e){if(a=a||this.errors,!a)return"No errors";e=e||{};for(var t=e.separator===void 0?", ":e.separator,n=e.dataVar===void 0?"data":e.dataVar,r="",o=0;o<a.length;o++){var i=a[o];i&&(r+=n+i.dataPath+" "+i.message+t)}return r.slice(0,-t.length)}function $i(a,e){return typeof e=="string"&&(e=new RegExp(e)),this._formats[a]=e,this}function Ii(a){var e;if(a._opts.$data&&(e=ci,a.addMetaSchema(e,e.$id,!0)),a._opts.meta!==!1){var t=ia;a._opts.$data&&(t=At(t,mi)),a.addMetaSchema(t,Vr,!0),a._refs["http://json-schema.org/schema"]=Vr}}function Ci(a){var e=a._opts.schemas;if(e)if(Array.isArray(e))a.addSchema(e);else for(var t in e)a.addSchema(e[t],t)}function Ri(a){for(var e in a._opts.formats){var t=a._opts.formats[e];a.addFormat(e,t)}}function Ni(a){for(var e in a._opts.keywords){var t=a._opts.keywords[e];a.addKeyword(e,t)}}function Bt(a,e){if(a._schemas[e]||a._refs[e])throw new Error('schema with key or id "'+e+'" already exists')}function ji(a){for(var e=Fe.copy(a._opts),t=0;t<rt.length;t++)delete e[rt[t]];return e}function Li(a){var e=a._opts.logger;if(e===!1)a.logger={log:Gr,warn:Gr,error:Gr};else{if(e===void 0&&(e=console),!(typeof e=="object"&&e.log&&e.warn&&e.error))throw new Error("logger must implement log, warn and error methods");a.logger=e}}function Gr(){}function Ti(a){if(a&&a.length)for(var e=0;e<a.length;e+=1){var t=a[e],n=void 0,r=void 0,o=void 0;switch(t.keyword){case"$ref":n="无法找到引用".concat(t.params.ref);break;case"additionalItems":n="",r=t.params.limit,n+="不允许超过".concat(r,"个元素");break;case"additionalProperties":n="不允许有额外的属性";break;case"anyOf":n="数据应为 anyOf 所指定的其中一个";break;case"const":n="应当等于常量";break;case"contains":n="应当包含一个有效项";break;case"custom":n='应当通过 "'.concat(t.keyword,' 关键词校验"');break;case"dependencies":n="",r=t.params.depsCount,n+="应当拥有属性".concat(t.params.property,"的依赖属性").concat(t.params.deps);break;case"enum":n="应当是预设定的枚举值之一";break;case"exclusiveMaximum":n="",o="".concat(t.params.comparison," ").concat(t.params.limit),n+="应当为 ".concat(o);break;case"exclusiveMinimum":n="",o="".concat(t.params.comparison," ").concat(t.params.limit),n+="应当为 ".concat(o);break;case"false schema":n="布尔模式出错";break;case"format":n='应当匹配格式 "'.concat(t.params.format,'"');break;case"formatExclusiveMaximum":n="formatExclusiveMaximum 应当是布尔值";break;case"formatExclusiveMinimum":n="formatExclusiveMinimum 应当是布尔值";break;case"formatMaximum":n="",o="".concat(t.params.comparison," ").concat(t.params.limit),n+="应当是 ".concat(o);break;case"formatMinimum":n="",o="".concat(t.params.comparison," ").concat(t.params.limit),n+="应当是 ".concat(o);break;case"if":n='应当匹配模式 "'.concat(t.params.failingKeyword,'" ');break;case"maximum":n="",o="".concat(t.params.comparison," ").concat(t.params.limit),n+="应当为 ".concat(o);break;case"maxItems":n="",r=t.params.limit,n+="不应多于 ".concat(r," 个项");break;case"maxLength":n="",r=t.params.limit,n+="不应多于 ".concat(r," 个字符");break;case"maxProperties":n="",r=t.params.limit,n+="不应有多于 ".concat(r," 个属性");break;case"minimum":n="",o="".concat(t.params.comparison," ").concat(t.params.limit),n+="应当为 ".concat(o);break;case"minItems":n="",r=t.params.limit,n+="不应少于 ".concat(r," 个项");break;case"minLength":n="",r=t.params.limit,n+="不应少于 ".concat(r," 个字符");break;case"minProperties":n="",r=t.params.limit,n+="不应有少于 ".concat(r," 个属性");break;case"multipleOf":n="应当是 ".concat(t.params.multipleOf," 的整数倍");break;case"not":n='不应当匹配 "not" schema';break;case"oneOf":n='只能匹配一个 "oneOf" 中的 schema';break;case"pattern":n='应当匹配模式 "'.concat(t.params.pattern,'"');break;case"patternRequired":n="应当有属性匹配模式 ".concat(t.params.missingPattern);break;case"propertyNames":n="属性名 '".concat(t.params.propertyName,"' 无效");break;case"required":n="应当有必需属性 ".concat(t.params.missingProperty);break;case"switch":n="由于 ".concat(t.params.caseIndex,' 失败，未通过 "switch" 校验, ');break;case"type":n="应当是 ".concat(t.params.type," 类型");break;case"uniqueItems":n="不应当含有重复项 (第 ".concat(t.params.j," 项与第 ").concat(t.params.i," 项是重复的)");break;default:continue}t.message=n}}var Qt={$$currentLocalizeFn:Ti,getCurrentLocalize:function(){return this.$$currentLocalizeFn},useLocal:function(e){this.$$currentLocalizeFn=e}};function ki(a,e){if(e===0)return!1;var t=a[e],n=[t,a[e-1]];return a.splice.apply(a,[e-1,2].concat(n))}function Vi(a,e){if(e===a.length-1)return!1;var t=a[e],n=[a[e+1],t];return a.splice.apply(a,[e,2].concat(n))}function Mi(a,e){return!!a.splice(e,1).length}function Ui(a,e){try{if(Ke(e)==="object")return a.fill(null).map(function(){return JSON.parse(JSON.stringify(e))})}catch(t){}}function zi(a,e){return a.reduce(function(t,n,r){return t[r>e?1:0].push(n),t},[[],[]])}function qi(a,e){return a.filter(function(t){return e.includes(t)})}function Wi(a,e,t){var n=va(a.$ref,e);a.$ref;var r=we(a,["$ref"]);return xe(_(_({},n),r),e,t)}function Ht(){for(var a=arguments.length,e=new Array(a),t=0;t<a;t++)e[t]=arguments[t];if(e.length<2)return e[0];for(var n={},r=[].concat(e),o=function(){var s=J(r[0])?r[0]:{},c=J(r[1])?r[1]:{};n=Object.assign({},s),Object.keys(c).reduce(function(d,l){var f=s[l],p=c[l];if(J(f)||J(p))if(J(f)&&J(p))d[l]=Ht(f,p);else{var u=J(f)?[f,p]:[p,f],v=Je(u,2),g=v[0],h=v[1];l==="additionalProperties"?d[l]=h===!0?g:!1:d[l]=g}else if(Array.isArray(f)||Array.isArray(p))if(Array.isArray(f)&&Array.isArray(p)){if(J(f[0])||J(p[0]))throw new Error("暂不支持如上数组对象元素合并");var b=qi([].concat(f),[].concat(p));if(b.length<=0)throw new Error("无法合并如上数据");b.length===0&&l==="type"?d[l]=b[0]:d[l]=b}else{var F=Array.isArray(f)?[f,p]:[p,f],E=Je(F,2),S=E[0],D=E[1];if(D===void 0)d[l]=S;else{if(!S.includes(D))throw new Error("无法合并如下数据");d[l]=D}}else f!==void 0&&p!==void 0?l==="maxLength"||l==="maximum"||l==="maxItems"||l==="exclusiveMaximum"||l==="maxProperties"?d[l]=Math.min(f,p):l==="minLength"||l==="minimum"||l==="minItems"||l==="exclusiveMinimum"||l==="minProperties"?d[l]=Math.max(f,p):l==="multipleOf"?d[l]=jn(f,p):d[l]=f:d[l]=f===void 0?p:f;return d},n),r.splice(0,2,n)};r.length>=2;)o();return n}function Kt(a,e,t){var n=_(_({},a),{},{allOf:a.allOf.map(function(s){return xe(s,e,t)})});try{var r=n.allOf,o=we(n,["allOf"]);return Ht.apply(void 0,[o].concat(_e(r)))}catch(s){console.error(`无法合并allOf，丢弃allOf配置继续渲染: 
`.concat(s)),n.allOf;var i=we(n,["allOf"]);return i}}function Bi(a){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return a.hasOwnProperty("allOf")&&(a=Kt(a,e,t)),a.hasOwnProperty("$ref")&&(a=Wi(a,e,t)),a}function xe(a){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return J(a)?Bi(a,e,t):{}}var at=/{{(.*)}}/;function Gt(a,e,t,n){if(t!==void 0){var r=at.exec(t);if(at.lastIndex=0,r){var o=r[1].trim(),i=new Function("parentFormData","rootFormData","return ".concat(o));return i(Ue(a,e,1),a)}return n()}}function Jt(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=a.schema,t=a.uiSchema,n=arguments.length>1?arguments[1]:void 0,r=tr({schema:e,uiSchema:t,containsSpec:!1});return["title","description"].reduce(function(o,i){return r[i]&&(o["ui:".concat(i)]=String(r[i]).replace(/\$index/g,n+1)),o},{})}function Qi(a){var e=a.schema,t=e===void 0?{}:e,n=a.uiSchema,r=n===void 0?{}:n,o=a.curNodePath,i=o===void 0?"":o,s=a.rootFormData,c=s===void 0?{}:s,d=r["ui:widget"]||t["ui:widget"],l=r["ui:hidden"]||t["ui:hidden"];return d==="HiddenWidget"||d==="hidden"||!!Gt(c,i,l,function(){return typeof l=="function"?l(Ue(c,i,1),c):l})}function Hi(a,e){var t=e.schema,n=t===void 0?{}:t,r=e.uiSchema,o=r===void 0?{}:r,i=n["ui:field"]||o["ui:field"];if(typeof i=="function"||Ke(i)==="object"||typeof i=="string")return{field:i,fieldProps:o["ui:fieldProps"]||n["ui:fieldProps"]};var s=a[ha(n)];if(s)return{field:s};if(!s&&(n.anyOf||n.oneOf))return{field:null};throw console.error("当前schema:",n),new Error("不支持的field类型, type: ".concat(n.type))}function Mr(a){var e=a.schema,t=e===void 0?{}:e,n=a.uiSchema,r=n===void 0?{}:n,o=a.curNodePath,i=a.rootFormData,s=i===void 0?{}:i;return Object.assign.apply(Object,[{}].concat(_e([t,r].map(function(c){return Object.keys(c).reduce(function(d,l){var f=c[l];if(l==="ui:options"&&J(f))return _(_({},d),f);if(l!=="ui:hidden"){if(l.indexOf("ui:")===0)return _(_({},d),{},G({},l.substring(3),o===void 0?f:Gt(s,o,f,function(){return f})));if(l.indexOf("fui:")===0)return _(_({},d),{},G({},l.substring(4),f.call(null,Ue(s,o,1),s,o)))}return d},{})}))))}function tr(a){var e=a.schema,t=e===void 0?{}:e,n=a.uiSchema,r=n===void 0?{}:n,o=a.containsSpec,i=o===void 0?!0:o,s=a.curNodePath,c=a.rootFormData,d={};return i&&(d.readonly=!!t.readOnly,t.multipleOf!==void 0&&(d.step=t.multipleOf),(t.minimum||t.minimum===0)&&(d.min=t.minimum),(t.maximum||t.maximum===0)&&(d.max=t.maximum),(t.minLength||t.minLength===0)&&(d.minlength=t.minLength),(t.maxLength||t.maxLength===0)&&(d.maxlength=t.maxLength),(t.format==="date-time"||t.format==="date")&&(t.type==="array"?(d.isRange=!0,d.isNumberValue=!(t.items&&t.items.type==="string")):d.isNumberValue=t.type!=="string")),t.title&&(d.title=t.title),t.description&&(d.description=t.description),_(_({},d),Mr({schema:t,uiSchema:r,curNodePath:s,rootFormData:c}))}function Sr(a){var e=a.schema,t=e===void 0?{}:e,n=a.uiSchema,r=n===void 0?{}:n,o=a.curNodePath,i=a.rootFormData,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,c=tr({schema:t,uiSchema:r,curNodePath:o,rootFormData:i});!c.widget&&s&&Object.assign(c,s({schema:t,uiSchema:r}));var d=c.widget,l=c.title,f=c.labelWidth,p=c.description,u=c.attrs,v=c.class,g=c.style,h=c.widgetListeners,b=c.fieldAttrs,F=c.fieldStyle,E=c.fieldClass,S=c.emptyValue,D=c.width,x=c.getWidget,C=c.renderScopedSlots,I=c.renderChildren,R=c.onChange,T=c.required,j=we(c,["widget","title","labelWidth","description","attrs","class","style","widgetListeners","fieldAttrs","fieldStyle","fieldClass","emptyValue","width","getWidget","renderScopedSlots","renderChildren","onChange","required"]);return{widget:d,label:l,labelWidth:f,description:p,widgetAttrs:u,widgetClass:v,widgetStyle:g,fieldAttrs:b,width:D,fieldStyle:F,fieldClass:E,emptyValue:S,getWidget:x,renderScopedSlots:C,renderChildren:I,onChange:R,widgetListeners:h,uiProps:j,uiRequired:T}}function la(a){var e=a.schema,t=e===void 0?{}:e,n=a.uiSchema,r=n===void 0?{}:n,o=a.errorSchema,i=o===void 0?{}:o;return Object.assign.apply(Object,[{}].concat(_e([t,r,i].map(function(s){return Object.keys(s).reduce(function(c,d){var l=s[d];return d==="err:options"&&J(l)?_(_({},c),l):d.indexOf("err:")===0?_(_({},c),{},G({},d.substring(4),l)):c},{})}))))}function Ki(a,e){if(!Array.isArray(e))return a;var t=function(f){return f.reduce(function(p,u){return p[u]=!0,p},{})},n=function(f){return f.length>1?"properties '".concat(f.join("', '"),"'"):"property '".concat(f[0],"'")},r=t(a),o=e.filter(function(l){return l==="*"||r[l]}),i=t(o),s=a.filter(function(l){return!i[l]}),c=o.indexOf("*");if(c===-1){if(s.length)throw new Error("uiSchema order list does not contain ".concat(n(s)));return o}if(c!==o.lastIndexOf("*"))throw new Error("uiSchema order list contains more than one wildcard item");var d=_e(o);return d.splice.apply(d,[c,1].concat(_e(s))),d}function Gi(a){return Array.isArray(a.enum)&&a.enum.length===1||a.hasOwnProperty("const")}function Ji(a){if(Array.isArray(a.enum)&&a.enum.length===1)return a.enum[0];if(a.hasOwnProperty("const"))return a.const;throw new Error("schema cannot be inferred as a constant")}function Ur(a){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=xe(a,e),n=t.oneOf||t.anyOf;return Array.isArray(t.enum)?!0:Array.isArray(n)?n.every(function(r){return Gi(r)}):!1}function ua(a){return Array.isArray(a.items)&&a.items.length>0&&a.items.every(function(e){return J(e)})}function Zt(a){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return!a.uniqueItems||!a.items?!1:Ur(a.items,e)}function Yt(a){return a.additionalItems===!0&&console.warn("additionalItems=true is currently not supported"),J(a.additionalItems)}function wa(a,e,t,n){if(a.enum){var r=Mr({schema:a,uiSchema:e,curNodePath:t,rootFormData:n}),o=r.enumNames||a.enumNames;return a.enum.map(function(c,d){var l=o&&o[d]||String(c);return{label:l,value:c}})}var i=a.oneOf||a.anyOf,s=e.oneOf||e.anyOf;return i.map(function(c,d){var l=s&&s[d]?Mr({schema:c,uiSchema:s[d],curNodePath:t,rootFormData:n}):{},f=Ji(c),p=l.title||c.title||String(f);return{label:p,value:f}})}function Zi(a,e,t){if(a)return a;if(e){var n=t.split(".").pop();if(n&&n!=="".concat(Number(n)))return n}return""}var Be=Xt(),tt=null,nt=null;function Xt(){var a=new di({errorDataPath:"property",allErrors:!0,multipleOfPrecision:8,schemaId:"auto",unknownFormats:"ignore"});return a.addFormat("data-url",/^data:([a-z]+\/[a-z0-9-+.]+)?;(?:name=(.*);)?base64,(.*)$/),a.addFormat("color",/^(#?([0-9A-Fa-f]{3,4}){1,2}\b|aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow|(rgba?|hsla?)\(.*\))$/),a}function Yi(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return a===null?[]:a.map(function(e){var t=e.dataPath,n=e.keyword,r=e.message,o=e.params,i=e.schemaPath,s="".concat(t);return{name:n,property:s,message:r,params:o,stack:"".concat(s," ").concat(r).trim(),schemaPath:i}})}function Xi(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=a.formData,t=a.schema,n=a.transformErrors,r=a.additionalMetaSchemas,o=r===void 0?[]:r,i=a.customFormats,s=i===void 0?{}:i,c=!fr(nt,o),d=!fr(tt,s);(c||d)&&(Be=Xt()),o&&c&&Array.isArray(o)&&(Be.addMetaSchema(o),nt=o),s&&d&&J(s)&&(Object.keys(s).forEach(function(u){Be.addFormat(u,s[u])}),tt=s);var l=null;try{Be.validate(t,e)}catch(u){l=u}Qt.getCurrentLocalize()(Be.errors);var f=Yi(Be.errors);Be.errors=null;var p=l&&l.message&&typeof l.message=="string"&&l.message.includes("no schema with key or ref ");return p&&(f=[].concat(_e(f),[{stack:l.message}])),typeof n=="function"&&(f=n(f)),{errors:f}}function es(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=a.formData,t=a.schema,n=a.uiSchema,r=a.transformErrors,o=a.additionalMetaSchemas,i=o===void 0?[]:o,s=a.customFormats,c=s===void 0?{}:s,d=a.errorSchema,l=d===void 0?{}:d,f=a.required,p=f===void 0?!1:f,u=a.propPath,v=u===void 0?"":u,g=a.isOnlyFirstError,h=g===void 0?!0:g,b=t.type==="array"&&Array.isArray(e)&&e.length===0,F=e===void 0||b;if(p){if(F){var E={keyword:"required",params:{missingProperty:v}},S=la({schema:t,uiSchema:n,errorSchema:l}).required;return S?E.message=S:Qt.getCurrentLocalize()([E]),[E]}}else if(F&&!b)return[];var D=Xi({formData:e,schema:t,transformErrors:r,additionalMetaSchemas:i,customFormats:c}).errors;D=D.filter(function(C){return C.property===""&&!C.schemaPath.includes("#/anyOf/")&&!C.schemaPath.includes("#/oneOf/")||C.name==="additionalProperties"});var x=la({schema:t,uiSchema:n,errorSchema:l});return(h&&D.length>0?[D[0]]:D).reduce(function(C,I){return I.message=x[I.name]!==void 0?x[I.name]:I.message,C.push(I),C},[])}function fa(a,e){try{return Be.validate(a,e)}catch(t){return!1}}function rs(a,e,t){for(var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,r=0;r<e.length;r++){var o=xe(e[r],t,a);if(o.properties){var i=_(_({},t.definitions?{definitions:t.definitions}:{}),{},{anyOf:Object.keys(o.properties).map(function(f){return{required:[f]}})}),s=void 0;if(o.anyOf){var c=Zr({},o);c.allOf?c.allOf=c.allOf.slice():c.allOf=[],c.allOf.push(i),s=c}else s=Object.assign({},o,i);if(n||delete s.required,fa(s,a))return r}else if(fa(o,a))return r}if(e[0]&&e[0].properties){var d=Object.keys(e[0].properties).find(function(f){return e[0].properties[f].const});if(d){for(var l=0;l<e.length;l++)if(e[l].properties&&e[l].properties[d]&&e[l].properties[d].const===a[d])return l}}return-1}function ca(a,e,t){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,r=rs(a,e,t,n);return r===-1?0:r}function da(a,e){if(Array.isArray(e))return Array.isArray(a)?e.map(function(n,r){return a[r]?da(a[r],n):n}):(console.warn("无效的formData，已覆盖数据",e),a);if(J(e)){var t=Object.assign({},a);return Object.keys(e).reduce(function(n,r){return n[r]=da(a?a[r]:{},e[r]),n},t)}return e}function ar(a,e,t){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,o=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!1,i=J(a)?a:{},s=J(n)?n:{};"allOf"in i&&(i=Kt(i,t,s));var c=e;if(J(c)&&J(i.default))c=Cr(c,i.default);else if("default"in i)c=i.default;else if("$ref"in i){var d=va(i.$ref,t);return ar(d,c,t,s,r,o)}else if(ua(i))c=i.items.map(function(h,b){return ar(h,Array.isArray(e)?e[b]:void 0,t,s,r,o)});else if("oneOf"in i){var l=xe(i.oneOf[ca(s,i.oneOf,t,o)],t,s);i=Cr(i,l),delete i.oneOf}else if("anyOf"in i){var f=xe(i.anyOf[ca(s,i.anyOf,t,o)],t,s);i=Cr(i,f),delete i.anyOf}switch(typeof c=="undefined"&&(c=i.default),ha(i)){case"null":return null;case"object":return Object.keys(i.properties||{}).reduce(function(h,b){var F=ar(i.properties[b],(c||{})[b],t,(s||{})[b],r,o);return(r||F!==void 0)&&(h[b]=F),h},{});case"array":if(Array.isArray(c)&&(c=c.map(function(h,b){return ar(i.items[b]||i.additionalItems||{},h,t,{},r,o)})),Array.isArray(n)&&(c=n.map(function(h,b){return ar(i.items,(c||{})[b],t,h,{},r,o)})),i.minItems){if(Zt(i,t))return c||[];var p=c?c.length:0;if(i.minItems>p){var u=c||[],v=Array.isArray(i.items)?i.additionalItems:i.items,g=Ui(new Array(i.minItems-p),ar(v,v.defaults,t,{},r,o));return u.concat(g)}}c=c===void 0?[]:c}return c}function br(a,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(!J(a))throw new Error("Invalid schema: ".concat(a));var o=xe(a,t,e),i=ar(o,a.default,t,e,n,r);return typeof e=="undefined"?i:J(e)||Array.isArray(e)?da(i,e):e===0||e===!1||e===""?e:e||i}function en(a,e){e===void 0&&(e={});var t=e.insertAt;if(!(!a||typeof document=="undefined")){var n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=a:r.appendChild(document.createTextNode(a))}}var as='.genFromComponent{font-size:14px;line-height:1;word-wrap:break-word;word-break:break-word;padding:0;margin:0}.genFromComponent a,.genFromComponent h1,.genFromComponent h2,.genFromComponent h3,.genFromComponent li,.genFromComponent p,.genFromComponent ul{font-size:14px}.genFromComponent .genFormIcon{width:12px;height:12px;vertical-align:top}.genFromComponent .genFormBtn{display:inline-block;line-height:1;white-space:nowrap;cursor:pointer;background:rgba(var(--primary-color),0);border:1px solid #dcdfe6;color:#606266;-webkit-appearance:none;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;outline:none;margin:0;-webkit-transition:.1s;transition:.1s;font-weight:500;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;padding:12px 20px;font-size:14px;border-radius:4px}.genFromComponent .genFormBtn.is-plain:focus,.genFromComponent .genFormBtn.is-plain:hover{background:#fff;border-color:#409eff;color:#409eff}.genFromComponent .hiddenWidget{display:none}.genFromComponent .fieldGroupWrap+.fieldGroupWrap .fieldGroupWrap_title{margin-top:20px}.genFromComponent .fieldGroupWrap_title{position:relative;display:block;width:100%;line-height:26px;margin-bottom:8px;font-size:15px;font-weight:700;border:0}.genFromComponent .fieldGroupWrap_des{font-size:12px;line-height:20px;margin-bottom:10px;color:#999}.genFromComponent .genFromWidget_des{padding:0;margin-top:0;margin-bottom:2px;font-size:12px;line-height:20px;color:#999;text-align:left;width:100%;-ms-flex-negative:0;flex-shrink:0}.genFromComponent .formItemErrorBox{margin:0 auto;color:#ff5757;padding-top:2px;position:absolute;top:100%;left:0;display:-webkit-box!important;line-height:16px;text-overflow:ellipsis;overflow:hidden;-webkit-box-orient:vertical;-webkit-line-clamp:1;white-space:normal;font-size:12px;text-align:left}.genFromComponent .genFormIcon-qs{fill:#606266;vertical-align:middle;display:inline-block;width:16px;height:16px;margin-left:2px;margin-top:-2px;cursor:pointer}.genFromComponent .genFormItemRequired:before{content:"*";color:#f56c6c;margin-right:4px}.genFromComponent .appendCombining_box{margin-bottom:22px}.genFromComponent .appendCombining_box .appendCombining_box{margin-bottom:10px}.genFromComponent .appendCombining_box{padding:10px;background:hsla(0,0%,94.9%,.8);-webkit-box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 0 3px 1px rgba(0,0,0,.1);box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 0 3px 1px rgba(0,0,0,.1)}.genFromComponent .validateWidget{margin-bottom:0!important;width:100%!important;-ms-flex-preferred-size:100%!important;flex-basis:100%!important;padding:0!important}.genFromComponent .validateWidget .formItemErrorBox{padding:5px 0;position:relative}.genFromComponent .arrayField:not(.genFormItem){margin-bottom:22px}.genFromComponent .arrayField:not(.genFormItem) .arrayField{margin-bottom:10px}.genFromComponent .arrayOrderList{background:rgba(var(--n-color-embedded-popover),0.5);-webkit-box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 0 3px 1px rgba(0,0,0,.1);box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 0 3px 1px rgba(0,0,0,.1)}.genFromComponent .arrayOrderList_item{position:relative;padding:25px 10px 12px;border-radius:2px;margin-bottom:6px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.genFromComponent .arrayOrderList_bottomAddBtn{text-align:right;padding:15px 10px;margin-bottom:10px}.genFromComponent .bottomAddBtn{width:40%;min-width:10px;max-width:180px}.genFromComponent .arrayListItem_content{padding-top:15px;-webkit-box-flex:1;-ms-flex:1;flex:1;margin:0 auto;-webkit-box-shadow:0 -1px 0 0 rgba(0,0,0,.05);box-shadow:0 -1px 0 0 rgba(0,0,0,.05)}.genFromComponent .arrayListItem_index,.genFromComponent .arrayListItem_operateTool{position:absolute;height:25px}.genFromComponent .arrayListItem_index{top:6px;line-height:18px;height:18px;padding:0 6px;background-color:rgba(0,0,0,.28);color:#fff;font-size:12px;border-radius:2px}.genFromComponent .arrayListItem_operateTool{width:75px;right:9px;top:-1px;text-align:right;font-size:0}.genFromComponent .arrayListItem_btn{vertical-align:top;display:inline-block;padding:6px;margin:0;font-size:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;border:none;cursor:pointer;text-align:center;background:transparent;color:#666}.genFromComponent .arrayListItem_btn:hover{opacity:.6}.genFromComponent .arrayListItem_btn[disabled]{color:#999;opacity:.3!important;cursor:not-allowed}.genFromComponent .arrayListItem_orderBtn-bottom,.genFromComponent .arrayListItem_orderBtn-top{background-color:#f0f9eb}.genFromComponent .arrayListItem_btn-delete{background-color:#fef0f0}.genFromComponent .formFooter_item{text-align:right;border-top:1px solid rgba(0,0,0,.08);padding-top:10px}.genFromComponent.formInlineFooter>.fieldGroupWrap{display:inline-block;margin-right:10px}.genFromComponent.formInline .validateWidget{margin-right:0}.genFromComponent.formInline .formFooter_item{border-top:none;padding-top:0}.genFromWidget_des_mini{font-size:14px;line-height:1.5715}.layoutColumn .layoutColumn_w100{width:100%!important;-ms-flex-preferred-size:100%!important;flex-basis:100%!important}.layoutColumn .fieldGroupWrap_box{width:100%;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-align:start;-ms-flex-align:start;align-items:flex-start;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start;-ms-flex-line-pack:start;align-content:flex-start}.layoutColumn .fieldGroupWrap_box>div{width:100%;-ms-flex-preferred-size:100%;flex-basis:100%}.layoutColumn .fieldGroupWrap_box>.genFormItem{-webkit-box-flex:0;-ms-flex-positive:0;flex-grow:0;-ms-flex-negative:0;flex-shrink:0;-webkit-box-sizing:border-box;box-sizing:border-box;padding-right:10px}.layoutColumn.layoutColumn-1 .fieldGroupWrap_box>.genFormItem{padding-right:0}.layoutColumn.layoutColumn-2 .fieldGroupWrap_box>.genFormItem{width:50%;-ms-flex-preferred-size:50%;flex-basis:50%}.layoutColumn.layoutColumn-3 .fieldGroupWrap_box>.genFormItem{width:33.333%;-ms-flex-preferred-size:33.333%;flex-basis:33.333%}';en(as);var ts={formFooter:{type:Object,default:function(){return{show:!0,okBtn:"保存",cancelBtn:"取消"}}},modelValue:{type:null,default:function(){return{}},required:!0},fallbackLabel:{type:Boolean,default:!1},strictMode:{type:Boolean,default:!1},formProps:{type:Object,default:function(){return{}}},schema:{type:Object,default:function(){return{}},required:!0},uiSchema:{type:Object,default:function(){return{}}},customFormats:{type:Object,default:function(){return{}}},customRule:{type:Function,default:null},errorSchema:{type:Object,default:function(){return{}}}},ns={name:"FormFooter",props:{okBtn:{type:String,default:"保存"},okBtnProps:{type:Object,default:function(){return{}}},cancelBtn:{type:String,default:"取消"},formItemAttrs:{type:Object,default:function(){return{}}},globalOptions:{type:Object,default:function(){return{}}}},emits:["cancel","submit"],setup:function(e,t){var n=t.emit,r=e.globalOptions.COMPONENT_MAP;return function(){return N(ee(r.formItem),_({class:{formFooter_item:!0}},e.formItemAttrs),{default:function(){return[N(ee(r.button),{onClick:function(){n("cancel")}},{default:function(){return e.cancelBtn}}),N(ee(r.button),_({style:{marginLeft:"10px"},type:"primary",onClick:function(){n("submit")}},e.okBtnProps),{default:function(){return e.okBtn}})]}})}}},wr={name:"FieldGroupWrap",inject:["genFormProvide"],props:{curNodePath:{type:String,default:""},showTitle:{type:Boolean,default:!0},showDescription:{type:Boolean,default:!0},title:{type:String,default:""},description:{type:String,default:""}},computed:{trueTitle:function(){var e,t=this.title;if(t)return t;var n;if(typeof((e=this.genFormProvide.fallbackLabel)===null||e===void 0?void 0:e.value)=="boolean"){var r;n=(r=this.genFormProvide.fallbackLabel)===null||r===void 0?void 0:r.value}else n=this.genFormProvide.fallbackLabel;var o=n&&this.curNodePath.split(".").pop();return o!=="".concat(Number(o))?o:""}}},os={class:"fieldGroupWrap"},is={key:0,class:"fieldGroupWrap_title"},ss={class:"fieldGroupWrap_box"};function ls(a,e,t,n,r,o){return Qe(),He("div",os,[t.showTitle&&o.trueTitle?(Qe(),He("h3",is,yn(o.trueTitle),1)):za("v-if",!0),t.showDescription&&t.description?(Qe(),He("p",{key:1,class:"fieldGroupWrap_des",innerHTML:t.description},null,8,["innerHTML"])):za("v-if",!0),or("div",ss,[Pn(a.$slots,"default")])])}wr.render=ls;wr.__file="utils/components/FieldGroupWrap.vue";var Ae={formProps:{type:null},globalOptions:{type:null},schema:{type:Object,default:function(){return{}}},uiSchema:{type:Object,default:function(){return{}}},errorSchema:{type:Object,default:function(){return{}}},customRule:{type:Function,default:null},customFormats:{type:Object,default:function(){return{}}},rootSchema:{type:Object,default:function(){return{}}},rootFormData:{type:null,default:function(){return{}}},curNodePath:{type:String,default:""},required:{type:Boolean,default:!1},needValidFieldGroup:{type:Boolean,default:!0}},us={class:"genFormIcon genFormIcon-down",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},fs=or("path",{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"},null,-1);function cs(a,e){return Qe(),He("svg",us,[fs])}var Fa={};Fa.render=cs;Fa.__file="utils/icons/IconCaretDown.vue";var ds={class:"genFormIcon genFormIcon-up",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ms=or("path",{d:"M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"},null,-1);function hs(a,e){return Qe(),He("svg",ds,[ms])}var Oa={};Oa.render=hs;Oa.__file="utils/icons/IconCaretUp.vue";var vs={class:"genFormIcon genFormIcon-close",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ps=or("path",{d:`M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1
            191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0
            0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z`},null,-1);function gs(a,e){return Qe(),He("svg",vs,[ps])}var Da={};Da.render=gs;Da.__file="utils/icons/IconClose.vue";var ys={class:"genFormIcon genFormIcon-plus",t:"1551322312294",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"10297","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"200",height:"200"},Ps=or("path",{d:"M474 152m8 0l60 0q8 0 8 8l0 704q0 8-8 8l-60 0q-8 0-8-8l0-704q0-8 8-8Z","p-id":"10298"},null,-1),bs=or("path",{d:"M168 474m8 0l672 0q8 0 8 8l0 60q0 8-8 8l-672 0q-8 0-8-8l0-60q0-8 8-8Z","p-id":"10299"},null,-1);function Es(a,e){return Qe(),He("svg",ys,[Ps,bs])}var _a={};_a.render=Es;_a.__file="utils/icons/IconPlus.vue";var Ss={class:"genFormIcon genFormIcon-qs",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},ws=or("path",{d:`M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 708c-22.1
            0-40-17.9-40-40s17.9-40 40-40 40 17.9 40 40-17.9 40-40 40zm62.9-219.5a48.3 48.3 0 0
            0-30.9 44.8V620c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8v-21.5c0-23.1 6.7-45.9 19.9-64.9 12.9-18.6 30.9-32.8
            52.1-40.9 34-13.1 56-41.6 56-72.7 0-44.1-43.1-80-96-80s-96 35.9-96 80v7.6c0 4.4-3.6
            8-8 8h-48c-4.4 0-8-3.6-8-8V420c0-39.3 17.2-76 48.4-103.3C430.4 290.4 470 276 512 276s81.6 14.5 111.6
            40.7C654.8 344 672 380.7 672 420c0 57.8-38.1 109.8-97.1 132.5z`},null,-1);function Fs(a,e){return Qe(),He("svg",Ss,[ws])}var xa={};xa.render=Fs;xa.__file="utils/icons/IconQuestion.vue";var Ze={name:"Widget",props:{isFormData:{type:Boolean,default:!0},curValue:{type:null,default:0},schema:{type:Object,default:function(){return{}}},uiSchema:{type:Object,default:function(){return{}}},errorSchema:{type:Object,default:function(){return{}}},customFormats:{type:Object,default:function(){return{}}},customRule:{type:Function,default:null},widget:{type:[String,Function,Object],default:null},required:{type:Boolean,default:!1},uiRequired:{type:Boolean},emptyValue:{type:null,default:void 0},rootFormData:{type:null},curNodePath:{type:String,default:""},label:{type:String,default:""},width:{type:String,default:""},labelWidth:{type:String,default:""},description:{type:String,default:""},widgetAttrs:{type:Object,default:function(){return{}}},widgetClass:{type:Object,default:function(){return{}}},widgetStyle:{type:Object,default:function(){return{}}},fieldAttrs:{type:Object,default:function(){return{}}},fieldClass:{type:Object,default:function(){return{}}},fieldStyle:{type:Object,default:function(){return{}}},uiProps:{type:Object,default:function(){return{}}},formProps:null,getWidget:null,renderScopedSlots:null,globalOptions:null,onChange:null},emits:["otherDataChange"],inheritAttrs:!0,setup:function(e,t){var n=t.emit,r=gn("genFormProvide"),o=Le({get:function(){return e.isFormData?Ue(e.rootFormData,e.curNodePath):e.curValue},set:function(d){var l=d===""||d===null?e.emptyValue:d;e.isFormData?jr(e.rootFormData,e.curNodePath,l):n("otherDataChange",l)}}),i=Le(function(){var c;return(c=e.uiRequired)!==null&&c!==void 0?c:e.required});e.uiProps.enumOptions&&e.uiProps.enumOptions.length>0&&o.value===void 0&&o.value!==e.uiProps.enumOptions[0]&&(e.schema.items?o.value=[]:i.value&&e.formProps.defaultSelectFirstOption&&(o.value=e.uiProps.enumOptions[0].value));var s=dr(null);return typeof e.getWidget=="function"&&ur(s,function(){e.getWidget.call(null,s.value)}),function(){var c,d=Cn(e.curNodePath),l=e.formProps&&e.formProps.isMiniDes,f=l!=null?l:e.globalOptions.HELPERS.isMiniDes(e.formProps),p=e.description?N("div",{innerHTML:e.description,class:{genFromWidget_des:!0,genFromWidget_des_mini:f}}):null,u=e.globalOptions.COMPONENT_MAP,v=f&&p?N(ee(u.popover),_({style:{margin:"0 2px",fontSize:"16px",cursor:"pointer"},placement:"top",trigger:"hover"},(c=e.formProps)===null||c===void 0?void 0:c.popover),{default:function(){return p},reference:function(){return N(xa)}}):null,g=_(_({},e.fieldStyle),e.width?{width:e.width,flexBasis:e.width,paddingRight:"10px"}:{}),h=Zi(e.label,e.widget&&r.fallbackLabel.value,e.curNodePath);return N(ee(u.formItem),_(_(_({class:_(_({},e.fieldClass),{},{genFormItem:!0}),style:g},e.fieldAttrs),e.labelWidth?{labelWidth:e.labelWidth}:{}),e.isFormData?{prop:d?"__$$root":e.curNodePath,rules:[{validator:function(F,E,S){d&&(E=e.rootFormData);var D=es({formData:E,schema:e.schema,uiSchema:e.uiSchema,customFormats:e.customFormats,errorSchema:e.errorSchema,required:i.value,propPath:e.curNodePath});if(D.length>0)return S?S(D[0].message):Promise.reject(D[0].message);var x=e.customRule;return x&&typeof x=="function"?x({field:e.curNodePath,value:E,rootFormData:e.rootFormData,callback:S}):S?S():Promise.resolve()},trigger:"change"}]}:{}),_(_({error:function(F){return F.error?N("div",{class:{formItemErrorBox:!0},title:F.error},[F.error]):null}},h?{label:function(){return N("span",{class:{genFormLabel:!0,genFormItemRequired:i.value}},["".concat(h)].concat(_e(v?[v]:[]),["".concat(e.formProps&&e.formProps.labelSuffix||"")]))}}:{}),{},{default:function(F){return[].concat(_e(!f&&p?[p]:[]),_e(e.widget?[N(ee(e.widget),_(_(_({style:e.widgetStyle,class:e.widgetClass},e.widgetAttrs),e.uiProps),{},{modelValue:o.value,ref:s,"onUpdate:modelValue":function(S){var D=o.value;D!==S&&(o.value=S,e.onChange&&e.onChange({curVal:S,preVal:D,parentFormData:Ue(e.rootFormData,e.curNodePath,1),rootFormData:e.rootFormData}))}},F?function(){return Object.keys(F).reduce(function(E,S){return E[S]=F[S],[e.widgetAttrs[S],e.uiProps[S]].forEach(function(D){D&&typeof D=="function"&&(E[S]=function(){D.apply(void 0,arguments),E[S].apply(E,arguments)})}),E},{})}():{}),_({},e.renderScopedSlots?typeof e.renderScopedSlots=="function"?e.renderScopedSlots():e.renderScopedSlots:{}))]:[]))}}))}}},Os={name:"ObjectField",props:Ae,setup:function(e){var t=function(o){return Array.isArray(e.schema.required)&&!!~e.schema.required.indexOf(o)},n=function(o){var i=!1,s=!1;return J(e.schema.dependencies)&&(s=Object.entries(e.schema.dependencies).some(function(c){var d=Je(c,2),l=d[0],f=d[1],p=!!(Array.isArray(f)&&~f.indexOf(o));return i=i||p,p&&Ue(e.rootFormData,e.curNodePath)[l]!==void 0})),{isDependency:i,curDependent:s}};return function(){var r=e.curNodePath,o=tr({schema:e.schema,uiSchema:e.uiSchema,curNodePath:r,rootFormData:e.rootFormData}),i=o.title,s=o.description,c=o.showTitle,d=o.showDescription,l=o.order,f=o.fieldClass,p=o.fieldAttrs,u=o.fieldStyle,v=o.onlyShowIfDependent,g=Object.keys(e.schema.properties||{}),h=Ki(g,l),b=h.map(function(F){var E=t(F),S=n(F),D=S.isDependency,x=S.curDependent;return D&&v&&!x?null:N(nr,_(_({key:F},e),{},{schema:e.schema.properties[F],uiSchema:e.uiSchema[F],errorSchema:e.errorSchema[F],required:E||x,curNodePath:Nr(r,F)}))});return N(wr,_({title:i,description:s,showTitle:c,showDescription:d,curNodePath:r,class:_({},f),style:u},p),{default:function(){return[].concat(_e(b),_e(e.needValidFieldGroup?[N(Ze,{key:"validateWidget-object",class:{validateWidget:!0,"validateWidget-object":!0},schema:Object.entries(e.schema).reduce(function(E,S){var D=Je(S,2),x=D[0],C=D[1];return(e.schema.additionalProperties===!1||!["properties","id","$id"].includes(x))&&(E[x]=C),E},{}),uiSchema:e.uiSchema,errorSchema:e.errorSchema,curNodePath:r,rootFormData:e.rootFormData,globalOptions:e.globalOptions})]:[]))}})}}},Aa={name:"StringField",props:Ae,setup:function(e,t){var n=t.attrs,r=Le(function(){var o=Ur(e.schema)&&wa(e.schema,e.uiSchema,e.curNodePath,e.rootFormData),i=Sr({schema:e.schema,uiSchema:e.uiSchema,curNodePath:e.curNodePath,rootFormData:e.rootFormData},function(){var s=e.schema.type==="number"||e.schema.type==="integer";return{widget:o?e.globalOptions.WIDGET_MAP.common.select:e.globalOptions.WIDGET_MAP.formats[e.schema.format]||(s?e.globalOptions.WIDGET_MAP.types.number:e.globalOptions.WIDGET_MAP.types.string)}});return o&&!i.uiProps.enumOptions&&(i.uiProps.enumOptions=o),i});return function(){return N(Ze,_(_(_({},e),n),r.value))}}},Ds={name:"NumberField",props:Ae,setup:function(e,t){var n=t.attrs;return function(){return N(Aa,_(_({},e),n))}}},_s={name:"IntegerField",props:Ae,setup:function(e,t){var n=t.attrs;return function(){return N(Aa,_(_({},e),n))}}},xs={name:"BooleanField",props:Ae,setup:function(e,t){var n=t.attrs;return function(){var r=e.schema,o=e.uiSchema,i=e.curNodePath,s=e.rootFormData,c=e.globalOptions,d=wa({enumNames:r.enumNames||["true","false"],enum:r.enum||[!0,!1]},o,i,s),l=Sr({schema:r,uiSchema:o,curNodePath:i,rootFormData:s},function(){return{widget:c.WIDGET_MAP.types.boolean}});return l.uiProps.enumOptions=l.uiProps.enumOptions||d,N(Ze,_(_(_({},n),e),l))}}},rn={name:"ArrayOrderList",emits:["arrayOperate"],props:{vNodeList:{type:Array,default:[]},tupleItemsLength:{type:Number,default:0},addable:{type:Boolean,default:!0},showIndexNumber:{type:Boolean,default:!1},sortable:{type:Boolean,default:!0},removable:{type:Boolean,default:!0},maxItems:{},minItems:{},globalOptions:null},setup:function(e,t){var n=t.emit,r=Le(function(){var i=e.addable,s=e.maxItems,c=e.vNodeList;return i?s!==void 0?c.length<s:!0:!1}),o=Le(function(){var i=e.removable,s=e.minItems,c=e.vNodeList;return i?s!==void 0?c.length>s:!0:!1});return function(){return e.vNodeList.length<=0&&!e.addable?null:N("div",{class:{arrayOrderList:!0}},e.vNodeList.map(function(i,s){var c=i.key,d=i.vNode,l=e.tupleItemsLength+s,f=s+1;return N("div",{key:c,class:{arrayOrderList_item:!0}},[e.showIndexNumber?N("div",{class:{arrayListItem_index:!0}},f):null,N("div",{class:{arrayListItem_operateTool:!0}},[N("button",{style:_({},e.sortable?{}:{display:"none"}),class:{arrayListItem_btn:!0,"arrayListItem_orderBtn-top":!0},type:"button",disabled:!e.sortable||s===0,onClick:function(){n("arrayOperate",{command:"moveUp",data:{index:l}})}},[N(Oa)]),N("button",{style:_({},e.sortable?{}:{display:"none"}),class:{arrayListItem_btn:!0,"arrayListItem_orderBtn-bottom":!0},type:"button",disabled:!e.sortable||s===e.vNodeList.length-1,onClick:function(){n("arrayOperate",{command:"moveDown",data:{index:l}})}},[N(Fa)]),N("button",{style:_({},e.removable?{}:{display:"none"}),class:{arrayListItem_btn:!0,"arrayListItem_btn-delete":!0},type:"button",disabled:!o.value,onClick:function(){n("arrayOperate",{command:"remove",data:{index:l}})}},[N(Da)])]),N("div",{class:{arrayListItem_content:!0}},[d])])}).concat([N("p",{style:_({},r.value?{}:{display:"none"}),class:{arrayOrderList_bottomAddBtn:!0}},[N("button",{class:{bottomAddBtn:!0,"is-plain":!0,genFormBtn:!0},type:"button",onClick:function(){n("arrayOperate",{command:"add"})}},[N(_a,{style:{marginRight:"5px"}}),e.maxItems?"( ".concat(e.vNodeList.length," / ").concat(e.maxItems," )"):""])])]))}}},As={name:"ArrayFieldNormal",props:_(_({},Ae),{},{itemsFormData:{type:Array}}),setup:function(e,t){var n=t.attrs;return function(){var r=e.schema,o=e.uiSchema,i=e.curNodePath,s=e.rootFormData,c=e.itemsFormData,d=e.errorSchema,l=e.globalOptions,f=tr({schema:r,uiSchema:o,curNodePath:i,rootFormData:s}),p=f.title,u=f.description,v=f.addable,g=f.showIndexNumber,h=f.sortable,b=f.removable,F=f.showTitle,E=f.showDescription,S=f.fieldClass,D=f.fieldAttrs,x=f.fieldStyle,C=c.map(function(I,R){var T=Jt({schema:r.items,uiSchema:o.items},R);return{key:I.key,vNode:N(nr,_(_({key:I.key},e),{},{schema:r.items,required:![].concat(r.items.type).includes("null"),uiSchema:_(_({},o.items),T),errorSchema:d.items,curNodePath:Nr(i,R)}))}});return N(wr,{title:p,description:u,showTitle:F,showDescription:E,curNodePath:i,class:S,attrs:D,style:x},{default:function(){return N(rn,_(_({},n),{},{vNodeList:C,showIndexNumber:g,addable:v,sortable:h,removable:b,maxItems:r.maxItems,minItems:r.minItems,globalOptions:l}))}})}}},ot={name:"ArrayFieldMultiSelect",props:_({},Ae),setup:function(e,t){var n=t.attrs;return function(){var r=e.schema,o=e.rootSchema,i=e.uiSchema,s=e.curNodePath,c=e.rootFormData,d=e.globalOptions,l=xe(r.items,o),f=wa(l,i,s,c),p=Sr({schema:r,uiSchema:i,curNodePath:s,rootFormData:c},function(){return{widget:d.WIDGET_MAP.common.checkboxGroup}});return p.uiProps.multiple=!0,f&&!p.uiProps.enumOptions&&(p.uiProps.enumOptions=f),N(Ze,_(_(_({},n),e),p))}}},$s={name:"ArrayFieldTuple",props:_(_({},Ae),{},{itemsFormData:{type:Array,default:function(){return[]}}}),emits:["arrayOperate"],setup:function(e,t){var n=t.emit;t.attrs;var r=function(){var i=!Array.isArray(e.itemsFormData);if(i||e.itemsFormData.length<e.schema.items.length){var s=br(e.schema,void 0,e.rootSchema);i?n("arrayOperate",{command:"setNewTarget",data:{newTarget:s}}):n("arrayOperate",{command:"batchPush",data:{pushArray:s.slice(e.itemsFormData.length)}})}};return r(),function(){if(!Array.isArray(e.itemsFormData))return null;var o=e.schema,i=e.uiSchema,s=e.errorSchema,c=e.curNodePath,d=e.globalOptions,l=tr({schema:o,uiSchema:i,curNodePath:c,rootFormData:e.rootFormData}),f=l.title,p=l.description,u=l.addable,v=l.showIndexNumber,g=l.sortable,h=l.removable,b=l.showTitle,F=l.showDescription,E=l.fieldClass,S=l.fieldAttrs,D=l.fieldStyle,x=zi(e.itemsFormData,e.schema.items.length-1),C=x[0].map(function(T,j){return N(nr,_(_({key:T.key},e),{},{required:![].concat(o.items[j].type).includes("null"),schema:o.items[j],uiSchema:i.items?i.items[j]:{},errorSchema:s.items?s.items[j]:{},curNodePath:Nr(c,j)}))}),I=x[1].map(function(T,j){var L=Jt({schema:o.additionalItems,uiSchema:i.additionalItems},j);return{key:T.key,vNode:N(nr,_(_({key:T.key},e),{},{schema:o.additionalItems,required:![].concat(o.additionalItems.type).includes("null"),uiSchema:_(_({},i.additionalItems),L),errorSchema:s.additionalItems,curNodePath:Nr(e.curNodePath,j+o.items.length)}))}}),R=(u===void 0?!0:u)&&Yt(e.schema);return N(wr,_(_({title:f,description:p,showTitle:b,showDescription:F,curNodePath:c},S),{},{class:E,style:D}),{default:function(){return[].concat(_e(C),[N(rn,{onArrayOperate:function(){for(var L=arguments.length,$=new Array(L),A=0;A<L;A++)$[A]=arguments[A];return n.apply(void 0,["arrayOperate"].concat($))},vNodeList:I,tupleItemsLength:o.items.length,addable:R,showIndexNumber:v,sortable:g,removable:h,maxItems:o.maxItems,minItems:o.minItems,globalOptions:d})])}})}}},it={name:"ArrayFieldSpecialFormat",props:Ae,setup:function(e,t){var n=t.attrs,r=Le(function(){return Sr({schema:_({"ui:widget":e.globalOptions.WIDGET_MAP.formats[e.schema.format]},e.schema),uiSchema:e.uiSchema,curNodePath:e.curNodePath,rootFormData:e.rootFormData})});return function(){return N(Ze,_(_(_({},n),e),r.value))}}},Is={name:"ArrayField",props:Ae,setup:function(e){var t=this,n=function(){var f=e.rootFormData,p=e.curNodePath,u=Ue(f,p);return Array.isArray(u)?u:(console.error("error: type array，值必须为 array 类型"),[])},r=dr(n().map(function(){return Pr()})),o=Le(function(){return n()});ur(o,function(l,f){l!==f&&qa(l)!==qa(f)&&Array.isArray(l)&&(r.value=l.map(function(){return Pr()}))},{deep:!0});var i=Le(function(){return o.value.map(function(l,f){return{key:r.value[f],value:l}})}),s=Le(function(){return Mr({schema:e.schema,uiSchema:e.uiSchema,curNodePath:e.curNodePath,rootFormData:e.rootFormData})}),c=function(){var f=e.schema,p=e.rootSchema,u=f.items;return ua(f)&&Yt(f)&&(u=f.additionalItems),br(u,void 0,p)},d=function(f){var p=f.command,u=f.data,v={moveUp:function(E,S){var D=S.index;ki(E,D)},moveDown:function(E,S){var D=S.index;Vi(E,D)},remove:function(E,S){var D=S.index;Mi(E,D)},add:function(E,S){var D=S.newRowData;E.push(D)},batchPush:function(E,S){var D=S.pushArray;D.forEach(function(x){E.push(x)})},setNewTarget:function(E,S){var D=S.formData,x=S.nodePath,C=S.newTarget;jr(D,x,C)}},g=v[p];if(g){var h=u,b=u;p==="add"?(h={newRowData:c()},b={newRowData:Pr()}):p==="batchPush"?b={pushArray:h.pushArray.map(function(F){return Pr()})}:p==="setNewTarget"&&(h={formData:e.rootFormData,nodePath:e.curNodePath,newTarget:h.newTarget},b={formData:r,nodePath:"value",newTarget:h.newTarget.map(function(F){return Pr()})}),g.apply(null,[r.value,b]),g.apply(null,[o.value,h]),s.value.afterArrayOperate&&t.uiOptions.afterArrayOperate.call(null,o.value,p,u)}else throw new Error("错误 - 未知的操作：[".concat(p,"]"))};return function(){var l=e.schema,f=e.uiSchema,p=e.rootSchema,u=e.rootFormData,v=e.curNodePath,g=e.globalOptions;if(!l.hasOwnProperty("items"))throw new Error("[".concat(l,"] 请先定义 items属性"));if(Zt(l,p))return N(ot,_(_({},e),{},{class:G({},Rr(ot.name),!0)}));if(l.format||l["ui:widget"]||f["ui:widget"])return N(it,_(_({},e),{},{class:G({},Rr(it.name),!0)}));var h=ua(l)?$s:As;return N("div",[N(h,_(_({itemsFormData:i.value},e),{},{onArrayOperate:d,class:G({},Rr(h.name),!0)})),e.needValidFieldGroup?N(Ze,{key:"validateWidget-array",class:{validateWidget:!0,"validateWidget-array":!0},schema:Object.entries(l).reduce(function(b,F){var E=Je(F,2),S=E[0],D=E[1];return S!=="items"&&(b[S]=D),b},{}),uiSchema:f,errorSchema:e.errorSchema,curNodePath:v,rootFormData:u,globalOptions:g}):null])}}},an={name:"SelectLinkageField",props:_(_({},Ae),{},{combiningType:{type:String,default:"anyOf"},selectList:{type:Array,require:!0}}),setup:function(e){var t=function(i){var s=ca(i,e.selectList,e.rootSchema,!0);return s||0},n=dr(t(Ue(e.rootFormData,e.curNodePath))),r=function(){var i=Sr({schema:e.schema["".concat(e.combiningType,"Select")]||{},uiSchema:e.uiSchema["".concat(e.combiningType,"Select")]||{},curNodePath:e.curNodePath,rootFormData:e.rootFormData},function(){return{widget:"SelectWidget"}});if(i.label=i.label||e.schema.title,i.description=i.description||e.schema.description,!i.uiProps.enumOptions){var s=e.uiSchema[e.combiningType]||[];i.uiProps.enumOptions=e.selectList.map(function(c,d){var l=tr({schema:c,uiSchema:s[d],containsSpec:!1});return{label:l.title||"选项 ".concat(d+1),value:d}})}return N(Ze,_(_({key:"fieldSelect_".concat(e.combiningType),class:G({},"fieldSelect_".concat(e.combiningType),!0),isFormData:!1,curValue:n.value,curNodePath:e.curNodePath,rootFormData:e.rootFormData,globalOptions:e.globalOptions},i),{},{onOtherDataChange:function(d){n.value=d}}))};return ur(n,function(o,i){var s=Ue(e.rootFormData,e.curNodePath),c=br(e.selectList[o],void 0,e.rootSchema),d=Object.prototype.hasOwnProperty;if(J(s)){var l=xe(e.selectList[i],e.rootSchema);if(ha(l)==="object")for(var f in l.properties)d.call(l.properties,f)&&!d.call(c,f)&&Rn(s,f)}J(c)?Object.entries(c).forEach(function(p){var u=Je(p,2),v=u[0],g=u[1];g!==void 0&&(s[v]===void 0||J(g)||function(){var h,b=xe(e.selectList[o],e.rootSchema);return((h=b.properties[v])===null||h===void 0?void 0:h.const)!==void 0}())&&jr(s,v,g)}):jr(e.rootFormData,e.curNodePath,c===void 0&&fa(xe(e.selectList[o],e.rootSchema),s)?s:c)}),function(){var o,i=e.curNodePath,s=ft(i),c=e.schema.type==="object"||e.schema.properties,d=[r()],l=e.selectList[n.value];if(l){var f=e.schema,p=e.combiningType,u="".concat(e.combiningType,"Select");f.properties,f[p],f[u];var v=we(f,["properties",p,u].map(In));l=Object.assign({},v,l)}var g=c&&Ba(l&&l.properties);if(l&&!g){var h=Qa(tr({schema:e.schema,uiSchema:e.uiSchema,containsSpec:!1,curNodePath:i,rootFormData:e.rootFormData}),function(C){return C===e.combiningType?void 0:"ui:".concat(C)}),b=Qa(la({schema:e.schema,uiSchema:e.uiSchema,errorSchema:e.errorSchema}),function(C){return C===e.combiningType?void 0:"err:".concat(C)});d.push(N(nr,_(_({key:"appendSchema_".concat(e.combiningType)},e),{},{schema:_({"ui:showTitle":!1,"ui:showDescription":!1},l),required:e.required,uiSchema:_(_({},h),(e.uiSchema[e.combiningType]||[])[n.value]),errorSchema:_(_({},b),(e.errorSchema[e.combiningType]||[])[n.value])})))}var F=null;if(c&&!Ba(e.schema.properties)){var E,S=l;S.title,S.description,S.properties;var D=we(S,["title","description","properties"]),x=Object.assign({},e.schema,D);delete x[e.combiningType],F=N(nr,_(_({key:"origin_".concat(e.combiningType),class:(E={},G(E,"".concat(e.combiningType,"_originBox"),!0),G(E,"".concat(s,"-originBox"),!0),E)},e),{},{schema:x}))}return d.push(N(Ze,{key:"validateWidget-".concat(e.combiningType),class:G({validateWidget:!0},"validateWidget-".concat(e.combiningType),!0),schema:e.schema,uiSchema:e.uiSchema,errorSchema:e.errorSchema,curNodePath:e.curNodePath,rootFormData:e.rootFormData,globalOptions:e.globalOptions})),N("div",[F,N("div",{key:"appendBox_".concat(e.combiningType),class:(o={appendCombining_box:!0},G(o,"".concat(e.combiningType,"_appendBox"),!0),G(o,"".concat(s,"-appendBox"),!0),o)},d)])}}},Cs={name:"AnyOfField",setup:function(e,t){var n=t.attrs,r=t.slots;return function(){return N(an,_(_({},n),{},{combiningType:"anyOf",selectList:n.schema.anyOf}),r)}}},Rs={name:"oneOfField",setup:function(e,t){var n=t.attrs,r=t.slots;return function(){return N(an,_(_({},n),{},{combiningType:"oneOf",selectList:n.schema.oneOf}),r)}}},Jr={array:Is,boolean:xs,integer:_s,number:Ds,object:Os,string:Aa,null:{render:function(){return null}},anyOf:Cs,oneOf:Rs},nr={name:"SchemaField",props:Ae,setup:function(e){return function(){var t,n=xe(e.schema,e.rootSchema),r=_(_({},e),{},{schema:n});if(Object.keys(n).length===0)return null;var o=Hi(Jr,r),i=o.field,s=o.fieldProps,c=Qi({schema:n,uiSchema:e.uiSchema,curNodePath:e.curNodePath,rootFormData:e.rootFormData}),d=ft(e.curNodePath);if(n.anyOf&&n.anyOf.length>0&&!Ur(n)){var l;return N(ee(Jr.anyOf),_({class:(l={},G(l,"".concat(d,"-anyOf"),!0),G(l,"fieldItem",!0),G(l,"anyOfField",!0),l)},r))}if(n.oneOf&&n.oneOf.length>0&&!Ur(n)){var f;return N(ee(Jr.oneOf),_({class:(f={},G(f,"".concat(d,"-oneOf"),!0),G(f,"fieldItem",!0),G(f,"oneOfField",!0),f)},r))}return i&&!c?N(ee(i),_(_({},r),{},{fieldProps:s,class:(t={},G(t,Rr(i.name)||i,!0),G(t,"hiddenWidget",c),G(t,"fieldItem",!0),G(t,d,!0),t)})):null}}};function Ns(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e={name:"VueForm",props:ts,emits:["update:modelValue","change","cancel","submit","validation-failed","form-mounted"],setup:function(n,r){var o=r.slots,i=r.emit,s=lt();!e.installed&&a.WIDGET_MAP.widgetComponents&&(Object.entries(a.WIDGET_MAP.widgetComponents).forEach(function(g){var h=Je(g,2),b=h[0],F=h[1];return s.appContext.app.component(b,F)}),e.installed=!0);var c=bn(n,"fallbackLabel");En("genFormProvide",{fallbackLabel:c});var d=dr(br(n.schema,n.modelValue,n.schema,n.strictMode)),l=Le(function(){return _({show:!0,okBtn:"保存",okBtnProps:{},cancelBtn:"取消"},n.formFooter)}),f=null,p=function(h,b){i("update:modelValue",h),i("change",{newValue:h,oldValue:b})},u=function(h,b){if(!fr(h,b)){var F=br(n.schema,n.modelValue,n.schema,n.strictMode);fr(d.value,F)||(d.value=F)}};ur(d,function(g,h){p(g,h)},{deep:!0}),ur(function(){return n.schema},function(g,h){u(g,h)}),ur(function(){return n.modelValue},function(g,h){u(g,h)}),p(d.value,n.modelValue);var v=function(){return o.default?o.default({formData:d,formRefFn:function(){return f}}):l.value.show?N(ns,{globalOptions:a,okBtn:l.value.okBtn,okBtnProps:l.value.okBtnProps,cancelBtn:l.value.cancelBtn,formItemAttrs:l.value.formItemAttrs,onCancel:function(){i("cancel")},onSubmit:function(){(f.$$validate||f.validate)(function(b,F){return b?i("submit",d):(console.warn(F),i("validation-failed",F))})}}):[]};return function(){var g,h=n.formProps,b=h.layoutColumn,F=b===void 0?1:b,E=h.inlineFooter;h.labelSuffix,h.isMiniDes,h.defaultSelectFirstOption,h.popover;var S=we(h,["layoutColumn","inlineFooter","labelSuffix","isMiniDes","defaultSelectFirstOption","popover"]),D=S.inline,x=D===void 0?!1:D,C=S.labelPosition,I=C===void 0?"top":C,R={schema:n.schema,uiSchema:n.uiSchema,errorSchema:n.errorSchema,customFormats:n.customFormats,customRule:n.customRule,rootSchema:n.schema,rootFormData:d.value,curNodePath:"",globalOptions:a,formProps:_({labelPosition:I,labelSuffix:"：",defaultSelectFirstOption:!0,inline:x},n.formProps)};return N(ee(a.COMPONENT_MAP.form),_({class:(g={genFromComponent:!0,formInlineFooter:E,formInline:x},G(g,"genFromComponent_".concat(n.schema.id,"Form"),!!n.schema.id),G(g,"layoutColumn",!x),G(g,"layoutColumn-".concat(F),!x),g),setFormRef:function(j){f=j,s.ctx.$$uiFormRef=f,i("form-mounted",j,{formData:d.value})},onSubmit:function(j){j.preventDefault()},model:d,labelPosition:I,inline:x},S),{default:function(){return[N(nr,R),v()]}})}}};return e.install=function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};t.component(n.name||e.name,e)},e}var js={name:"CheckboxesWidget",props:{enumOptions:{default:function(){return[]},type:[Array]}},setup:function(e,t){var n=t.attrs;return function(){return N(ee("n-checkbox-group"),n,{default:function(){return N(ee("n-space"),{itemStyle:"display: flex"},{default:function(){return e.enumOptions.map(function(i,s){return N(ee("n-checkbox"),{key:s,value:i.value},{default:function(){return i.label}})})}})}})}}},Ls=$e(js,{model:"value"}),Ts={name:"RadioWidget",props:{enumOptions:{default:function(){return[]},type:[Array]}},setup:function(e,t){var n=t.attrs;return function(){return N(ee("n-radio-group"),n,{default:function(){return e.enumOptions.map(function(o,i){return N(ee("n-radio"),{key:i,value:o.value},{default:function(){return o.label}})})}})}}},ks=$e(Ts,{model:"value"}),Vs={name:"SelectWidget",props:{enumOptions:{default:function(){return[]},type:[Array]}},setup:function(e,t){var n=t.attrs;return function(){return N(ee("n-select"),_({options:e.enumOptions},n))}}},Ms=$e(Vs,{model:"value"}),Us={name:"DatePickerWidget",inheritAttrs:!1,setup:function(e,t){var n=t.attrs;return function(){var r=n.isNumberValue,o=n.isRange,i=n.modelValue,s=n["onUpdate:modelValue"],c=we(n,["isNumberValue","isRange","modelValue","onUpdate:modelValue"]),d=o&&i&&i.length===0?null:i;return N(ee("n-date-picker"),_(_({type:o?"daterange":"date"},c),r?{value:d,onUpdateValue:s}:{valueFormat:r?"T":"yyyy-MM-dd",formattedValue:d,onUpdateFormattedValue:s}))}}},zs={name:"DatePickerWidget",inheritAttrs:!1,setup:function(e,t){var n=t.attrs;return function(){var r=n.isNumberValue,o=n.isRange,i=n.modelValue,s=n["onUpdate:modelValue"],c=we(n,["isNumberValue","isRange","modelValue","onUpdate:modelValue"]),d=o&&i&&i.length===0?null:i;return N(ee("n-date-picker"),_(_({type:o?"datetimerange":"datetime"},c),r?{value:d,onUpdateValue:s}:{valueFormat:r?"T":"yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",formattedValue:d,onUpdateFormattedValue:s}))}}},qs={name:"TimePickerWidget",inheritAttrs:!1,setup:function(e,t){var n=t.attrs;return function(){var r=n.modelValue,o=n["onUpdate:modelValue"],i=we(n,["modelValue","onUpdate:modelValue"]);return N(ee("n-time-picker"),_(_({},i),{},{valueFormat:"HH:mm:ss",formattedValue:r,onUpdateFormattedValue:o}))}}},Ws={name:"UploadWidget",props:{modelValue:{default:null,type:[String,Array]},responseFileUrl:{default:function(){return function(e){return e?e.url||e.data&&e.data.url:""}},type:[Function]},btnText:{type:String,default:"点击上传"},slots:{type:null,default:null}},setup:function(e,t){var n=t.attrs,r=t.emit,o=e.modelValue,i=Array.isArray(o),s=n.fileList||function(){return i?o.map(function(p,u){return{id:String(u),status:"finished",name:"已上传文件（".concat(u+1,"）"),url:p}}):o?[{id:"1",status:"finished",name:"已上传文件",url:o}]:[]}(),c=dr(s),d=function(u){var v={};try{v=JSON.parse(u.response)}catch(g){}return e.responseFileUrl(v)||v.url||""},l=function(u){var v;if(i)v=u.length?u.reduce(function(b,F){var E=F.url;return E&&b.push(E),b},[]):[];else{var g=u[u.length-1],h=g&&g.url;h&&(v=h)}r("update:modelValue",v)},f=lt().appContext.config.globalProperties;return function(){n["onUpdate:modelValue"];var p=we(n,["onUpdate:modelValue"]),u=_(_({fileList:c.value,"on-error":function(){f.$message&&f.$message.error("文件上传失败")}},p),{},{"onUpdate:fileList":function(h){l(h)},"on-change":function(h){var b=h.fileList;c.value=b},"on-finish":function(h){var b=h.file,F=h.event;return b.url=d(F.target),b}});i||(u.max=1);var v=_({default:function(){return N(ee("n-button"),{type:"primary"},{default:function(){return e.btnText}})}},e.slots||{});return N(ee("n-upload"),u,v)}}},Ie={CheckboxesWidget:Ls,RadioWidget:ks,SelectWidget:Ms,TimePickerWidget:qs,DatePickerWidget:Us,DateTimePickerWidget:zs,UploadWidget:Ws,InputWidget:$e("n-input"),ColorWidget:$e("n-color-picker"),TextAreaWidget:$e("n-textarea"),InputNumberWidget:$e("n-input-number"),AutoCompleteWidget:$e("n-auto-complete"),SliderWidget:$e("n-slider"),RateWidget:$e("n-rate"),SwitchWidget:$e("n-switch")},Bs=Ie.CheckboxesWidget,Qs=Ie.RadioWidget,Hs=Ie.SelectWidget,Ks=Ie.TimePickerWidget,Gs=Ie.DatePickerWidget,Js=Ie.DateTimePickerWidget,Zs=Ie.InputWidget,Ys=Ie.SwitchWidget,st=Ie.InputNumberWidget,Xs=Ie.ColorWidget,el={types:{boolean:Ys,string:Zs,number:st,integer:st},formats:{color:Xs,time:Ks,date:Gs,"date-time":Js},common:{select:Hs,radioGroup:Qs,checkboxGroup:Bs},widgetComponents:Ie},rl=".genFromComponent .n-form-item-blank{-ms-flex-wrap:wrap;flex-wrap:wrap}.genFromComponent .n-form-item.n-form-item--top-labelled{grid-template-rows:none}.genFromComponent .formFooter_item .n-form-item-blank{-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end}.genFromComponent .n-form-item-feedback--error .n-form-item-feedback__line{display:-webkit-box!important;text-overflow:ellipsis;overflow:hidden;-webkit-box-orient:vertical;-webkit-line-clamp:2;white-space:normal;text-align:left;line-height:1.2;font-size:12px}.genFromComponent .validateWidget .n-form-item-blank,.genFromComponent .validateWidget .n-form-item-feedback-wrapper{min-height:auto}";en(rl);var al={WIDGET_MAP:el,COMPONENT_MAP:{form:Ir({inheritAttrs:!1,setup:function(e,t){var n=t.attrs,r=t.slots,o={top:{labelAlign:"left",labelPlacement:"top"},left:{labelAlign:"left",labelPlacement:"left"},right:{labelAlign:"right",labelPlacement:"left"}},i=dr(null);return n.setFormRef&&Sn(function(){i.value.$$validate=function(s){i.value.validate(function(c){return c?s(!1,c):s(!0)})},n.setFormRef(i.value)}),function(){n.setFormRef;var s=n.labelPosition,c=n.model,d=we(n,["setFormRef","labelPosition","model"]);return N(ee("n-form"),_(_({ref:i,model:c.value},o[s||"top"]),d),r)}}}),formItem:Ir({inheritAttrs:!1,setup:function(e,t){var n=t.attrs,r=t.slots;return function(){var o=n.prop,i=n.rules,s=we(n,["prop","rules"]),c=_(_({},s),{},{path:o,rule:(i||[]).map(function(d){return{trigger:d.trigger,asyncValidator:function(f,p,u){return d.validator(f,p,u)}}})});return N(ee("n-form-item"),c,r)}}}),button:"n-button",popover:Ir({setup:function(e,t){var n=t.attrs,r=t.slots;return function(){return N(ee("n-popover"),n,{trigger:r.reference,default:r.default})}}})},HELPERS:{isMiniDes:function(e){return e&&["left","right"].includes(e.labelPosition)}}},tl=Ns(al);const ol=tl;export{ol as V};
