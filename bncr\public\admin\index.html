<!-- prettier-ignore -->
<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
	<head>
		<script>self["MonacoEnvironment"] = (function (paths) {
          return {
            globalAPI: false,
            getWorkerUrl : function (moduleId, label) {
              var result =  paths[label];
              if (/^((http:)|(https:)|(file:)|(\/\/))/.test(result)) {
                var currentUrl = String(window.location);
                var currentOrigin = currentUrl.substr(0, currentUrl.length - window.location.hash.length - window.location.search.length - window.location.pathname.length);
                if (result.substring(0, currentOrigin.length) !== currentOrigin) {
                  var js = '/*' + label + '*/importScripts("' + result + '");';
                  var blob = new Blob([js], { type: 'application/javascript' });
                  return URL.createObjectURL(blob);
                }
              }
              return result;
            }
          };
        })({
  "editorWorkerService": "./worker/editor.worker.bundle.js",
  "css": "./worker/css.worker.bundle.js",
  "html": "./worker/html.worker.bundle.js",
  "json": "./worker/json.worker.bundle.js",
  "typescript": "./worker/ts.worker.bundle.js",
  "javascript": "./worker/ts.worker.bundle.js",
  "less": "./worker/css.worker.bundle.js",
  "scss": "./worker/css.worker.bundle.js",
  "handlebars": "./worker/html.worker.bundle.js",
  "razor": "./worker/html.worker.bundle.js"
});</script>

		<meta charset="UTF-8" />
		<link rel="icon" href="./favicon.svg" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>BncrAdmin</title>
		<script type="module" crossorigin src="./assets/index-b380aaed.js"></script>
		<link rel="stylesheet" href="./assets/index-32a0d8a6.css">
	</head>
	<body>
		<div id="app">
			<div id="appLoading"></div>
		</div>
		
	</body>
</html>
