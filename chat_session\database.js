const sqlite3 = require('sqlite3').verbose();

// 连接到SQLite数据库文件，如果文件不存在，则会自动创建
const db = new sqlite3.Database('./chat_history.db', (err) => {
  if (err) {
    console.error('Error opening database', err.message);
  } else {
    console.log('Connected to the SQLite database.');
    // 创建一个表来存储聊天记录
    // session_id: 唯一的会话ID
    // role: 角色 (user 或 model)
    // content: 消息内容
    // timestamp: 记录时间，用于排序
    db.run(`CREATE TABLE IF NOT EXISTS messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      session_id TEXT NOT NULL,
      role TEXT NOT NULL,
      content TEXT NOT NULL,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    )`, (err) => {
      if (err) {
        console.error('Error creating table', err.message);
      } else {
        console.log('Table "messages" is ready.');
      }
    });
  }
});

module.exports = db;