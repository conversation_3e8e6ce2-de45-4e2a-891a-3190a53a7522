require('dotenv').config();
const express = require('express');
const { v4: uuidv4 } = require('uuid');
const cors = require('cors');
const db = require('./database.js'); // 引入数据库配置
const { GoogleGenerativeAI } = require('@google/generative-ai');

const app = express();
const port = 3000;

// 中间件
app.use(cors()); // 允许跨域
app.use(express.json()); // 解析JSON请求体

// 初始化Gemini客户端
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-pro" });

// API 端点

/**
 * @route POST /start
 * @desc 开启一个新的聊天会话，并返回会话ID
 */
app.post('/start', (req, res) => {
  const sessionId = uuidv4();
  res.json({ sessionId });
  console.log(`New session started: ${sessionId}`);
});

/**
 * @route POST /chat
 * @desc 处理聊天请求
 */
app.post('/chat', async (req, res) => {
  const { sessionId, message } = req.body;

  if (!sessionId || !message) {
    return res.status(400).json({ error: 'sessionId and message are required.' });
  }

  try {
    // 1. 从数据库获取该会话的历史记录
    const history = await getHistory(sessionId);

    // 2. 构造发送给Gemini的聊天历史
    const chat = model.startChat({
      history: history,
      generationConfig: {
        maxOutputTokens: 100,
      },
    });

    // 3. 发送新消息给Gemini
    const result = await chat.sendMessage(message);
    const response = await result.response;
    const modelResponse = response.text();

    // 4. 将用户消息和模型回复存入数据库
    await saveMessage(sessionId, 'user', message);
    await saveMessage(sessionId, 'model', modelResponse);

    // 5. 将模型的回复返回给前端
    res.json({ reply: modelResponse });

  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({ error: 'Failed to communicate with the model.' });
  }
});

// --- 数据库辅助函数 ---

/**
 * 将消息保存到数据库
 * @param {string} sessionId 
 * @param {'user' | 'model'} role 
 * @param {string} content 
 */
function saveMessage(sessionId, role, content) {
  return new Promise((resolve, reject) => {
    const query = `INSERT INTO messages (session_id, role, content) VALUES (?, ?, ?)`;
    db.run(query, [sessionId, role, content], function(err) {
      if (err) {
        return reject(err);
      }
      resolve(this.lastID);
    });
  });
}

/**
 * 从数据库获取指定会话的历史记录
 * @param {string} sessionId 
 * @returns {Promise<Array<{role: string, parts: string}>>}
 */
function getHistory(sessionId) {
  return new Promise((resolve, reject) => {
    const query = `SELECT role, content FROM messages WHERE session_id = ? ORDER BY timestamp ASC`;
    db.all(query, [sessionId], (err, rows) => {
      if (err) {
        return reject(err);
      }
      // 将数据库格式转换为Gemini API需要的格式
      const formattedHistory = rows.map(row => ({
        role: row.role,
        parts: [{ text: row.content }], // Gemini API需要parts是一个对象数组
      }));
      resolve(formattedHistory);
    });
  });
}

// 启动服务器
app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});