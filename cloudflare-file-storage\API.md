# API Documentation

## Authentication

All API endpoints require authentication using an API key. Include the API key in one of the following ways:

1. **Header**: `X-API-Key: your-api-key`
2. **Authorization Header**: `Authorization: Bearer your-api-key`

## Base URL

Replace `https://your-site.pages.dev` with your actual Cloudflare Pages URL.

## Endpoints

### 1. Upload File

Upload a file to the storage service.

**Endpoint**: `POST /api/upload`

**Headers**:
- `X-API-Key: your-api-key`
- `Content-Type: multipart/form-data`

**Body**: Form data with `file` field containing the file to upload

**Response**:
```json
{
  "success": true,
  "fileId": "123e4567-e89b-12d3-a456-426614174000",
  "fileName": "example.jpg",
  "size": 1024000,
  "contentType": "image/jpeg",
  "uploadedAt": "2024-07-31T12:00:00.000Z",
  "expiresAt": "2024-08-01T12:00:00.000Z",
  "accessUrl": "/api/files/123e4567-e89b-12d3-a456-426614174000/url"
}
```

**Error Response**:
```json
{
  "error": "File size exceeds maximum allowed size of 50MB"
}
```

### 2. Get File Information

Retrieve information about a specific file.

**Endpoint**: `GET /api/files/{fileId}`

**Headers**:
- `X-API-Key: your-api-key`

**Response**:
```json
{
  "fileId": "123e4567-e89b-12d3-a456-426614174000",
  "fileName": "example.jpg",
  "size": 1024000,
  "contentType": "image/jpeg",
  "uploadedAt": "2024-07-31T12:00:00.000Z",
  "expiresAt": "2024-08-01T12:00:00.000Z",
  "accessUrl": "/api/files/123e4567-e89b-12d3-a456-426614174000/url"
}
```

### 3. Get File Access URL

Get the direct access URL for a file or serve the file directly.

**Endpoint**: `GET /api/files/{fileId}/url`

**Headers**:
- `X-API-Key: your-api-key` (optional for direct file access)

**Query Parameters**:
- `direct=true`: Serve the file directly instead of returning URL information

**Response** (without `direct=true`):
```json
{
  "fileId": "123e4567-e89b-12d3-a456-426614174000",
  "fileName": "example.jpg",
  "accessUrl": "https://your-site.pages.dev/api/files/123e4567-e89b-12d3-a456-426614174000/url?direct=true",
  "directUrl": "https://your-site.pages.dev/api/files/123e4567-e89b-12d3-a456-426614174000/url?direct=true",
  "contentType": "image/jpeg",
  "size": 1024000,
  "expiresAt": "2024-08-01T12:00:00.000Z"
}
```

**Response** (with `direct=true`): Returns the actual file content with appropriate headers.

### 4. Delete File

Delete a specific file from storage.

**Endpoint**: `DELETE /api/files/{fileId}`

**Headers**:
- `X-API-Key: your-api-key`

**Response**:
```json
{
  "success": true,
  "message": "File deleted successfully",
  "fileId": "123e4567-e89b-12d3-a456-426614174000"
}
```

### 5. List Files

List all files with pagination support.

**Endpoint**: `GET /api/files`

**Headers**:
- `X-API-Key: your-api-key`

**Query Parameters**:
- `limit`: Number of files to return (1-100, default: 50)
- `cursor`: Pagination cursor for next page
- `includeExpired`: Include expired files in results (default: false)

**Response**:
```json
{
  "files": [
    {
      "fileId": "123e4567-e89b-12d3-a456-426614174000",
      "fileName": "example.jpg",
      "size": 1024000,
      "contentType": "image/jpeg",
      "uploadedAt": "2024-07-31T12:00:00.000Z",
      "expiresAt": "2024-08-01T12:00:00.000Z",
      "expired": false,
      "accessUrl": "/api/files/123e4567-e89b-12d3-a456-426614174000/url",
      "lastModified": "2024-07-31T12:00:00.000Z"
    }
  ],
  "pagination": {
    "hasMore": true,
    "cursor": "next-page-cursor",
    "limit": 50,
    "count": 1
  },
  "meta": {
    "totalFiles": 1,
    "expiredFilesFound": 0,
    "cleanupPerformed": false
  }
}
```

### 6. Manual Cleanup

Manually trigger cleanup of expired files.

**Endpoint**: `POST /api/cleanup`

**Headers**:
- `X-API-Key: your-api-key`

**Response**:
```json
{
  "success": true,
  "message": "Cleanup completed",
  "filesDeleted": 5,
  "filesScanned": 100,
  "errors": []
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 400 | Bad Request (invalid parameters, file too large, etc.) |
| 401 | Unauthorized (missing or invalid API key) |
| 404 | Not Found (file doesn't exist or has expired) |
| 500 | Internal Server Error |

## Rate Limiting

The API implements basic rate limiting through Cloudflare's built-in protection. For high-volume usage, consider implementing additional rate limiting in your application.

## File Constraints

- **Maximum file size**: 50MB (configurable)
- **Allowed file types**: 
  - Images: JPEG, PNG, WebP, GIF
  - Audio: MP3, WAV, M4A, OGG
- **File expiry**: 24 hours (configurable)
- **Storage limit**: Subject to Cloudflare R2 limits

## CORS Support

The API supports Cross-Origin Resource Sharing (CORS) for web applications. Configure allowed origins using the `CORS_ORIGIN` environment variable.

## Examples

### JavaScript Fetch API

```javascript
// Upload file
const formData = new FormData();
formData.append('file', fileInput.files[0]);

const uploadResponse = await fetch('/api/upload', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key'
  },
  body: formData
});

const uploadResult = await uploadResponse.json();
console.log('File uploaded:', uploadResult);

// Get file info
const infoResponse = await fetch(`/api/files/${uploadResult.fileId}`, {
  headers: {
    'X-API-Key': 'your-api-key'
  }
});

const fileInfo = await infoResponse.json();
console.log('File info:', fileInfo);
```

### Python Requests

```python
import requests

# Upload file
with open('example.jpg', 'rb') as f:
    files = {'file': f}
    headers = {'X-API-Key': 'your-api-key'}
    response = requests.post('https://your-site.pages.dev/api/upload', 
                           files=files, headers=headers)
    result = response.json()
    print('File uploaded:', result)

# Get file info
file_id = result['fileId']
response = requests.get(f'https://your-site.pages.dev/api/files/{file_id}', 
                       headers=headers)
file_info = response.json()
print('File info:', file_info)
```

### cURL

```bash
# Upload file
curl -X POST "https://your-site.pages.dev/api/upload" \
  -H "X-API-Key: your-api-key" \
  -F "file=@example.jpg"

# Get file info
curl -X GET "https://your-site.pages.dev/api/files/file-id-here" \
  -H "X-API-Key: your-api-key"

# Download file
curl -X GET "https://your-site.pages.dev/api/files/file-id-here/url?direct=true" \
  -H "X-API-Key: your-api-key" \
  -o downloaded-file.jpg
```
