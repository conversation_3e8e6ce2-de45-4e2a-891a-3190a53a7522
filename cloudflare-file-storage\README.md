# Cloudflare File Storage Service

A secure, temporary file storage service built on Cloudflare Pages and R2, designed for AI model integration and file sharing.

## 🚀 Features

- **Secure File Upload**: Support for images (JPEG, PNG, WebP, GIF) and audio files (MP3, WAV, M4A, OGG)
- **Temporary Storage**: Files automatically expire after 24 hours (configurable)
- **REST API**: Complete API for programmatic access
- **Web Interface**: User-friendly drag-and-drop interface
- **CORS Support**: Cross-origin requests enabled for web applications
- **API Key Authentication**: Secure access control
- **Automatic Cleanup**: Scheduled cleanup of expired files
- **File Size Limits**: Configurable maximum file size (default: 50MB)

## 📋 API Endpoints

### Upload File
```http
POST /api/upload
Content-Type: multipart/form-data
X-API-Key: your-api-key

Form data:
- file: The file to upload
```

### Get File Information
```http
GET /api/files/{fileId}
X-API-Key: your-api-key
```

### Get File Access URL
```http
GET /api/files/{fileId}/url
X-API-Key: your-api-key

Query parameters:
- direct=true: Serve file directly instead of returning URL info
```

### Delete File
```http
DELETE /api/files/{fileId}
X-API-Key: your-api-key
```

### List Files
```http
GET /api/files
X-API-Key: your-api-key

Query parameters:
- limit: Number of files to return (max 100, default 50)
- cursor: Pagination cursor
- includeExpired: Include expired files in results (default false)
```

### Manual Cleanup
```http
POST /api/cleanup
X-API-Key: your-api-key
```

## 🛠️ Setup and Deployment

### Prerequisites

- Node.js 18+ and npm
- Cloudflare account
- Wrangler CLI installed globally: `npm install -g wrangler`

### 1. Clone and Install

```bash
git clone <repository-url>
cd cloudflare-file-storage
npm install
```

### 2. Configure Cloudflare

```bash
# Login to Cloudflare
wrangler login

# Create R2 bucket
npm run setup:bucket
# or manually: wrangler r2 bucket create file-storage-bucket
```

### 3. Set Environment Variables

```bash
# Set API secret key
wrangler secret put API_SECRET_KEY
# Enter a secure random string when prompted

# Optional: Set page access password
wrangler secret put PAGE_PASSWORD

# Optional: Set CORS origin
wrangler secret put CORS_ORIGIN
# Enter allowed origins, e.g., "https://yourdomain.com" or "*" for all
```

### 4. Configure Settings

Edit `wrangler.toml` to customize:

```toml
[vars]
MAX_FILE_SIZE = "********"  # 50MB in bytes
ALLOWED_FILE_TYPES = "image/jpeg,image/png,image/webp,image/gif,audio/mpeg,audio/wav,audio/m4a,audio/ogg"
FILE_EXPIRY_HOURS = "24"    # Files expire after 24 hours
```

### 5. Deploy

```bash
# Build and deploy
npm run deploy

# Or deploy with preview
wrangler pages deploy dist --project-name your-project-name
```

## 🔧 Local Development

```bash
# Build static files
npm run build

# Start local development server
npm run dev
# or: wrangler pages dev dist --compatibility-date=2024-07-31
```

Visit `http://localhost:8788` to access the web interface.

## 🔐 Security Features

- **API Key Authentication**: All API endpoints require a valid API key
- **File Type Validation**: Only allowed file types can be uploaded
- **File Size Limits**: Configurable maximum file size
- **CORS Protection**: Configurable cross-origin access
- **Automatic Expiry**: Files are automatically deleted after expiration
- **Security Headers**: Standard security headers applied to all responses

## 📊 Usage Examples

### JavaScript/Node.js

```javascript
const apiKey = 'your-api-key';
const baseUrl = 'https://your-site.pages.dev';

// Upload a file
async function uploadFile(file) {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch(`${baseUrl}/api/upload`, {
    method: 'POST',
    headers: {
      'X-API-Key': apiKey
    },
    body: formData
  });
  
  return await response.json();
}

// Get file URL
async function getFileUrl(fileId) {
  const response = await fetch(`${baseUrl}/api/files/${fileId}/url`, {
    headers: {
      'X-API-Key': apiKey
    }
  });
  
  return await response.json();
}
```

### Python

```python
import requests

api_key = 'your-api-key'
base_url = 'https://your-site.pages.dev'

# Upload a file
def upload_file(file_path):
    with open(file_path, 'rb') as f:
        files = {'file': f}
        headers = {'X-API-Key': api_key}
        response = requests.post(f'{base_url}/api/upload', 
                               files=files, headers=headers)
        return response.json()

# Get file URL
def get_file_url(file_id):
    headers = {'X-API-Key': api_key}
    response = requests.get(f'{base_url}/api/files/{file_id}/url', 
                          headers=headers)
    return response.json()
```

### cURL

```bash
# Upload file
curl -X POST "https://your-site.pages.dev/api/upload" \
  -H "X-API-Key: your-api-key" \
  -F "file=@/path/to/your/file.jpg"

# Get file info
curl -X GET "https://your-site.pages.dev/api/files/file-id" \
  -H "X-API-Key: your-api-key"

# Download file directly
curl -X GET "https://your-site.pages.dev/api/files/file-id/url?direct=true" \
  -H "X-API-Key: your-api-key" \
  -o downloaded-file.jpg
```

## 🔄 Automatic Cleanup

The service includes automatic cleanup of expired files:

- **Scheduled Cleanup**: Runs every 6 hours via Cloudflare Cron Triggers
- **Manual Cleanup**: Use the `/api/cleanup` endpoint
- **On-Access Cleanup**: Expired files are removed when accessed

## 📝 Configuration Options

| Variable | Description | Default |
|----------|-------------|---------|
| `MAX_FILE_SIZE` | Maximum file size in bytes | ******** (50MB) |
| `ALLOWED_FILE_TYPES` | Comma-separated list of allowed MIME types | Images and audio |
| `FILE_EXPIRY_HOURS` | Hours until files expire | 24 |
| `API_SECRET_KEY` | Secret key for API authentication | (required) |
| `PAGE_PASSWORD` | Password for web interface access | (optional) |
| `CORS_ORIGIN` | Allowed CORS origins | * (all origins) |

## 🚨 Troubleshooting

### Common Issues

1. **"Missing API key" error**: Ensure you've set the `API_SECRET_KEY` secret
2. **"File type not allowed"**: Check the `ALLOWED_FILE_TYPES` configuration
3. **"File too large"**: Adjust the `MAX_FILE_SIZE` setting
4. **CORS errors**: Configure the `CORS_ORIGIN` environment variable

### Logs and Monitoring

- Check Cloudflare Pages deployment logs
- Monitor R2 storage usage in Cloudflare dashboard
- Use the cleanup endpoint to see cleanup statistics

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review Cloudflare Pages and R2 documentation
- Open an issue in the repository
