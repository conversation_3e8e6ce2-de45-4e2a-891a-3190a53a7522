#!/bin/bash

# Cloudflare File Storage Deployment Script
# This script automates the deployment process

set -e  # Exit on any error

echo "🚀 Starting Cloudflare File Storage deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if wrangler is installed
if ! command -v wrangler &> /dev/null; then
    print_error "Wrangler CLI is not installed. Please install it first:"
    echo "npm install -g wrangler"
    exit 1
fi

# Check if user is logged in to Cloudflare
print_status "Checking Cloudflare authentication..."
if ! wrangler whoami &> /dev/null; then
    print_warning "Not logged in to Cloudflare. Please login first:"
    wrangler login
fi

# Install dependencies
print_status "Installing dependencies..."
npm install

# Create R2 bucket if it doesn't exist
print_status "Setting up R2 bucket..."
if wrangler r2 bucket list | grep -q "file-storage-bucket"; then
    print_success "R2 bucket 'file-storage-bucket' already exists"
else
    print_status "Creating R2 bucket..."
    wrangler r2 bucket create file-storage-bucket
    print_success "R2 bucket created successfully"
fi

# Check for required secrets
print_status "Checking required secrets..."

# Function to check if a secret exists
check_secret() {
    local secret_name=$1
    local required=$2
    
    # This is a workaround since wrangler doesn't have a direct way to check secrets
    # We'll assume secrets need to be set if this is a fresh deployment
    if [ "$required" = "true" ]; then
        print_warning "Please ensure the secret '$secret_name' is set."
        echo "Run: wrangler secret put $secret_name"
    fi
}

check_secret "API_SECRET_KEY" "true"

# Optional secrets
print_status "Optional secrets (you can set these if needed):"
echo "- PAGE_PASSWORD: wrangler secret put PAGE_PASSWORD"
echo "- CORS_ORIGIN: wrangler secret put CORS_ORIGIN"

# Build the project
print_status "Building the project..."
npm run build

# Deploy to Cloudflare Pages
print_status "Deploying to Cloudflare Pages..."
wrangler pages deploy dist

print_success "Deployment completed successfully!"

echo ""
echo "📋 Next steps:"
echo "1. Set your API_SECRET_KEY if you haven't already:"
echo "   wrangler secret put API_SECRET_KEY"
echo ""
echo "2. Optional: Set a page password for web interface:"
echo "   wrangler secret put PAGE_PASSWORD"
echo ""
echo "3. Optional: Configure CORS origins:"
echo "   wrangler secret put CORS_ORIGIN"
echo ""
echo "4. Your file storage service is now live!"
echo "   Visit your Cloudflare Pages URL to access the web interface"
echo ""
echo "🔧 Configuration:"
echo "- Max file size: 50MB (configurable in wrangler.toml)"
echo "- File expiry: 24 hours (configurable in wrangler.toml)"
echo "- Automatic cleanup: Every 6 hours"
echo ""
echo "📚 API Documentation: See README.md for usage examples"
