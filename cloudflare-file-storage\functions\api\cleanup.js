/**
 * File Cleanup API Endpoint
 * POST /api/cleanup - Manually trigger cleanup of expired files
 * This endpoint can be called by a cron job or manually to clean up expired files
 */

export async function onRequestPost(context) {
  const { request, env } = context;
  
  try {
    // Verify API key
    const authResult = await verifyApi<PERSON>ey(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify({ error: authResult.error }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const cleanupResult = await cleanupExpiredFiles(env);

    return new Response(JSON.stringify({
      success: true,
      message: 'Cleanup completed',
      filesDeleted: cleanupResult.deletedCount,
      filesScanned: cleanupResult.scannedCount,
      errors: cleanupResult.errors
    }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        ...getCorsHeaders(env)
      }
    });

  } catch (error) {
    console.error('Cleanup error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle OPTIONS request for CORS
export async function onRequestOptions(context) {
  return new Response(null, {
    status: 200,
    headers: getCorsHeaders(context.env)
  });
}

/**
 * Clean up expired files from R2 storage
 */
async function cleanupExpiredFiles(env) {
  let scannedCount = 0;
  let deletedCount = 0;
  const errors = [];
  let cursor = null;
  
  try {
    do {
      // List objects from R2
      const listOptions = { limit: 100 };
      if (cursor) {
        listOptions.cursor = cursor;
      }

      const result = await env.FILE_BUCKET.list(listOptions);
      
      for (const object of result.objects) {
        scannedCount++;
        
        try {
          const metadata = object.customMetadata || {};
          
          // Check if file has expired
          if (metadata.expiresAt && new Date(metadata.expiresAt) < new Date()) {
            await env.FILE_BUCKET.delete(object.key);
            deletedCount++;
            console.log(`Deleted expired file: ${object.key}`);
          }
        } catch (error) {
          console.error(`Error processing file ${object.key}:`, error);
          errors.push(`${object.key}: ${error.message}`);
        }
      }
      
      cursor = result.truncated ? result.cursor : null;
    } while (cursor);
    
  } catch (error) {
    console.error('Error during cleanup:', error);
    errors.push(`General cleanup error: ${error.message}`);
  }

  return {
    scannedCount,
    deletedCount,
    errors
  };
}

// Utility functions
async function verifyApiKey(request, env) {
  const authHeader = request.headers.get('Authorization');
  const apiKey = request.headers.get('X-API-Key');
  
  if (!authHeader && !apiKey) {
    return { success: false, error: 'Missing API key' };
  }

  const providedKey = authHeader?.replace('Bearer ', '') || apiKey;
  
  if (providedKey !== env.API_SECRET_KEY) {
    return { success: false, error: 'Invalid API key' };
  }

  return { success: true };
}

function getCorsHeaders(env) {
  const corsOrigin = env.CORS_ORIGIN || '*';
  return {
    'Access-Control-Allow-Origin': corsOrigin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    'Access-Control-Max-Age': '86400'
  };
}
