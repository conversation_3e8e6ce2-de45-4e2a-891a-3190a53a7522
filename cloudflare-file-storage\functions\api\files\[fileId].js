/**
 * File Information API Endpoint
 * GET /api/files/{fileId} - Get file information
 * DELETE /api/files/{fileId} - Delete file
 */

export async function onRequestGet(context) {
  const { request, env, params } = context;
  
  try {
    // Verify API key
    const authResult = await verifyApiKey(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify({ error: authResult.error }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const fileId = params.fileId;
    
    // List objects with the fileId prefix to find the file
    const objects = await env.FILE_BUCKET.list({ prefix: `${fileId}/` });
    
    if (objects.objects.length === 0) {
      return new Response(JSON.stringify({ error: 'File not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const fileObject = objects.objects[0];
    const metadata = fileObject.customMetadata || {};
    
    // Check if file has expired
    if (metadata.expiresAt && new Date(metadata.expiresAt) < new Date()) {
      // File has expired, delete it
      await env.FILE_BUCKET.delete(fileObject.key);
      return new Response(JSON.stringify({ error: 'File has expired' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      fileId: fileId,
      fileName: metadata.originalName || fileObject.key.split('/').pop(),
      size: fileObject.size,
      contentType: metadata.contentType || 'application/octet-stream',
      uploadedAt: metadata.uploadedAt,
      expiresAt: metadata.expiresAt,
      accessUrl: `/api/files/${fileId}/url`
    }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        ...getCorsHeaders(env)
      }
    });

  } catch (error) {
    console.error('Get file info error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function onRequestDelete(context) {
  const { request, env, params } = context;
  
  try {
    // Verify API key
    const authResult = await verifyApiKey(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify({ error: authResult.error }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const fileId = params.fileId;
    
    // List objects with the fileId prefix to find the file
    const objects = await env.FILE_BUCKET.list({ prefix: `${fileId}/` });
    
    if (objects.objects.length === 0) {
      return new Response(JSON.stringify({ error: 'File not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Delete the file
    const fileObject = objects.objects[0];
    await env.FILE_BUCKET.delete(fileObject.key);

    return new Response(JSON.stringify({
      success: true,
      message: 'File deleted successfully',
      fileId: fileId
    }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        ...getCorsHeaders(env)
      }
    });

  } catch (error) {
    console.error('Delete file error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle OPTIONS request for CORS
export async function onRequestOptions(context) {
  return new Response(null, {
    status: 200,
    headers: getCorsHeaders(context.env)
  });
}

// Utility functions
async function verifyApiKey(request, env) {
  const authHeader = request.headers.get('Authorization');
  const apiKey = request.headers.get('X-API-Key');
  
  if (!authHeader && !apiKey) {
    return { success: false, error: 'Missing API key' };
  }

  const providedKey = authHeader?.replace('Bearer ', '') || apiKey;
  
  if (providedKey !== env.API_SECRET_KEY) {
    return { success: false, error: 'Invalid API key' };
  }

  return { success: true };
}

function getCorsHeaders(env) {
  const corsOrigin = env.CORS_ORIGIN || '*';
  return {
    'Access-Control-Allow-Origin': corsOrigin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    'Access-Control-Max-Age': '86400'
  };
}
