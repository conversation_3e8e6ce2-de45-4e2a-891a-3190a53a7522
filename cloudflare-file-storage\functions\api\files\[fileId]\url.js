/**
 * File Access URL API Endpoint
 * GET /api/files/{fileId}/url - Get file access URL or serve file directly
 */

export async function onRequestGet(context) {
  const { request, env, params } = context;
  
  try {
    const fileId = params.fileId;
    const url = new URL(request.url);
    const direct = url.searchParams.get('direct') === 'true';
    
    // List objects with the fileId prefix to find the file
    const objects = await env.FILE_BUCKET.list({ prefix: `${fileId}/` });
    
    if (objects.objects.length === 0) {
      return new Response(JSON.stringify({ error: 'File not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const fileObject = objects.objects[0];
    const metadata = fileObject.customMetadata || {};
    
    // Check if file has expired
    if (metadata.expiresAt && new Date(metadata.expiresAt) < new Date()) {
      // File has expired, delete it
      await env.FILE_BUCKET.delete(fileObject.key);
      return new Response(JSON.stringify({ error: 'File has expired' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // If direct access is requested, serve the file directly
    if (direct) {
      const object = await env.FILE_BUCKET.get(fileObject.key);
      
      if (!object) {
        return new Response(JSON.stringify({ error: 'File not found' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const headers = new Headers();
      object.writeHttpMetadata(headers);
      headers.set('etag', object.httpEtag);
      headers.set('Content-Disposition', `inline; filename="${metadata.originalName || fileObject.key.split('/').pop()}"`);
      
      // Add CORS headers for direct access
      const corsHeaders = getCorsHeaders(env);
      Object.entries(corsHeaders).forEach(([key, value]) => {
        headers.set(key, value);
      });

      return new Response(object.body, { headers });
    }

    // Otherwise, return the access URL information
    const baseUrl = new URL(request.url).origin;
    const accessUrl = `${baseUrl}/api/files/${fileId}/url?direct=true`;

    return new Response(JSON.stringify({
      fileId: fileId,
      fileName: metadata.originalName || fileObject.key.split('/').pop(),
      accessUrl: accessUrl,
      directUrl: accessUrl,
      contentType: metadata.contentType || 'application/octet-stream',
      size: fileObject.size,
      expiresAt: metadata.expiresAt
    }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        ...getCorsHeaders(env)
      }
    });

  } catch (error) {
    console.error('Get file URL error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle OPTIONS request for CORS
export async function onRequestOptions(context) {
  return new Response(null, {
    status: 200,
    headers: getCorsHeaders(context.env)
  });
}

function getCorsHeaders(env) {
  const corsOrigin = env.CORS_ORIGIN || '*';
  return {
    'Access-Control-Allow-Origin': corsOrigin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    'Access-Control-Max-Age': '86400'
  };
}
