/**
 * Files List API Endpoint
 * GET /api/files - List all files with pagination
 */

export async function onRequestGet(context) {
  const { request, env } = context;
  
  try {
    // Verify API key
    const authResult = await verifyApi<PERSON>ey(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify({ error: authResult.error }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(request.url);
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '50'), 100); // Max 100 items
    const cursor = url.searchParams.get('cursor');
    const includeExpired = url.searchParams.get('includeExpired') === 'true';

    // List objects from R2
    const listOptions = { limit };
    if (cursor) {
      listOptions.cursor = cursor;
    }

    const result = await env.FILE_BUCKET.list(listOptions);
    const files = [];
    const expiredFiles = [];

    for (const object of result.objects) {
      const metadata = object.customMetadata || {};
      const fileId = object.key.split('/')[0]; // Extract fileId from key
      
      // Check if file has expired
      const isExpired = metadata.expiresAt && new Date(metadata.expiresAt) < new Date();
      
      if (isExpired && !includeExpired) {
        expiredFiles.push(object.key);
        continue;
      }

      files.push({
        fileId: fileId,
        fileName: metadata.originalName || object.key.split('/').pop(),
        size: object.size,
        contentType: metadata.contentType || 'application/octet-stream',
        uploadedAt: metadata.uploadedAt,
        expiresAt: metadata.expiresAt,
        expired: isExpired,
        accessUrl: `/api/files/${fileId}/url`,
        lastModified: object.uploaded
      });
    }

    // Clean up expired files in the background (don't wait for completion)
    if (expiredFiles.length > 0) {
      // Note: In a production environment, you might want to use a scheduled worker
      // or queue for cleanup to avoid blocking the response
      Promise.all(expiredFiles.map(key => env.FILE_BUCKET.delete(key)))
        .catch(error => console.error('Error cleaning up expired files:', error));
    }

    const response = {
      files: files,
      pagination: {
        hasMore: result.truncated,
        cursor: result.cursor,
        limit: limit,
        count: files.length
      },
      meta: {
        totalFiles: files.length,
        expiredFilesFound: expiredFiles.length,
        cleanupPerformed: expiredFiles.length > 0
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        ...getCorsHeaders(env)
      }
    });

  } catch (error) {
    console.error('List files error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle OPTIONS request for CORS
export async function onRequestOptions(context) {
  return new Response(null, {
    status: 200,
    headers: getCorsHeaders(context.env)
  });
}

// Utility functions
async function verifyApiKey(request, env) {
  const authHeader = request.headers.get('Authorization');
  const apiKey = request.headers.get('X-API-Key');
  
  if (!authHeader && !apiKey) {
    return { success: false, error: 'Missing API key' };
  }

  const providedKey = authHeader?.replace('Bearer ', '') || apiKey;
  
  if (providedKey !== env.API_SECRET_KEY) {
    return { success: false, error: 'Invalid API key' };
  }

  return { success: true };
}

function getCorsHeaders(env) {
  const corsOrigin = env.CORS_ORIGIN || '*';
  return {
    'Access-Control-Allow-Origin': corsOrigin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    'Access-Control-Max-Age': '86400'
  };
}
