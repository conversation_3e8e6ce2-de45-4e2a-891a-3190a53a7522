/**
 * File Upload API Endpoint
 * POST /api/upload
 */

export async function onRequestPost(context) {
  const { request, env } = context;
  
  try {
    // Verify API key
    const authResult = await verifyApiKey(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify({ error: authResult.error }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse multipart form data
    const formData = await request.formData();
    const file = formData.get('file');
    
    if (!file) {
      return new Response(JSON.stringify({ error: 'No file provided' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate file
    const validation = validateFile(file, env);
    if (!validation.valid) {
      return new Response(JSON.stringify({ error: validation.error }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate unique file ID and key
    const fileId = generateFileId();
    const fileKey = `${fileId}/${file.name}`;
    const expiryTime = new Date(Date.now() + parseInt(env.FILE_EXPIRY_HOURS) * 60 * 60 * 1000);

    // Prepare metadata
    const metadata = {
      originalName: file.name,
      contentType: file.type,
      size: file.size,
      uploadedAt: new Date().toISOString(),
      expiresAt: expiryTime.toISOString(),
      fileId: fileId
    };

    // Upload to R2
    await env.FILE_BUCKET.put(fileKey, file.stream(), {
      httpMetadata: {
        contentType: file.type,
        cacheControl: 'public, max-age=31536000'
      },
      customMetadata: metadata
    });

    // Return success response
    return new Response(JSON.stringify({
      success: true,
      fileId: fileId,
      fileName: file.name,
      size: file.size,
      contentType: file.type,
      uploadedAt: metadata.uploadedAt,
      expiresAt: metadata.expiresAt,
      accessUrl: `/api/files/${fileId}/url`
    }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        ...getCorsHeaders(env)
      }
    });

  } catch (error) {
    console.error('Upload error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle OPTIONS request for CORS
export async function onRequestOptions(context) {
  return new Response(null, {
    status: 200,
    headers: getCorsHeaders(context.env)
  });
}

// Utility functions
function generateFileId() {
  return crypto.randomUUID();
}

function validateFile(file, env) {
  const maxSize = parseInt(env.MAX_FILE_SIZE);
  const allowedTypes = env.ALLOWED_FILE_TYPES.split(',');

  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File size exceeds maximum allowed size of ${Math.round(maxSize / 1024 / 1024)}MB`
    };
  }

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }

  return { valid: true };
}

async function verifyApiKey(request, env) {
  const authHeader = request.headers.get('Authorization');
  const apiKey = request.headers.get('X-API-Key');
  
  if (!authHeader && !apiKey) {
    return { success: false, error: 'Missing API key' };
  }

  const providedKey = authHeader?.replace('Bearer ', '') || apiKey;
  
  if (providedKey !== env.API_SECRET_KEY) {
    return { success: false, error: 'Invalid API key' };
  }

  return { success: true };
}

function getCorsHeaders(env) {
  const corsOrigin = env.CORS_ORIGIN || '*';
  return {
    'Access-Control-Allow-Origin': corsOrigin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    'Access-Control-Max-Age': '86400'
  };
}
