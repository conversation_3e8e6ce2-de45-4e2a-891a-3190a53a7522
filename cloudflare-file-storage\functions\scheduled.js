/**
 * Scheduled Worker for automatic file cleanup
 * This function runs on a schedule to clean up expired files
 * Configure the schedule in wrangler.toml under [triggers]
 */

export default {
  async scheduled(event, env, ctx) {
    console.log('Starting scheduled cleanup task...');
    
    try {
      const cleanupResult = await cleanupExpiredFiles(env);
      
      console.log(`Cleanup completed: ${cleanupResult.deletedCount} files deleted, ${cleanupResult.scannedCount} files scanned`);
      
      if (cleanupResult.errors.length > 0) {
        console.error('Cleanup errors:', cleanupResult.errors);
      }
      
      // You could also send notifications here if needed
      // For example, send to a webhook or email service if there were errors
      
    } catch (error) {
      console.error('Scheduled cleanup failed:', error);
    }
  }
};

/**
 * Clean up expired files from R2 storage
 */
async function cleanupExpiredFiles(env) {
  let scannedCount = 0;
  let deletedCount = 0;
  const errors = [];
  let cursor = null;
  
  try {
    do {
      // List objects from R2
      const listOptions = { limit: 100 };
      if (cursor) {
        listOptions.cursor = cursor;
      }

      const result = await env.FILE_BUCKET.list(listOptions);
      
      for (const object of result.objects) {
        scannedCount++;
        
        try {
          const metadata = object.customMetadata || {};
          
          // Check if file has expired
          if (metadata.expiresAt && new Date(metadata.expiresAt) < new Date()) {
            await env.FILE_BUCKET.delete(object.key);
            deletedCount++;
            console.log(`Deleted expired file: ${object.key}`);
          }
        } catch (error) {
          console.error(`Error processing file ${object.key}:`, error);
          errors.push(`${object.key}: ${error.message}`);
        }
      }
      
      cursor = result.truncated ? result.cursor : null;
    } while (cursor);
    
  } catch (error) {
    console.error('Error during cleanup:', error);
    errors.push(`General cleanup error: ${error.message}`);
  }

  return {
    scannedCount,
    deletedCount,
    errors
  };
}
