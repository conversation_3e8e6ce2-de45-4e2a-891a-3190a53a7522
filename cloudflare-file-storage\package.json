{"name": "cloudflare-file-storage", "version": "1.0.0", "description": "A file storage service built on Cloudflare Pages and R2", "main": "index.js", "scripts": {"build": "npm run build:static", "build:static": "mkdir -p dist && cp -r public/* dist/", "dev": "wrangler pages dev dist --compatibility-date=2024-07-31 --compatibility-flags=nodejs_compat", "deploy": "npm run build && wrangler pages deploy dist", "preview": "wrangler pages dev dist", "setup": "npm run setup:bucket && npm run setup:secrets", "setup:bucket": "wrangler r2 bucket create file-storage-bucket", "setup:secrets": "echo 'Please run: wrangler secret put API_SECRET_KEY'", "test": "node test-api.js", "test:local": "node test-api.js http://localhost:8788", "clean": "rm -rf dist node_modules"}, "keywords": ["cloudflare", "pages", "r2", "file-storage", "api"], "author": "Your Name", "license": "MIT", "devDependencies": {"wrangler": "^3.0.0", "node-fetch": "^3.3.0", "form-data": "^4.0.0"}}