<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudflare File Storage</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>📁 Cloudflare File Storage</h1>
            <p>Secure temporary file storage powered by Cloudflare R2</p>
        </header>

        <!-- API Key Configuration -->
        <div class="api-config">
            <label for="apiKey">API Key:</label>
            <input type="password" id="apiKey" placeholder="Enter your API key">
            <button onclick="saveApiKey()">Save</button>
            <span id="apiStatus" class="status"></span>
        </div>

        <!-- Upload Section -->
        <div class="upload-section">
            <div class="upload-area" id="uploadArea">
                <div class="upload-content">
                    <div class="upload-icon">📤</div>
                    <h3>Drop files here or click to browse</h3>
                    <p>Supported: Images (JPEG, PNG, WebP, GIF) and Audio (MP3, WAV, M4A, OGG)</p>
                    <p>Max size: 50MB per file</p>
                </div>
                <input type="file" id="fileInput" multiple accept="image/*,audio/*" style="display: none;">
            </div>
            
            <div class="upload-progress" id="uploadProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Uploading...</div>
            </div>
        </div>

        <!-- Files List -->
        <div class="files-section">
            <div class="section-header">
                <h2>📋 Your Files</h2>
                <button onclick="refreshFiles()" class="refresh-btn">🔄 Refresh</button>
            </div>
            
            <div id="filesList" class="files-list">
                <div class="loading">Loading files...</div>
            </div>
            
            <div class="pagination" id="pagination" style="display: none;">
                <button id="prevBtn" onclick="loadPreviousPage()">← Previous</button>
                <span id="pageInfo"></span>
                <button id="nextBtn" onclick="loadNextPage()">Next →</button>
            </div>
        </div>

        <!-- File Preview Modal -->
        <div id="previewModal" class="modal" style="display: none;">
            <div class="modal-content">
                <span class="close" onclick="closePreview()">&times;</span>
                <div id="previewContent"></div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="script.js"></script>
</body>
</html>
