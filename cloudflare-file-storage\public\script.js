// Global variables
let apiKey = localStorage.getItem('apiKey') || '';
let currentCursor = null;
let hasMore = false;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Set API key if stored
    if (apiKey) {
        document.getElementById('apiKey').value = apiKey;
        document.getElementById('apiStatus').textContent = '✓ Saved';
        document.getElementById('apiStatus').className = 'status success';
    }
    
    // Setup event listeners
    setupEventListeners();
    
    // Load files if API key is available
    if (apiKey) {
        loadFiles();
    }
}

function setupEventListeners() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    
    // Upload area click
    uploadArea.addEventListener('click', () => {
        if (apiKey) {
            fileInput.click();
        } else {
            showToast('Please enter your API key first', 'error');
        }
    });
    
    // File input change
    fileInput.addEventListener('change', handleFileSelect);
    
    // Drag and drop
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    
    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
}

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function handleDragOver(e) {
    document.getElementById('uploadArea').classList.add('dragover');
}

function handleDragLeave(e) {
    document.getElementById('uploadArea').classList.remove('dragover');
}

function handleDrop(e) {
    document.getElementById('uploadArea').classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFiles(files);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        handleFiles(files);
    }
}

function saveApiKey() {
    const keyInput = document.getElementById('apiKey');
    const newApiKey = keyInput.value.trim();
    
    if (!newApiKey) {
        showToast('Please enter an API key', 'error');
        return;
    }
    
    apiKey = newApiKey;
    localStorage.setItem('apiKey', apiKey);
    
    document.getElementById('apiStatus').textContent = '✓ Saved';
    document.getElementById('apiStatus').className = 'status success';
    
    showToast('API key saved successfully', 'success');
    
    // Load files after saving API key
    loadFiles();
}

async function handleFiles(files) {
    if (!apiKey) {
        showToast('Please enter your API key first', 'error');
        return;
    }
    
    const fileArray = Array.from(files);
    
    for (let i = 0; i < fileArray.length; i++) {
        await uploadFile(fileArray[i], i + 1, fileArray.length);
    }
    
    // Refresh file list after uploads
    setTimeout(() => {
        loadFiles();
    }, 1000);
}

async function uploadFile(file, current, total) {
    const progressElement = document.getElementById('uploadProgress');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    
    // Show progress
    progressElement.style.display = 'block';
    progressText.textContent = `Uploading ${file.name} (${current}/${total})...`;
    progressFill.style.width = '0%';
    
    try {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch('/api/upload', {
            method: 'POST',
            headers: {
                'X-API-Key': apiKey
            },
            body: formData
        });
        
        progressFill.style.width = '100%';
        
        if (response.ok) {
            const result = await response.json();
            showToast(`✓ ${file.name} uploaded successfully`, 'success');
        } else {
            const error = await response.json();
            showToast(`✗ Failed to upload ${file.name}: ${error.error}`, 'error');
        }
    } catch (error) {
        showToast(`✗ Upload failed: ${error.message}`, 'error');
    }
    
    // Hide progress after a delay
    if (current === total) {
        setTimeout(() => {
            progressElement.style.display = 'none';
        }, 1000);
    }
}

async function loadFiles(cursor = null) {
    if (!apiKey) {
        document.getElementById('filesList').innerHTML = '<div class="loading">Please enter your API key to view files</div>';
        return;
    }

    document.getElementById('filesList').innerHTML = '<div class="loading">Loading files...</div>';

    try {
        let url = '/api/files?limit=20';
        if (cursor) {
            url += `&cursor=${cursor}`;
        }

        const response = await fetch(url, {
            headers: {
                'X-API-Key': apiKey
            }
        });

        if (response.ok) {
            const data = await response.json();
            displayFiles(data.files);
            updatePagination(data.pagination);
        } else {
            const error = await response.json();
            document.getElementById('filesList').innerHTML = `<div class="loading">Error: ${error.error}</div>`;
        }
    } catch (error) {
        document.getElementById('filesList').innerHTML = `<div class="loading">Error loading files: ${error.message}</div>`;
    }
}

function displayFiles(files) {
    const filesList = document.getElementById('filesList');

    if (files.length === 0) {
        filesList.innerHTML = '<div class="loading">No files found. Upload some files to get started!</div>';
        return;
    }

    const filesHtml = files.map(file => {
        const fileIcon = getFileIcon(file.contentType);
        const fileSize = formatFileSize(file.size);
        const uploadDate = new Date(file.uploadedAt).toLocaleString();
        const expiryDate = file.expiresAt ? new Date(file.expiresAt).toLocaleString() : 'Never';
        const isExpired = file.expired;

        return `
            <div class="file-item ${isExpired ? 'expired' : ''}">
                <div class="file-icon">${fileIcon}</div>
                <div class="file-info">
                    <div class="file-name">${file.fileName}</div>
                    <div class="file-meta">
                        ${fileSize} • ${file.contentType} • Uploaded: ${uploadDate}
                        ${isExpired ? '<br><span style="color: #ef4444;">⚠️ Expired</span>' : `<br>Expires: ${expiryDate}`}
                    </div>
                </div>
                <div class="file-actions">
                    <button class="btn btn-primary" onclick="previewFile('${file.fileId}', '${file.fileName}', '${file.contentType}')">
                        👁️ Preview
                    </button>
                    <a href="/api/files/${file.fileId}/url?direct=true" target="_blank" class="btn btn-secondary">
                        📥 Download
                    </a>
                    <button class="btn btn-secondary" onclick="copyFileUrl('${file.fileId}')">
                        🔗 Copy URL
                    </button>
                    <button class="btn btn-danger" onclick="deleteFile('${file.fileId}', '${file.fileName}')">
                        🗑️ Delete
                    </button>
                </div>
            </div>
        `;
    }).join('');

    filesList.innerHTML = filesHtml;
}

function getFileIcon(contentType) {
    if (contentType.startsWith('image/')) return '🖼️';
    if (contentType.startsWith('audio/')) return '🎵';
    if (contentType.startsWith('video/')) return '🎬';
    return '📄';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function updatePagination(pagination) {
    const paginationElement = document.getElementById('pagination');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const pageInfo = document.getElementById('pageInfo');

    if (pagination.hasMore || currentCursor) {
        paginationElement.style.display = 'flex';
        prevBtn.disabled = !currentCursor;
        nextBtn.disabled = !pagination.hasMore;
        pageInfo.textContent = `Showing ${pagination.count} files`;
        hasMore = pagination.hasMore;
        if (pagination.hasMore) {
            currentCursor = pagination.cursor;
        }
    } else {
        paginationElement.style.display = 'none';
    }
}

function loadNextPage() {
    if (hasMore && currentCursor) {
        loadFiles(currentCursor);
    }
}

function loadPreviousPage() {
    // For simplicity, reload from the beginning
    // In a production app, you'd want to implement proper bidirectional pagination
    currentCursor = null;
    loadFiles();
}

function refreshFiles() {
    currentCursor = null;
    loadFiles();
}

async function deleteFile(fileId, fileName) {
    if (!confirm(`Are you sure you want to delete "${fileName}"?`)) {
        return;
    }

    try {
        const response = await fetch(`/api/files/${fileId}`, {
            method: 'DELETE',
            headers: {
                'X-API-Key': apiKey
            }
        });

        if (response.ok) {
            showToast(`✓ ${fileName} deleted successfully`, 'success');
            loadFiles(); // Refresh the list
        } else {
            const error = await response.json();
            showToast(`✗ Failed to delete ${fileName}: ${error.error}`, 'error');
        }
    } catch (error) {
        showToast(`✗ Delete failed: ${error.message}`, 'error');
    }
}

async function copyFileUrl(fileId) {
    const url = `${window.location.origin}/api/files/${fileId}/url?direct=true`;

    try {
        await navigator.clipboard.writeText(url);
        showToast('✓ File URL copied to clipboard', 'success');
    } catch (error) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('✓ File URL copied to clipboard', 'success');
    }
}

async function previewFile(fileId, fileName, contentType) {
    const modal = document.getElementById('previewModal');
    const previewContent = document.getElementById('previewContent');

    const fileUrl = `/api/files/${fileId}/url?direct=true`;

    let content = '';

    if (contentType.startsWith('image/')) {
        content = `
            <h3>📷 ${fileName}</h3>
            <img src="${fileUrl}" alt="${fileName}" style="max-width: 100%; max-height: 70vh; object-fit: contain;">
        `;
    } else if (contentType.startsWith('audio/')) {
        content = `
            <h3>🎵 ${fileName}</h3>
            <audio controls style="width: 100%;">
                <source src="${fileUrl}" type="${contentType}">
                Your browser does not support the audio element.
            </audio>
        `;
    } else {
        content = `
            <h3>📄 ${fileName}</h3>
            <p>Preview not available for this file type.</p>
            <a href="${fileUrl}" target="_blank" class="btn btn-primary">Open in new tab</a>
        `;
    }

    previewContent.innerHTML = content;
    modal.style.display = 'flex';
}

function closePreview() {
    document.getElementById('previewModal').style.display = 'none';
}

function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastContainer');
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    toastContainer.appendChild(toast);

    // Remove toast after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

// Close modal when clicking outside
window.addEventListener('click', function(event) {
    const modal = document.getElementById('previewModal');
    if (event.target === modal) {
        closePreview();
    }
});
