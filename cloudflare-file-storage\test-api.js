#!/usr/bin/env node

/**
 * Simple API test script for Cloudflare File Storage
 * Usage: node test-api.js <base-url> <api-key> [test-file-path]
 */

const fs = require('fs');
const path = require('path');

// Configuration
const baseUrl = process.argv[2] || 'http://localhost:8788';
const apiKey = process.argv[3];
const testFilePath = process.argv[4] || path.join(__dirname, 'test-image.jpg');

if (!apiKey) {
    console.error('Usage: node test-api.js <base-url> <api-key> [test-file-path]');
    console.error('Example: node test-api.js https://your-site.pages.dev your-api-key ./test.jpg');
    process.exit(1);
}

console.log('🧪 Testing Cloudflare File Storage API');
console.log(`Base URL: ${baseUrl}`);
console.log(`API Key: ${apiKey.substring(0, 8)}...`);
console.log(`Test file: ${testFilePath}`);
console.log('');

// Test functions
async function testUpload() {
    console.log('📤 Testing file upload...');
    
    if (!fs.existsSync(testFilePath)) {
        console.log('⚠️  Test file not found, creating a dummy file...');
        // Create a simple test file
        fs.writeFileSync(testFilePath, 'This is a test file for API testing');
    }
    
    try {
        const FormData = (await import('form-data')).default;
        const fetch = (await import('node-fetch')).default;
        
        const formData = new FormData();
        formData.append('file', fs.createReadStream(testFilePath));
        
        const response = await fetch(`${baseUrl}/api/upload`, {
            method: 'POST',
            headers: {
                'X-API-Key': apiKey,
                ...formData.getHeaders()
            },
            body: formData
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Upload successful!');
            console.log(`   File ID: ${result.fileId}`);
            console.log(`   File Name: ${result.fileName}`);
            console.log(`   Size: ${result.size} bytes`);
            console.log(`   Expires: ${result.expiresAt}`);
            return result.fileId;
        } else {
            console.log('❌ Upload failed:', result.error);
            return null;
        }
    } catch (error) {
        console.log('❌ Upload error:', error.message);
        return null;
    }
}

async function testGetFileInfo(fileId) {
    console.log('\n📋 Testing get file info...');
    
    try {
        const fetch = (await import('node-fetch')).default;
        
        const response = await fetch(`${baseUrl}/api/files/${fileId}`, {
            headers: {
                'X-API-Key': apiKey
            }
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Get file info successful!');
            console.log(`   File Name: ${result.fileName}`);
            console.log(`   Size: ${result.size} bytes`);
            console.log(`   Content Type: ${result.contentType}`);
            console.log(`   Uploaded: ${result.uploadedAt}`);
            return true;
        } else {
            console.log('❌ Get file info failed:', result.error);
            return false;
        }
    } catch (error) {
        console.log('❌ Get file info error:', error.message);
        return false;
    }
}

async function testGetFileUrl(fileId) {
    console.log('\n🔗 Testing get file URL...');
    
    try {
        const fetch = (await import('node-fetch')).default;
        
        const response = await fetch(`${baseUrl}/api/files/${fileId}/url`, {
            headers: {
                'X-API-Key': apiKey
            }
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Get file URL successful!');
            console.log(`   Access URL: ${result.accessUrl}`);
            console.log(`   Direct URL: ${result.directUrl}`);
            return true;
        } else {
            console.log('❌ Get file URL failed:', result.error);
            return false;
        }
    } catch (error) {
        console.log('❌ Get file URL error:', error.message);
        return false;
    }
}

async function testListFiles() {
    console.log('\n📝 Testing list files...');
    
    try {
        const fetch = (await import('node-fetch')).default;
        
        const response = await fetch(`${baseUrl}/api/files?limit=10`, {
            headers: {
                'X-API-Key': apiKey
            }
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ List files successful!');
            console.log(`   Total files: ${result.files.length}`);
            console.log(`   Has more: ${result.pagination.hasMore}`);
            if (result.files.length > 0) {
                console.log(`   First file: ${result.files[0].fileName}`);
            }
            return true;
        } else {
            console.log('❌ List files failed:', result.error);
            return false;
        }
    } catch (error) {
        console.log('❌ List files error:', error.message);
        return false;
    }
}

async function testDeleteFile(fileId) {
    console.log('\n🗑️  Testing delete file...');
    
    try {
        const fetch = (await import('node-fetch')).default;
        
        const response = await fetch(`${baseUrl}/api/files/${fileId}`, {
            method: 'DELETE',
            headers: {
                'X-API-Key': apiKey
            }
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Delete file successful!');
            console.log(`   Message: ${result.message}`);
            return true;
        } else {
            console.log('❌ Delete file failed:', result.error);
            return false;
        }
    } catch (error) {
        console.log('❌ Delete file error:', error.message);
        return false;
    }
}

// Run all tests
async function runTests() {
    console.log('Starting API tests...\n');
    
    let passed = 0;
    let total = 0;
    
    // Test upload
    total++;
    const fileId = await testUpload();
    if (fileId) passed++;
    
    if (fileId) {
        // Test get file info
        total++;
        if (await testGetFileInfo(fileId)) passed++;
        
        // Test get file URL
        total++;
        if (await testGetFileUrl(fileId)) passed++;
        
        // Test list files
        total++;
        if (await testListFiles()) passed++;
        
        // Test delete file
        total++;
        if (await testDeleteFile(fileId)) passed++;
    }
    
    console.log('\n📊 Test Results:');
    console.log(`   Passed: ${passed}/${total}`);
    console.log(`   Success Rate: ${Math.round((passed/total) * 100)}%`);
    
    if (passed === total) {
        console.log('🎉 All tests passed!');
        process.exit(0);
    } else {
        console.log('⚠️  Some tests failed. Check the output above for details.');
        process.exit(1);
    }
}

// Check if required modules are available
async function checkDependencies() {
    try {
        await import('node-fetch');
        await import('form-data');
    } catch (error) {
        console.log('❌ Missing dependencies. Please install:');
        console.log('   npm install node-fetch form-data');
        process.exit(1);
    }
}

// Main execution
checkDependencies().then(() => {
    runTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
});
