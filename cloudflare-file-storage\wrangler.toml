name = "cloudflare-file-storage"
compatibility_date = "2024-07-31"
compatibility_flags = ["nodejs_compat"]

# Pages configuration
pages_build_output_dir = "dist"

# R2 bucket binding
[[r2_buckets]]
binding = "FILE_BUCKET"
bucket_name = "file-storage-bucket"

# Environment variables
[vars]
MAX_FILE_SIZE = "50000000"  # 50MB in bytes
ALLOWED_FILE_TYPES = "image/jpeg,image/png,image/webp,image/gif,audio/mpeg,audio/wav,audio/m4a,audio/ogg"
FILE_EXPIRY_HOURS = "24"    # Files expire after 24 hours

# Secrets (set via wrangler secret put)
# API_SECRET_KEY - for API authentication
# CORS_ORIGIN - allowed CORS origins (optional)

[build]
command = "npm run build"

# Scheduled tasks for automatic cleanup
[triggers]
crons = ["0 */6 * * *"]  # Run every 6 hours
