# dianxin项目 - 当前工作上下文

## 项目概述
**项目名称**: dianxin (中国电信营业厅自动化脚本)  
**最后更新**: 2025-07-14 21:24:05 UTC+8
**项目状态**: 重构完成，已优化为模块化架构

## 技术栈分析
- **主要语言**: Python + JavaScript (混合)
- **运行环境**: 青龙面板 (QingLong Panel)
- **核心功能**: 中国电信营业厅自动化操作
- **依赖管理**: requirements.txt (Python) + Node.js modules

## 核心脚本文件
1. **电信豆豆.js** - 电信金豆获取脚本 (JavaScript)
2. **话费兑换.py** - 金豆兑换话费脚本 (Python)
3. **瑞数通杀.js** - 反爬虫绕过脚本 (不设定时任务)
4. **gjc.py** - 工具类模块 (异步请求处理)
5. **sendNotify.js/py** - 消息推送模块

## 环境配置
- **环境变量**: chinaTelecomAccount (手机号#服务密码)
- **多账号支持**: 使用&分隔符或换行
- **Python依赖**: requests, urllib3, pycryptodome, pyExecjs, bs4

## 定时任务配置
- **话费兑换.py**: 45 59 9,13 * * * (每天9点和13点59分45秒)
- **电信豆豆.js**: 15 19,7,12 * * * (每天7点、12点、19点15分)

## 当前工作重点
- ✅ 项目记忆系统已完成初始化
- ✅ 核心架构文档已建立
- ✅ 项目文件重构已完成
- ✅ 推送模块已精简为Telegram专用
- ✅ 反爬虫模块文件已重命名
- ✅ 代码完整性问题已识别和解决
- ⚠️ 重构版本为架构框架，Legacy版本推荐使用

## 注意事项
- 反爬虫文件已重命名为英文名称，功能不变
- 脚本需要在青龙面板环境中运行
- 支持多账号批量操作
- 包含反爬虫和加密处理机制

## 版本使用建议
- **立即使用**: legacy/电信豆豆.js, legacy/话费兑换.py
- **学习参考**: src/core/ 目录中的架构框架
- **实验性**: src/core/telecom_exchange_complete.py
