# dianxin项目架构决策记录 (ADR)

## ADR-001: 混合语言架构选择
**日期**: 2025-07-14  
**状态**: 已采用  

### 背景
项目需要在青龙面板环境中运行，同时处理复杂的加密逻辑和业务流程。

### 决策
采用Python + JavaScript混合架构：
- Python处理复杂业务逻辑和加密算法
- JavaScript适配青龙面板原生环境

### 理由
1. **平台兼容性**: 青龙面板对JavaScript支持更好
2. **功能互补**: Python在加密和数据处理方面更强
3. **开发效率**: 利用两种语言的优势
4. **维护性**: 模块化设计便于维护

### 后果
- 需要维护两套运行环境
- 增加了部署复杂度
- 提高了开发灵活性

---

## ADR-002: 异步编程模型
**日期**: 2025-07-14  
**状态**: 已采用  

### 背景
需要处理多账号并发操作，提高执行效率。

### 决策
采用异步编程模型：
- Python使用asyncio + aiohttp
- JavaScript使用Promise + async/await

### 理由
1. **性能提升**: 并发处理多个账号
2. **资源优化**: 减少线程开销
3. **响应性**: 提高用户体验
4. **扩展性**: 支持更多并发操作

### 后果
- 增加了代码复杂度
- 需要处理异步异常
- 提高了执行效率

---

## ADR-003: 反爬虫策略
**日期**: 2025-07-14  
**状态**: 已采用  

### 背景
电信营业厅网站使用瑞数通反爬虫系统，需要绕过检测。

### 决策
实现专门的反爬虫绕过模块：
- 独立的瑞数通杀.js文件
- 动态请求头生成
- 访问频率控制

### 理由
1. **稳定性**: 确保脚本正常运行
2. **隐蔽性**: 避免被检测和封禁
3. **可维护性**: 独立模块便于更新
4. **复用性**: 可供其他脚本使用

### 后果
- 需要持续更新反爬虫策略
- 增加了系统复杂度
- 提高了成功率

---

## ADR-004: 多重加密支持
**日期**: 2025-07-14  
**状态**: 已采用  

### 背景
电信API使用多种加密算法，需要支持不同的加密方式。

### 决策
实现多重加密支持：
- RSA公钥加密
- DES3对称加密
- AES加密算法

### 理由
1. **兼容性**: 适配不同API接口
2. **安全性**: 保护敏感数据传输
3. **灵活性**: 支持算法切换
4. **标准化**: 使用成熟的加密库

### 后果
- 增加了依赖库数量
- 提高了安全性
- 增加了代码复杂度

---

## ADR-005: 配置驱动设计
**日期**: 2025-07-14  
**状态**: 已采用  

### 背景
需要支持多账号配置和灵活的任务调度。

### 决策
采用环境变量配置驱动：
- chinaTelecomAccount环境变量
- 支持多账号格式
- 标准cron表达式

### 理由
1. **灵活性**: 易于配置修改
2. **安全性**: 避免硬编码敏感信息
3. **标准化**: 符合青龙面板规范
4. **扩展性**: 支持新的配置项

### 后果
- 依赖外部配置
- 需要配置文档
- 提高了灵活性

---

## ADR-006: 模块化架构
**日期**: 2025-07-14  
**状态**: 已采用  

### 背景
项目包含多个功能模块，需要良好的代码组织。

### 决策
采用模块化架构设计：
- 独立的业务脚本
- 共享的工具类库
- 统一的通知机制

### 理由
1. **可维护性**: 模块独立便于维护
2. **可复用性**: 工具类可重复使用
3. **可扩展性**: 易于添加新功能
4. **可测试性**: 模块独立便于测试

### 后果
- 增加了文件数量
- 需要模块间协调
- 提高了代码质量

---

## ADR-007: 错误处理策略
**日期**: 2025-07-14  
**状态**: 已采用  

### 背景
网络请求和业务操作可能出现各种异常，需要完善的错误处理。

### 决策
实现分层错误处理策略：
- 自动重试机制
- 异常分类处理
- 通知告警机制

### 理由
1. **稳定性**: 提高脚本运行稳定性
2. **可观测性**: 及时发现和处理问题
3. **用户体验**: 减少手动干预需求
4. **可维护性**: 便于问题定位

### 后果
- 增加了代码复杂度
- 需要完善的日志系统
- 提高了系统可靠性
