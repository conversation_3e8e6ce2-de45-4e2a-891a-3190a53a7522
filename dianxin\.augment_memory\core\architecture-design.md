# dianxin项目架构设计文档

## 整体架构概览

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    青龙面板 (QingLong Panel)                    │
├─────────────────────────────────────────────────────────────┤
│  定时任务调度器                                                │
│  ├── 话费兑换.py (45 59 9,13 * * *)                          │
│  └── 电信豆豆.js (15 19,7,12 * * *)                          │
├─────────────────────────────────────────────────────────────┤
│  核心业务模块                                                 │
│  ├── 认证模块 (登录/验证)                                      │
│  ├── 业务操作模块 (金豆获取/兑换)                               │
│  ├── 反爬虫模块 (瑞数通杀.js)                                  │
│  └── 通知推送模块 (sendNotify)                                │
├─────────────────────────────────────────────────────────────┤
│  工具支撑层                                                   │
│  ├── 网络请求处理 (gjc.py)                                    │
│  ├── 加密解密服务                                             │
│  ├── 缓存管理 (Cache.js)                                     │
│  └── 配置管理                                                │
├─────────────────────────────────────────────────────────────┤
│  外部接口层                                                   │
│  ├── 中国电信营业厅API                                         │
│  ├── 验证码识别服务                                           │
│  └── 消息推送服务                                             │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件设计

### 1. 认证与安全组件
**职责**: 处理用户认证、加密解密、反爬虫绕过

**关键特性**:
- 多重加密支持 (RSA, DES3, AES)
- 动态请求头生成
- 瑞数通反爬虫绕过
- SSL证书处理

**实现文件**:
- `瑞数通杀.js` - 反爬虫核心
- `话费兑换.py` - 加密处理逻辑
- `电信豆豆.js` - 混淆代码执行

### 2. 业务逻辑组件
**职责**: 执行电信营业厅相关业务操作

**核心功能**:
- 用户登录验证
- 金豆余额查询
- 金豆获取任务
- 话费兑换操作
- 权益领取

**实现文件**:
- `电信豆豆.js` - 金豆获取主逻辑
- `话费兑换.py` - 兑换业务处理
- `电信0点权益.py` - 权益领取

### 3. 网络通信组件
**职责**: 处理HTTP请求、响应解析、错误重试

**技术实现**:
- 异步请求处理 (aiohttp)
- 代理支持
- 自动重试机制
- 响应数据解析

**实现文件**:
- `gjc.py` - 通用请求处理
- 各业务脚本中的网络模块

### 4. 配置管理组件
**职责**: 环境变量管理、多账号配置、任务调度配置

**配置项**:
- `chinaTelecomAccount` - 账号配置
- 定时任务规则
- 推送通知配置
- 代理设置

## 数据流设计

### 1. 用户认证流程
```
环境变量读取 → 账号解析 → 登录请求 → 加密处理 → 
认证验证 → Token获取 → 会话保持
```

### 2. 业务执行流程
```
任务触发 → 用户认证 → 业务API调用 → 
数据解析 → 结果处理 → 通知推送
```

### 3. 错误处理流程
```
异常捕获 → 错误分类 → 重试判断 → 
日志记录 → 通知告警 → 任务终止
```

## 扩展性设计

### 1. 多账号支持
- 配置格式: `手机号#服务密码`
- 分隔符支持: `&` 或换行
- 批量处理机制

### 2. 模块化扩展
- 独立的业务脚本
- 共享的工具类库
- 统一的配置管理

### 3. 平台适配
- 青龙面板原生支持
- 标准cron表达式
- 环境变量配置

## 安全性考虑

### 1. 数据加密
- 敏感信息RSA加密传输
- 本地缓存数据保护
- 通信过程SSL加密

### 2. 反检测机制
- 请求头随机化
- 访问频率控制
- 瑞数通反爬虫绕过

### 3. 错误处理
- 异常信息脱敏
- 重试次数限制
- 失败通知机制
