# dianxin项目最佳实践指南

## 代码开发最佳实践

### 1. Python代码规范
```python
# 推荐的导入顺序
import sys
import os
import asyncio
import aiohttp
import requests
from loguru import logger
from Crypto.PublicKey import RSA

# 异步函数命名规范
async def get_user_info():
    """获取用户信息的异步函数"""
    pass

# 错误处理最佳实践
try:
    result = await api_call()
except aiohttp.ClientError as e:
    logger.error(f"网络请求失败: {e}")
    return None
except Exception as e:
    logger.error(f"未知错误: {e}")
    raise
```

### 2. JavaScript代码规范
```javascript
// 使用严格模式
'use strict';

// 异步函数处理
async function processAccount(account) {
    try {
        const result = await makeRequest(account);
        return result;
    } catch (error) {
        console.error(`处理账号失败: ${error.message}`);
        return null;
    }
}

// 避免全局变量污染
(function() {
    // 代码逻辑
})();
```

### 3. 配置管理最佳实践
```python
# 环境变量读取
def get_accounts():
    """安全地读取账号配置"""
    account_str = os.environ.get('chinaTelecomAccount', '')
    if not account_str:
        logger.error("未配置账号信息")
        return []
    
    accounts = []
    for line in account_str.strip().split('\n'):
        if '#' in line:
            accounts.append(line.strip())
    return accounts
```

## 安全最佳实践

### 1. 敏感信息处理
- **不要硬编码**: 敏感信息通过环境变量传递
- **日志脱敏**: 记录日志时隐藏敏感信息
- **加密传输**: 使用HTTPS和加密算法
- **定期更新**: 定期更换密钥和证书

### 2. 反爬虫最佳实践
```python
# 请求头随机化
def get_random_headers():
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
    ]
    return {
        'User-Agent': random.choice(user_agents),
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
    }

# 访问频率控制
async def rate_limited_request(url, delay=1):
    await asyncio.sleep(delay)
    return await make_request(url)
```

### 3. 错误处理最佳实践
```python
# 分层异常处理
class TelecomAPIError(Exception):
    """电信API相关异常"""
    pass

class AuthenticationError(TelecomAPIError):
    """认证失败异常"""
    pass

# 重试装饰器
def retry(max_attempts=3, delay=1):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        raise
                    await asyncio.sleep(delay * (2 ** attempt))
        return wrapper
    return decorator
```

## 性能优化最佳实践

### 1. 异步编程优化
```python
# 批量处理账号
async def process_accounts_batch(accounts, batch_size=5):
    """批量处理账号，控制并发数"""
    semaphore = asyncio.Semaphore(batch_size)
    
    async def process_with_semaphore(account):
        async with semaphore:
            return await process_single_account(account)
    
    tasks = [process_with_semaphore(acc) for acc in accounts]
    return await asyncio.gather(*tasks, return_exceptions=True)
```

### 2. 缓存策略
```python
# 内存缓存
cache = {}

def get_cached_data(key, ttl=300):
    """获取缓存数据，支持TTL"""
    if key in cache:
        data, timestamp = cache[key]
        if time.time() - timestamp < ttl:
            return data
    return None

def set_cache_data(key, data):
    """设置缓存数据"""
    cache[key] = (data, time.time())
```

### 3. 资源管理
```python
# 使用上下文管理器
async with aiohttp.ClientSession() as session:
    async with session.get(url) as response:
        data = await response.json()

# 及时释放资源
try:
    # 业务逻辑
    pass
finally:
    # 清理资源
    if 'session' in locals():
        await session.close()
```

## 监控和日志最佳实践

### 1. 日志配置
```python
from loguru import logger

# 配置日志格式
logger.add(
    "logs/telecom_{time:YYYY-MM-DD}.log",
    rotation="1 day",
    retention="7 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    level="INFO"
)

# 结构化日志
logger.info("账号处理完成", extra={
    "account": "138****1234",
    "status": "success",
    "duration": 2.5
})
```

### 2. 性能监控
```python
import time
from functools import wraps

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            logger.info(f"{func.__name__} 执行完成，耗时: {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"{func.__name__} 执行失败，耗时: {duration:.2f}s，错误: {e}")
            raise
    return wrapper
```

## 部署和运维最佳实践

### 1. 青龙面板配置
- **环境变量**: 统一在青龙面板中配置
- **定时任务**: 使用标准cron表达式
- **日志管理**: 定期清理日志文件
- **依赖管理**: 及时更新依赖库

### 2. 监控告警
```python
# 通知推送集成
async def send_notification(title, content, level="info"):
    """发送通知消息"""
    try:
        # 调用sendNotify模块
        await notify(title, content)
        logger.info(f"通知发送成功: {title}")
    except Exception as e:
        logger.error(f"通知发送失败: {e}")
```

### 3. 容错设计
- **优雅降级**: 部分功能失败不影响整体
- **自动恢复**: 临时故障自动重试
- **状态检查**: 定期检查服务状态
- **备份机制**: 重要数据定期备份
