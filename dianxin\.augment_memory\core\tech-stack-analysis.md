# dianxin项目技术栈深度分析

## 项目定位
**项目类型**: 中国电信营业厅自动化脚本集合  
**目标平台**: 青龙面板 (QingLong Panel)  
**应用场景**: 电信用户自动化任务执行  

## 核心技术栈

### 1. 编程语言
- **Python 3.x**
  - 主要用于复杂业务逻辑处理
  - 异步编程支持 (asyncio, aiohttp)
  - 加密解密处理 (pycryptodome)
  
- **JavaScript (Node.js)**
  - 用于青龙面板原生支持
  - 混淆代码执行 (反检测)
  - DOM解析和XML处理

### 2. 关键依赖库

#### Python依赖
```
requests - HTTP请求处理
urllib3 - 底层HTTP库
pycryptodome - 加密解密
pyExecjs - JavaScript执行引擎
bs4 - HTML解析
loguru - 日志处理
lxml - XML解析
aiohttp - 异步HTTP客户端
```

#### JavaScript依赖
```
got - HTTP请求库
xmldom - XML DOM解析
crypto-js - 加密处理
fs - 文件系统操作
```

### 3. 核心功能模块

#### 认证与安全
- RSA公钥加密
- DES3对称加密
- AES加密支持
- SSL证书处理
- 反爬虫绕过 (瑞数通杀)

#### 网络请求
- 异步HTTP请求处理
- 代理支持
- 重试机制
- Cookie管理
- 请求头伪装

#### 数据处理
- JSON数据解析
- XML文档处理
- HTML内容提取
- 时间戳处理
- Base64编码解码

## 架构特点

### 1. 混合语言架构
- Python处理复杂逻辑和加密
- JavaScript适配青龙面板环境
- 共享配置和通知机制

### 2. 模块化设计
- gjc.py: 通用工具类
- sendNotify: 消息推送模块
- Cache.js: 缓存管理
- 独立的业务脚本

### 3. 配置驱动
- 环境变量配置账号信息
- 支持多账号批量处理
- 灵活的定时任务配置

## 运行环境要求
- 青龙面板环境
- Python 3.x运行时
- Node.js运行时
- 网络连接 (访问电信服务)
- 定时任务调度器
