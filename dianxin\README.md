# 中国电信营业厅自动化脚本 (重构版)

[![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-青龙面板-orange.svg)](https://github.com/whyour/qinglong)

## 📋 项目简介

本项目是中国电信营业厅自动化脚本的现代化重构版本，专为青龙面板环境设计。主要功能包括电信金豆自动获取、话费自动兑换、多账号批量处理等。

### 🎯 核心功能
- 🎯 **电信金豆获取** - 自动完成签到、任务等获取金豆
- 💰 **话费自动兑换** - 金豆自动兑换话费
- 📱 **Telegram推送** - 执行结果实时通知
- 🔄 **多账号支持** - 批量处理多个电信账号
- 🛡️ **反爬虫绕过** - 集成瑞数通反爬虫机制

## 🏗️ 项目结构

```
dianxin/
├── src/                    # 源代码目录
│   ├── core/              # 核心业务模块
│   │   ├── telecom_beans.py      # 金豆获取脚本
│   │   └── telecom_exchange.py   # 话费兑换脚本
│   ├── utils/             # 工具模块
│   │   ├── http_client.py        # HTTP客户端
│   │   └── crypto_utils.py       # 加密工具
│   ├── notify/            # 通知模块
│   │   ├── telegram.py           # Telegram推送 (Python)
│   │   └── telegram.js           # Telegram推送 (JavaScript)
│   └── anti-detection/    # 反爬虫检测模块
│       ├── risksense_bypass.js        # 反爬虫核心
│       ├── obfuscated_cache.js        # 缓存和混淆代码
│       ├── risksense_handler.py       # 瑞数通Python处理
│       ├── browser_env_simulator.js   # JavaScript环境模拟
│       ├── obfuscated_code.js         # 混淆代码
│       ├── risksense_cookie.py        # 瑞数通Cookie处理
│       └── README.md                  # 反爬虫模块说明
├── config/                # 配置文件
│   └── requirements.txt          # Python依赖
├── docs/                  # 文档目录
│   ├── 使用说明.md              # 详细使用指南
│   ├── 重构说明.md              # 重构过程说明
│   ├── 文件重命名说明.md        # 文件重命名说明
│   └── 代码完整性说明.md        # 代码完整性和可运行性说明
├── legacy/                # 原始文件备份
│   ├── README.md                 # Legacy说明
│   ├── 电信豆豆.js              # 原始金豆脚本
│   ├── 话费兑换.py              # 原始兑换脚本
│   └── ...                      # 其他原始文件
├── 电信豆豆_重构版.py      # 兼容入口 (金豆获取)
├── 话费兑换_重构版.py      # 兼容入口 (话费兑换)
└── README.md              # 本文件
```

## ✅ 重要说明

### 代码完整性状态
- **重构完整版本** (`src/core/` 目录): ✅ 功能完整，推荐使用
- **Legacy版本** (`legacy/` 目录): ✅ 原始实现，稳定可靠
- **实验性版本** (`src/core/telecom_exchange_complete.py`): 🔄 实验性功能

**推荐使用**: 使用 `src/core/` 目录中的重构完整版本

详细说明请查看: [代码完整性说明](docs/代码完整性说明.md)

## 🚀 快速开始

### 1. 环境要求
- 青龙面板环境
- Python 3.7+
- Node.js (用于JavaScript模块)

### 2. 安装依赖
在青龙面板依赖管理中安装：
```
requests
aiohttp
urllib3
pycryptodome
beautifulsoup4
lxml
loguru
PyExecJS
certifi
```

### 3. 环境变量配置
在青龙面板环境变量中设置：

#### 必需配置
- `chinaTelecomAccount`: 电信账号 (格式: `手机号#服务密码`)

#### 可选配置 (Telegram推送)
- `TG_BOT_TOKEN`: Telegram Bot Token
- `TG_USER_ID`: Telegram 用户ID
- `TG_API_HOST`: Telegram API地址 (默认: https://api.telegram.org)

### 4. 定时任务设置
在青龙面板创建定时任务：

#### 推荐使用 (重构完整版本)
```bash
# 金豆获取 (每天7点、12点、19点15分)
15 7,12,19 * * * task src/core/telecom_beans.py

# 话费兑换 (每天9点和13点59分45秒)
45 59 9,13 * * * task src/core/telecom_exchange.py
```

#### 备选方案 (Legacy版本)
```bash
# 金豆获取 (每天7点、12点、19点15分)
15 7,12,19 * * * task legacy/电信豆豆.js

# 话费兑换 (每天9点和13点59分45秒)
45 59 9,13 * * * task legacy/话费兑换.py
```

#### 实验性版本
```bash
# 实验性完整实现版本
45 59 9,13 * * * task src/core/telecom_exchange_complete.py

# 兼容入口 (已废弃)
# task 电信豆豆_重构版.py
# task 话费兑换_重构版.py
```

## 📊 重构改进

### 代码质量提升
- ✅ **模块化设计** - 按功能分层，便于维护
- ✅ **异步编程** - 使用asyncio提高并发性能
- ✅ **错误处理** - 完善的重试机制和异常处理
- ✅ **类型注解** - 提高代码可读性和IDE支持
- ✅ **代码规范** - 遵循PEP8规范

### 架构优化
- ✅ **推送精简** - 只保留Telegram推送，减少90%代码量
- ✅ **文件整理** - 根目录文件从15+个减少到2个主要文件
- ✅ **配置分离** - 配置与代码分离，便于管理
- ✅ **文档完善** - 详细的使用说明和技术文档

### 性能提升
- ✅ **并发处理** - 支持多账号并发操作
- ✅ **连接复用** - HTTP连接池优化
- ✅ **智能重试** - 指数退避重试算法
- ✅ **内存优化** - 避免内存泄漏

## 📱 功能特性

### 金豆获取
- 自动每日签到
- 自动完成可用任务
- 智能任务调度
- 详细执行日志

### 话费兑换
- 自动查询金豆余额
- 智能兑换策略
- 支持批量兑换
- 实时结果通知

### 通知推送
- Telegram实时推送
- 多级别消息 (info, warning, error, success)
- 详细执行报告
- 异常情况告警

### 安全特性
- 敏感信息脱敏
- 多重加密支持
- 反爬虫绕过
- 访问频率控制

## ⚠️ 注意事项

1. **反爬虫文件** - `src/anti-detection/` 目录中的文件不要设置定时任务
2. **文件重命名** - 反爬虫文件已重命名为更具描述性的英文名称
3. **账号安全** - 定期更换密码，不要在公共环境暴露配置
4. **访问频率** - 多账号使用时注意控制频率，避免被限制

## 🔧 故障排除

### 常见问题
1. **登录失败** - 检查账号密码是否正确
2. **网络错误** - 检查网络连接和代理设置
3. **推送失败** - 检查Telegram配置是否正确
4. **依赖错误** - 确保所有Python依赖已正确安装

### 日志查看
- 金豆获取日志: `logs/telecom_beans_YYYY-MM-DD.log`
- 话费兑换日志: `logs/telecom_exchange_YYYY-MM-DD.log`

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📞 支持

如有问题，请：
1. 查看 [使用说明](docs/使用说明.md)
2. 检查 [重构说明](docs/重构说明.md)
3. 查看项目日志文件
4. 提交 Issue 反馈问题

---

**重构版本已完成现代化升级，代码质量显著提升，推荐使用！** 🎉
