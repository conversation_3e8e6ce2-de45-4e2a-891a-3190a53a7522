# 中国电信营业厅自动化脚本使用说明

## 📋 项目简介

本项目是中国电信营业厅自动化脚本的重构版本，主要功能包括：
- 🎯 电信金豆自动获取
- 💰 金豆自动兑换话费
- 📱 Telegram 推送通知
- 🔄 多账号批量处理

## ⚠️ 重要提醒

### 版本状态说明
- **Legacy版本** (`legacy/` 目录): ✅ **推荐使用** - 功能完整，可直接运行
- **重构版本** (`src/` 目录): ⚠️ 架构框架，部分功能需要进一步开发

**立即使用建议**: 使用 `legacy/` 目录中的原始文件

详细说明: [代码完整性说明](代码完整性说明.md)

## 🏗️ 项目结构

```
dianxin/
├── src/                    # 源代码目录
│   ├── core/              # 核心业务模块
│   │   ├── telecom_beans.py      # 金豆获取脚本
│   │   └── telecom_exchange.py   # 话费兑换脚本
│   ├── utils/             # 工具模块
│   │   ├── http_client.py        # HTTP客户端
│   │   └── crypto_utils.py       # 加密工具
│   ├── notify/            # 通知模块
│   │   ├── telegram.py           # Telegram推送 (Python)
│   │   └── telegram.js           # Telegram推送 (JavaScript)
│   └── anti-detection/    # 反爬虫检测模块
│       ├── risksense_bypass.js        # 反爬虫核心 (不设定时任务)
│       ├── obfuscated_cache.js        # 缓存和混淆代码
│       ├── risksense_handler.py       # 瑞数通Python处理
│       ├── browser_env_simulator.js   # JavaScript环境模拟
│       ├── obfuscated_code.js         # 混淆代码
│       ├── risksense_cookie.py        # 瑞数通Cookie处理
│       └── README.md                  # 反爬虫模块说明
├── config/                # 配置文件
│   └── requirements.txt          # Python依赖
├── docs/                  # 文档目录
│   ├── 使用说明.md              # 本文件
│   └── 重构说明.md              # 重构说明
├── 电信豆豆_重构版.py      # 兼容入口 (金豆获取)
└── 话费兑换_重构版.py      # 兼容入口 (话费兑换)
```

## 🚀 安装配置

### 1. 青龙面板配置

#### 添加脚本文件

##### 推荐方式 (Legacy版本)
将以下文件添加到青龙面板脚本管理：
- `legacy/电信豆豆.js` - 金豆获取脚本 ✅ **推荐**
- `legacy/话费兑换.py` - 话费兑换脚本 ✅ **推荐**
- `legacy/电信0点权益.py` - 权益领取脚本 (可选)

##### 实验性方式 (重构版本)
- `src/core/telecom_exchange_complete.py` - 完整实现版本 (部分功能)
- 整个 `src/` 目录 - 核心模块代码
- **注意**: `src/anti-detection/` 目录中的文件不要设置定时任务

#### Python 依赖安装
在青龙面板依赖管理中创建 Python3 依赖，名称如下：
```
requests
aiohttp
urllib3
pycryptodome
beautifulsoup4
lxml
loguru
PyExecJS
certifi
```

### 2. 环境变量配置

在青龙面板环境变量中创建：

#### 账号配置 (必需)
- **变量名**: `chinaTelecomAccount`
- **变量值**: `手机号#服务密码`
- **多账号**: 使用 `&` 分隔或换行分隔

示例：
```
***********#password123
***********#password456
```

#### Telegram 推送配置 (可选)
- **TG_BOT_TOKEN**: Telegram Bot Token
- **TG_USER_ID**: Telegram 用户ID
- **TG_API_HOST**: Telegram API地址 (默认: https://api.telegram.org)
- **TG_PROXY_HOST**: 代理主机 (可选)
- **TG_PROXY_PORT**: 代理端口 (可选)

### 3. 定时任务设置

在青龙面板创建定时任务：

#### 推荐配置 (Legacy版本)

##### 金豆获取任务
- **名称**: 电信金豆获取
- **命令**: `task legacy/电信豆豆.js`
- **定时规则**: `15 7,12,19 * * *` (每天7点、12点、19点15分执行)

##### 话费兑换任务
- **名称**: 电信话费兑换
- **命令**: `task legacy/话费兑换.py`
- **定时规则**: `45 59 9,13 * * *` (每天9点和13点59分45秒执行)

#### 实验性配置 (重构版本)

##### 完整实现版本
- **名称**: 电信话费兑换 (完整版)
- **命令**: `task src/core/telecom_exchange_complete.py`
- **定时规则**: `45 59 9,13 * * *`

##### 架构框架版本 (不推荐)
- ⚠️ `电信豆豆_重构版.py` - 仅为架构框架，无法直接使用
- ⚠️ `话费兑换_重构版.py` - 仅为架构框架，无法直接使用

## 🔧 功能特性

### 重构优势
- ✅ **模块化设计**: 代码结构清晰，便于维护
- ✅ **异步处理**: 提高执行效率，支持并发操作
- ✅ **错误处理**: 完善的异常处理和重试机制
- ✅ **日志记录**: 详细的日志记录，便于问题排查
- ✅ **安全加密**: 多重加密支持，保护数据安全
- ✅ **精简推送**: 只保留 Telegram 推送，减少依赖

### 核心功能
1. **自动登录**: 支持多账号自动登录验证
2. **任务执行**: 自动完成签到、任务等获取金豆
3. **智能兑换**: 根据金豆余额自动兑换话费
4. **实时通知**: 执行结果实时推送到 Telegram
5. **反爬虫**: 集成瑞数通反爬虫绕过机制

## 📊 监控和日志

### 日志文件
- 金豆获取日志: `logs/telecom_beans_YYYY-MM-DD.log`
- 话费兑换日志: `logs/telecom_exchange_YYYY-MM-DD.log`
- 日志自动轮转: 每日轮转，保留7天

### 通知内容
推送通知包含以下信息：
- 执行时间
- 处理结果统计
- 各账号详细状态
- 获得金豆数量或兑换金额

## ⚠️ 注意事项

### 重要提醒
1. **反爬虫模块** (`src/anti-detection/` 目录) 中的文件不要设置定时任务
2. **文件重命名**: 反爬虫文件已重命名为更具描述性的英文名称
3. 脚本需要在青龙面板环境中运行
4. 建议设置 Telegram 推送以便及时了解执行状态
5. 多账号使用时注意不要过于频繁，避免被限制

### 安全建议
- 定期更换账号密码
- 不要在公共环境中暴露配置信息
- 建议使用独立的 Telegram Bot
- 定期检查日志文件，及时发现异常

### 故障排除
1. **登录失败**: 检查账号密码是否正确
2. **网络错误**: 检查网络连接和代理设置
3. **推送失败**: 检查 Telegram 配置是否正确
4. **依赖错误**: 确保所有 Python 依赖已正确安装

## 🔄 更新日志

### v2.0.0 (重构版)
- 完全重构代码架构，提高可维护性
- 精简推送模块，只保留 Telegram 推送
- 优化异步处理，提高执行效率
- 完善错误处理和日志记录
- 模块化设计，便于功能扩展

## 📞 技术支持

如遇到问题，请：
1. 查看日志文件排查具体错误
2. 检查环境变量配置是否正确
3. 确认依赖包是否完整安装
4. 参考本文档进行配置检查

---

**祝您使用愉快！** 🎉
