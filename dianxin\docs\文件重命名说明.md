# 反爬虫模块文件重命名说明

## 📋 重命名目的

为了提高代码的可读性和可维护性，将反爬虫模块中的中文文件名和不具描述性的文件名重命名为更具意义的英文名称。

## 🔄 文件重命名对照表

| 原文件名 | 新文件名 | 功能描述 |
|---------|---------|----------|
| 瑞数通杀.js | risksense_bypass.js | 瑞数通反爬虫绕过核心 |
| Cache.js | obfuscated_cache.js | 混淆缓存代码 |
| Ruishu.py | risksense_handler.py | 瑞数通Python处理器 |
| gjc.js | browser_env_simulator.js | 浏览器环境模拟器 |
| daima2.js | obfuscated_code.js | 混淆代码 |
| ruishucookie.py | risksense_cookie.py | 瑞数通Cookie处理 |

## 📁 新的文件结构

```
src/anti-detection/
├── risksense_bypass.js        # 瑞数通反爬虫绕过核心
├── obfuscated_cache.js        # 缓存和混淆代码执行
├── risksense_handler.py       # 瑞数通反爬虫Python处理模块
├── browser_env_simulator.js   # JavaScript环境模拟和V8引擎配置
├── obfuscated_code.js         # 混淆代码和反检测逻辑
├── risksense_cookie.py        # 瑞数通Cookie处理和管理
└── README.md                  # 反爬虫模块说明文档
```

## 🔧 代码更新

### 已更新的引用
1. **risksense_cookie.py** 中对 `Cache.js` 的引用已更新为 `obfuscated_cache.js`
2. **所有文档** 中的文件名引用已同步更新
3. **项目结构图** 已反映新的文件名

### 需要注意的引用
如果在其他脚本中有对这些文件的直接引用，需要相应更新：

```python
# 旧的引用方式
import Ruishu
from ruishucookie import initCookie

# 新的引用方式
import risksense_handler
from risksense_cookie import initCookie
```

```javascript
// 旧的引用方式
require('./Cache.js')
require('./gjc.js')

// 新的引用方式
require('./obfuscated_cache.js')
require('./browser_env_simulator.js')
```

## ⚠️ 重要提醒

### 1. 功能不变
- 文件重命名不影响任何功能
- 所有反爬虫逻辑保持不变
- 文件内容完全一致

### 2. 兼容性
- 如果有外部脚本引用这些文件，需要更新引用路径
- 青龙面板中的任务配置无需更改（因为这些文件不设定时任务）

### 3. 维护优势
- **可读性提升**: 文件名更清晰地表达功能
- **国际化**: 使用英文命名，便于国际化协作
- **专业性**: 符合现代软件开发规范
- **可维护性**: 便于理解和维护

## 📊 重命名规则

### 命名规范
1. **功能导向**: 文件名反映主要功能
2. **英文命名**: 使用英文单词，避免中文
3. **下划线分隔**: 使用下划线连接多个单词
4. **描述性**: 名称具有自解释性

### 命名模式
- `risksense_*`: 瑞数通相关功能
- `obfuscated_*`: 混淆代码相关
- `browser_*`: 浏览器环境相关
- `*_simulator`: 模拟器类文件
- `*_handler`: 处理器类文件
- `*_cookie`: Cookie相关功能

## 🔄 迁移指南

### 对于开发者
1. 更新任何直接引用这些文件的代码
2. 更新文档中的文件名引用
3. 更新配置文件中的路径引用

### 对于用户
1. 无需任何操作，功能完全兼容
2. 如有自定义脚本引用这些文件，需要更新路径

## 📈 后续计划

### 持续改进
- 继续优化文件命名规范
- 完善代码注释和文档
- 提升代码可读性

### 版本管理
- 在版本更新日志中记录文件重命名
- 提供迁移指南和兼容性说明

---

**文件重命名完成，项目结构更加清晰和专业！** 🎉
