# Legacy 目录说明

## 📁 目录用途

本目录存放项目重构前的原始文件，仅作为参考和备份使用。

## 📋 文件列表

### 原始脚本文件
- **电信豆豆.js** - 原始的金豆获取脚本 (JavaScript版本)
- **话费兑换.py** - 原始的话费兑换脚本 (Python版本)
- **电信0点权益.py** - 0点权益领取脚本
- **汇总推送.py** - 汇总推送脚本

### 文档文件
- **使用说明.txt** - 原始的使用说明文档

## ⚠️ 重要提醒

1. **不建议使用** - 这些文件已被重构版本替代
2. **仅供参考** - 可用于了解原始实现逻辑
3. **不要设置定时任务** - 避免与重构版本冲突
4. **保留备份** - 作为项目历史记录保存

## 🔄 替代方案

| 原始文件 | 重构版本 | 说明 |
|---------|---------|------|
| 电信豆豆.js | 电信豆豆_重构版.py | 金豆获取功能 |
| 话费兑换.py | 话费兑换_重构版.py | 话费兑换功能 |
| 电信0点权益.py | (暂未重构) | 权益领取功能 |
| 汇总推送.py | src/notify/telegram.py | 推送通知功能 |

## 📈 重构改进

重构版本相比原始版本的主要改进：
- ✅ **模块化设计** - 代码结构更清晰
- ✅ **异步处理** - 性能更好
- ✅ **错误处理** - 更稳定可靠
- ✅ **日志系统** - 便于问题排查
- ✅ **推送精简** - 只保留Telegram推送
- ✅ **配置管理** - 更灵活的配置方式

## 🚀 迁移建议

如果您正在使用原始版本，建议：
1. 备份当前配置
2. 切换到重构版本
3. 测试功能是否正常
4. 删除原始版本的定时任务

---

**注意**: 这些文件仅作历史记录保存，请使用重构版本进行日常操作。
