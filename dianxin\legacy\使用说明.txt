1、添加文件夹下 【 所有文件 】 到青龙面板脚本管理
2、创建python3依赖，名称如下名称:
requests
urllib3
pycryptodome
pyExecjs
bs4
（其余缺啥补啥）

3.环境变量
    创建变量名称:
        chinaTelecomAccount
    值为：
        手机号#服务密码 （多账号&）
4 定时任务
    创建任务→名称（自定义）
        1、命令脚本
            task xxx/xxx/话费兑换.py
            定时规则：45 59 9,13 * * *
            (电信金豆换话费.py更换自己放脚本的目录）
        2、命令脚本
            task xxx/xxx/电信豆豆.js
            定时规则：15 19,7,12 * * *
            (电信金豆换话费.py更换自己放脚本的目录）

注意事项 
    1.瑞数通杀.js 不要更改名字，不用设置定时任务
    2.定时任务设置
        话费兑换.py
        电信豆豆.js