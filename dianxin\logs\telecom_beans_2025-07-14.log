2025-07-14 22:55:05 | INFO | 🚀 开始执行金豆获取任务，共 1 个账号
2025-07-14 22:55:06 | ERROR | 账号 136****2943 登录失败: {'headerInfos': {'code': '0000', 'reason': '操作成功'}, 'responseData': {'resultCode': 'X10401', 'resultDesc': '为保护您的账户安全，当前版本不支持密码登录，请升级至最新版本或使用其他方式登录', 'attach': '', 'data': None}}
2025-07-14 22:55:06 | INFO | 💰 金豆获取任务完成，耗时: 0.79秒
2025-07-14 22:55:06 | INFO | 📊 账号成功: 0/1
2025-07-14 22:55:06 | INFO | 🎯 总获得金豆: 0
2025-07-14 22:55:06 | INFO | 📋 完成任务: 0
2025-07-14 22:55:06 | ERROR | 📤 发送通知失败: object bool can't be used in 'await' expression
2025-07-14 23:05:50 | INFO | 🚀 开始执行金豆获取任务，共 1 个账号
2025-07-14 23:05:50 | ERROR | 账号 136****2943 登录失败: {'headerInfos': {'code': '0000', 'reason': '操作成功'}, 'responseData': {'resultCode': 'X10401', 'resultDesc': '为保护您的账户安全，当前版本不支持密码登录，请升级至最新版本或使用其他方式登录', 'attach': '', 'data': None}}
2025-07-14 23:05:50 | INFO | 💰 金豆获取任务完成，耗时: 0.77秒
2025-07-14 23:05:50 | INFO | 📊 账号成功: 0/1
2025-07-14 23:05:50 | INFO | 🎯 总获得金豆: 0
2025-07-14 23:05:50 | INFO | 📋 完成任务: 0
2025-07-14 23:05:51 | WARNING | 通知发送失败，可能未配置Telegram: object bool can't be used in 'await' expression
2025-07-14 23:05:51 | INFO | 📤 通知发送完成
2025-07-14 23:23:25 | INFO | 🚀 开始执行金豆获取任务，共 1 个账号
2025-07-14 23:23:27 | INFO | 账号 153****0497 登录成功
2025-07-14 23:23:27 | ERROR | 📱153****0497 签到请求失败
2025-07-14 23:23:27 | ERROR | 📱153****0497 获取任务请求失败: 412
2025-07-14 23:23:27 | INFO | 📱153****0497 处理完成: 完成 0 个任务，总获得 0 金豆
2025-07-14 23:23:27 | INFO | 💰 金豆获取任务完成，耗时: 1.42秒
2025-07-14 23:23:27 | INFO | 📊 账号成功: 1/1
2025-07-14 23:23:27 | INFO | 🎯 总获得金豆: 0
2025-07-14 23:23:27 | INFO | 📋 完成任务: 0
2025-07-14 23:23:27 | WARNING | 通知发送失败，可能未配置Telegram: object bool can't be used in 'await' expression
2025-07-14 23:23:27 | INFO | 📤 通知发送完成
