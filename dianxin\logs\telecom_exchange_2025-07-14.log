2025-07-14 23:08:05 | INFO | 🚀 开始执行话费兑换任务，共 1 个账号
2025-07-14 23:08:06 | ERROR | 账号 136****2943 登录失败: {'headerInfos': {'code': '0000', 'reason': '操作成功'}, 'responseData': {'resultCode': 'X10401', 'resultDesc': '为保护您的账户安全，当前版本不支持密码登录，请升级至最新版本或使用其他方式登录', 'attach': '', 'data': None}}
2025-07-14 23:08:06 | INFO | 💰 话费兑换任务完成，耗时: 0.72秒
2025-07-14 23:08:06 | INFO | 📊 账号成功: 0/1
2025-07-14 23:08:06 | INFO | 🎯 兑换成功: 0/0
2025-07-14 23:08:06 | WARNING | 通知发送失败，可能未配置Telegram: object bool can't be used in 'await' expression
2025-07-14 23:08:06 | INFO | 📤 通知发送完成
2025-07-14 23:11:11 | INFO | 🚀 开始执行话费兑换任务，共 1 个账号
2025-07-14 23:11:12 | ERROR | 账号 136****2943 登录失败: {'headerInfos': {'code': '0000', 'reason': '操作成功'}, 'responseData': {'resultCode': 'X10401', 'resultDesc': '为保护您的账户安全，当前版本不支持密码登录，请升级至最新版本或使用其他方式登录', 'attach': '', 'data': None}}
2025-07-14 23:11:12 | INFO | 💰 话费兑换任务完成，耗时: 0.92秒
2025-07-14 23:11:12 | INFO | 📊 账号成功: 0/1
2025-07-14 23:11:12 | INFO | 🎯 兑换成功: 0/0
2025-07-14 23:11:12 | WARNING | 通知发送失败，可能未配置Telegram: object bool can't be used in 'await' expression
2025-07-14 23:11:12 | INFO | 📤 通知发送完成
2025-07-14 23:11:48 | INFO | 🚀 开始执行话费兑换任务，共 1 个账号
2025-07-14 23:11:48 | ERROR | 账号 153****0497 登录失败: {'headerInfos': {'code': '0000', 'reason': '操作成功'}, 'responseData': {'resultCode': 'X10401', 'resultDesc': '为保护您的账户安全，当前版本不支持密码登录，请升级至最新版本或使用其他方式登录', 'attach': '', 'data': None}}
2025-07-14 23:11:48 | INFO | 💰 话费兑换任务完成，耗时: 0.59秒
2025-07-14 23:11:48 | INFO | 📊 账号成功: 0/1
2025-07-14 23:11:48 | INFO | 🎯 兑换成功: 0/0
2025-07-14 23:11:48 | WARNING | 通知发送失败，可能未配置Telegram: object bool can't be used in 'await' expression
2025-07-14 23:11:48 | INFO | 📤 通知发送完成
2025-07-14 23:23:42 | INFO | 🚀 开始执行话费兑换任务，共 1 个账号
2025-07-14 23:23:43 | INFO | 账号 153****0497 登录成功
2025-07-14 23:23:43 | WARNING | 获取反爬虫cookie失败: Cannot connect to host wapact.189.cn:9001 ssl:default [[SSL: DH_KEY_TOO_SMALL] dh key too small (_ssl.c:1010)]
2025-07-14 23:23:43 | INFO | 📱153****0497 获取到 1元话费 的cookies ⏳用时: 0.127 秒
2025-07-14 23:23:44 | ERROR | 📱153****0497 发生错误: Cannot connect to host wapact.189.cn:9001 ssl:default [[SSL: DH_KEY_TOO_SMALL] dh key too small (_ssl.c:1010)]
2025-07-14 23:23:46 | WARNING | 获取反爬虫cookie失败: Cannot connect to host wapact.189.cn:9001 ssl:default [[SSL: DH_KEY_TOO_SMALL] dh key too small (_ssl.c:1010)]
2025-07-14 23:23:46 | INFO | 📱153****0497 获取到 2元话费 的cookies ⏳用时: 0.102 秒
2025-07-14 23:23:46 | ERROR | 📱153****0497 发生错误: Cannot connect to host wapact.189.cn:9001 ssl:default [[SSL: DH_KEY_TOO_SMALL] dh key too small (_ssl.c:1010)]
2025-07-14 23:23:48 | WARNING | 获取反爬虫cookie失败: Cannot connect to host wapact.189.cn:9001 ssl:default [[SSL: DH_KEY_TOO_SMALL] dh key too small (_ssl.c:1010)]
2025-07-14 23:23:48 | INFO | 📱153****0497 获取到 5元话费 的cookies ⏳用时: 0.102 秒
2025-07-14 23:23:49 | ERROR | 📱153****0497 发生错误: Cannot connect to host wapact.189.cn:9001 ssl:default [[SSL: DH_KEY_TOO_SMALL] dh key too small (_ssl.c:1010)]
2025-07-14 23:23:51 | INFO | 📱153****0497 处理完成: 所有兑换均失败
2025-07-14 23:23:51 | INFO | 💰 话费兑换任务完成，耗时: 8.98秒
2025-07-14 23:23:51 | INFO | 📊 账号成功: 0/1
2025-07-14 23:23:51 | INFO | 🎯 兑换成功: 0/3
2025-07-14 23:23:51 | WARNING | 通知发送失败，可能未配置Telegram: object bool can't be used in 'await' expression
2025-07-14 23:23:51 | INFO | 📤 通知发送完成
