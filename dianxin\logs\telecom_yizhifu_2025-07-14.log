2025-07-14 23:35:01 | INFO | 未找到登录缓存，将使用密码登录
2025-07-14 23:35:01 | ERROR | 没有可用的账号配置
2025-07-14 23:37:40 | INFO | 未找到登录缓存，将使用密码登录
2025-07-14 23:37:40 | ERROR | 没有可用的账号配置
2025-07-14 23:38:21 | INFO | 未找到登录缓存，将使用密码登录
2025-07-14 23:38:21 | ERROR | 没有可用的账号配置
2025-07-14 23:41:04 | INFO | 未找到登录缓存，将使用密码登录
2025-07-14 23:41:04 | INFO | 🚀 开始执行翼支付领券任务，共 1 个账号
2025-07-14 23:41:04 | ERROR | 执行任务异常: Resolver requires aiodns library
2025-07-14 23:41:04 | INFO | 💰 翼支付领券任务完成，耗时: 0.01秒
2025-07-14 23:41:04 | INFO | 📊 账号成功: 0/1
2025-07-14 23:41:04 | INFO | 🎫 查询优惠券: 0
2025-07-14 23:41:04 | INFO | 📦 查询权益包: 0
2025-07-14 23:41:04 | WARNING | 通知发送失败，可能未配置Telegram: object bool can't be used in 'await' expression
2025-07-14 23:41:04 | INFO | 📤 通知发送完成
2025-07-14 23:41:44 | INFO | 未找到登录缓存，将使用密码登录
2025-07-14 23:41:44 | INFO | 🚀 开始执行翼支付领券任务，共 1 个账号
2025-07-14 23:41:44 | ERROR | 执行任务异常: Resolver requires aiodns library
2025-07-14 23:41:44 | INFO | 💰 翼支付领券任务完成，耗时: 0.00秒
2025-07-14 23:41:44 | INFO | 📊 账号成功: 0/1
2025-07-14 23:41:44 | INFO | 🎫 查询优惠券: 0
2025-07-14 23:41:44 | INFO | 📦 查询权益包: 0
2025-07-14 23:41:44 | WARNING | 通知发送失败，可能未配置Telegram: object bool can't be used in 'await' expression
2025-07-14 23:41:44 | INFO | 📤 通知发送完成
2025-07-14 23:42:18 | INFO | 未找到登录缓存，将使用密码登录
2025-07-14 23:42:18 | INFO | 🚀 开始执行翼支付领券任务，共 1 个账号
2025-07-14 23:42:18 | INFO | 📱153****0497 使用密码登录
2025-07-14 23:42:18 | INFO | 📱153****0497 开始登录...
2025-07-14 23:42:19 | INFO | 📱153****0497 登录成功
2025-07-14 23:42:19 | ERROR | 📱153****0497 获取session_key异常: 'NoneType' object has no attribute 'get'
2025-07-14 23:42:19 | INFO | 💰 翼支付领券任务完成，耗时: 1.83秒
2025-07-14 23:42:19 | INFO | 📊 账号成功: 0/1
2025-07-14 23:42:19 | INFO | 🎫 查询优惠券: 0
2025-07-14 23:42:19 | INFO | 📦 查询权益包: 0
2025-07-14 23:42:20 | WARNING | 通知发送失败，可能未配置Telegram: object bool can't be used in 'await' expression
2025-07-14 23:42:20 | INFO | 📤 通知发送完成
2025-07-14 23:43:28 | INFO | 加载登录缓存成功
2025-07-14 23:43:28 | INFO | 🚀 开始执行翼支付领券任务，共 1 个账号
2025-07-14 23:43:29 | INFO | 📱153****0497 使用缓存登录
2025-07-14 23:43:29 | ERROR | 📱153****0497 获取session_key异常: 'NoneType' object has no attribute 'get'
2025-07-14 23:43:29 | INFO | 💰 翼支付领券任务完成，耗时: 0.59秒
2025-07-14 23:43:29 | INFO | 📊 账号成功: 0/1
2025-07-14 23:43:29 | INFO | 🎫 查询优惠券: 0
2025-07-14 23:43:29 | INFO | 📦 查询权益包: 0
2025-07-14 23:43:29 | WARNING | 通知发送失败，可能未配置Telegram: object bool can't be used in 'await' expression
2025-07-14 23:43:29 | INFO | 📤 通知发送完成
2025-07-14 23:45:13 | INFO | 加载登录缓存成功
2025-07-14 23:45:13 | INFO | 🚀 开始执行翼支付领券任务，共 1 个账号
2025-07-14 23:45:13 | INFO | 正在获取翼支付公钥...
2025-07-14 23:45:13 | INFO | 翼支付公钥获取成功
2025-07-14 23:45:13 | INFO | 📱153****0497 使用缓存登录
2025-07-14 23:45:13 | ERROR | 📱153****0497 获取session_key异常: 'NoneType' object has no attribute 'get'
2025-07-14 23:45:13 | INFO | 💰 翼支付领券任务完成，耗时: 0.58秒
2025-07-14 23:45:13 | INFO | 📊 账号成功: 0/1
2025-07-14 23:45:13 | INFO | 🎫 查询优惠券: 0
2025-07-14 23:45:13 | INFO | 📦 查询权益包: 0
2025-07-14 23:45:14 | INFO | 📤 通知发送完成
2025-07-14 23:46:22 | INFO | 加载登录缓存成功
2025-07-14 23:46:22 | INFO | 🚀 开始执行翼支付领券任务，共 1 个账号
2025-07-14 23:46:22 | INFO | 正在获取翼支付公钥...
2025-07-14 23:46:22 | DEBUG | 请求成功: https://mapi-h5.bestpay.com.cn/gapi/mapi-gateway/applyLoginFactor - 状态码: 200
2025-07-14 23:46:22 | INFO | 翼支付公钥获取成功
2025-07-14 23:46:22 | INFO | 📱153****0497 使用缓存登录
2025-07-14 23:46:22 | DEBUG | 📱153****0497 发送session_key请求...
2025-07-14 23:46:23 | DEBUG | 请求成功: https://mapi-welcome.bestpay.com.cn/gapi/AppFusionLogin/authorizeAndRegister - 状态码: 200
2025-07-14 23:46:23 | DEBUG | 📱153****0497 session_key响应: {'result': None, 'success': False, 'errorCode': 'API500003', 'errorMsg': '由于系统维护，该功能暂时无法使用，由此带来的不便敬请谅解'}
2025-07-14 23:46:23 | ERROR | 📱153****0497 获取session_key异常: 'NoneType' object has no attribute 'get'
2025-07-14 23:46:23 | INFO | 💰 翼支付领券任务完成，耗时: 0.61秒
2025-07-14 23:46:23 | INFO | 📊 账号成功: 0/1
2025-07-14 23:46:23 | INFO | 🎫 查询优惠券: 0
2025-07-14 23:46:23 | INFO | 📦 查询权益包: 0
2025-07-14 23:46:23 | INFO | 📤 通知发送完成
2025-07-14 23:47:27 | INFO | 加载登录缓存成功
2025-07-14 23:47:27 | INFO | 🚀 开始执行翼支付领券任务，共 1 个账号
2025-07-14 23:47:27 | INFO | 正在获取翼支付公钥...
2025-07-14 23:47:27 | DEBUG | 请求成功: https://mapi-h5.bestpay.com.cn/gapi/mapi-gateway/applyLoginFactor - 状态码: 200
2025-07-14 23:47:27 | INFO | 翼支付公钥获取成功
2025-07-14 23:47:27 | INFO | 📱153****0497 使用缓存登录
2025-07-14 23:47:28 | DEBUG | 📱153****0497 发送session_key请求...
2025-07-14 23:47:28 | DEBUG | 请求成功: https://mapi-welcome.bestpay.com.cn/gapi/AppFusionLogin/authorizeAndRegister - 状态码: 200
2025-07-14 23:47:28 | DEBUG | 📱153****0497 session_key响应: {'result': None, 'success': False, 'errorCode': 'API500003', 'errorMsg': '由于系统维护，该功能暂时无法使用，由此带来的不便敬请谅解'}
2025-07-14 23:47:28 | ERROR | 📱153****0497 获取session_key失败: API500003 - 由于系统维护，该功能暂时无法使用，由此带来的不便敬请谅解
2025-07-14 23:47:28 | INFO | 💰 翼支付领券任务完成，耗时: 0.60秒
2025-07-14 23:47:28 | INFO | 📊 账号成功: 0/1
2025-07-14 23:47:28 | INFO | 🎫 查询优惠券: 0
2025-07-14 23:47:28 | INFO | 📦 查询权益包: 0
2025-07-14 23:47:28 | INFO | 📤 通知发送完成
2025-07-14 23:51:31 | INFO | 加载登录缓存成功
2025-07-14 23:51:31 | INFO | 🚀 开始执行翼支付领券任务，共 1 个账号
2025-07-14 23:51:31 | INFO | 正在获取翼支付公钥...
2025-07-14 23:51:31 | INFO | 翼支付公钥获取成功
2025-07-14 23:51:31 | INFO | 📱153****0497 使用缓存登录
2025-07-14 23:51:32 | ERROR | 📱153****0497 获取session_key失败: API500003 - 由于系统维护，该功能暂时无法使用，由此带来的不便敬请谅解
2025-07-14 23:51:32 | INFO | 💰 翼支付领券任务完成，耗时: 0.58秒
2025-07-14 23:51:32 | INFO | 📊 账号成功: 0/1
2025-07-14 23:51:32 | INFO | 🎫 查询优惠券: 0
2025-07-14 23:51:32 | INFO | 📦 查询权益包: 0
2025-07-14 23:51:32 | INFO | 📤 通知发送完成
