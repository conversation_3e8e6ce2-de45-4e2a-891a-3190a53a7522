#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国电信话费兑换脚本 (重构版 - 完整实现)
集成了实际的业务逻辑和反爬虫模块
cron: 45 59 9,13 * * *
new Env('电信金豆兑换话费');
"""

import os
import sys
import asyncio
import aiohttp
import json
import time
import datetime
import random
import base64
import ssl
import certifi
import re
import requests
from typing import List, Dict, Optional, Tuple
from loguru import logger
from http import cookiejar
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.ssl_ import create_urllib3_context

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, 'src'))

# 导入加密模块
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5, DES3, AES
from Crypto.Util.Padding import pad, unpad

# 导入重构的模块
from notify.telegram import send_notification

# 导入反爬虫模块
sys.path.append(os.path.join(project_root, 'src', 'anti-detection'))
try:
    # 尝试导入反爬虫模块
    from risksense_cookie import initCookie
    logger.info("反爬虫模块导入成功")
except ImportError as e:
    logger.warning(f"反爬虫模块导入失败: {e}")


# SSL和HTTP适配器配置
class BlockAll(cookiejar.CookiePolicy):
    return_ok = set_ok = domain_return_ok = path_return_ok = lambda self, *args, **kwargs: False
    netscape = True
    rfc2965 = hide_cookie2 = False


ORIGIN_CIPHERS = ('DEFAULT@SECLEVEL=0')


class DESAdapter(HTTPAdapter):
    def __init__(self, *args, **kwargs):
        CIPHERS = ORIGIN_CIPHERS.split(':')
        random.shuffle(CIPHERS)
        CIPHERS = ':'.join(CIPHERS)
        self.CIPHERS = CIPHERS + ':!aNULL:!eNULL:!MD5'
        super().__init__(*args, **kwargs)

    def init_poolmanager(self, *args, **kwargs):
        context = create_urllib3_context(ciphers=self.CIPHERS)
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        context.set_ciphers('DEFAULT@SECLEVEL=0')  # 设置更低的安全级别
        kwargs['ssl_context'] = context
        return super(DESAdapter, self).init_poolmanager(*args, **kwargs)

    def proxy_manager_for(self, *args, **kwargs):
        context = create_urllib3_context(ciphers=self.CIPHERS)
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        context.set_ciphers('DEFAULT@SECLEVEL=0')  # 设置更低的安全级别
        kwargs['ssl_context'] = context
        return super(DESAdapter, self).proxy_manager_for(*args, **kwargs)


class TelecomExchangeService:
    """电信话费兑换服务类 (完整实现)"""
    
    def __init__(self):
        """初始化服务"""
        self.accounts = self._load_accounts()
        self.results = []
        self.load_token = {}
        
        # 确保日志目录存在
        os.makedirs("logs", exist_ok=True)
        
        # 配置日志
        logger.add(
            "logs/telecom_exchange_{time:YYYY-MM-DD}.log",
            rotation="1 day",
            retention="7 days",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
            level="INFO"
        )
        
        # 加密密钥配置
        self.des3_key = b'1234567`90koiuyhgtfrdews'
        self.des3_iv = 8 * b'\0'
        
        # RSA公钥配置
        self.public_key_b64 = '''-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBkLT15ThVgz6/NOl6s8GNPofdWzWbCkWnkaAm7O2LjkM1H7dMvzkiqdxU02jamGRHLX/ZNMCXHnPcW/sDhiFCBN18qFvy8g6VYb9QtroI09e176s+ZCtiv7hbin2cCTj99iUpnEloZm19lwHyo69u5UMiPMpq0/XKBO8lYhN/gwIDAQAB
-----END PUBLIC KEY-----'''
        
        self.public_key_data = '''-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC+ugG5A8cZ3FqUKDwM57GM4io6JGcStivT8UdGt67PEOihLZTw3P7371+N47PrmsCpnTRzbTgcupKtUv8ImZalYk65dU8rjC/ridwhw9ffW2LBwvkEnDkkKKRi2liWIItDftJVBiWOh17o6gfbPoNrWORcAdcbpk2L+udld5kZNwIDAQAB
-----END PUBLIC KEY-----'''
        
        # 错误码映射
        self.errcode = {
            "0": "兑换成功",
            "412": "兑换次数已达上限",
            "413": "商品已兑完",
            "420": "未知错误",
            "410": "该活动未开始",
            "501": "服务器处理错误",
            "Y0001": "当前等级不足，去升级兑当前话费",
            "Y0002": "使用翼相连网络600分钟或连接并拓展网络500分钟可兑换此奖品",
            "Y0003": "使用翼相连共享流量400M或共享WIFI：2GB可兑换此奖品",
            "Y0004": "使用翼相连共享流量2GB可兑换此奖品",
            "Y0005": "当前等级不足，去升级兑当前话费",
            "E0001": "您的网龄不足10年，暂不能兑换"
        }
        
        # 初始化HTTP会话
        self._init_session()
    
    def _init_session(self):
        """初始化HTTP会话"""
        requests.packages.urllib3.disable_warnings()
        
        self.session = requests.Session()
        self.session.verify = False  # 禁用SSL验证以解决DH_KEY_TOO_SMALL问题
        self.session.headers = {
            "User-Agent": "Mozilla/5.0 (Linux; Android 13; 22081212C Build/TKQ1.220829.002) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.97 Mobile Safari/537.36",
            "Referer": "https://wapact.189.cn:9001/JinDouMall/JinDouMall_independentDetails.html"
        }
        self.session.mount('https://', DESAdapter())
        self.session.cookies.set_policy(BlockAll())
    
    def _load_accounts(self) -> List[Tuple[str, str]]:
        """加载账号配置"""
        account_str = '***********#093300'#os.environ.get('chinaTelecomAccount', '')
        if not account_str:
            logger.error("未配置 chinaTelecomAccount 环境变量")
            return []
        
        accounts = []
        for line in account_str.strip().split('\n'):
            line = line.strip()
            if '#' in line:
                phone, password = line.split('#', 1)
                accounts.append((phone.strip(), password.strip()))
        
        logger.info(f"加载了 {len(accounts)} 个账号")
        return accounts
    
    def _mask_phone(self, phone: str) -> str:
        """手机号脱敏"""
        if len(phone) >= 11:
            return f"{phone[:3]}****{phone[-4:]}"
        return phone
    
    def get_network_time(self) -> datetime.datetime:
        """获取网络时间"""
        try:
            response = requests.get("https://acs.m.taobao.com/gw/mtop.common.getTimestamp/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "t" in data["data"]:
                    timestamp = int(data["data"]["t"])
                    return datetime.datetime.fromtimestamp(timestamp / 1000)
        except Exception as e:
            logger.warning(f"获取网络时间失败，使用本地时间: {e}")
        
        return datetime.datetime.now()
    
    def encrypt_des3(self, text: str) -> str:
        """DES3加密"""
        cipher = DES3.new(self.des3_key, DES3.MODE_CBC, self.des3_iv)
        ciphertext = cipher.encrypt(pad(text.encode(), DES3.block_size))
        return ciphertext.hex()
    
    def decrypt_des3(self, text: str) -> str:
        """DES3解密"""
        ciphertext = bytes.fromhex(text)
        cipher = DES3.new(self.des3_key, DES3.MODE_CBC, self.des3_iv)
        plaintext = unpad(cipher.decrypt(ciphertext), DES3.block_size)
        return plaintext.decode()
    
    def rsa_encrypt_b64(self, plaintext: str) -> str:
        """RSA加密并Base64编码"""
        public_key = RSA.import_key(self.public_key_b64)
        cipher = PKCS1_v1_5.new(public_key)
        ciphertext = cipher.encrypt(plaintext.encode())
        return base64.b64encode(ciphertext).decode()
    
    def encode_phone(self, text: str) -> str:
        """手机号编码 (更新版本)"""
        encoded_chars = []
        for char in text:
            encoded_chars.append(chr(ord(char) + 2 & 65535))
        return ''.join(encoded_chars)

    def encode_password(self, text: str) -> str:
        """密码编码"""
        encoded_chars = []
        for char in text:
            encoded_chars.append(chr(ord(char) + 2 & 65535))
        return ''.join(encoded_chars)

    def user_login_normal(self, phone: str, password: str) -> Optional[str]:
        """用户登录 (更新版本)"""
        try:
            # 生成UUID (使用新的方式)
            uuid = str(random.randint(****************, ****************))

            timestamp = self.get_network_time().strftime("%Y%m%d%H%M%S")
            login_auth_cipher = f'iPhone 14 13.2.{uuid[:12]}{phone}{timestamp}{password}0$$$0.'

            login_data = {
                "headerInfos": {
                    "code": "userLoginNormal",
                    "timestamp": timestamp,
                    "broadAccount": "",
                    "broadToken": "",
                    "clientType": "#12.2.0#channel50#iPhone 14 Pro#",
                    "shopId": "20002",
                    "source": "110003",
                    "sourcePassword": "Sid98s",
                    "token": "",
                    "userLoginName": self.encode_phone(phone)
                },
                "content": {
                    "attach": "test",
                    "fieldData": {
                        "loginType": "4",
                        "accountType": "",
                        "loginAuthCipherAsymmertric": self.rsa_encrypt_b64(login_auth_cipher),
                        "deviceUid": uuid[:16],
                        "phoneNum": self.encode_phone(phone),
                        "isChinatelecom": "",
                        "systemVersion": "13.2.3",
                        "authentication": self.encode_password(password)
                    }
                }
            }

            response = self.session.post(
                'https://appgologin.189.cn:9031/login/client/userLoginNormal',
                json=login_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if (result.get('responseData') and
                    result['responseData'].get('data') and
                    result['responseData']['data'].get('loginSuccessResult')):

                    login_result = result['responseData']['data']['loginSuccessResult']
                    self.load_token[phone] = login_result

                    # 获取ticket
                    ticket = self.get_ticket(phone, login_result['userId'], login_result['token'])
                    if ticket:
                        logger.info(f"账号 {self._mask_phone(phone)} 登录成功")
                        return ticket
                    else:
                        logger.error(f"账号 {self._mask_phone(phone)} 获取ticket失败")
                        return None
                else:
                    logger.error(f"账号 {self._mask_phone(phone)} 登录失败: {result}")
                    return None
            else:
                logger.error(f"账号 {self._mask_phone(phone)} 登录请求失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"账号 {self._mask_phone(phone)} 登录异常: {e}")
            return None

    def get_ticket(self, phone: str, user_id: str, token: str) -> Optional[str]:
        """获取ticket"""
        try:
            timestamp = self.get_network_time().strftime("%Y%m%d%H%M%S")
            xml_data = f'''<Request><HeaderInfos><Code>getSingle</Code><Timestamp>{timestamp}</Timestamp><BroadAccount></BroadAccount><BroadToken></BroadToken><ClientType>#12.2.0#channel50#iPhone 14 Pro#</ClientType><ShopId>20002</ShopId><Source>110003</Source><SourcePassword>Sid98s</SourcePassword><Token>{token}</Token><UserLoginName>{self.encode_phone(phone)}</UserLoginName></HeaderInfos><Content><Attach>test</Attach><FieldData><TargetId>{self.encrypt_des3(user_id)}</TargetId><Url>4a6862274835b451</Url></FieldData></Content></Request>'''

            response = self.session.post(
                'https://appgologin.189.cn:9031/map/clientXML',
                data=xml_data,
                headers={'user-agent': 'CtClient;10.4.1;Android;13;22081212C;NTQzNzgx!#!MTgwNTg1'},
                timeout=30
            )

            if response.status_code == 200:
                tickets = re.findall('<Ticket>(.*?)</Ticket>', response.text)
                if tickets:
                    return self.decrypt_des3(tickets[0])
                else:
                    logger.error(f"账号 {self._mask_phone(phone)} 未找到ticket")
                    return None
            else:
                logger.error(f"账号 {self._mask_phone(phone)} 获取ticket请求失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"账号 {self._mask_phone(phone)} 获取ticket异常: {e}")
            return None

    async def exchange_credit(self, phone: str, session: aiohttp.ClientSession, title: str, activity_id: str, amount: float) -> Dict:
        """兑换话费"""
        masked_phone = self._mask_phone(phone)
        result = {
            'phone': masked_phone,
            'title': title,
            'success': False,
            'message': '',
            'amount': amount
        }

        try:
            # 第一次请求：获取cookie (使用反爬虫模块)
            start_time = time.time()
            try:
                # 尝试使用反爬虫模块获取cookie
                cookies = await self._get_rs_cookies('https://wapact.189.cn:9001/gateway/standExchange/detailNew/exchange', session)
                end_time = time.time()
                logger.info(f"📱{masked_phone} 获取到 {title} 的cookies ⏳用时: {end_time - start_time:.3f} 秒")
            except Exception as e:
                logger.warning(f"📱{masked_phone} 反爬虫模块获取cookie失败: {e}，使用简单cookie")
                cookies = {}

            # 等待到指定时间 (9:59:59 或 13:59:59)
            await self._wait_for_exchange_time()

            # 发送兑换请求
            url = "https://wapact.189.cn:9001/gateway/standExchange/detailNew/exchange"
            request_start_time = datetime.datetime.now()

            async with session.post(url, json={"activityId": activity_id}, cookies=cookies) as response:
                request_end_time = datetime.datetime.now()

                logger.info(f"📱{masked_phone} 发送兑换请求的时间: {request_start_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")
                logger.info(f"📱{masked_phone} ⌛请求耗时: {(request_end_time - request_start_time).total_seconds():.6f} 秒")

                if response.status == 412:
                    result['message'] = "遇到连续 412 错误，已终止本次兑换"
                    logger.error(f"📱{masked_phone} 😿 {result['message']}")
                    return result

                logger.info(f"📱{masked_phone} 响应码: {response.status}")

                if response.status == 200:
                    response_json = await response.json()
                    if response_json.get("code") == 0:
                        biz_data = response_json.get("biz", {})
                        if biz_data and "resultCode" in biz_data:
                            result_code = biz_data["resultCode"]
                            if result_code in self.errcode:
                                result['message'] = self.errcode[result_code]
                                if result_code == "0":
                                    result['success'] = True
                                    logger.info(f'📱{masked_phone} ------ {title} {result["message"]}')
                                else:
                                    logger.warning(f'📱{masked_phone} ------ {title} {result["message"]}')
                            else:
                                result['message'] = f"未知错误码: {result_code}"
                                logger.error(f'📱{masked_phone} {result["message"]}')
                        else:
                            result['message'] = "响应数据格式错误"
                            logger.error(f'📱{masked_phone} {result["message"]}: {response_json}')
                    else:
                        result['message'] = f"API返回错误: {response_json}"
                        logger.error(f'📱{masked_phone} {result["message"]}')
                else:
                    response_text = await response.text()
                    result['message'] = f"请求失败: {response.status}"
                    logger.error(f"📱{masked_phone} 兑换请求失败: {response_text}")

        except Exception as e:
            result['message'] = f"兑换异常: {str(e)}"
            logger.error(f"📱{masked_phone} 发生错误: {e}")

        return result

    async def _get_rs_cookies(self, url: str, session: aiohttp.ClientSession) -> Dict:
        """获取反爬虫cookie"""
        try:
            # 调用反爬虫模块获取cookies
            cookies_str = initCookie(url)
            if cookies_str:
                # 解析cookie字符串为字典
                cookies = {}
                for cookie in cookies_str.split('; '):
                    if '=' in cookie:
                        key, value = cookie.split('=', 1)
                        cookies[key] = value
                return cookies
            else:
                return {}
        except Exception as e:
            logger.warning(f"获取反爬虫cookie失败: {e}")
            return {}

    async def _wait_for_exchange_time(self):
        """等待到兑换时间"""
        now = self.get_network_time()
        current_hour = now.hour

        # 设置目标时间 (9:59:59 或 13:59:59)
        if current_hour == 9:
            target_time = now.replace(hour=9, minute=59, second=59, microsecond=803600)
        elif current_hour == 13:
            target_time = now.replace(hour=13, minute=59, second=59, microsecond=793600)
        else:
            # 不在兑换时间段，不等待
            return

        # 计算等待时间
        time_diff = (target_time - now).total_seconds()

        # 如果等待时间在合理范围内，则等待
        if 0 <= time_diff <= 300:
            logger.info(f"⏱️等待 {time_diff:.2f} 秒到兑换时间...")
            await asyncio.sleep(time_diff)

    async def process_account(self, phone: str, password: str) -> Dict:
        """处理单个账号"""
        masked_phone = self._mask_phone(phone)
        result = {
            'phone': masked_phone,
            'success': False,
            'message': '',
            'exchanges': []
        }

        try:
            # 登录
            ticket = self.user_login_normal(phone, password)
            if not ticket:
                result['message'] = '登录失败'
                return result

            # 创建异步HTTP会话
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                connector=aiohttp.TCPConnector(ssl=False)
            ) as session:
                # 兑换配置 (根据实际需要配置)
                exchanges = [
                    {"title": "1元话费", "activityId": "ac2023111515000", "amount": 1},
                    {"title": "2元话费", "activityId": "ac2023111515001", "amount": 2},
                    {"title": "5元话费", "activityId": "ac2023111515002", "amount": 5},
                ]

                # 执行兑换
                for exchange in exchanges:
                    exchange_result = await self.exchange_credit(
                        phone, session, exchange["title"],
                        exchange["activityId"], exchange["amount"]
                    )
                    result['exchanges'].append(exchange_result)

                    # 兑换间隔
                    await asyncio.sleep(random.uniform(1, 3))

                # 统计结果
                success_count = sum(1 for ex in result['exchanges'] if ex['success'])
                total_amount = sum(ex['amount'] for ex in result['exchanges'] if ex['success'])

                if success_count > 0:
                    result['success'] = True
                    result['message'] = f'成功兑换 {success_count} 项，总计 {total_amount} 元话费'
                else:
                    result['message'] = '所有兑换均失败'

                logger.info(f"📱{masked_phone} 处理完成: {result['message']}")

        except Exception as e:
            result['message'] = f'处理异常: {str(e)}'
            logger.error(f"📱{masked_phone} 处理异常: {e}")

        return result

    async def run(self):
        """运行话费兑换任务"""
        if not self.accounts:
            logger.error("没有可用的账号配置")
            return

        logger.info(f"🚀 开始执行话费兑换任务，共 {len(self.accounts)} 个账号")
        start_time = time.time()

        try:
            # 并发处理账号 (限制并发数为3)
            semaphore = asyncio.Semaphore(3)

            async def process_with_semaphore(account):
                async with semaphore:
                    return await self.process_account(account[0], account[1])

            tasks = [process_with_semaphore(account) for account in self.accounts]
            self.results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理异常结果
            for i, result in enumerate(self.results):
                if isinstance(result, Exception):
                    phone = self._mask_phone(self.accounts[i][0])
                    self.results[i] = {
                        'phone': phone,
                        'success': False,
                        'message': f'处理异常: {str(result)}',
                        'exchanges': []
                    }

        except Exception as e:
            logger.error(f"执行任务异常: {e}")

        # 统计结果
        total_time = time.time() - start_time
        success_count = sum(1 for r in self.results if r.get('success'))
        total_exchanges = sum(len(r.get('exchanges', [])) for r in self.results)
        successful_exchanges = sum(
            sum(1 for ex in r.get('exchanges', []) if ex.get('success'))
            for r in self.results
        )

        logger.info(f"💰 话费兑换任务完成，耗时: {total_time:.2f}秒")
        logger.info(f"📊 账号成功: {success_count}/{len(self.accounts)}")
        logger.info(f"🎯 兑换成功: {successful_exchanges}/{total_exchanges}")

        # 发送通知
        await self._send_notification()

    async def _send_notification(self):
        """发送通知"""
        try:
            current_time = self.get_network_time().strftime('%Y-%m-%d %H:%M:%S')

            title = "📱 电信话费兑换结果 (完整版)"

            content_lines = [
                f"🕐 执行时间: {current_time}",
                f"📊 处理结果: {sum(1 for r in self.results if r.get('success'))}/{len(self.accounts)}",
                "",
                "📋 详细结果:"
            ]

            for result in self.results:
                status = "✅" if result['success'] else "❌"
                content_lines.append(f"{status} {result['phone']}: {result['message']}")

                # 添加兑换详情
                for exchange in result.get('exchanges', []):
                    ex_status = "✅" if exchange['success'] else "❌"
                    content_lines.append(f"  {ex_status} {exchange['title']}: {exchange['message']}")

            content = "\n".join(content_lines)

            try:
                send_notification(title, content, 'info')
            except Exception as e:
                logger.warning(f"通知发送失败，可能未配置Telegram: {e}")
            logger.info("📤 通知发送完成")

        except Exception as e:
            logger.error(f"📤 发送通知失败: {e}")


async def main():
    """主函数"""
    service = TelecomExchangeService()
    await service.run()


if __name__ == '__main__':
    asyncio.run(main())
