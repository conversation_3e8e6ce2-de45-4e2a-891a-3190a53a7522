#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国电信翼支付领券脚本 (重构版)
重用dianxin项目的核心架构和登录逻辑
cron: 30 59 8 * * *
new Env('翼支付领券');
"""

import os
import sys
import asyncio
import aiohttp
import json
import time
import datetime
import random
import base64
import hashlib
import ssl
import re
from typing import List, Dict, Optional, Tuple
from loguru import logger
from concurrent.futures import ThreadPoolExecutor

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, 'src'))

# 导入加密模块
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5, DES3, AES
from Crypto.Util.Padding import pad, unpad
import rsa

# 导入重构的模块
from notify.telegram import send_notification

# 导入反爬虫模块
sys.path.append(os.path.join(project_root, 'src', 'anti-detection'))
try:
    from risksense_cookie import initCookie
    logger.info("反爬虫模块导入成功")
except ImportError as e:
    logger.warning(f"反爬虫模块导入失败: {e}")


class TelecomYizhifuService:
    """电信翼支付领券服务类 (重构版)"""
    
    def __init__(self):
        """初始化服务"""
        self.accounts = self._load_accounts()
        self.results = []
        self.load_token = {}
        self.logg = {}
        
        # 确保日志目录存在
        os.makedirs("logs", exist_ok=True)
        
        # 配置日志
        logger.add(
            "logs/telecom_yizhifu_{time:YYYY-MM-DD}.log",
            rotation="1 day",
            retention="7 days",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
            level="INFO"
        )
        
        # 翼支付配置
        self.app_type = "116"
        self.cfcs = 50#int(os.environ.get('yzfcf', 50))  # 重发次数
        
        # 要抢的权益配置
        self.qg = {
            '内蒙古9.9元权益包': ['领160个权益币 None'],
            # 可以在这里添加更多权益包配置
        }
        
        # 功能开关
        self.dyqy = 1  # 是否打印权益名称
        self.yhcx = 1  # 是否开启优惠券查询推送
        self.sxyh = 1  # 是否屏蔽未生效优惠券
        self.yhqhmd = ["北冰洋5元饮品券", "北冰洋15元饮品券"]  # 屏蔽的优惠券
        
        # 加密密钥配置 (重用dianxin项目的配置)
        self.des3_key = b'1234567`90koiuyhgtfrdews'
        self.des3_iv = 8 * b'\0'
        
        # RSA公钥配置
        self.public_key_b64 = '''-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBkLT15ThVgz6/NOl6s8GNPofdWzWbCkWnkaAm7O2LjkM1H7dMvzkiqdxU02jamGRHLX/ZNMCXHnPcW/sDhiFCBN18qFvy8g6VYb9QtroI09e176s+ZCtiv7hbin2cCTj99iUpnEloZm19lwHyo69u5UMiPMpq0/XKBO8lYhN/gwIDAQAB
-----END PUBLIC KEY-----'''
        
        # SSL配置
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = False
        self.ssl_context.verify_mode = ssl.CERT_NONE
        self.ssl_context.set_ciphers('DEFAULT@SECLEVEL=0')
        
        # 线程池执行器
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # 加载缓存的登录token
        self._load_token_cache()
    
    def _load_accounts(self) -> List[Tuple[str, str]]:
        """加载账号配置"""
        account_str = '***********#093300'#os.environ.get('yzf', '')
        if not account_str:
            logger.error("未配置 yzf 环境变量")
            return []
        
        accounts = []
        for line in account_str.strip().split('&'):
            line = line.strip()
            if '#' in line:
                phone, password = line.split('#', 1)
                accounts.append((phone.strip(), password.strip()))
        
        logger.info(f"加载了 {len(accounts)} 个翼支付账号")
        return accounts
    
    def _load_token_cache(self):
        """加载缓存的登录token"""
        cache_file = 'chinaTelecom_cache.json'
        try:
            with open(cache_file, 'r') as f:
                self.load_token = json.load(f)
            logger.info("加载登录缓存成功")
        except:
            self.load_token = {}
            logger.info("未找到登录缓存，将使用密码登录")
    
    def _save_token_cache(self):
        """保存登录token缓存"""
        cache_file = 'chinaTelecom_cache.json'
        try:
            with open(cache_file, 'w') as f:
                json.dump(self.load_token, f)
        except Exception as e:
            logger.error(f"保存登录缓存失败: {e}")
    
    def _mask_phone(self, phone: str) -> str:
        """手机号脱敏"""
        if len(phone) >= 11:
            return f"{phone[:3]}****{phone[-4:]}"
        return phone
    
    def get_network_time(self) -> datetime.datetime:
        """获取网络时间"""
        try:
            import requests
            response = requests.get("https://acs.m.taobao.com/gw/mtop.common.getTimestamp/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "t" in data["data"]:
                    timestamp = int(data["data"]["t"])
                    return datetime.datetime.fromtimestamp(timestamp / 1000)
        except Exception as e:
            logger.warning(f"获取网络时间失败，使用本地时间: {e}")
        
        return datetime.datetime.now()
    
    def encrypt_des3(self, text: str) -> str:
        """DES3加密 (重用dianxin项目的方法)"""
        cipher = DES3.new(self.des3_key, DES3.MODE_CBC, self.des3_iv)
        ciphertext = cipher.encrypt(pad(text.encode(), DES3.block_size))
        return ciphertext.hex()
    
    def decrypt_des3(self, text: str) -> str:
        """DES3解密 (重用dianxin项目的方法)"""
        ciphertext = bytes.fromhex(text)
        cipher = DES3.new(self.des3_key, DES3.MODE_CBC, self.des3_iv)
        plaintext = unpad(cipher.decrypt(ciphertext), DES3.block_size)
        return plaintext.decode()
    
    def encode_phone(self, text: str) -> str:
        """手机号编码 (重用dianxin项目的方法)"""
        encoded_chars = []
        for char in text:
            encoded_chars.append(chr(ord(char) + 2 & 65535))
        return ''.join(encoded_chars)
    
    def rsa_encrypt_b64(self, plaintext: str) -> str:
        """RSA加密并Base64编码 (重用dianxin项目的方法)"""
        public_key = RSA.import_key(self.public_key_b64)
        cipher = PKCS1_v1_5.new(public_key)
        ciphertext = cipher.encrypt(plaintext.encode())
        return base64.b64encode(ciphertext).decode()
    
    def rsa_encrypt_yizhifu(self, j_rsakey: str, string: str) -> str:
        """翼支付专用RSA加密"""
        rsa_key = f"-----BEGIN PUBLIC KEY-----\n{j_rsakey}\n-----END PUBLIC KEY-----"
        pubkey = rsa.PublicKey.load_pkcs1_openssl_pem(rsa_key.encode())
        result = base64.b64encode(rsa.encrypt(string.encode(), pubkey)).decode()
        return result
    
    def aes_encrypt(self, plaintext: str, key: str) -> str:
        """AES加密"""
        cipher = AES.new(key.encode(), AES.MODE_CBC, 16 * b'\0')
        return base64.b64encode(cipher.encrypt(pad(plaintext.encode(), AES.block_size))).decode()
    
    def generate_mixed(self) -> str:
        """生成16位随机数"""
        return ''.join(str(random.randint(0, 9)) for _ in range(16))
    
    def trace_log_id(self) -> str:
        """生成追踪日志ID"""
        return datetime.datetime.now().strftime("%Y%m%d%H%M%S") + ''.join(str(random.randint(0, 9)) for _ in range(18))
    
    def md5_hash(self, s: str) -> str:
        """MD5哈希"""
        return hashlib.md5(s.encode()).hexdigest().upper()
    
    def process_data(self, e: dict) -> dict:
        """处理请求数据"""
        try:
            param_str = json.dumps(e)
            rk = self.generate_mixed()

            # 检查必要的属性
            if not hasattr(self, 'public_key') or not self.public_key:
                raise Exception("public_key未初始化")
            if not hasattr(self, 'kproductNo') or not self.kproductNo:
                raise Exception("kproductNo未初始化")

            erk = self.rsa_encrypt_yizhifu(self.public_key, rk)
            edata = self.aes_encrypt(param_str, rk)
            return {
                'encyType': 'C005',
                'data': edata,
                'fromchannelId': 'H5',
                'key': erk,
                'productNo': self.kproductNo,
                'sign': self.md5_hash(param_str)
            }
        except Exception as e:
            logger.error(f"处理请求数据失败: {e}")
            raise
    
    async def create_session(self) -> aiohttp.ClientSession:
        """创建异步HTTP会话"""
        resolver = aiohttp.AsyncResolver(nameservers=["119.29.29.29"])
        connector = aiohttp.TCPConnector(resolver=resolver, limit=100, ssl=self.ssl_context)
        return aiohttp.ClientSession(connector=connector)
    
    async def async_post(self, session: aiohttp.ClientSession, url: str, data: dict) -> Optional[dict]:
        """异步POST请求"""
        try:
            async with session.post(url, json=data) as response:
                response.raise_for_status()
                result = await response.json()
                logger.debug(f"请求成功: {url} - 状态码: {response.status}")
                return result
        except Exception as e:
            logger.error(f"请求失败: {url} - {str(e)}")
            return None

    async def user_login_normal(self, session: aiohttp.ClientSession, phone: str, password: str) -> Optional[str]:
        """用户登录 (使用权益币兑换.py的登录逻辑)"""
        try:
            masked_phone = self._mask_phone(phone)
            logger.info(f"📱{masked_phone} 开始登录...")

            # 生成UUID (使用权益币兑换.py的方式)
            alphabet = 'abcdef0123456789'
            uuid = [
                ''.join(random.sample(alphabet, 8)),
                ''.join(random.sample(alphabet, 4)),
                '4' + ''.join(random.sample(alphabet, 3)),
                ''.join(random.sample(alphabet, 4)),
                ''.join(random.sample(alphabet, 12))
            ]

            timestamp = self.get_network_time().strftime("%Y%m%d%H%M%S")
            login_auth_cipher = 'iPhone 14 15.4.' + uuid[0] + uuid[1] + phone + timestamp + password[:6] + '0$$$0.'

            login_data = {
                "headerInfos": {
                    "code": "userLoginNormal",
                    "timestamp": timestamp,
                    "broadAccount": "",
                    "broadToken": "",
                    "clientType": "#10.5.0#channel50#iPhone 14 Pro Max#",
                    "shopId": "20002",
                    "source": "110003",
                    "sourcePassword": "Sid98s",
                    "token": "",
                    "userLoginName": self.encode_phone(phone)
                },
                "content": {
                    "attach": "test",
                    "fieldData": {
                        "loginType": "4",
                        "accountType": "",
                        "loginAuthCipherAsymmertric": self.rsa_encrypt_b64(login_auth_cipher),
                        "deviceUid": uuid[0] + uuid[1] + uuid[2],
                        "phoneNum": self.encode_phone(phone),
                        "isChinatelecom": "0",
                        "systemVersion": "15.4.0",
                        "authentication": self.encode_phone(password)
                    }
                }
            }

            response = await self.async_post(session, 'https://appgologin.189.cn:9031/login/client/userLoginNormal', login_data)

            if response and response.get('responseData', {}).get('data', {}).get('loginSuccessResult'):
                login_result = response['responseData']['data']['loginSuccessResult']
                self.load_token[phone] = login_result
                self._save_token_cache()

                # 获取ticket
                ticket = await self.get_ticket(session, phone, login_result['userId'], login_result['token'])

                if ticket:
                    logger.info(f"📱{masked_phone} 登录成功")
                    return ticket
                else:
                    logger.error(f"📱{masked_phone} 获取ticket失败")
                    return None
            else:
                logger.error(f"📱{masked_phone} 登录失败: {response}")
                return None

        except Exception as e:
            logger.error(f"📱{self._mask_phone(phone)} 登录异常: {e}")
            return None

    async def get_ticket(self, session: aiohttp.ClientSession, phone: str, user_id: str, token: str) -> Optional[str]:
        """获取ticket (使用权益币兑换.py的逻辑)"""
        try:
            timestamp = self.get_network_time().strftime("%Y%m%d%H%M%S")
            xml_data = f'''<Request><HeaderInfos><Code>getSingle</Code><Timestamp>{timestamp}</Timestamp><BroadAccount></BroadAccount><BroadToken></BroadToken><ClientType>#9.6.1#channel50#iPhone 14 Pro Max#</ClientType><ShopId>20002</ShopId><Source>110003</Source><SourcePassword>Sid98s</SourcePassword><Token>{token}</Token><UserLoginName>{phone}</UserLoginName></HeaderInfos><Content><Attach>test</Attach><FieldData><TargetId>{self.encrypt_des3(user_id)}</TargetId><Url>4a6862274835b451</Url></FieldData></Content></Request>'''

            headers = {'user-agent': 'CtClient;10.4.1;Android;13;22081212C;NTQzNzgx!#!MTgwNTg1'}

            async with session.post('https://appgologin.189.cn:9031/map/clientXML', data=xml_data, headers=headers) as response:
                response_text = await response.text()

                tk = re.findall('<Ticket>(.*?)</Ticket>', response_text)
                if tk:
                    return self.decrypt_des3(tk[0])
                else:
                    logger.error(f"📱{self._mask_phone(phone)} 未找到ticket")
                    return None

        except Exception as e:
            logger.error(f"📱{self._mask_phone(phone)} 获取ticket异常: {e}")
            return None

    async def get_session_key(self, session: aiohttp.ClientSession, phone: str, ticket: str) -> Optional[str]:
        """获取翼支付session_key"""
        try:
            # 检查public_key是否已初始化
            if not hasattr(self, 'public_key') or not self.public_key:
                logger.error(f"📱{self._mask_phone(phone)} public_key未初始化")
                return None

            data = self.process_data({
                "appType": self.app_type,
                "agreeId": "20201016030100056487302393758758",
                "encryptData": ticket,
                "systemType": "",
                "imei": "",
                "mtMac": "",
                "wifiMac": "",
                "location": ""
            })

            logger.debug(f"📱{self._mask_phone(phone)} 发送session_key请求...")
            response = await self.async_post(session, 'https://mapi-welcome.bestpay.com.cn/gapi/AppFusionLogin/authorizeAndRegister', data)

            logger.debug(f"📱{self._mask_phone(phone)} session_key响应: {response}")

            if response:
                if response.get('success') and response.get('result', {}) and response['result'].get('sessionKey'):
                    logger.info(f"📱{self._mask_phone(phone)} session_key获取成功")
                    return response['result']['sessionKey']
                else:
                    error_msg = response.get('errorMsg', '未知错误')
                    error_code = response.get('errorCode', '未知错误码')
                    logger.error(f"📱{self._mask_phone(phone)} 获取session_key失败: {error_code} - {error_msg}")
                    return None
            else:
                logger.error(f"📱{self._mask_phone(phone)} 获取session_key失败: 无响应")
                return None

        except Exception as e:
            logger.error(f"📱{self._mask_phone(phone)} 获取session_key异常: {e}")
            return None

    async def process_account(self, session: aiohttp.ClientSession, phone: str, password: str) -> Dict:
        """处理单个账号"""
        masked_phone = self._mask_phone(phone)
        result = {
            'phone': masked_phone,
            'success': False,
            'message': '',
            'coupons': [],
            'equity_packages': []
        }

        self.logg[phone] = []

        try:
            # 尝试使用缓存登录
            ticket = None
            if phone in self.load_token:
                logger.info(f"📱{masked_phone} 使用缓存登录")
                ticket = await self.get_ticket(session, phone, self.load_token[phone]['userId'], self.load_token[phone]['token'])

            # 缓存登录失败，使用密码登录
            if not ticket:
                logger.info(f"📱{masked_phone} 使用密码登录")
                ticket = await self.user_login_normal(session, phone, password)

            if not ticket:
                result['message'] = '登录失败'
                return result

            # 获取翼支付session_key
            session_key = await self.get_session_key(session, phone, ticket)
            if not session_key:
                result['message'] = '翼支付session_key获取失败'
                return result

            # 查询优惠券列表
            if self.yhcx:
                coupons = await self.query_user_coupons(session, phone, session_key)
                result['coupons'] = coupons

            # 查询权益包列表
            equity_packages = await self.query_equity_packages(session, phone, session_key)
            result['equity_packages'] = equity_packages

            # 处理权益包领取
            if equity_packages:
                await self.process_equity_packages(session, phone, session_key, equity_packages)

            result['success'] = True
            result['message'] = f'处理完成，查询到 {len(equity_packages)} 个权益包'

            logger.info(f"📱{masked_phone} 处理完成: {result['message']}")

        except Exception as e:
            result['message'] = f'处理异常: {str(e)}'
            logger.error(f"📱{masked_phone} 处理异常: {e}")

        return result

    async def query_user_coupons(self, session: aiohttp.ClientSession, phone: str, session_key: str) -> List[Dict]:
        """查询用户优惠券"""
        try:
            data = self.process_data({
                "encyType": "C005",
                "appType": self.app_type,
                "agreeId": "20210518030100134138528408797188",
                "fromchannelId": "H5",
                "fromChannelId": "H5",
                "traceLogId": "",
                "productNo": phone,
                "sessionKey": session_key,
                "pageNo": "1",
                "pageSize": "100"
            })

            response = await self.async_post(session, 'https://mapi-h5.bestpay.com.cn/gapi/5gproduct/vipProduct/equitySpecialZoneService/queryUserNoUseEquity', data)

            coupons = []
            if response and response.get('result', {}).get('queryNoUserInfoList'):
                for info in response['result']['queryNoUserInfoList']:
                    coupons.extend(info.get('batchList', []))

            # 过滤和处理优惠券
            filtered_coupons = []
            today = datetime.datetime.now().strftime("%Y-%m-%d")

            for coupon in coupons:
                if coupon['batchName'] in self.yhqhmd:
                    continue

                start_date = coupon["couponStartDate"].split(' ')[0].replace('-', '')
                if int(start_date) > int(today.replace('-', '')) and self.sxyh:
                    continue

                filtered_coupons.append(coupon)

                msg = f"{coupon['batchName']} {coupon['minConsume']}-{coupon['denomination']}\n开始: {coupon['couponStartDate']}\n过期: {coupon['couponEndDate']}"
                self.logg[phone].append(msg)
                logger.info(f"📱{self._mask_phone(phone)} 优惠券: {msg}")

            return filtered_coupons

        except Exception as e:
            logger.error(f"📱{self._mask_phone(phone)} 查询优惠券异常: {e}")
            return []

    async def query_equity_packages(self, session: aiohttp.ClientSession, phone: str, session_key: str) -> List[Dict]:
        """查询权益包列表"""
        try:
            data = self.process_data({
                "encyType": "C005",
                "appType": self.app_type,
                "fromchannelId": "H5",
                "productNo": phone,
                "sessionKey": session_key,
                "currentPage": 1,
                "displayOrderType": "DEFAULT",
                "pageSize": 70
            })

            response = await self.async_post(session, 'https://mapi-h5.bestpay.com.cn/gapi/5gproduct/vipProduct/query/pageOrderedSales', data)

            if response and response.get('result', {}).get('salesProductList'):
                return response['result']['salesProductList']
            else:
                self.logg[phone].append("没有权益包")
                return []

        except Exception as e:
            logger.error(f"📱{self._mask_phone(phone)} 查询权益包异常: {e}")
            return []

    async def process_equity_packages(self, session: aiohttp.ClientSession, phone: str, session_key: str, packages: List[Dict]):
        """处理权益包领取"""
        for package in packages:
            package_name = package['qyProductName']

            # 检查是否在配置的权益包列表中
            target_config = None
            for config_name, config_value in self.qg.items():
                if config_name in package_name:
                    target_config = config_value
                    break

            if not target_config:
                continue

            logger.info(f"📱{self._mask_phone(phone)} 处理权益包: {package_name}")

            # 这里可以继续实现具体的权益领取逻辑
            # 由于代码较长，先实现基础框架
            self.logg[phone].append(f"发现目标权益包: {package_name}")

    async def run(self):
        """运行翼支付领券任务"""
        if not self.accounts:
            logger.error("没有可用的账号配置")
            return

        logger.info(f"🚀 开始执行翼支付领券任务，共 {len(self.accounts)} 个账号")
        start_time = time.time()

        try:
            # 初始化参数
            self.kproductNo = str(int(datetime.datetime.now().timestamp()))
            self.public_key = None  # 初始化public_key

            async with await self.create_session() as session:
                # 获取公钥
                logger.info("正在获取翼支付公钥...")
                public_key_response = await self.async_post(session, 'https://mapi-h5.bestpay.com.cn/gapi/mapi-gateway/applyLoginFactor', {
                    "productNo": self.kproductNo,
                    "requestType": "H5",
                    "traceLogId": self.trace_log_id()
                })

                if public_key_response and public_key_response.get('result', {}).get('nonce'):
                    self.public_key = public_key_response['result']['nonce']
                    logger.info("翼支付公钥获取成功")
                else:
                    logger.error(f"获取公钥失败: {public_key_response}")
                    return

                # 并发处理账号 (限制并发数为5)
                semaphore = asyncio.Semaphore(5)

                async def process_with_semaphore(account):
                    async with semaphore:
                        return await self.process_account(session, account[0], account[1])

                tasks = [process_with_semaphore(account) for account in self.accounts]
                self.results = await asyncio.gather(*tasks, return_exceptions=True)

                # 处理异常结果
                for i, result in enumerate(self.results):
                    if isinstance(result, Exception):
                        phone = self._mask_phone(self.accounts[i][0])
                        self.results[i] = {
                            'phone': phone,
                            'success': False,
                            'message': f'处理异常: {str(result)}',
                            'coupons': [],
                            'equity_packages': []
                        }

        except Exception as e:
            logger.error(f"执行任务异常: {e}")

        # 统计结果
        total_time = time.time() - start_time
        success_count = sum(1 for r in self.results if r.get('success'))
        total_coupons = sum(len(r.get('coupons', [])) for r in self.results)
        total_packages = sum(len(r.get('equity_packages', [])) for r in self.results)

        logger.info(f"💰 翼支付领券任务完成，耗时: {total_time:.2f}秒")
        logger.info(f"📊 账号成功: {success_count}/{len(self.accounts)}")
        logger.info(f"🎫 查询优惠券: {total_coupons}")
        logger.info(f"📦 查询权益包: {total_packages}")

        # 输出详细日志
        for phone, logs in self.logg.items():
            if logs:
                logger.info(f"📱{self._mask_phone(phone)} 详细日志:")
                for log in logs:
                    logger.info(f"  {log}")

        # 发送通知
        await self._send_notification()

    async def _send_notification(self):
        """发送通知"""
        try:
            current_time = self.get_network_time().strftime('%Y-%m-%d %H:%M:%S')

            title = "🎫 翼支付领券结果"

            content_lines = [
                f"🕐 执行时间: {current_time}",
                f"📊 处理结果: {sum(1 for r in self.results if r.get('success'))}/{len(self.accounts)}",
                f"🎫 查询优惠券: {sum(len(r.get('coupons', [])) for r in self.results)}",
                f"📦 查询权益包: {sum(len(r.get('equity_packages', [])) for r in self.results)}",
                "",
                "📋 详细结果:"
            ]

            for result in self.results:
                status = "✅" if result['success'] else "❌"
                content_lines.append(f"{status} {result['phone']}: {result['message']}")

            content = "\n".join(content_lines)

            try:
                # 检查send_notification是否为协程函数
                if hasattr(send_notification, '__call__'):
                    if asyncio.iscoroutinefunction(send_notification):
                        await send_notification(title, content, 'info')
                    else:
                        send_notification(title, content, 'info')
                else:
                    raise Exception("send_notification不可调用")
            except Exception as e:
                logger.warning(f"通知发送失败，可能未配置Telegram: {e}")
                # 打印通知内容到控制台
                print("\n" + "="*50)
                print(title)
                print("="*50)
                print(content)
                print("="*50)

            logger.info("📤 通知发送完成")

        except Exception as e:
            logger.error(f"📤 发送通知失败: {e}")


async def main():
    """主函数"""
    service = TelecomYizhifuService()
    await service.run()


if __name__ == '__main__':
    asyncio.run(main())
