#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国电信翼支付领券脚本 (测试版)
用于验证代码重用和架构完整性
"""

import os
import sys
import asyncio
import aiohttp
import json
import time
import datetime
import random
from typing import List, Dict, Optional, Tuple
from loguru import logger

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, 'src'))

# 导入重构的模块
from notify.telegram import send_notification


class TelecomYizhifuTestService:
    """电信翼支付领券服务类 (测试版)"""
    
    def __init__(self):
        """初始化服务"""
        self.accounts = self._load_accounts()
        self.results = []
        
        # 确保日志目录存在
        os.makedirs("logs", exist_ok=True)
        
        # 配置日志
        logger.add(
            "logs/telecom_yizhifu_test_{time:YYYY-MM-DD}.log",
            rotation="1 day",
            retention="7 days",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
            level="INFO"
        )
    
    def _load_accounts(self) -> List[Tuple[str, str]]:
        """加载账号配置"""
        # 测试版本使用模拟账号
        test_accounts = [
            ("***********", "test123"),
            ("***********", "test456")
        ]
        
        account_str = os.environ.get('yzf', '')
        if account_str:
            accounts = []
            for line in account_str.strip().split('&'):
                line = line.strip()
                if '@' in line:
                    phone, password = line.split('@', 1)
                    accounts.append((phone.strip(), password.strip()))
            logger.info(f"加载了 {len(accounts)} 个真实账号")
            return accounts
        else:
            logger.info(f"使用 {len(test_accounts)} 个测试账号")
            return test_accounts
    
    def _mask_phone(self, phone: str) -> str:
        """手机号脱敏"""
        if len(phone) >= 11:
            return f"{phone[:3]}****{phone[-4:]}"
        return phone
    
    def get_network_time(self) -> datetime.datetime:
        """获取网络时间"""
        try:
            import requests
            response = requests.get("https://acs.m.taobao.com/gw/mtop.common.getTimestamp/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "t" in data["data"]:
                    timestamp = int(data["data"]["t"])
                    return datetime.datetime.fromtimestamp(timestamp / 1000)
        except Exception as e:
            logger.warning(f"获取网络时间失败，使用本地时间: {e}")
        
        return datetime.datetime.now()
    
    async def mock_login(self, phone: str, password: str) -> bool:
        """模拟登录"""
        masked_phone = self._mask_phone(phone)
        logger.info(f"📱{masked_phone} 开始模拟登录...")
        
        # 模拟网络延迟
        await asyncio.sleep(random.uniform(0.5, 1.5))
        
        # 模拟登录成功率 (90%)
        success = random.random() > 0.1
        
        if success:
            logger.info(f"📱{masked_phone} 模拟登录成功")
            return True
        else:
            logger.error(f"📱{masked_phone} 模拟登录失败")
            return False
    
    async def mock_get_session_key(self, phone: str) -> Optional[str]:
        """模拟获取翼支付session_key"""
        masked_phone = self._mask_phone(phone)
        
        # 模拟网络延迟
        await asyncio.sleep(random.uniform(0.3, 0.8))
        
        # 模拟获取成功率 (85%)
        success = random.random() > 0.15
        
        if success:
            session_key = f"mock_session_key_{random.randint(100000, 999999)}"
            logger.info(f"📱{masked_phone} 获取session_key成功")
            return session_key
        else:
            logger.error(f"📱{masked_phone} 获取session_key失败")
            return None
    
    async def mock_query_coupons(self, phone: str, session_key: str) -> List[Dict]:
        """模拟查询优惠券"""
        masked_phone = self._mask_phone(phone)
        
        # 模拟优惠券列表
        mock_coupons = [
            {"batchName": "星巴克5元券", "minConsume": "30", "denomination": "5", "couponStartDate": "2025-07-14 00:00:00", "couponEndDate": "2025-08-14 23:59:59"},
            {"batchName": "肯德基10元券", "minConsume": "50", "denomination": "10", "couponStartDate": "2025-07-14 00:00:00", "couponEndDate": "2025-08-14 23:59:59"},
            {"batchName": "麦当劳8元券", "minConsume": "40", "denomination": "8", "couponStartDate": "2025-07-15 00:00:00", "couponEndDate": "2025-08-15 23:59:59"},
        ]
        
        # 模拟网络延迟
        await asyncio.sleep(random.uniform(0.5, 1.0))
        
        # 随机返回部分优惠券
        available_coupons = random.sample(mock_coupons, random.randint(1, len(mock_coupons)))
        
        logger.info(f"📱{masked_phone} 查询到 {len(available_coupons)} 张优惠券")
        return available_coupons
    
    async def mock_query_equity_packages(self, phone: str, session_key: str) -> List[Dict]:
        """模拟查询权益包"""
        masked_phone = self._mask_phone(phone)
        
        # 模拟权益包列表
        mock_packages = [
            {"qyProductName": "内蒙古9.9元权益包", "orderNo": "ORDER001"},
            {"qyProductName": "视频会员权益包", "orderNo": "ORDER002"},
            {"qyProductName": "流量加油包", "orderNo": "ORDER003"},
        ]
        
        # 模拟网络延迟
        await asyncio.sleep(random.uniform(0.5, 1.0))
        
        # 随机返回部分权益包
        available_packages = random.sample(mock_packages, random.randint(0, len(mock_packages)))
        
        logger.info(f"📱{masked_phone} 查询到 {len(available_packages)} 个权益包")
        return available_packages
    
    async def process_account(self, phone: str, password: str) -> Dict:
        """处理单个账号"""
        masked_phone = self._mask_phone(phone)
        result = {
            'phone': masked_phone,
            'success': False,
            'message': '',
            'coupons': [],
            'equity_packages': []
        }
        
        try:
            # 模拟登录
            if not await self.mock_login(phone, password):
                result['message'] = '登录失败'
                return result
            
            # 模拟获取session_key
            session_key = await self.mock_get_session_key(phone)
            if not session_key:
                result['message'] = '获取session_key失败'
                return result
            
            # 模拟查询优惠券
            coupons = await self.mock_query_coupons(phone, session_key)
            result['coupons'] = coupons
            
            # 模拟查询权益包
            equity_packages = await self.mock_query_equity_packages(phone, session_key)
            result['equity_packages'] = equity_packages
            
            result['success'] = True
            result['message'] = f'处理完成，查询到 {len(coupons)} 张优惠券，{len(equity_packages)} 个权益包'
            
            logger.info(f"📱{masked_phone} 处理完成: {result['message']}")
        
        except Exception as e:
            result['message'] = f'处理异常: {str(e)}'
            logger.error(f"📱{masked_phone} 处理异常: {e}")
        
        return result
    
    async def run(self):
        """运行翼支付领券任务"""
        if not self.accounts:
            logger.error("没有可用的账号配置")
            return
        
        logger.info(f"🚀 开始执行翼支付领券任务 (测试版本)，共 {len(self.accounts)} 个账号")
        start_time = time.time()
        
        try:
            # 并发处理账号 (限制并发数为3)
            semaphore = asyncio.Semaphore(3)
            
            async def process_with_semaphore(account):
                async with semaphore:
                    return await self.process_account(account[0], account[1])
            
            tasks = [process_with_semaphore(account) for account in self.accounts]
            self.results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            for i, result in enumerate(self.results):
                if isinstance(result, Exception):
                    phone = self._mask_phone(self.accounts[i][0])
                    self.results[i] = {
                        'phone': phone,
                        'success': False,
                        'message': f'处理异常: {str(result)}',
                        'coupons': [],
                        'equity_packages': []
                    }
        
        except Exception as e:
            logger.error(f"执行任务异常: {e}")
        
        # 统计结果
        total_time = time.time() - start_time
        success_count = sum(1 for r in self.results if r.get('success'))
        total_coupons = sum(len(r.get('coupons', [])) for r in self.results)
        total_packages = sum(len(r.get('equity_packages', [])) for r in self.results)
        
        logger.info(f"💰 翼支付领券任务完成，耗时: {total_time:.2f}秒")
        logger.info(f"📊 账号成功: {success_count}/{len(self.accounts)}")
        logger.info(f"🎫 查询优惠券: {total_coupons}")
        logger.info(f"📦 查询权益包: {total_packages}")
        
        # 发送通知
        await self._send_notification()
    
    async def _send_notification(self):
        """发送通知"""
        try:
            current_time = self.get_network_time().strftime('%Y-%m-%d %H:%M:%S')
            
            title = "🎫 翼支付领券结果 (测试版本)"
            
            content_lines = [
                f"🕐 执行时间: {current_time}",
                f"📊 处理结果: {sum(1 for r in self.results if r.get('success'))}/{len(self.accounts)}",
                f"🎫 查询优惠券: {sum(len(r.get('coupons', [])) for r in self.results)}",
                f"📦 查询权益包: {sum(len(r.get('equity_packages', [])) for r in self.results)}",
                "",
                "📋 详细结果:"
            ]
            
            for result in self.results:
                status = "✅" if result['success'] else "❌"
                content_lines.append(f"{status} {result['phone']}: {result['message']}")
                
                # 添加优惠券详情
                for coupon in result.get('coupons', []):
                    content_lines.append(f"  🎫 {coupon['batchName']} {coupon['minConsume']}-{coupon['denomination']}")
                
                # 添加权益包详情
                for package in result.get('equity_packages', []):
                    content_lines.append(f"  📦 {package['qyProductName']}")
            
            content = "\n".join(content_lines)
            
            try:
                await send_notification(title, content, 'info')
            except Exception as e:
                logger.warning(f"通知发送失败，可能未配置Telegram: {e}")
                # 打印通知内容到控制台
                print("\n" + "="*50)
                print(title)
                print("="*50)
                print(content)
                print("="*50)
            
            logger.info("📤 通知发送完成")
        
        except Exception as e:
            logger.error(f"📤 发送通知失败: {e}")


async def main():
    """主函数"""
    service = TelecomYizhifuTestService()
    await service.run()


if __name__ == '__main__':
    asyncio.run(main())
