/**
 * Telegram 推送通知模块 (JavaScript版本)
 * 精简版本，只支持 Telegram Bot 推送
 */

const got = require('got');

class TelegramNotifier {
    constructor() {
        // 从环境变量读取配置
        this.botToken = process.env.TG_BOT_TOKEN || '';
        this.userId = process.env.TG_USER_ID || '';
        this.apiHost = process.env.TG_API_HOST || 'https://api.telegram.org';
        this.proxyHost = process.env.TG_PROXY_HOST || '';
        this.proxyPort = process.env.TG_PROXY_PORT || '';
        this.timeout = 15000;
        
        // 验证配置
        this.enabled = !!(this.botToken && this.userId);
        if (!this.enabled) {
            console.warn('⚠️ Telegram 推送未配置或配置不完整');
        }
    }
    
    /**
     * 获取代理配置
     * @returns {Object|null} 代理配置对象
     */
    getProxyConfig() {
        if (this.proxyHost && this.proxyPort) {
            return {
                protocol: 'http',
                host: this.proxyHost,
                port: parseInt(this.proxyPort)
            };
        }
        return null;
    }
    
    /**
     * 发送 Telegram API 请求
     * @param {string} method - API 方法名
     * @param {Object} data - 请求数据
     * @returns {Promise<boolean>} 发送是否成功
     */
    async sendRequest(method, data) {
        if (!this.enabled) {
            console.error('❌ Telegram 推送未启用');
            return false;
        }
        
        const url = `${this.apiHost}/bot${this.botToken}/${method}`;
        const proxyConfig = this.getProxyConfig();
        
        const options = {
            json: data,
            timeout: this.timeout,
            retry: {
                limit: 2,
                methods: ['POST']
            }
        };
        
        // 添加代理配置
        if (proxyConfig) {
            options.agent = {
                http: new (require('http').Agent)({
                    host: proxyConfig.host,
                    port: proxyConfig.port
                }),
                https: new (require('https').Agent)({
                    host: proxyConfig.host,
                    port: proxyConfig.port
                })
            };
        }
        
        try {
            const response = await got.post(url, options);
            const result = JSON.parse(response.body);
            
            if (result.ok) {
                console.log('✅ Telegram 消息发送成功');
                return true;
            } else {
                console.error(`❌ Telegram API 错误: ${result.description || '未知错误'}`);
                return false;
            }
        } catch (error) {
            if (error.name === 'TimeoutError') {
                console.error('❌ Telegram 请求超时');
            } else if (error.response) {
                console.error(`❌ Telegram 请求失败，状态码: ${error.response.statusCode}`);
            } else {
                console.error(`❌ Telegram 发送异常: ${error.message}`);
            }
            return false;
        }
    }
    
    /**
     * 发送文本消息
     * @param {string} title - 消息标题
     * @param {string} content - 消息内容
     * @param {string} parseMode - 解析模式 ('HTML', 'Markdown', null)
     * @returns {Promise<boolean>} 发送是否成功
     */
    async sendMessage(title, content = '', parseMode = 'HTML') {
        if (!this.enabled) {
            return false;
        }
        
        // 格式化消息
        let message;
        if (title) {
            message = parseMode === 'HTML' 
                ? `<b>${title}</b>\n\n${content}`
                : `*${title}*\n\n${content}`;
        } else {
            message = content;
        }
        
        // 限制消息长度 (Telegram 限制 4096 字符)
        if (message.length > 4000) {
            message = message.substring(0, 3900) + '\n\n... (消息过长，已截断)';
        }
        
        const data = {
            chat_id: this.userId,
            text: message,
            parse_mode: parseMode,
            disable_web_page_preview: true
        };
        
        return await this.sendRequest('sendMessage', data);
    }
    
    /**
     * 发送带级别的通知消息
     * @param {string} title - 消息标题
     * @param {string} content - 消息内容
     * @param {string} level - 消息级别 (info, warning, error, success)
     * @returns {Promise<boolean>} 发送是否成功
     */
    async sendNotification(title, content = '', level = 'info') {
        // 根据级别添加图标
        const levelIcons = {
            info: '📢',
            warning: '⚠️',
            error: '❌',
            success: '✅'
        };
        
        const icon = levelIcons[level] || '📢';
        const formattedTitle = `${icon} ${title}`;
        
        return await this.sendMessage(formattedTitle, content);
    }
}

// 创建全局实例
const notifier = new TelegramNotifier();

/**
 * 发送通知消息 (兼容旧接口)
 * @param {string} title - 消息标题
 * @param {string} content - 消息内容
 * @returns {Promise<boolean>} 发送是否成功
 */
async function notify(title, content = '') {
    return await notifier.sendMessage(title, content);
}

/**
 * 发送通知消息 (新接口)
 * @param {string} title - 消息标题
 * @param {string} content - 消息内容
 * @param {string} level - 消息级别
 * @returns {Promise<boolean>} 发送是否成功
 */
async function sendNotification(title, content = '', level = 'info') {
    return await notifier.sendNotification(title, content, level);
}

/**
 * 检查 Telegram 推送是否可用
 * @returns {boolean} 是否可用
 */
function isEnabled() {
    return notifier.enabled;
}

/**
 * 获取配置状态
 * @returns {Object} 配置状态信息
 */
function getStatus() {
    return {
        enabled: notifier.enabled,
        botToken: notifier.botToken ? '已配置' : '未配置',
        userId: notifier.userId ? '已配置' : '未配置',
        apiHost: notifier.apiHost,
        proxy: notifier.proxyHost && notifier.proxyPort ? '已配置' : '未配置'
    };
}

// 导出模块
module.exports = {
    notify,
    sendNotification,
    isEnabled,
    getStatus,
    TelegramNotifier
};

// 如果直接运行此文件，执行测试
if (require.main === module) {
    (async () => {
        console.log('🧪 开始 Telegram 推送测试...');
        console.log('📊 配置状态:', getStatus());
        
        if (isEnabled()) {
            const success = await notify('测试消息', '这是一条测试消息，用于验证 Telegram 推送功能是否正常工作。');
            console.log(success ? '✅ Telegram 推送测试成功' : '❌ Telegram 推送测试失败');
        } else {
            console.log('❌ Telegram 推送未配置，无法测试');
        }
    })();
}
