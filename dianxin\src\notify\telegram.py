#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram 推送通知模块
精简版本，只支持 Telegram Bot 推送
"""

import os
import requests
import json
import time
from typing import Optional, Dict, Any
from loguru import logger


class TelegramNotifier:
    """Telegram 推送通知类"""
    
    def __init__(self):
        """初始化 Telegram 推送配置"""
        self.bot_token = os.environ.get('TG_BOT_TOKEN', '')
        self.user_id = os.environ.get('TG_USER_ID', '')
        self.api_host = os.environ.get('TG_API_HOST', 'https://api.telegram.org')
        self.proxy_host = os.environ.get('TG_PROXY_HOST', '')
        self.proxy_port = os.environ.get('TG_PROXY_PORT', '')
        self.timeout = 15
        
        # 验证配置
        self.enabled = bool(self.bot_token and self.user_id)
        if not self.enabled:
            logger.warning("Telegram 推送未配置或配置不完整")
    
    def _get_proxies(self) -> Optional[Dict[str, str]]:
        """获取代理配置"""
        if self.proxy_host and self.proxy_port:
            proxy_url = f"http://{self.proxy_host}:{self.proxy_port}"
            return {
                'http': proxy_url,
                'https': proxy_url
            }
        return None
    
    def _send_request(self, method: str, data: Dict[str, Any]) -> bool:
        """发送 Telegram API 请求"""
        if not self.enabled:
            logger.error("Telegram 推送未启用")
            return False
        
        url = f"{self.api_host}/bot{self.bot_token}/{method}"
        proxies = self._get_proxies()
        
        try:
            response = requests.post(
                url,
                json=data,
                proxies=proxies,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('ok'):
                    logger.info("Telegram 消息发送成功")
                    return True
                else:
                    logger.error(f"Telegram API 错误: {result.get('description', '未知错误')}")
            else:
                logger.error(f"Telegram 请求失败，状态码: {response.status_code}")
                
        except requests.exceptions.Timeout:
            logger.error("Telegram 请求超时")
        except requests.exceptions.RequestException as e:
            logger.error(f"Telegram 请求异常: {e}")
        except Exception as e:
            logger.error(f"Telegram 发送失败: {e}")
        
        return False
    
    def send_message(self, title: str, content: str, parse_mode: str = 'HTML') -> bool:
        """
        发送文本消息
        
        Args:
            title: 消息标题
            content: 消息内容
            parse_mode: 解析模式 ('HTML', 'Markdown', None)
        
        Returns:
            bool: 发送是否成功
        """
        if not self.enabled:
            return False
        
        # 格式化消息
        if title:
            message = f"<b>{title}</b>\n\n{content}" if parse_mode == 'HTML' else f"*{title}*\n\n{content}"
        else:
            message = content
        
        # 限制消息长度 (Telegram 限制 4096 字符)
        if len(message) > 4000:
            message = message[:3900] + "\n\n... (消息过长，已截断)"
        
        data = {
            'chat_id': self.user_id,
            'text': message,
            'parse_mode': parse_mode,
            'disable_web_page_preview': True
        }
        
        return self._send_request('sendMessage', data)
    
    def send_document(self, title: str, file_path: str, caption: str = '') -> bool:
        """
        发送文档
        
        Args:
            title: 文档标题
            file_path: 文件路径
            caption: 文档说明
        
        Returns:
            bool: 发送是否成功
        """
        if not self.enabled:
            return False
        
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False
        
        url = f"{self.api_host}/bot{self.bot_token}/sendDocument"
        proxies = self._get_proxies()
        
        try:
            with open(file_path, 'rb') as file:
                files = {'document': file}
                data = {
                    'chat_id': self.user_id,
                    'caption': f"{title}\n{caption}" if caption else title
                }
                
                response = requests.post(
                    url,
                    data=data,
                    files=files,
                    proxies=proxies,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('ok'):
                        logger.info("Telegram 文档发送成功")
                        return True
                    else:
                        logger.error(f"Telegram API 错误: {result.get('description', '未知错误')}")
                else:
                    logger.error(f"Telegram 文档发送失败，状态码: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"Telegram 文档发送异常: {e}")
        
        return False


# 全局通知实例
notifier = TelegramNotifier()


def notify(title: str, content: str = '') -> bool:
    """
    发送通知消息 (兼容旧接口)
    
    Args:
        title: 消息标题
        content: 消息内容
    
    Returns:
        bool: 发送是否成功
    """
    return notifier.send_message(title, content)


def send_notification(title: str, content: str = '', level: str = 'info') -> bool:
    """
    发送通知消息 (新接口)
    
    Args:
        title: 消息标题
        content: 消息内容
        level: 消息级别 (info, warning, error)
    
    Returns:
        bool: 发送是否成功
    """
    # 根据级别添加图标
    level_icons = {
        'info': '📢',
        'warning': '⚠️',
        'error': '❌',
        'success': '✅'
    }
    
    icon = level_icons.get(level, '📢')
    formatted_title = f"{icon} {title}"
    
    return notifier.send_message(formatted_title, content)


if __name__ == '__main__':
    # 测试代码
    test_title = "测试消息"
    test_content = "这是一条测试消息，用于验证 Telegram 推送功能是否正常工作。"
    
    if notify(test_title, test_content):
        print("✅ Telegram 推送测试成功")
    else:
        print("❌ Telegram 推送测试失败")
