#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加密工具模块
提供RSA、DES3、AES等加密算法的统一接口
"""

import base64
import hashlib
import hmac
import json
import random
import string
import time
from typing import Optional, Union, Dict, Any

from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5, DES3, AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Util.strxor import strxor
from loguru import logger


class CryptoUtils:
    """加密工具类"""
    
    @staticmethod
    def generate_random_string(length: int = 16, chars: str = None) -> str:
        """
        生成随机字符串
        
        Args:
            length: 字符串长度
            chars: 字符集，默认为字母数字
        
        Returns:
            str: 随机字符串
        """
        if chars is None:
            chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    @staticmethod
    def md5_hash(data: Union[str, bytes]) -> str:
        """
        MD5哈希
        
        Args:
            data: 要哈希的数据
        
        Returns:
            str: MD5哈希值
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
        return hashlib.md5(data).hexdigest()
    
    @staticmethod
    def sha256_hash(data: Union[str, bytes]) -> str:
        """
        SHA256哈希
        
        Args:
            data: 要哈希的数据
        
        Returns:
            str: SHA256哈希值
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
        return hashlib.sha256(data).hexdigest()
    
    @staticmethod
    def hmac_sha256(key: Union[str, bytes], data: Union[str, bytes]) -> str:
        """
        HMAC-SHA256签名
        
        Args:
            key: 签名密钥
            data: 要签名的数据
        
        Returns:
            str: HMAC签名
        """
        if isinstance(key, str):
            key = key.encode('utf-8')
        if isinstance(data, str):
            data = data.encode('utf-8')
        return hmac.new(key, data, hashlib.sha256).hexdigest()


class RSAEncryption:
    """RSA加密类"""
    
    def __init__(self, public_key: Optional[str] = None, private_key: Optional[str] = None):
        """
        初始化RSA加密
        
        Args:
            public_key: RSA公钥 (PEM格式)
            private_key: RSA私钥 (PEM格式)
        """
        self.public_key = None
        self.private_key = None
        
        if public_key:
            self.load_public_key(public_key)
        if private_key:
            self.load_private_key(private_key)
    
    def load_public_key(self, key_data: str):
        """加载RSA公钥"""
        try:
            # 处理公钥格式
            if not key_data.startswith('-----BEGIN'):
                # 如果是纯base64格式，添加PEM头尾
                key_data = f"-----BEGIN PUBLIC KEY-----\n{key_data}\n-----END PUBLIC KEY-----"
            
            self.public_key = RSA.import_key(key_data)
            logger.info("RSA公钥加载成功")
        except Exception as e:
            logger.error(f"RSA公钥加载失败: {e}")
            raise
    
    def load_private_key(self, key_data: str):
        """加载RSA私钥"""
        try:
            if not key_data.startswith('-----BEGIN'):
                key_data = f"-----BEGIN PRIVATE KEY-----\n{key_data}\n-----END PRIVATE KEY-----"
            
            self.private_key = RSA.import_key(key_data)
            logger.info("RSA私钥加载成功")
        except Exception as e:
            logger.error(f"RSA私钥加载失败: {e}")
            raise
    
    def encrypt(self, data: Union[str, bytes]) -> str:
        """
        RSA加密
        
        Args:
            data: 要加密的数据
        
        Returns:
            str: Base64编码的加密结果
        """
        if not self.public_key:
            raise ValueError("未加载RSA公钥")
        
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        try:
            cipher = PKCS1_v1_5.new(self.public_key)
            encrypted = cipher.encrypt(data)
            return base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            logger.error(f"RSA加密失败: {e}")
            raise
    
    def decrypt(self, encrypted_data: str) -> str:
        """
        RSA解密
        
        Args:
            encrypted_data: Base64编码的加密数据
        
        Returns:
            str: 解密后的数据
        """
        if not self.private_key:
            raise ValueError("未加载RSA私钥")
        
        try:
            encrypted_bytes = base64.b64decode(encrypted_data)
            cipher = PKCS1_v1_5.new(self.private_key)
            decrypted = cipher.decrypt(encrypted_bytes, None)
            
            if decrypted is None:
                raise ValueError("RSA解密失败")
            
            return decrypted.decode('utf-8')
        except Exception as e:
            logger.error(f"RSA解密失败: {e}")
            raise


class AESEncryption:
    """AES加密类"""
    
    def __init__(self, key: Union[str, bytes], mode: int = AES.MODE_CBC):
        """
        初始化AES加密
        
        Args:
            key: AES密钥
            mode: 加密模式
        """
        if isinstance(key, str):
            key = key.encode('utf-8')
        
        # 确保密钥长度为16、24或32字节
        if len(key) not in [16, 24, 32]:
            key = hashlib.sha256(key).digest()[:32]
        
        self.key = key
        self.mode = mode
    
    def encrypt(self, data: Union[str, bytes], iv: Optional[bytes] = None) -> Dict[str, str]:
        """
        AES加密
        
        Args:
            data: 要加密的数据
            iv: 初始化向量
        
        Returns:
            Dict: 包含加密数据和IV的字典
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        if iv is None:
            iv = CryptoUtils.generate_random_string(16).encode('utf-8')
        
        try:
            cipher = AES.new(self.key, self.mode, iv)
            padded_data = pad(data, AES.block_size)
            encrypted = cipher.encrypt(padded_data)
            
            return {
                'data': base64.b64encode(encrypted).decode('utf-8'),
                'iv': base64.b64encode(iv).decode('utf-8')
            }
        except Exception as e:
            logger.error(f"AES加密失败: {e}")
            raise
    
    def decrypt(self, encrypted_data: str, iv: str) -> str:
        """
        AES解密
        
        Args:
            encrypted_data: Base64编码的加密数据
            iv: Base64编码的初始化向量
        
        Returns:
            str: 解密后的数据
        """
        try:
            encrypted_bytes = base64.b64decode(encrypted_data)
            iv_bytes = base64.b64decode(iv)
            
            cipher = AES.new(self.key, self.mode, iv_bytes)
            decrypted = cipher.decrypt(encrypted_bytes)
            unpadded = unpad(decrypted, AES.block_size)
            
            return unpadded.decode('utf-8')
        except Exception as e:
            logger.error(f"AES解密失败: {e}")
            raise


class DES3Encryption:
    """DES3加密类"""
    
    def __init__(self, key: Union[str, bytes]):
        """
        初始化DES3加密
        
        Args:
            key: DES3密钥
        """
        if isinstance(key, str):
            key = key.encode('utf-8')
        
        # DES3密钥长度必须为16或24字节
        if len(key) == 16:
            key = key + key[:8]  # 扩展到24字节
        elif len(key) != 24:
            key = hashlib.sha256(key).digest()[:24]
        
        self.key = key
    
    def encrypt(self, data: Union[str, bytes], iv: Optional[bytes] = None) -> Dict[str, str]:
        """
        DES3加密
        
        Args:
            data: 要加密的数据
            iv: 初始化向量
        
        Returns:
            Dict: 包含加密数据和IV的字典
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        if iv is None:
            iv = CryptoUtils.generate_random_string(8).encode('utf-8')
        
        try:
            cipher = DES3.new(self.key, DES3.MODE_CBC, iv)
            padded_data = pad(data, DES3.block_size)
            encrypted = cipher.encrypt(padded_data)
            
            return {
                'data': base64.b64encode(encrypted).decode('utf-8'),
                'iv': base64.b64encode(iv).decode('utf-8')
            }
        except Exception as e:
            logger.error(f"DES3加密失败: {e}")
            raise
    
    def decrypt(self, encrypted_data: str, iv: str) -> str:
        """
        DES3解密
        
        Args:
            encrypted_data: Base64编码的加密数据
            iv: Base64编码的初始化向量
        
        Returns:
            str: 解密后的数据
        """
        try:
            encrypted_bytes = base64.b64decode(encrypted_data)
            iv_bytes = base64.b64decode(iv)
            
            cipher = DES3.new(self.key, DES3.MODE_CBC, iv_bytes)
            decrypted = cipher.decrypt(encrypted_bytes)
            unpadded = unpad(decrypted, DES3.block_size)
            
            return unpadded.decode('utf-8')
        except Exception as e:
            logger.error(f"DES3解密失败: {e}")
            raise


# 便捷函数
def create_rsa_encryptor(public_key: str) -> RSAEncryption:
    """创建RSA加密器"""
    return RSAEncryption(public_key=public_key)


def create_aes_encryptor(key: Union[str, bytes]) -> AESEncryption:
    """创建AES加密器"""
    return AESEncryption(key)


def create_des3_encryptor(key: Union[str, bytes]) -> DES3Encryption:
    """创建DES3加密器"""
    return DES3Encryption(key)
