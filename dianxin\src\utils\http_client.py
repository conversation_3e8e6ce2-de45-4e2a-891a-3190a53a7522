#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP 客户端工具类
重构自 gjc.py，提供统一的异步HTTP请求处理
"""

import asyncio
import aiohttp
import time
import random
import json
from typing import Optional, Dict, Any, Union
from loguru import logger


class HttpClient:
    """异步HTTP客户端类"""
    
    def __init__(self, timeout: int = 30, max_retries: int = 3):
        """
        初始化HTTP客户端
        
        Args:
            timeout: 请求超时时间(秒)
            max_retries: 最大重试次数
        """
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = None
        
        # 默认请求头
        self.default_headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Pragma": "no-cache",
            "Upgrade-Insecure-Requests": "1",
            "User-Agent": self._get_random_user_agent()
        }
    
    def _get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36 Edg/127.0.0.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36"
        ]
        return random.choice(user_agents)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.create_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_session()
    
    async def create_session(self):
        """创建aiohttp会话"""
        if self.session is None or self.session.closed:
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
                ssl=False  # 忽略SSL验证
            )
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=self.default_headers
            )
    
    async def close_session(self):
        """关闭aiohttp会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
    
    async def _make_request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        data: Optional[Union[Dict, str]] = None,
        json_data: Optional[Dict] = None,
        proxy: Optional[str] = None,
        **kwargs
    ) -> Optional[aiohttp.ClientResponse]:
        """
        执行HTTP请求
        
        Args:
            method: 请求方法 (GET, POST, etc.)
            url: 请求URL
            headers: 请求头
            data: 表单数据
            json_data: JSON数据
            proxy: 代理地址
            **kwargs: 其他参数
        
        Returns:
            aiohttp.ClientResponse: 响应对象
        """
        if not self.session:
            await self.create_session()
        
        # 合并请求头
        request_headers = self.default_headers.copy()
        if headers:
            request_headers.update(headers)
        
        # 随机化User-Agent
        request_headers["User-Agent"] = self._get_random_user_agent()
        
        try:
            async with self.session.request(
                method=method,
                url=url,
                headers=request_headers,
                data=data,
                json=json_data,
                proxy=proxy,
                **kwargs
            ) as response:
                return response
        except Exception as e:
            logger.error(f"请求异常: {method} {url} - {e}")
            raise
    
    async def request_with_retry(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        data: Optional[Union[Dict, str]] = None,
        json_data: Optional[Dict] = None,
        proxy: Optional[str] = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        带重试机制的HTTP请求
        
        Args:
            method: 请求方法
            url: 请求URL
            headers: 请求头
            data: 表单数据
            json_data: JSON数据
            proxy: 代理地址
            **kwargs: 其他参数
        
        Returns:
            Dict: 包含响应信息的字典
        """
        last_exception = None
        
        for attempt in range(self.max_retries):
            try:
                # 添加随机延迟，避免请求过于频繁
                if attempt > 0:
                    delay = random.uniform(1, 3) * (2 ** (attempt - 1))
                    await asyncio.sleep(delay)
                    logger.info(f"第 {attempt + 1} 次重试请求: {method} {url}")
                
                response = await self._make_request(
                    method, url, headers, data, json_data, proxy, **kwargs
                )
                
                if response:
                    # 读取响应内容
                    try:
                        text_content = await response.text()
                        
                        # 尝试解析JSON
                        try:
                            json_content = json.loads(text_content)
                        except json.JSONDecodeError:
                            json_content = None
                        
                        result = {
                            'status_code': response.status,
                            'headers': dict(response.headers),
                            'text': text_content,
                            'json': json_content,
                            'url': str(response.url),
                            'success': 200 <= response.status < 300
                        }
                        
                        if result['success']:
                            logger.info(f"请求成功: {method} {url} - {response.status}")
                            return result
                        else:
                            logger.warning(f"请求失败: {method} {url} - {response.status}")
                            if attempt == self.max_retries - 1:
                                return result
                    
                    except Exception as e:
                        logger.error(f"读取响应内容失败: {e}")
                        last_exception = e
                
            except Exception as e:
                logger.error(f"请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                last_exception = e
                
                # 如果是最后一次尝试，不再重试
                if attempt == self.max_retries - 1:
                    break
        
        logger.error(f"请求最终失败: {method} {url}")
        return {
            'status_code': 0,
            'headers': {},
            'text': '',
            'json': None,
            'url': url,
            'success': False,
            'error': str(last_exception) if last_exception else '未知错误'
        }
    
    async def get(self, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """GET请求"""
        return await self.request_with_retry('GET', url, **kwargs)
    
    async def post(self, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """POST请求"""
        return await self.request_with_retry('POST', url, **kwargs)
    
    async def put(self, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """PUT请求"""
        return await self.request_with_retry('PUT', url, **kwargs)
    
    async def delete(self, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """DELETE请求"""
        return await self.request_with_retry('DELETE', url, **kwargs)


# 便捷函数
async def get_rs(url: str, session: Optional[HttpClient] = None, **kwargs) -> Optional[Dict[str, Any]]:
    """
    便捷的GET请求函数 (兼容原gjc.py接口)
    
    Args:
        url: 请求URL
        session: HttpClient实例
        **kwargs: 其他参数
    
    Returns:
        Dict: 响应结果
    """
    if session:
        return await session.get(url, **kwargs)
    else:
        async with HttpClient() as client:
            return await client.get(url, **kwargs)


async def post_rs(url: str, session: Optional[HttpClient] = None, **kwargs) -> Optional[Dict[str, Any]]:
    """
    便捷的POST请求函数
    
    Args:
        url: 请求URL
        session: HttpClient实例
        **kwargs: 其他参数
    
    Returns:
        Dict: 响应结果
    """
    if session:
        return await session.post(url, **kwargs)
    else:
        async with HttpClient() as client:
            return await client.post(url, **kwargs)
