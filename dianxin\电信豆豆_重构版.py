#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电信豆豆获取脚本 - 重构版主入口
兼容原有调用方式，重定向到新的模块化脚本
cron: 15 7,12,19 * * *
new Env('电信金豆获取');
"""

import os
import sys
import asyncio

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from src.core.telecom_beans import main
    
    if __name__ == '__main__':
        print("🚀 启动电信金豆获取脚本 (重构版)")
        print("📁 项目结构已优化，使用模块化设计")
        print("📱 推送已精简为 Telegram 专用")
        print("-" * 50)
        
        asyncio.run(main())
        
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保项目结构完整，所有依赖已安装")
    sys.exit(1)
except Exception as e:
    print(f"❌ 脚本执行异常: {e}")
    sys.exit(1)
