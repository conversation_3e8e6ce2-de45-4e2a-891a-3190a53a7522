# 翼支付配置说明

## 📋 环境变量配置

### yzf 账号配置

翼支付脚本需要配置 `yzf` 环境变量来指定要处理的电信账号。

#### 配置格式
```
手机号@服务密码
```

**重要说明**:
- 使用的是**服务密码**，不是手机登录密码
- 服务密码是电信营业厅的6位数字密码
- 如果不知道服务密码，可以通过电信营业厅APP或网站重置

## 🎯 配置示例

### 示例1：单个账号
```bash
export yzf="***********@123456"
```

**解析结果**:
- 手机号: `***********`
- 服务密码: `123456`

### 示例2：两个账号
```bash
export yzf="***********@123456&***********@654321"
```

**解析结果**:
- 账号1: 手机号 `***********`，服务密码 `123456`
- 账号2: 手机号 `***********`，服务密码 `654321`

### 示例3：多个账号
```bash
export yzf="***********@123456&***********@654321&***********@789012"
```

**解析结果**:
- 账号1: 手机号 `***********`，服务密码 `123456`
- 账号2: 手机号 `***********`，服务密码 `654321`
- 账号3: 手机号 `***********`，服务密码 `789012`

## 🔧 代码解析过程

### 解析逻辑
```python
def _load_accounts(self) -> List[Tuple[str, str]]:
    # 1. 获取环境变量
    account_str = os.environ.get('yzf', '')
    
    # 2. 按 & 分割多个账号
    for line in account_str.strip().split('&'):
        line = line.strip()
        
        # 3. 按 @ 分割手机号和密码
        if '@' in line:
            phone, password = line.split('@', 1)
            accounts.append((phone.strip(), password.strip()))
```

### 解析步骤详解

#### 步骤1：获取环境变量
```python
account_str = os.environ.get('yzf', '')
# 例如: "***********@123456&***********@654321"
```

#### 步骤2：分割账号
```python
account_str.strip().split('&')
# 结果: ["***********@123456", "***********@654321"]
```

#### 步骤3：分割手机号和密码
```python
for line in ["***********@123456", "***********@654321"]:
    phone, password = line.split('@', 1)
    # 第一次: phone="***********", password="123456"
    # 第二次: phone="***********", password="654321"
```

## 🚀 使用方法

### 方法1：直接设置环境变量
```bash
# Linux/Mac
export yzf="***********@123456&***********@654321"
python src/core/telecom_yizhifu.py

# Windows CMD
set yzf=***********@123456&***********@654321
python src/core/telecom_yizhifu.py

# Windows PowerShell
$env:yzf="***********@123456&***********@654321"
python src/core/telecom_yizhifu.py
```

### 方法2：青龙面板配置
在青龙面板的环境变量中添加：
```
变量名: yzf
变量值: ***********@123456&***********@654321
```

### 方法3：.env 文件配置
创建 `.env` 文件：
```bash
yzf=***********@123456&***********@654321
```

## ⚠️ 注意事项

### 1. 服务密码说明
- **不是手机登录密码**
- **不是SIM卡PIN码**
- **是电信营业厅的6位数字服务密码**

### 2. 服务密码获取方法
1. **电信营业厅APP**: 登录后在"我的"-"安全中心"中查看或重置
2. **电信网上营业厅**: 登录后在账户管理中查看或重置
3. **拨打10000**: 人工客服可以帮助重置服务密码
4. **营业厅柜台**: 携带身份证到营业厅重置

### 3. 格式要求
- 手机号和密码之间必须用 `@` 分隔
- 多个账号之间必须用 `&` 分隔
- 不要有多余的空格（代码会自动去除空格）

### 4. 错误示例
```bash
# ❌ 错误：使用了错误的分隔符
export yzf="***********:123456"

# ❌ 错误：缺少分隔符
export yzf="***********123456"

# ❌ 错误：使用了错误的账号分隔符
export yzf="***********@123456,***********@654321"

# ✅ 正确：使用正确的格式
export yzf="***********@123456&***********@654321"
```

## 🔍 调试方法

### 查看解析结果
运行脚本时，会在日志中显示解析结果：
```
2025-07-14 23:35:01.401 | INFO | 加载了 2 个翼支付账号
```

### 配置错误提示
如果配置错误，会显示详细的错误信息和配置示例：
```
2025-07-14 23:35:01.401 | ERROR | 未配置 yzf 环境变量
2025-07-14 23:35:01.401 | INFO | 配置示例:
2025-07-14 23:35:01.401 | INFO |   单个账号: export yzf="***********@service123"
2025-07-14 23:35:01.401 | INFO |   多个账号: export yzf="***********@service123&***********@service456"
```

## 📝 其他配置项

### yzfcf 重发次数配置
```bash
export yzfcf=50  # 设置重发次数为50次，默认50次
```

### 权益包配置
在代码中的 `self.qg` 字典中配置要抢的权益包：
```python
self.qg = {
    '内蒙古9.9元权益包': ['领160个权益币 None'],
    '视频会员权益包': ['会员权益 None'],
    # 添加更多权益包...
}
```

## 🎯 完整配置示例

### 青龙面板完整配置
```bash
# 翼支付账号配置
yzf=***********@123456&***********@654321&***********@789012

# 重发次数配置
yzfcf=50

# 定时任务配置
# 30 59 8 * * * task src/core/telecom_yizhifu.py
```

### 本地运行完整示例
```bash
#!/bin/bash
# 设置环境变量
export yzf="***********@123456&***********@654321"
export yzfcf=50

# 运行脚本
python src/core/telecom_yizhifu.py
```

---

**📞 如有问题，请检查服务密码是否正确，或联系电信客服10000重置服务密码。**
