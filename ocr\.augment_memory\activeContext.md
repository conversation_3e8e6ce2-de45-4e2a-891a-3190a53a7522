# OCR项目 - 当前工作上下文

## 项目概述
- **项目名称**: OCR智能识别系统
- **技术栈**: Cloudflare Worker + JavaScript + Qwen VL API
- **项目类型**: 基于通义千问大模型的多模态智能识别引擎
- **最后更新**: 2025/07/15 11:12 (优化文件上传处理，图片文件使用Base64，文档文件上传到通义千问服务器)

## 核心功能
1. **多模态模型支持**: 支持通义千问和Google Gemini两种AI模型
2. **智能文件处理**: 图片文件使用Base64处理，文档文件上传到通义千问服务器
3. **多种输入方式**: 支持文件上传、URL输入、Base64输入三种方式
4. **数学公式识别**: 自动转换为LaTeX格式
5. **验证码识别**: 专门优化的验证码识别功能
6. **历史记录**: 云端同步识别历史，支持跨设备访问和管理
7. **Cookie管理**: 安全的云端Cookie存储和管理
8. **高级模式**: 支持自定义Prompt进行个性化识别

## 技术架构
- **前端**: 纯HTML/CSS/JavaScript，响应式设计
- **后端**: Cloudflare Worker无服务器架构
- **AI引擎**: 支持通义千问VL和Google Gemini多模态大模型
- **存储**: KV存储(Cookie + 历史记录) + LocalStorage(本地缓存)
- **数学渲染**: MathJax支持LaTeX公式渲染
- **同步机制**: 混合存储架构，本地+云端双重保障
- **模型切换**: 前端UI选择器 + API Header参数控制

## 当前状态
- ✅ 项目已完成开发
- ✅ 支持多种图像输入方式
- ✅ 完整的用户界面和交互
- ✅ 身份验证和安全机制
- ✅ API文档和使用说明
- ✅ **新增**: 云端历史记录同步功能（默认启用）

## 项目文件结构
```
ocr/
├── worker.js          # 主要的Cloudflare Worker代码
└── .augment_memory/   # 项目记忆系统
    ├── activeContext.md
    ├── core/
    └── task-logs/
```

## 关键特性
1. **智能文件处理**: 图片文件Base64处理，文档文件服务器上传
2. **隐私保护**: 图片文件不上传到第三方服务器，仅在浏览器中处理
3. **文档支持**: 通义千问模型支持PDF、Word、Excel等文档识别
4. **智能识别**: 自动区分验证码和普通文本/数学公式
5. **实时预览**: 图像上传后立即显示预览
6. **历史管理**: 完整的识别历史记录和管理功能
7. **云端同步**: 识别历史在不同设备间自动同步（默认启用）
8. **响应式设计**: 适配各种设备屏幕
9. **安全认证**: 密码保护和API Key认证双重机制

## 部署信息
- **平台**: Cloudflare Workers
- **域名**: 用户自定义
- **环境变量**: PASSWORD, API_KEY, SETTINGS_KV
- **依赖服务**: 通义千问API (chat.qwenlm.ai)

## 使用流程
1. 用户访问系统并进行身份验证
2. 在设置中配置通义千问Cookie
3. 选择图像输入方式(文件/URL/Base64)
4. 系统自动识别并返回结果
5. 识别历史自动同步到云端，支持跨设备访问
6. 支持复制结果和查看历史记录

## 技术亮点
- 无服务器架构，零运维成本
- 前后端一体化设计
- 智能的数学公式LaTeX转换
- 优雅的用户界面和交互体验
- 完善的错误处理和用户反馈
