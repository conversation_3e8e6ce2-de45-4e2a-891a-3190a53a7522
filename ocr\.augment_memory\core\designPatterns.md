# OCR项目设计模式

## 创建型模式

### 1. 单例模式 (Singleton Pattern)
**应用场景**: HistoryManager类
```javascript
class HistoryManager {
    constructor(maxHistory = 20) { 
        this.maxHistory = maxHistory; 
    }
    // 单一实例管理历史记录
}
const historyManager = new HistoryManager();
```

**优势**:
- 确保历史管理器的唯一性
- 全局访问点，便于状态管理
- 避免重复初始化和资源浪费

### 2. 工厂模式 (Factory Pattern)
**应用场景**: 不同类型的图像处理
```javascript
async function processImage(data, type) {
    // 根据type创建不同的处理策略
    if (type === "file") {
        // 文件处理逻辑
    } else if (type === "base64") {
        // Base64处理逻辑
    } else if (type === "url") {
        // URL处理逻辑
    }
}
```

**优势**:
- 封装对象创建逻辑
- 支持多种输入类型的统一处理
- 易于扩展新的输入类型

## 结构型模式

### 1. 适配器模式 (Adapter Pattern)
**应用场景**: 不同输入格式的统一处理
```javascript
// 将不同格式的图像数据适配为统一的API调用
async function adaptImageData(data, type) {
    switch(type) {
        case "file":
            return await fileToFormData(data);
        case "base64":
            return { base64Image: data };
        case "url":
            return { imageUrl: data };
    }
}
```

**优势**:
- 统一不同数据源的接口
- 降低系统耦合度
- 便于维护和扩展

### 2. 装饰器模式 (Decorator Pattern)
**应用场景**: API请求的增强
```javascript
async function enhancedFetch(endpoint, options) {
    // 添加认证头
    const enhancedOptions = {
        ...options,
        headers: {
            ...options.headers,
            'x-advanced-mode': elements.advancedMode.checked,
            'x-custom-prompt': btoa(encodeURIComponent(elements.promptInput.value))
        }
    };
    return fetch(endpoint, enhancedOptions);
}
```

**优势**:
- 动态添加功能而不修改原有代码
- 支持高级模式和自定义Prompt
- 保持接口的一致性

### 3. 外观模式 (Facade Pattern)
**应用场景**: 复杂UI操作的简化接口
```javascript
class UIFacade {
    static showLoading() {
        elements.loading.style.display = 'block';
        elements.resultContainer.classList.remove('show');
    }
    
    static showResult(result) {
        elements.loading.style.display = 'none';
        elements.resultDiv.innerHTML = result;
        elements.resultContainer.classList.add('show');
    }
}
```

**优势**:
- 简化复杂的UI操作
- 提供统一的接口
- 隐藏实现细节

## 行为型模式

### 1. 观察者模式 (Observer Pattern)
**应用场景**: 事件监听和状态变化
```javascript
// DOM事件监听
elements.uploadArea.addEventListener('dragover', handleDragOver);
elements.uploadArea.addEventListener('drop', handleDrop);
elements.advancedMode.addEventListener('change', toggleAdvancedMode);

// 状态变化通知
function updateTokenUI(cookie) {
    // 更新UI状态
    // 通知历史管理器刷新
    historyManager.displayHistory(currentToken);
}
```

**优势**:
- 松耦合的事件处理
- 支持一对多的依赖关系
- 便于扩展新的监听器

### 2. 策略模式 (Strategy Pattern)
**应用场景**: 不同的图像识别策略
```javascript
const recognitionStrategies = {
    file: async (data) => {
        // 文件上传策略
        const formData = new FormData();
        formData.append('file', data);
        return await uploadAndRecognize(formData);
    },
    
    base64: async (data) => {
        // Base64识别策略
        return await recognizeBase64(data);
    },
    
    url: async (data) => {
        // URL识别策略
        return await recognizeUrl(data);
    }
};
```

**优势**:
- 算法族的封装和互换
- 避免大量的条件判断
- 易于添加新的识别策略

### 3. 命令模式 (Command Pattern)
**应用场景**: 历史记录操作
```javascript
class HistoryCommand {
    constructor(action, data) {
        this.action = action;
        this.data = data;
    }
    
    execute() {
        switch(this.action) {
            case 'add':
                return historyManager.addHistory(...this.data);
            case 'delete':
                return historyManager.deleteHistory(this.data);
            case 'clear':
                return historyManager.clearHistory();
        }
    }
}
```

**优势**:
- 将请求封装为对象
- 支持撤销和重做操作
- 便于记录和审计

### 4. 模板方法模式 (Template Method Pattern)
**应用场景**: 图像处理的通用流程
```javascript
async function processImageTemplate(data, type) {
    // 1. 验证输入
    if (!validateInput(data, type)) return;
    
    // 2. 显示加载状态
    showLoading();
    
    // 3. 处理图像 (具体实现由子类决定)
    const result = await processSpecificType(data, type);
    
    // 4. 显示结果
    showResult(result);
    
    // 5. 保存历史
    saveToHistory(data, result);
}
```

**优势**:
- 定义算法骨架，延迟具体实现
- 代码复用和一致性
- 便于维护和扩展

## 架构模式

### 1. MVC模式 (Model-View-Controller)
```javascript
// Model: 数据管理
class DataModel {
    constructor() {
        this.currentToken = '';
        this.history = [];
    }
}

// View: 界面展示
class UIView {
    render(data) {
        // 更新界面
    }
}

// Controller: 业务逻辑
class AppController {
    constructor(model, view) {
        this.model = model;
        this.view = view;
    }
    
    handleUserAction(action, data) {
        // 处理用户操作
        this.model.update(data);
        this.view.render(this.model.getData());
    }
}
```

### 2. 发布-订阅模式 (Pub-Sub Pattern)
```javascript
class EventBus {
    constructor() {
        this.events = {};
    }
    
    subscribe(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }
    
    publish(event, data) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(data));
        }
    }
}
```

### 3. 模块模式 (Module Pattern)
```javascript
const OCRApp = (function() {
    // 私有变量和方法
    let currentToken = '';
    
    function privateMethod() {
        // 私有实现
    }
    
    // 公共接口
    return {
        init: function() {
            // 初始化应用
        },
        
        processImage: function(data, type) {
            // 公共方法
        }
    };
})();
```

## 设计原则应用

### 1. 单一职责原则 (SRP)
- HistoryManager只负责历史记录管理
- UIFacade只负责UI操作封装
- 每个函数都有明确的单一职责

### 2. 开闭原则 (OCP)
- 通过策略模式支持新的识别类型
- 通过配置驱动支持功能扩展
- 接口稳定，实现可扩展

### 3. 依赖倒置原则 (DIP)
- 依赖抽象接口而非具体实现
- 通过依赖注入降低耦合
- 高层模块不依赖低层模块

### 4. 接口隔离原则 (ISP)
- 细粒度的接口设计
- 客户端不依赖不需要的接口
- 模块化的功能划分

这些设计模式的应用使得OCR项目具有良好的可维护性、可扩展性和代码复用性。
