# OCR项目交接文档

## 项目概述
**项目名称**: OCR智能识别系统  
**技术栈**: Cloudflare Worker + JavaScript + 通义千问VL API  
**最后更新**: 2025/07/13 15:06
**当前版本**: v2.1 (完成云端历史同步功能)

## 核心功能特性

### 1. 多模态图像识别
- **文件上传**: 支持拖拽、点击选择、粘贴图片
- **URL输入**: 直接输入图片链接进行识别
- **Base64输入**: 支持Base64编码图片数据
- **智能识别**: 自动区分验证码和文本/数学公式
- **LaTeX转换**: 数学公式自动转换为LaTeX格式

### 2. 云端历史同步 ⭐ 新功能
- **跨设备同步**: 识别历史在不同设备间自动同步
- **混合存储**: 本地缓存 + 云端存储双重保障
- **智能合并**: 自动合并本地和云端数据，去重处理
- **实时同步**: 识别完成后自动保存到云端
- **默认启用**: 无需用户配置，开箱即用

### 3. 用户体验优化
- **响应式设计**: 适配桌面和移动设备
- **实时预览**: 图片上传后立即显示
- **MathJax渲染**: 支持LaTeX公式的可视化渲染
- **历史管理**: 完整的历史记录查看、复制、删除功能
- **状态反馈**: 清晰的加载状态和操作反馈

## 技术架构

### 后端架构 (Cloudflare Workers)
```
请求路由:
├── /login                    # 用户登录
├── /api/settings            # Cookie配置管理
├── /api/history             # 历史记录同步 (新增)
├── /api/recognize/url       # URL图片识别
├── /api/recognize/base64    # Base64图片识别
├── /recognize               # 文件上传识别
├── /proxy/upload            # 文件上传代理
├── /api-docs                # API文档
└── /                        # 主页面
```

### 存储架构
- **Cloudflare KV**: 
  - Cookie配置: `QWEN_COOKIE`
  - 历史记录: `history_{token_suffix}`
- **LocalStorage**: 
  - 历史缓存: `imageRecognition_history_{token}`
  - 用户设置: `advancedMode`, `customPrompt`

### 前端架构
- **HistoryManager类**: 核心历史管理，支持云端同步
- **事件驱动**: 基于DOM事件的交互处理
- **异步处理**: 所有网络操作使用async/await
- **模块化设计**: 功能分离，代码组织清晰

## 部署配置

### 环境变量
```
PASSWORD=your_access_password      # 访问密码
API_KEY=your_api_key              # API认证密钥 (可选)
```

### KV命名空间
```
SETTINGS_KV                       # 绑定到Worker的KV命名空间
```

### 部署步骤
1. 创建Cloudflare Worker
2. 绑定KV命名空间到 `SETTINGS_KV`
3. 设置环境变量 `PASSWORD` (必需)
4. 上传 `worker.js` 文件
5. 测试功能正常工作

## API接口文档

### 认证方式
- **Web界面**: 密码 + Session Cookie
- **API调用**: `Authorization: Bearer {API_KEY}`

### 核心接口

#### 历史记录同步 (新增)
```http
GET /api/history
Headers: x-token: {user_token}
Response: {"history": [...]}

POST /api/history  
Body: {"token": "{user_token}", "history": [...]}
Response: {"success": true}
```

#### 图像识别
```http
POST /api/recognize/url
Body: {"imageUrl": "https://..."}

POST /api/recognize/base64
Body: {"base64Image": "data:image/..."}
```

## 数据流程

### 识别流程
```
用户输入 → 数据验证 → 格式转换 → API调用 → 结果处理 → 云端同步 → 界面更新
```

### 同步流程
```
识别完成 → 本地存储 → 云端保存 → 跨设备可见
设备访问 → 加载本地 → 获取云端 → 智能合并 → 显示历史
```

## 关键代码结构

### HistoryManager类 (核心)
```javascript
class HistoryManager {
  // 混合存储策略
  async loadHistory(token)      // 合并本地和云端数据
  async saveHistory(token, history)  // 同步保存到本地和云端
  async addHistory(token, image, result)  // 添加新记录
  async displayHistory(token)   // 显示历史列表
}
```

### 主要功能模块
- **认证模块**: `handleLogin()`, `checkAuth()`
- **识别模块**: `handleImageUrlRecognition()`, `handleBase64Recognition()`
- **历史模块**: `HistoryManager` 类
- **界面模块**: 事件监听器和DOM操作

## 安全机制

### 数据安全
- **HTTPS传输**: 所有数据通过HTTPS加密传输
- **Token验证**: 历史记录基于用户token隔离
- **输入验证**: 严格的数据验证和过滤
- **错误处理**: 完善的异常捕获和处理

### 隐私保护
- **本地缓存**: 敏感数据优先本地存储
- **Token截取**: 仅使用token后10位作为存储标识
- **数据隔离**: 不同用户数据完全隔离

## 性能优化

### 前端优化
- **异步加载**: MathJax等库按需加载
- **防抖处理**: 输入事件防抖，减少API调用
- **本地缓存**: 优先使用本地数据，提升响应速度
- **智能合并**: 高效的数据合并算法

### 后端优化
- **边缘计算**: Cloudflare全球节点部署
- **KV存储**: 分布式存储，低延迟访问
- **异步处理**: 非阻塞I/O操作
- **错误降级**: 云端失败时自动使用本地存储

## 监控和维护

### 日志监控
- **Cloudflare Analytics**: 请求统计和性能监控
- **Console日志**: 详细的错误追踪
- **用户反馈**: 界面错误提示和状态通知

### 维护要点
- **定期检查**: KV存储使用量和性能
- **版本管理**: 代码版本控制和回滚机制
- **用户支持**: Cookie配置和使用指导

## 未来规划

### 短期优化
- 添加历史记录搜索功能
- 优化大量历史记录的加载性能
- 增加更多图片格式支持

### 长期规划
- 支持多种AI模型选择
- 实现历史记录分类和标签
- 添加批量处理功能
- 支持历史记录导出

## 故障排除

### 常见问题
1. **Cookie过期**: 提示用户重新获取Cookie
2. **网络异常**: 自动降级到本地存储
3. **存储限制**: KV存储容量监控和清理
4. **同步冲突**: 基于时间戳的冲突解决

### 应急处理
- **服务中断**: 本地存储保障基本功能
- **数据丢失**: 多重备份机制
- **性能问题**: 缓存策略和负载均衡

---

**联系信息**: 如有技术问题，请参考项目文档或联系开发团队。  
**文档版本**: v2.0 (2025/07/13)  
**更新说明**: 新增云端历史同步功能，默认启用跨设备同步。
