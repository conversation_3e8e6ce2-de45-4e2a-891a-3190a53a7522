import google.generativeai as genai

genai.configure(api_key="AIzaSyCG_PoCt11a0baF1Ef3vUJOZRbYQ3aIkcM")

try:
    # 推荐使用 GenerativeModel
    model = genai.GenerativeModel('models/gemini-2.5-flash')
    # 上传文件是处理本地文件的推荐方式
    print("Uploading file...")
    audio_file = genai.upload_file(path='123.wav', display_name="Sample Audio")
    print(f"Completed upload: {audio_file.uri}")
    # 生成内容
    response = model.generate_content(
        [
            "give me the text of this audio clip",
            audio_file
        ]
    )
    print("\n--- Response ---")
    print(response.text)

except Exception as e:
    print(f"An error occurred: {e}")