const fs = require('fs');
const Groq = require('groq-sdk');
const { performance } = require('perf_hooks');

const groq = new Groq({ apiKey: '********************************************************' });

async function main() {
  const audioFilePath = "./speech/234.wav"; // 将路径指向您的音频文件
  // 检查文件是否存在
  if (!fs.existsSync(audioFilePath)) {
    console.error(`错误：音频文件未找到，路径: ${audioFilePath}`);
    return; // 如果文件不存在则退出
  }
  try {
    console.log(`正在发送转录请求: ${audioFilePath}...`);
    // 2. 在 API 调用之前记录开始时间
    const startTime = performance.now();
    // 创建转录任务
    const transcription = await groq.audio.transcriptions.create({
      file: fs.createReadStream(audioFilePath), // 指向音频文件的可读流
      model: "whisper-large-v3-turbo",       // 用于转录的模型
      // prompt: "Specify context or spelling", // 可选：提供上下文提示
      language: "en",                        // 可选：指定音频语言
      temperature: 0.0,                      // 可选：控制随机性
    });
    // 3. 在收到响应后立即记录结束时间
    const endTime = performance.now();
    // 4. 计算并打印时间差
    const durationInMs = endTime - startTime;
    console.log(`\n请求成功！API 调用耗时: ${(durationInMs / 1000).toFixed(2)} 秒 (${durationInMs.toFixed(0)} 毫秒)`);
    // 打印完整的转录对象
    console.log("\n--- 转录结果 ---");
    // console.log(JSON.stringify(transcription.text, null, 2));
    // 如果您只想打印转录后的文本，可以使用下面这行：
    console.log("\n转录文本内容:", transcription.text);
    return transcription.text;
  } catch (error) {
    console.error("\n请求过程中发生错误:");
    console.error(error);
  }
}
main();