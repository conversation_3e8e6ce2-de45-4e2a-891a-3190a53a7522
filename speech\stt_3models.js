const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');
const Groq = require('groq-sdk');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const { GoogleAIFileManager } = require("@google/generative-ai/server");
const { performance } = require('perf_hooks'); // 用于精确计时

// --- Groq API_key ---
const groqApiKey = '********************************************************'
// --- gemini API_key ---
const geminiApiKey = 'AIzaSyCG_PoCt11a0baF1Ef3vUJOZRbYQ3aIkcM';
// --- Elevenlabs API 配置 ---
const API_URL = "https://api.elevenlabs.io/v1/speech-to-text";
const PARAMS = { "allow_unauthenticated": "1" };

// 用于模拟不同浏览器的请求
const DEFAULT_USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/20100101 Firefox/126.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:126.0) Gecko/20100101 Firefox/126.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:124.0) Gecko/20100101 Firefox/124.0",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 14; Pixel 8 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 13; SM-S918U1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.70 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edg/124.0.2478.80",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/123.0.2420.97",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
    "Mozilla/5.0 (iPad; CPU OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6.1 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 12; SM-A525F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_7_10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.4 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 11; CPH2239) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/121.0.2277.128",
    "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.0 Safari/537.36"
];

const DEFAULT_ACCEPT_LANGUAGES = [
    "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,ja;q=0.6",
    "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,ja;q=0.5",
    "en-GB,en;q=0.9,en-US;q=0.8,de;q=0.7,fr;q=0.6",
    "ja-JP,ja;q=0.9,en-US;q=0.8,en;q=0.7",
    "de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6",
    "fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,es;q=0.6",
    "es-ES,es;q=0.9,en;q=0.8,pt;q=0.7",
    "ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7,ja;q=0.6",
    "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7",
    "it-IT,it;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6",
    "pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,es;q=0.6",
    "en-CA,en;q=0.9,fr-CA;q=0.8",
    "en-AU,en;q=0.9,en-GB;q=0.8",
    "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7,ja;q=0.6",
    "ar-SA,ar;q=0.9,en-US;q=0.8,en;q=0.7",
    "hi-IN,hi;q=0.9,en-US;q=0.8,en;q=0.7",
    "nl-NL,nl;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6",
    "sv-SE,sv;q=0.9,en-US;q=0.8,en;q=0.7,fi;q=0.6",
    "fi-FI,fi;q=0.9,en-US;q=0.8,en;q=0.7,sv;q=0.6",
    "pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6",
    "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
    "cs-CZ,cs;q=0.9,en-US;q=0.8,en;q=0.7,sk;q=0.6",
    "hu-HU,hu;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6",
    "el-GR,el;q=0.9,en-US;q=0.8,en;q=0.7",
    "id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7"
];

async function transcribeWithElevenlabs(audioFilePath) {
    if (!fs.existsSync(audioFilePath)) {
        console.error(`错误：音频文件 '${audioFilePath}' 未找到。`);
        return null;
    }
    console.log(`\n正在处理文件: ${path.basename(audioFilePath)}`);
    // 构建随机请求头
    const headers = {
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": DEFAULT_ACCEPT_LANGUAGES[Math.floor(Math.random() * DEFAULT_ACCEPT_LANGUAGES.length)],
        "origin": "https://elevenlabs.io",
        "referer": "https://elevenlabs.io/",
        "user-agent": DEFAULT_USER_AGENTS[Math.floor(Math.random() * DEFAULT_USER_AGENTS.length)],
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
    };
    // 使用 FormData 构建请求体
    const formData = new FormData();
    formData.append("model_id", "scribe_v1");
    formData.append("tag_audio_events", "true");
    formData.append("diarize", "true"); // 说话人分离固定为启用
    // 获取文件扩展名和对应的MIME类型
    const fileExtension = path.extname(audioFilePath).toLowerCase();
    const mimeTypeMap = {
        ".mp3": "audio/mpeg", ".wav": "audio/wav", ".flac": "audio/flac",
        ".m4a": "audio/mp4", ".ogg": "audio/ogg", ".opus": "audio/opus",
        ".aac": "audio/aac", ".webm": "audio/webm", ".mp4": "video/mp4",
        ".mov": "video/quicktime"
    };
    const mimeType = mimeTypeMap[fileExtension] || 'application/octet-stream';
    if (mimeType === 'application/octet-stream') {
        console.warn(`  警告：未知的音频文件扩展名 '${fileExtension}'，使用通用MIME类型 '${mimeType}'。`);
    }
    // 将文件流附加到表单数据
    formData.append('file', fs.createReadStream(audioFilePath), {
        filename: path.basename(audioFilePath),
        contentType: mimeType,
    });
    try {
        console.log("\n开始上传并请求转录...");
        const startTime = performance.now();
        const response = await axios.post(API_URL, formData, {
            params: PARAMS,
            headers: {
                ...headers,
                ...formData.getHeaders(), // 这行至关重要
            },
            timeout: 600000, // 10分钟超时 (单位: 毫秒)
        });
        const endTime = performance.now();
        const apiCallDuration = (endTime - startTime) / 1000;
        console.log(`转录请求完成，耗时: ${apiCallDuration.toFixed(2)} 秒`);
        console.log("成功从API获取并解析JSON响应。");
        return response.data; // axios自动解析JSON响应
    } catch (error) {
        // 详细的错误处理
        if (axios.isAxiosError(error)) {
            if (error.code === 'ECONNABORTED') {
                console.error("请求超时：API调用在设定的超时时间内未完成。");
            } else if (error.response) {
                // 请求已发出，但服务器以非2xx状态码响应
                console.error(`请求过程中发生HTTP错误: ${error.message}`);
                console.error(`  服务器响应状态码: ${error.response.status}`);
                console.error(`  服务器响应内容:`, error.response.data); // data可能是对象或字符串
            } else if (error.request) {
                // 请求已发出，但没有收到响应
                console.error("网络错误：请求已发出但未收到响应。请检查您的网络连接。");
            } else {
                // 设置请求时发生错误
                console.error(`设置请求时出错: ${error.message}`);
            }
        } else {
            // 其他所有未知错误
            console.error(`处理过程中发生其他未知错误: ${error.message}`);
        }
        return null;
    }
}

async function transcribeWithGroq(audioFilePath) {
  const groq = new Groq({ apiKey: groqApiKey });
  // 检查文件是否存在
  if (!fs.existsSync(audioFilePath)) {
    console.error(`错误：音频文件未找到，路径: ${audioFilePath}`);
    return; // 如果文件不存在则退出
  }
  try {
    console.log(`正在发送转录请求: ${audioFilePath}...`);
    // 2. 在 API 调用之前记录开始时间
    const startTime = performance.now();
    // 创建转录任务
    const transcription = await groq.audio.transcriptions.create({
      file: fs.createReadStream(audioFilePath),
      model: "whisper-large-v3-turbo",       // 用于转录的模型
      // prompt: "Specify context or spelling", // 可选：提供上下文提示
      language: "en",                        // 可选：指定音频语言
      temperature: 0.0,                      // 可选：控制随机性
    });
    // 3. 在收到响应后立即记录结束时间
    const endTime = performance.now();
    // 4. 计算并打印时间差
    const durationInMs = endTime - startTime;
    console.log(`\n请求成功！API 调用耗时: ${(durationInMs / 1000).toFixed(2)} 秒 (${durationInMs.toFixed(0)} 毫秒)`);
    // 打印完整的转录对象
    console.log("\n--- 转录结果 ---");
    // console.log(JSON.stringify(transcription.text, null, 2));
    // 如果您只想打印转录后的文本，可以使用下面这行：
    console.log("\n转录文本内容:", transcription.text);
    return transcription.text;
  } catch (error) {
    console.error("\n请求过程中发生错误:");
    console.error(error);
  }
}

async function transcribeWithGemini(audioFilePath, audioMimeType) {
  // 1. 检查文件是否存在
  if (!fs.existsSync(audioFilePath)) {
    throw new Error(`音频文件未找到: ${audioFilePath}`);
  }
  // 2. 获取文件大小以决定使用哪种策略
  const stats = fs.statSync(audioFilePath);
  const fileSizeInBytes = stats.size;
  const oneMB = 1 * 1024 * 1024; // 1MB in bytes
  // 3. 初始化AI客户端和模型
  const genAI = new GoogleGenerativeAI(geminiApiKey);
  const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });
  const prompt = "请将下面的音频内容转录成文字。请直接输出转录后的文本，不要添加任何额外的说明或标题。";
  let parts;
  // 4. 根据文件大小选择不同的处理逻辑
  if (fileSizeInBytes < oneMB) {
    // --- 策略一：文件小于1MB，使用Base64直接上传 ---
    console.log(`文件大小为 ${(fileSizeInBytes / 1024).toFixed(2)} KB (< 1MB)，使用Base64直接上传方法...`);
    const base64Data = fs.readFileSync(audioFilePath).toString('base64');
    parts = [
      { text: prompt },
      {
        inlineData: {
          mimeType: audioMimeType,
          data: base64Data,
        },
      },
    ];
  } else {
    // --- 策略二：文件大于等于1MB，使用File API上传 ---
    console.log(`文件大小为 ${(fileSizeInBytes / (1024 * 1024)).toFixed(2)} MB (>= 1MB)，使用File API上传方法...`);
    const fileManager = new GoogleAIFileManager(geminiApiKey);
    let uploadedFileResponse;
    try {
      console.log("步骤 1/3: 开始上传文件...");
      uploadedFileResponse = await fileManager.uploadFile(
        audioFilePath,
        {
          mimeType: audioMimeType,
          displayName: `speech-to-text-audio-${Date.now()}`,
        }
      );
      const uploadedFile = uploadedFileResponse.file;
      console.log(`步骤 2/3: 文件上传成功！文件资源名: ${uploadedFile.name}`);
      parts = [
        { text: prompt },
        {
          fileData: {
            mimeType: uploadedFile.mimeType,
            fileUri: uploadedFile.uri,
          },
        },
      ];
    } finally {
      // 确保即使转录失败，上传的临时文件也能被删除
      if (uploadedFileResponse) {
        console.log("收尾工作: 正在删除云端临时文件...");
        await fileManager.deleteFile(uploadedFileResponse.file.name);
        console.log("临时文件已删除。");
      }
    }
  }
  // 5. 发送最终请求并获取结果（集成计时功能）
  console.log("步骤 3/3: 发送转录请求到 Gemini API...");
  const startTime = performance.now();
  const result = await model.generateContent({ contents: [{ role: 'user', parts }] });
  const endTime = performance.now();
  const durationInMs = endTime - startTime;
  console.log(`✅ Gemini API 响应成功！耗时: ${(durationInMs / 1000).toFixed(3)} 秒.`);
  const transcript = result.response.text();
  return transcript;
}