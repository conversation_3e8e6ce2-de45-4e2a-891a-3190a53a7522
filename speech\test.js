const Groq = require('groq-sdk');
const path = require('path');
const fs = require('fs/promises');

// --- Groq API_key ---
const groqApiKey = '********************************************************'

const text = "Thank you for contacting us. I completely understand your frustration with the canceled flight, and I'm here to help you get rebooked quickly. I just need a few details from your original reservation, like your booking confirmation number or passenger info. Once I have those, I'll find the next available flight and make sure you reach your destination smoothly.";

async function textToSpeech(text) {
  const groq = new Groq({ apiKey: groqApiKey });
  console.log("🚀 正在向 Groq 文本转语音 (TTS) API 发送请求...");
  const apiStartTime = performance.now(); // 记录 API 请求开始时间
  try {
    // 3. 调用 Groq API 的 audio.speech.create 方法
    const response = await groq.audio.speech.create({
      model: "playai-tts",
      voice: "Arista-PlayAI",
      response_format: "mp3",
      input: text,
    });
    const apiEndTime = performance.now(); // 记录 API 响应到达时间
    const apiDurationInSeconds = ((apiEndTime - apiStartTime) / 1000).toFixed(2);
    console.log(`⏱️  API 响应耗时: ${apiDurationInSeconds} s`);
    const filePath = path.resolve(__dirname, `${Date.now()}.mp3`);
    const audioBuffer = await response.arrayBuffer();
    await fs.writeFile(filePath, Buffer.from(audioBuffer));
    console.log(`✅ 成功！音频文件已保存至: ${filePath}`);
  } catch (error) {
    console.error("❌ 执行过程中发生错误:", error);
  }
}

// 执行主函数
textToSpeech(text);