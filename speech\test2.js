const axios = require('axios');
const fs = require('fs/promises');

async function getTTSAudio(text, prompt, voice, outputFilePath) {
  const baseURL = 'https://www.openai.fm/api/generate';

  console.log(`🚀 正在发送 TTS 请求...`);

  try {
    // 使用 axios 发送 GET 请求
    // - `params` 对象会自动将数据编码为 URL 查询字符串
    // - `responseType: 'arraybuffer'` 是关键，它告诉 axios 我们期望接收二进制数据
    const response = await axios.get(baseURL, {
      params: {
        input: text,
        prompt: prompt,
        voice: voice,
      },
      responseType: 'arraybuffer',
    });

    // 将返回的二进制 Buffer 数据写入文件
    await fs.writeFile(outputFilePath, response.data);

    console.log(`✅ 成功！音频文件已保存到: ${outputFilePath}`);

  } catch (error) {
    console.error('❌ 请求失败:');
    if (error.response) {
      // 如果服务器返回了错误状态码
      console.error(`  状态码: ${error.response.status}`);
      // 尝试将返回的错误信息（可能是文本）打印出来
      console.error(`  错误信息: ${Buffer.from(error.response.data).toString()}`);
    } else {
      // 如果是网络错误或其他问题
      console.error(`  错误详情: ${error.message}`);
    }
  }
}

// --- 配置您的文本和参数 ---
const textToConvert = `Thank you for contacting us. I completely understand your frustration with the canceled flight, and I'm here to help you get rebooked quickly.

I just need a few details from your original reservation, like your booking confirmation number or passenger info. Once I have those, I'll find the next available flight and make sure you reach your destination smoothly.`;

const promptText = `Voice Affect: Calm, composed, and reassuring; project quiet authority and confidence.

Tone: Sincere, empathetic, and gently authoritative—express genuine apology while conveying competence.

Pacing: Steady and moderate; unhurried enough to communicate care, yet efficient enough to demonstrate professionalism.

Emotion: Genuine empathy and understanding; speak with warmth, especially during apologies ("I'm very sorry for any disruption...").

Pronunciation: Clear and precise, emphasizing key reassurances ("smoothly," "quickly," "promptly") to reinforce confidence.

Pauses: Brief pauses after offering assistance or requesting details, highlighting willingness to listen and support.`;

// --- 执行函数 ---
getTTSAudio(textToConvert, promptText, 'alloy', 'rebooking_assistance.mp3');