const axios = require('axios');
const fs = require('fs/promises');
const path = require('path');
const { performance } = require('perf_hooks');
const Groq = require('groq-sdk');

// --- greq API 配置 ---
const groqApiKey = '********************************************************'

const text = "Thank you for contacting us. I completely understand your frustration with the canceled flight, and I'm here to help you get rebooked quickly. I just need a few details from your original reservation, like your booking confirmation number or passenger info. Once I have those, I'll find the next available flight and make sure you reach your destination smoothly.";

async function TTSWithOpenAI(text) {
  const baseURL = 'https://www.openai.fm/api/generate';
  const prompt = `Voice Affect: Calm, composed, and reassuring; project quiet authority and confidence.
Tone: Sincere, empathetic, and gently authoritative—express genuine apology while conveying competence.
Pacing: Steady and moderate; unhurried enough to communicate care, yet efficient enough to demonstrate professionalism.
Emotion: Genuine empathy and understanding; speak with warmth, especially during apologies ("I'm very sorry for any disruption...").
Pronunciation: Clear and precise, emphasizing key reassurances ("smoothly," "quickly," "promptly") to reinforce confidence.
Pauses: Brief pauses after offering assistance or requesting details, highlighting willingness to listen and support.`;
  const voice = 'shimmer';
  console.log(`🚀 正在发送 TTS 请求...`);
  const apiStartTime = performance.now(); // 记录 API 请求开始时间
  try {
    const response = await axios.get(baseURL, {
      params: {
        input: text,
        prompt: prompt,
        voice: voice,
      },
      responseType: 'arraybuffer',
    });
    const apiEndTime = performance.now(); // 记录 API 响应到达时间
    const apiDurationInSeconds = ((apiEndTime - apiStartTime) / 1000).toFixed(2);
    console.log(`⏱️  API 响应耗时: ${apiDurationInSeconds} s`);
    const filePath = path.resolve(__dirname, `${Date.now()}.mp3`);
    await fs.writeFile(filePath, response.data);
    console.log(`✅ 成功！音频文件已保存到: ${filePath}`);
    return filePath;
  } catch (error) {
    console.error('❌ 请求失败:');
    if (error.response) {
      // 如果服务器返回了错误状态码
      console.error(`  状态码: ${error.response.status}`);
      // 尝试将返回的错误信息（可能是文本）打印出来
      console.error(`  错误信息: ${Buffer.from(error.response.data).toString()}`);
    } else {
      // 如果是网络错误或其他问题
      console.error(`  错误详情: ${error.message}`);
    }
  }
}

async function TTSWithOOXX(text) {
    const params = {
        api_key: 'dg-X2GwZw3JqxWe7fmLhjL7ZWVXs1LrG3pU',
        t: text,
        v: 'en-US-ShimmerTurboMultilingualNeural',
    };
    console.log('正在发送 TTS 请求...');
    const apiStartTime = performance.now(); // 记录 API 请求开始时间
    try {
        const response = await axios.get('https://tts.ooxx.gq/tts', {
            params: params,
            responseType: 'arraybuffer',
        });
        const apiEndTime = performance.now(); // 记录 API 响应到达时间
        const apiDurationInSeconds = ((apiEndTime - apiStartTime) / 1000).toFixed(2);
        console.log(`⏱️  API 响应耗时: ${apiDurationInSeconds} s`);
        const filePath = path.resolve(__dirname, `${Date.now()}.mp3`);
        await fs.writeFile(filePath, response.data);
        console.log(`✅ 成功！音频文件已保存至: ${filePath}`);
        return filePath;
    } catch (error) {
        // 详细错误处理
        if (error.response) {
            // 请求成功发出，但服务器以状态码响应
            console.error(`  状态码: ${error.response.status}`);
            const errorMessage = Buffer.from(error.response.data).toString('utf-8');
            console.error(`  错误信息: ${errorMessage}`);
        } else if (error.request) {
            // 请求已发出，但没有收到响应
            console.error('  未收到 TTS 服务的响应。');
        } else {
            // 在设置请求或文件写入时触发了错误
            console.error(`  发生错误: ${error.message}`);
        }
    }
}

async function TTSWithGreq(text) {
  const groq = new Groq({ apiKey: groqApiKey });
  console.log("🚀 正在向 Groq 文本转语音 (TTS) API 发送请求...");
  const apiStartTime = performance.now(); // 记录 API 请求开始时间
  try {
    const response = await groq.audio.speech.create({
      model: "playai-tts",
      voice: "Arista-PlayAI",
      response_format: "mp3",
      input: text,
    });
    const apiEndTime = performance.now(); // 记录 API 响应到达时间
    const apiDurationInSeconds = ((apiEndTime - apiStartTime) / 1000).toFixed(2);
    console.log(`⏱️  API 响应耗时: ${apiDurationInSeconds} s`);
    const filePath = path.resolve(__dirname, `${Date.now()}.mp3`);
    const audioBuffer = await response.arrayBuffer();
    await fs.writeFile(filePath, Buffer.from(audioBuffer));
    console.log(`✅ 成功！音频文件已保存至: ${filePath}`);
    return filePath;
  } catch (error) {
    console.error("❌ 执行过程中发生错误:", error);
  }
}

TTSWithOOXX(text)