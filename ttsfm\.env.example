# TTSFM Environment Configuration

# Server Configuration
HOST=0.0.0.0
PORT=7000

# SSL Configuration
VERIFY_SSL=true

# Flask Configuration
FLASK_ENV=production
FLASK_APP=app.py
DEBUG=false

# API Key Protection (Optional)
# Set REQUIRE_API_KEY=true to enable API key authentication
REQUIRE_API_KEY=false

# Set your API key here when protection is enabled
# This key will be required for all TTS generation requests
TTSFM_API_KEY=your-secret-api-key-here

# Example usage:
# 1. Set REQUIRE_API_KEY=true
# 2. Set TTSFM_API_KEY to your desired secret key
# 3. Restart the application
# 4. All TTS requests will now require the API key in:
#    - Authorization header (Bearer token) - OpenAI compatible
#    - X-API-Key header
#    - api_key query parameter
#    - api_key in JSON body