name: Release and Publish

on:
  push:
    tags:
      - 'v*'  # Triggers on version tags like v1.0.0, v3.0.1, etc.

permissions:
  contents: write
  id-token: write

jobs:
  release-and-publish:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Test package import
      run: |
        pip install -e .
        python -c "import ttsfm; print(f'✅ TTSFM imported successfully')"
        python -c "from ttsfm import TTSClient; print('✅ TTSClient imported successfully')"

    - name: Build package
      run: |
        python -m build
        echo "📦 Package built successfully"
        ls -la dist/

    - name: Check package
      run: |
        twine check dist/*
        echo "✅ Package validation passed"

    - name: Publish to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        attestations: true

    - name: Create GitHub Release
      uses: softprops/action-gh-release@v1
      with:
        body: |
          ## 🎉 TTSFM ${{ github.ref_name }}

          New release of TTSFM - Free Text-to-Speech API with OpenAI compatibility.

          ### 📦 Installation
          ```bash
          pip install ttsfm==${{ github.ref_name }}
          ```

          ### 🚀 Quick Start
          ```python
          from ttsfm import TTSClient

          client = TTSClient()
          response = client.generate_speech("Hello from TTSFM!")
          response.save_to_file("hello")
          ```

          ### 🐳 Docker
          ```bash
          docker run -p 8000:8000 dbcccc/ttsfm:latest
          ```

          ### ✨ Features
          - 🆓 Completely free (uses openai.fm service)
          - 🎯 OpenAI-compatible API
          - 🗣️ 11 voices available
          - 🎵 6 audio formats (MP3, WAV, OPUS, AAC, FLAC, PCM)
          - ⚡ Async and sync clients
          - 🌐 Web interface included
          - 🔧 CLI tool available

          ### 📚 Documentation
          See [README](https://github.com/dbccccccc/ttsfm#readme) for full documentation.
        draft: false
        prerelease: false
