# TTSFM 独立客户端

这是一个完全独立的TTSFM客户端实现，将所有依赖集中在一个文件中，使其具备独立运行的能力。

## 特性

- **完全独立**: 所有依赖都集中在一个文件中
- **兼容OpenAI API**: 支持OpenAI TTS API格式
- **多种语音**: 支持11种不同的语音选项
- **多种格式**: 支持MP3、WAV、OPUS、AAC、FLAC、PCM格式
- **长文本支持**: 自动分割长文本为多个块
- **错误处理**: 完善的异常处理和重试机制
- **中文注释**: 完整的中文代码注释

## 依赖要求

只需要以下Python标准库和第三方库：

```bash
pip install requests
pip install fake-useragent  # 可选，用于生成真实的User-Agent
```

## 快速开始

### 基本用法

```python
from standalone_client import TTSClient, Voice, AudioFormat

# 创建客户端
client = TTSClient()

# 生成语音
response = client.generate_speech(
    text="Hello, world!",
    voice=Voice.ALLOY,
    response_format=AudioFormat.MP3
)

# 保存音频文件
filename = response.save_to_file("hello")
print(f"音频已保存到: {filename}")

# 关闭客户端
client.close()
```

### 使用上下文管理器

```python
from standalone_client import TTSClient, Voice, AudioFormat

with TTSClient() as client:
    response = client.generate_speech(
        text="这是一个测试",
        voice=Voice.NOVA,
        response_format=AudioFormat.WAV
    )
    filename = response.save_to_file("test")
    print(f"音频已保存到: {filename}")
```

### 长文本处理

```python
from standalone_client import TTSClient, Voice, AudioFormat

long_text = """
这是一段很长的文本，会被自动分割成多个块进行处理。
TTSFM客户端可以处理长文本，通过将其分解为适合API限制的较小片段。
每个块都会单独处理，您会得到一个音频响应列表。
"""

with TTSClient() as client:
    responses = client.generate_speech_long_text(
        text=long_text,
        voice=Voice.ALLOY,
        response_format=AudioFormat.MP3,
        max_length=100  # 每块最大长度
    )
    
    print(f"生成了 {len(responses)} 个音频块")
    for i, resp in enumerate(responses):
        filename = resp.save_to_file(f"chunk_{i+1}")
        print(f"块 {i+1}: {filename}")
```

### 便利函数

```python
from standalone_client import generate_speech, Voice, AudioFormat

# 直接生成语音（自动管理客户端）
response = generate_speech(
    text="Hello from convenience function!",
    voice=Voice.ECHO,
    response_format=AudioFormat.WAV
)

filename = response.save_to_file("convenience")
print(f"音频已保存到: {filename}")
```

## 支持的语音

- `Voice.ALLOY` - alloy
- `Voice.ASH` - ash  
- `Voice.BALLAD` - ballad
- `Voice.CORAL` - coral
- `Voice.ECHO` - echo
- `Voice.FABLE` - fable
- `Voice.NOVA` - nova
- `Voice.ONYX` - onyx
- `Voice.SAGE` - sage
- `Voice.SHIMMER` - shimmer
- `Voice.VERSE` - verse

## 支持的音频格式

- `AudioFormat.MP3` - MP3格式
- `AudioFormat.WAV` - WAV格式
- `AudioFormat.OPUS` - OPUS格式
- `AudioFormat.AAC` - AAC格式
- `AudioFormat.FLAC` - FLAC格式
- `AudioFormat.PCM` - PCM格式

## 配置选项

```python
client = TTSClient(
    base_url="https://www.openai.fm",  # TTS服务URL
    api_key=None,                      # API密钥（如果需要）
    timeout=30.0,                      # 请求超时时间
    max_retries=3,                     # 最大重试次数
    verify_ssl=True,                   # 是否验证SSL证书
    preferred_format=AudioFormat.WAV   # 首选音频格式
)
```

## 错误处理

```python
from standalone_client import TTSClient, TTSException, NetworkException

try:
    with TTSClient() as client:
        response = client.generate_speech("Hello, world!")
        filename = response.save_to_file("hello")
        
except NetworkException as e:
    print(f"网络错误: {e}")
except TTSException as e:
    print(f"TTS错误: {e}")
except Exception as e:
    print(f"其他错误: {e}")
```

## 项目架构分析

### 原始ttsfm项目结构

```
ttsfm/
├── ttsfm/
│   ├── __init__.py          # 包初始化和导出
│   ├── client.py            # 同步TTS客户端
│   ├── async_client.py      # 异步TTS客户端
│   ├── models.py            # 数据模型和枚举
│   ├── exceptions.py        # 异常类定义
│   ├── utils.py             # 工具函数
│   └── cli.py               # 命令行接口
└── requirements.txt         # 依赖列表
```

### 独立客户端整合

`standalone_client.py` 将以下模块的功能整合到一个文件中：

1. **数据模型** (`models.py`):
   - `Voice` 枚举 - 语音选项
   - `AudioFormat` 枚举 - 音频格式
   - `TTSRequest` 数据类 - 请求模型
   - `TTSResponse` 数据类 - 响应模型

2. **异常处理** (`exceptions.py`):
   - `TTSException` - 基础异常类
   - `APIException` - API错误
   - `NetworkException` - 网络错误
   - `ValidationException` - 验证错误
   - 其他专用异常类

3. **工具函数** (`utils.py`):
   - HTTP头部生成
   - 文本清理和验证
   - URL处理
   - 文件大小格式化
   - 指数退避算法

4. **核心客户端** (`client.py`):
   - `TTSClient` 类 - 主要的TTS客户端
   - HTTP会话管理
   - 请求处理和重试逻辑
   - 响应处理

### 主要优势

1. **部署简单**: 只需一个Python文件
2. **依赖最小**: 只需要requests库
3. **功能完整**: 保留了原项目的所有核心功能
4. **易于维护**: 所有代码在一个文件中，便于理解和修改
5. **中文友好**: 完整的中文注释和文档

## 注意事项

1. 确保安装了必要的依赖：`pip install requests fake-useragent`
2. 如果没有安装`fake-useragent`，会使用默认的User-Agent
3. 默认使用`https://www.openai.fm`作为TTS服务
4. 支持长文本自动分块处理
5. 包含完整的错误处理和重试机制

## 许可证

本项目基于原ttsfm项目，保持相同的开源许可证。
