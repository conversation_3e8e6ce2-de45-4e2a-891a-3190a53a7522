#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTSFM独立客户端使用示例

这个脚本展示了如何使用standalone_client.py进行文本转语音
"""

from standalone_client import (
    TTSClient, Voice, AudioFormat,
    generate_speech, generate_speech_long_text,
    format_file_size
)

def example_basic_usage():
    """基本使用示例"""
    print("=" * 50)
    print("基本使用示例")
    print("=" * 50)
    
    # 创建客户端
    client = TTSClient()
    
    try:
        # 生成简单语音
        print("生成语音: 'Hello, World!'")
        response = client.generate_speech(
            text="Hello, World!",
            voice=Voice.ALLOY,
            response_format=AudioFormat.MP3
        )
        
        # 保存文件
        filename = response.save_to_file("hello_world")
        print(f"✓ 音频已保存到: {filename}")
        print(f"  文件大小: {format_file_size(response.size)}")
        print(f"  音频格式: {response.format.value}")
        print(f"  估算时长: {response.duration:.2f}秒")
        
    finally:
        client.close()

def example_context_manager():
    """上下文管理器示例"""
    print("\n" + "=" * 50)
    print("上下文管理器示例")
    print("=" * 50)
    
    # 使用上下文管理器（推荐方式）
    with TTSClient() as client:
        print("生成中文语音...")
        response = client.generate_speech(
            text="你好，这是一个中文语音测试。",
            voice=Voice.NOVA,
            response_format=AudioFormat.WAV
        )
        
        filename = response.save_to_file("chinese_test")
        print(f"✓ 中文语音已保存到: {filename}")

def example_different_voices():
    """不同语音示例"""
    print("\n" + "=" * 50)
    print("不同语音示例")
    print("=" * 50)
    
    # 测试不同的语音
    voices = [
        (Voice.ALLOY, "Alloy voice - balanced and natural"),
        (Voice.ECHO, "Echo voice - clear and articulate"),
        (Voice.NOVA, "Nova voice - warm and engaging"),
    ]
    
    with TTSClient() as client:
        for voice, description in voices:
            print(f"生成 {voice.value} 语音...")
            response = client.generate_speech(
                text=description,
                voice=voice,
                response_format=AudioFormat.MP3
            )
            
            filename = response.save_to_file(f"voice_{voice.value}")
            print(f"✓ {voice.value} 语音保存到: {filename}")

def example_different_formats():
    """不同格式示例"""
    print("\n" + "=" * 50)
    print("不同格式示例")
    print("=" * 50)
    
    text = "Testing different audio formats with TTSFM."
    formats = [AudioFormat.MP3, AudioFormat.WAV, AudioFormat.OPUS]
    
    with TTSClient() as client:
        for format in formats:
            print(f"生成 {format.value} 格式...")
            response = client.generate_speech(
                text=text,
                voice=Voice.ALLOY,
                response_format=format
            )
            
            filename = response.save_to_file(f"format_test_{format.value}")
            print(f"✓ {format.value} 格式保存到: {filename}")
            print(f"  请求格式: {format.value}")
            print(f"  实际格式: {response.format.value}")
            print(f"  文件大小: {format_file_size(response.size)}")

def example_long_text():
    """长文本处理示例"""
    print("\n" + "=" * 50)
    print("长文本处理示例")
    print("=" * 50)
    
    # 长文本示例
    long_text = """
    人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，
    它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
    该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
    
    自从人工智能诞生以来，理论和技术日益成熟，应用领域也不断扩大。
    可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
    人工智能可以对人的意识、思维的信息过程进行模拟。
    
    人工智能不是人的智能，但能像人那样思考、也可能超过人的智能。
    人工智能是一门极富挑战性的科学，从事这项工作的人必须懂得计算机知识，
    心理学和哲学。人工智能是包括十分广泛的科学，它由不同的领域组成。
    """
    
    with TTSClient() as client:
        print("处理长文本（自动分块）...")
        responses = client.generate_speech_long_text(
            text=long_text,
            voice=Voice.ALLOY,
            response_format=AudioFormat.MP3,
            max_length=200,  # 每块最大200字符
            preserve_words=True  # 保持单词完整
        )
        
        print(f"✓ 长文本已分割为 {len(responses)} 个音频块")
        
        total_size = 0
        total_duration = 0
        
        for i, response in enumerate(responses):
            filename = response.save_to_file(f"long_text_part_{i+1}")
            total_size += response.size
            total_duration += response.duration or 0
            
            print(f"  块 {i+1}: {filename}")
            print(f"    大小: {format_file_size(response.size)}")
            print(f"    时长: {response.duration:.2f}秒")
        
        print(f"\n总计:")
        print(f"  总大小: {format_file_size(total_size)}")
        print(f"  总时长: {total_duration:.2f}秒")

def example_convenience_functions():
    """便利函数示例"""
    print("\n" + "=" * 50)
    print("便利函数示例")
    print("=" * 50)
    
    # 使用便利函数（自动管理客户端生命周期）
    print("使用 generate_speech 便利函数...")
    response = generate_speech(
        text="This is a convenience function example.",
        voice=Voice.ECHO,
        response_format=AudioFormat.WAV
    )
    
    filename = response.save_to_file("convenience_example")
    print(f"✓ 便利函数生成的音频保存到: {filename}")
    
    # 长文本便利函数
    print("\n使用 generate_speech_long_text 便利函数...")
    responses = generate_speech_long_text(
        text="This is a longer text example using the convenience function. It will be automatically split into appropriate chunks for processing.",
        voice=Voice.NOVA,
        response_format=AudioFormat.MP3,
        max_length=50
    )
    
    print(f"✓ 便利函数生成了 {len(responses)} 个音频块")
    for i, resp in enumerate(responses):
        filename = resp.save_to_file(f"convenience_long_{i+1}")
        print(f"  块 {i+1}: {filename}")

def example_custom_instructions():
    """自定义指令示例"""
    print("\n" + "=" * 50)
    print("自定义指令示例")
    print("=" * 50)
    
    # 使用自定义语音指令
    custom_instructions = """
    Affect/personality: Enthusiastic and energetic
    Tone: Excited and upbeat, like a sports commentator
    Pronunciation: Clear and dynamic with emphasis on key words
    Pace: Slightly faster than normal to convey excitement
    Emotion: High energy and passionate delivery
    """
    
    with TTSClient() as client:
        print("生成带有自定义指令的语音...")
        response = client.generate_speech(
            text="Welcome to the amazing world of text-to-speech technology! This is absolutely incredible!",
            voice=Voice.ALLOY,
            response_format=AudioFormat.MP3,
            instructions=custom_instructions
        )
        
        filename = response.save_to_file("custom_instructions")
        print(f"✓ 自定义指令语音保存到: {filename}")

def example_error_handling():
    """错误处理示例"""
    print("\n" + "=" * 50)
    print("错误处理示例")
    print("=" * 50)
    
    from standalone_client import TTSException, ValidationException, NetworkException
    
    # 演示错误处理
    try:
        with TTSClient() as client:
            # 尝试使用空文本（会引发验证错误）
            response = client.generate_speech(text="")
    except ValidationException as e:
        print(f"✓ 捕获到验证错误: {e}")
    except TTSException as e:
        print(f"✓ 捕获到TTS错误: {e}")
    except Exception as e:
        print(f"✗ 意外错误: {e}")
    
    try:
        with TTSClient() as client:
            # 尝试使用过长的文本
            long_text = "x" * 5000
            response = client.generate_speech(text=long_text, max_length=100)
    except (ValidationException, ValueError) as e:
        print(f"✓ 捕获到文本长度错误: {e}")
    except Exception as e:
        print(f"✗ 意外错误: {e}")

def main():
    """主函数 - 运行所有示例"""
    print("TTSFM独立客户端使用示例")
    print("=" * 60)
    
    examples = [
        ("基本使用", example_basic_usage),
        ("上下文管理器", example_context_manager),
        ("不同语音", example_different_voices),
        ("不同格式", example_different_formats),
        ("长文本处理", example_long_text),
        ("便利函数", example_convenience_functions),
        ("自定义指令", example_custom_instructions),
        ("错误处理", example_error_handling),
    ]
    
    print("可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")
    print("0. 运行所有示例")
    
    try:
        choice = input("\n请选择要运行的示例 (0-{}): ".format(len(examples))).strip()
        
        if choice == "0":
            # 运行所有示例
            for name, func in examples:
                print(f"\n{'='*20} {name} {'='*20}")
                try:
                    func()
                    print(f"✓ {name} 示例完成")
                except Exception as e:
                    print(f"✗ {name} 示例失败: {e}")
        elif choice.isdigit() and 1 <= int(choice) <= len(examples):
            # 运行选定的示例
            name, func = examples[int(choice) - 1]
            print(f"\n运行示例: {name}")
            func()
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n\n示例演示结束")
    except Exception as e:
        print(f"\n运行示例时出错: {e}")

if __name__ == "__main__":
    main()
