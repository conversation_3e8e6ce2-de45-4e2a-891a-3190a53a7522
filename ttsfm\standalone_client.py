"""
独立的TTSFM客户端 - 所有依赖集中在一个文件中
支持文本转语音功能，兼容OpenAI API格式

使用示例:
    client = TTSClient()
    response = client.generate_speech("Hello, world!", voice="alloy", response_format="mp3")
    response.save_to_file("hello")
"""

import json
import time
import uuid
import logging
import os
import re
import random
from enum import Enum
from typing import Optional, Dict, Any, Union, List
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass
from datetime import datetime

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


# ============================================================================
# 数据模型和枚举类型
# ============================================================================

class Voice(str, Enum):
    """可用的语音选项"""
    ALLOY = "alloy"
    ASH = "ash"
    BALLAD = "ballad"
    CORAL = "coral"
    ECHO = "echo"
    FABLE = "fable"
    NOVA = "nova"
    ONYX = "onyx"
    SAGE = "sage"
    SHIMMER = "shimmer"
    VERSE = "verse"


class AudioFormat(str, Enum):
    """支持的音频输出格式"""
    MP3 = "mp3"
    WAV = "wav"
    OPUS = "opus"
    AAC = "aac"
    FLAC = "flac"
    PCM = "pcm"


@dataclass
class TTSRequest:
    """TTS请求模型"""
    input: str
    voice: Union[Voice, str] = Voice.ALLOY
    response_format: Union[AudioFormat, str] = AudioFormat.MP3
    instructions: Optional[str] = None
    model: Optional[str] = None
    speed: Optional[float] = None
    max_length: int = 4096
    validate_length: bool = True
    
    def __post_init__(self):
        """初始化后验证和规范化字段"""
        # 确保voice是有效的Voice枚举
        if isinstance(self.voice, str):
            try:
                self.voice = Voice(self.voice.lower())
            except ValueError:
                raise ValueError(f"Invalid voice: {self.voice}. Must be one of {list(Voice)}")

        # 确保response_format是有效的AudioFormat枚举
        if isinstance(self.response_format, str):
            try:
                self.response_format = AudioFormat(self.response_format.lower())
            except ValueError:
                raise ValueError(f"Invalid format: {self.response_format}. Must be one of {list(AudioFormat)}")

        # 验证输入文本
        if not self.input or not self.input.strip():
            raise ValueError("Input text cannot be empty")

        # 验证文本长度（如果启用）
        if self.validate_length:
            text_length = len(self.input)
            if text_length > self.max_length:
                raise ValueError(
                    f"Input text is too long ({text_length} characters). "
                    f"Maximum allowed length is {self.max_length} characters. "
                    f"Consider splitting your text into smaller chunks or disable "
                    f"length validation with validate_length=False."
                )

        # 验证max_length参数
        if self.max_length <= 0:
            raise ValueError("max_length must be a positive integer")

        # 验证speed（如果提供）
        if self.speed is not None and (self.speed < 0.25 or self.speed > 4.0):
            raise ValueError("Speed must be between 0.25 and 4.0")
    
    def to_dict(self) -> Dict[str, Any]:
        """将请求转换为字典用于API调用"""
        data = {
            "input": self.input,
            "voice": self.voice.value if isinstance(self.voice, Voice) else self.voice,
            "response_format": self.response_format.value if isinstance(self.response_format, AudioFormat) else self.response_format
        }
        
        if self.instructions:
            data["instructions"] = self.instructions
        
        if self.model:
            data["model"] = self.model
            
        if self.speed is not None:
            data["speed"] = self.speed
            
        return data


@dataclass
class TTSResponse:
    """TTS响应模型"""
    audio_data: bytes
    content_type: str
    format: AudioFormat
    size: int
    duration: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后计算派生字段"""
        if self.size is None:
            self.size = len(self.audio_data)
    
    def save_to_file(self, filename: str) -> str:
        """
        将音频数据保存到文件
        
        Args:
            filename: 目标文件名（如果缺少扩展名会自动添加）
            
        Returns:
            str: 使用的最终文件名
        """
        # 使用实际返回的格式作为扩展名，而不是任何请求的格式
        expected_extension = f".{self.format.value}"

        # 检查文件名是否已经有正确的扩展名
        if filename.endswith(expected_extension):
            final_filename = filename
        else:
            # 移除任何现有扩展名并添加正确的扩展名
            base_name = filename
            # 如果存在，移除常见的音频扩展名
            for ext in ['.mp3', '.wav', '.opus', '.aac', '.flac', '.pcm']:
                if base_name.endswith(ext):
                    base_name = base_name[:-len(ext)]
                    break
            final_filename = f"{base_name}{expected_extension}"

        # 如果目录不存在则创建
        os.makedirs(os.path.dirname(final_filename) if os.path.dirname(final_filename) else ".", exist_ok=True)

        # 写入音频数据
        with open(final_filename, "wb") as f:
            f.write(self.audio_data)

        return final_filename


# ============================================================================
# 异常类
# ============================================================================

class TTSException(Exception):
    """TTSFM相关错误的基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        code: Optional[str] = None, 
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.code = code or self.__class__.__name__
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.code:
            return f"[{self.code}] {self.message}"
        return self.message
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(message='{self.message}', code='{self.code}')"


class APIException(TTSException):
    """API相关错误异常"""
    
    def __init__(
        self, 
        message: str, 
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.status_code = status_code
        self.response_data = response_data or {}
    
    def __str__(self) -> str:
        if self.status_code:
            return f"[HTTP {self.status_code}] {self.message}"
        return super().__str__()


class NetworkException(TTSException):
    """网络相关错误异常"""
    
    def __init__(
        self, 
        message: str, 
        timeout: Optional[float] = None,
        retry_count: int = 0,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.timeout = timeout
        self.retry_count = retry_count


class ValidationException(TTSException):
    """输入验证错误异常"""
    
    def __init__(
        self, 
        message: str, 
        field: Optional[str] = None,
        value: Optional[Any] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.field = field
        self.value = value
    
    def __str__(self) -> str:
        if self.field:
            return f"Validation error for '{self.field}': {self.message}"
        return f"Validation error: {self.message}"


class RateLimitException(APIException):
    """API速率限制异常"""
    
    def __init__(
        self, 
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None,
        limit: Optional[int] = None,
        remaining: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, status_code=429, **kwargs)
        self.retry_after = retry_after
        self.limit = limit
        self.remaining = remaining
    
    def __str__(self) -> str:
        msg = super().__str__()
        if self.retry_after:
            msg += f" (retry after {self.retry_after}s)"
        return msg


class AuthenticationException(APIException):
    """认证和授权错误异常"""
    
    def __init__(
        self, 
        message: str = "Authentication failed",
        **kwargs
    ):
        super().__init__(message, status_code=401, **kwargs)


class ServiceUnavailableException(APIException):
    """TTS服务暂时不可用异常"""
    
    def __init__(
        self, 
        message: str = "Service temporarily unavailable",
        retry_after: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, status_code=503, **kwargs)
        self.retry_after = retry_after


class QuotaExceededException(APIException):
    """使用配额超出异常"""
    
    def __init__(
        self, 
        message: str = "Usage quota exceeded",
        quota_type: Optional[str] = None,
        limit: Optional[int] = None,
        used: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, status_code=402, **kwargs)
        self.quota_type = quota_type
        self.limit = limit
        self.used = used


class AudioProcessingException(TTSException):
    """音频处理错误异常"""
    
    def __init__(
        self, 
        message: str,
        audio_format: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.audio_format = audio_format


def create_exception_from_response(
    status_code: int, 
    response_data: Dict[str, Any],
    default_message: str = "API request failed"
) -> APIException:
    """从API响应创建适当的异常"""
    message = response_data.get("error", {}).get("message", default_message)
    
    if status_code == 401:
        return AuthenticationException(message, response_data=response_data)
    elif status_code == 402:
        return QuotaExceededException(message, response_data=response_data)
    elif status_code == 429:
        retry_after = response_data.get("retry_after")
        return RateLimitException(message, retry_after=retry_after, response_data=response_data)
    elif status_code == 503:
        retry_after = response_data.get("retry_after")
        return ServiceUnavailableException(message, retry_after=retry_after, response_data=response_data)
    else:
        return APIException(message, status_code=status_code, response_data=response_data)


# ============================================================================
# 工具函数
# ============================================================================

# 配置日志
logger = logging.getLogger(__name__)

# 音频格式的内容类型映射
CONTENT_TYPE_MAP = {
    AudioFormat.MP3: "audio/mpeg",
    AudioFormat.OPUS: "audio/opus",
    AudioFormat.AAC: "audio/aac",
    AudioFormat.FLAC: "audio/flac",
    AudioFormat.WAV: "audio/wav",
    AudioFormat.PCM: "audio/pcm"
}

# 内容类型到格式的反向映射
FORMAT_FROM_CONTENT_TYPE = {v: k for k, v in CONTENT_TYPE_MAP.items()}


def get_content_type(format: Union[AudioFormat, str]) -> str:
    """获取音频格式的MIME内容类型"""
    if isinstance(format, str):
        format = AudioFormat(format.lower())
    return CONTENT_TYPE_MAP.get(format, "audio/mpeg")


def get_format_from_content_type(content_type: str) -> AudioFormat:
    """从MIME内容类型获取音频格式"""
    return FORMAT_FROM_CONTENT_TYPE.get(content_type, AudioFormat.MP3)


def get_supported_format(requested_format: AudioFormat) -> AudioFormat:
    """
    将请求的格式映射到支持的格式

    Args:
        requested_format: 请求的音频格式

    Returns:
        AudioFormat: MP3或WAV（支持的格式）
    """
    if requested_format == AudioFormat.MP3:
        return AudioFormat.MP3
    else:
        # 所有其他格式（WAV, OPUS, AAC, FLAC, PCM）返回WAV
        return AudioFormat.WAV


def maps_to_wav(format_value: str) -> bool:
    """
    检查格式是否映射到WAV

    Args:
        format_value: 要检查的格式字符串

    Returns:
        bool: 如果格式映射到WAV则为True
    """
    return format_value.lower() in ['wav', 'opus', 'aac', 'flac', 'pcm']


def get_user_agent() -> str:
    """
    生成真实的User-Agent字符串

    Returns:
        str: 用于HTTP请求的User-Agent字符串
    """
    try:
        from fake_useragent import UserAgent
        ua = UserAgent()
        return ua.random
    except ImportError:
        # 如果fake_useragent不可用则使用回退
        return "TTSFM-Client/3.0.0 (Python)"


def get_realistic_headers() -> Dict[str, str]:
    """
    生成真实的HTTP请求头

    Returns:
        Dict[str, str]: HTTP请求头字典
    """
    user_agent = get_user_agent()

    headers = {
        "Accept": "application/json, audio/*",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": random.choice(["en-US,en;q=0.9", "en-GB,en;q=0.8", "en-CA,en;q=0.7"]),
        "Cache-Control": "no-cache",
        "DNT": "1",
        "Pragma": "no-cache",
        "User-Agent": user_agent,
        "X-Requested-With": "XMLHttpRequest",
    }

    # 为基于Chromium的浏览器添加特定头部
    if any(browser in user_agent.lower() for browser in ['chrome', 'edge', 'chromium']):
        version_match = re.search(r'(?:Chrome|Edge|Chromium)/(\d+)', user_agent)
        major_version = version_match.group(1) if version_match else "121"

        brands = []
        if 'google chrome' in user_agent.lower():
            brands.extend([
                f'"Google Chrome";v="{major_version}"',
                f'"Chromium";v="{major_version}"',
                '"Not A(Brand";v="99"'
            ])
        elif 'microsoft edge' in user_agent.lower():
            brands.extend([
                f'"Microsoft Edge";v="{major_version}"',
                f'"Chromium";v="{major_version}"',
                '"Not A(Brand";v="99"'
            ])
        else:
            brands.extend([
                f'"Chromium";v="{major_version}"',
                '"Not A(Brand";v="8"'
            ])

        headers.update({
            "Sec-Ch-Ua": ", ".join(brands),
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": random.choice(['"Windows"', '"macOS"', '"Linux"']),
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin"
        })

    # 随机添加一些可选头部
    if random.random() < 0.5:
        headers["Upgrade-Insecure-Requests"] = "1"

    return headers


def validate_text_length(text: str, max_length: int = 4096, raise_error: bool = True) -> bool:
    """
    验证文本长度是否超过最大允许字符数

    Args:
        text: 要验证的文本
        max_length: 最大允许长度（字符数）
        raise_error: 如果验证失败是否抛出异常

    Returns:
        bool: 如果文本在限制内则为True，否则为False

    Raises:
        ValueError: 如果文本超过max_length且raise_error为True
    """
    if not text:
        return True

    text_length = len(text)

    if text_length > max_length:
        if raise_error:
            raise ValueError(
                f"Text is too long ({text_length} characters). "
                f"Maximum allowed length is {max_length} characters. "
                f"TTS models typically support up to 4096 characters per request."
            )
        return False

    return True


def split_text_by_length(text: str, max_length: int = 4096, preserve_words: bool = True) -> List[str]:
    """
    将文本分割成不超过最大长度的块

    Args:
        text: 要分割的文本
        max_length: 每块的最大长度
        preserve_words: 是否避免分割单词

    Returns:
        List[str]: 文本块列表
    """
    if not text:
        return []

    if len(text) <= max_length:
        return [text]

    chunks = []

    if preserve_words:
        # 首先按句子分割，然后如果需要按单词分割
        sentences = re.split(r'[.!?]+', text)
        current_chunk = ""

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # 添加句子结尾标点符号
            if not sentence.endswith(('.', '!', '?')):
                sentence += '.'

            # 检查添加这个句子是否会超过限制
            test_chunk = current_chunk + (" " if current_chunk else "") + sentence

            if len(test_chunk) <= max_length:
                current_chunk = test_chunk
            else:
                # 如果当前块有内容则保存
                if current_chunk:
                    chunks.append(current_chunk.strip())

                # 如果单个句子太长，按单词分割
                if len(sentence) > max_length:
                    word_chunks = _split_by_words(sentence, max_length)
                    chunks.extend(word_chunks)
                    current_chunk = ""
                else:
                    current_chunk = sentence

        # 添加剩余的块
        if current_chunk:
            chunks.append(current_chunk.strip())
    else:
        # 简单的基于字符的分割
        for i in range(0, len(text), max_length):
            chunks.append(text[i:i + max_length])

    return [chunk for chunk in chunks if chunk.strip()]


def _split_by_words(text: str, max_length: int) -> List[str]:
    """
    当句子太长时按单词分割文本

    Args:
        text: 要分割的文本
        max_length: 每块的最大长度

    Returns:
        List[str]: 基于单词的块列表
    """
    words = text.split()
    chunks = []
    current_chunk = ""

    for word in words:
        test_chunk = current_chunk + (" " if current_chunk else "") + word

        if len(test_chunk) <= max_length:
            current_chunk = test_chunk
        else:
            if current_chunk:
                chunks.append(current_chunk)

            # 如果单个单词太长，分割它
            if len(word) > max_length:
                for i in range(0, len(word), max_length):
                    chunks.append(word[i:i + max_length])
                current_chunk = ""
            else:
                current_chunk = word

    if current_chunk:
        chunks.append(current_chunk)

    return chunks


def sanitize_text(text: str) -> str:
    """
    清理输入文本用于TTS处理

    移除HTML标记和可能有问题的字符，确保为文本转语音生成提供干净的文本输入。
    使用安全的正则表达式模式防止ReDoS攻击。

    Args:
        text: 要清理的输入文本

    Returns:
        str: 清理后的安全文本用于TTS处理

    Raises:
        ValueError: 如果输入文本太长（>50000字符）
    """
    if not text:
        return ""

    # 通过限制输入长度防止ReDoS攻击
    if len(text) > 50000:
        raise ValueError("Input text too long for sanitization (max 50000 characters)")

    # 使用简单的逐字符方法移除类似HTML的内容
    # 这避免了可能导致ReDoS的复杂正则表达式模式
    result = []
    i = 0
    while i < len(text):
        if text[i] == '<':
            # 找到标签的结尾
            j = i + 1
            while j < len(text) and text[j] != '>':
                j += 1
            if j < len(text):
                # 跳过整个标签
                i = j + 1
            else:
                # 没有闭合的>，作为常规字符处理
                result.append(text[i])
                i += 1
        elif text[i] == '&':
            # 处理HTML实体
            j = i + 1
            while j < len(text) and j < i + 10 and text[j] not in ' \t\n\r<>&':
                j += 1
            if j < len(text) and text[j] == ';':
                # 跳过实体
                i = j + 1
            else:
                # 不是有效实体，保留&
                result.append(' ')  # 为TTS替换为空格
                i += 1
        else:
            # 常规字符
            char = text[i]
            # 为TTS规范化引号
            if char in '""''`':
                result.append('"')
            elif char in '<>':
                # 跳过这些字符
                pass
            else:
                result.append(char)
            i += 1

    # 连接并使用安全正则表达式规范化空白字符
    sanitized = ''.join(result)
    sanitized = re.sub(r'[ \t\n\r\f\v]+', ' ', sanitized)

    return sanitized.strip()


def validate_url(url: str) -> bool:
    """
    验证URL是否格式正确

    Args:
        url: 要验证的URL

    Returns:
        bool: 如果URL有效则为True，否则为False
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def build_url(base_url: str, path: str) -> str:
    """
    从基础URL和路径构建完整URL

    Args:
        base_url: 基础URL
        path: 要附加的路径

    Returns:
        str: 完整URL
    """
    # 确保base_url以/结尾
    if not base_url.endswith('/'):
        base_url += '/'

    # 确保path不以/开头
    if path.startswith('/'):
        path = path[1:]

    return urljoin(base_url, path)


def exponential_backoff(attempt: int, base_delay: float = 1.0, max_delay: float = 60.0) -> float:
    """
    计算指数退避延迟

    Args:
        attempt: 尝试次数（从0开始）
        base_delay: 基础延迟（秒）
        max_delay: 最大延迟（秒）

    Returns:
        float: 延迟时间（秒）
    """
    delay = base_delay * (2 ** attempt)
    jitter = random.uniform(0.1, 0.3) * delay
    return min(delay + jitter, max_delay)


def estimate_audio_duration(text: str, words_per_minute: float = 150.0) -> float:
    """
    基于文本长度估算音频时长

    Args:
        text: 输入文本
        words_per_minute: 平均语速

    Returns:
        float: 估算时长（秒）
    """
    if not text:
        return 0.0

    # 计算单词数（简单的空白字符分割）
    word_count = len(text.split())

    # 计算时长（秒）
    duration = (word_count / words_per_minute) * 60.0

    # 为停顿和处理添加一些缓冲
    return duration * 1.1


def format_file_size(size_bytes: int) -> str:
    """
    以人类可读格式格式化文件大小

    Args:
        size_bytes: 字节大小

    Returns:
        str: 格式化的大小字符串
    """
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB"]
    i = 0

    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f} {size_names[i]}"


# ============================================================================
# 主要的TTS客户端类
# ============================================================================

class TTSClient:
    """
    同步TTS客户端，用于文本转语音生成

    该客户端提供了一个简单的接口，用于使用兼容OpenAI的TTS服务从文本生成语音。

    属性:
        base_url: TTS服务的基础URL
        api_key: 认证用的API密钥（如果需要）
        timeout: 请求超时时间（秒）
        max_retries: 最大重试次数
        verify_ssl: 是否验证SSL证书
    """

    def __init__(
        self,
        base_url: str = "https://www.openai.fm",
        api_key: Optional[str] = None,
        timeout: float = 30.0,
        max_retries: int = 3,
        verify_ssl: bool = True,
        preferred_format: Optional[AudioFormat] = None,
        **kwargs
    ):
        """
        初始化TTS客户端

        Args:
            base_url: TTS服务的基础URL
            api_key: 认证用的API密钥
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            verify_ssl: 是否验证SSL证书
            preferred_format: 首选音频格式（影响头部选择）
            **kwargs: 额外的配置选项
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.timeout = timeout
        self.max_retries = max_retries
        self.verify_ssl = verify_ssl
        self.preferred_format = preferred_format or AudioFormat.WAV

        # 验证基础URL
        if not validate_url(self.base_url):
            raise ValidationException(f"Invalid base URL: {self.base_url}")

        # 设置带有重试策略的HTTP会话
        self.session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=max_retries,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST"],  # 更新的参数名
            backoff_factor=1
        )

        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=10,
            pool_maxsize=10
        )
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置默认头部
        self.session.headers.update(get_realistic_headers())

        if self.api_key:
            self.session.headers["Authorization"] = f"Bearer {self.api_key}"

        logger.info(f"Initialized TTS client with base URL: {self.base_url}")

    def _get_headers_for_format(self, requested_format: AudioFormat) -> Dict[str, str]:
        """
        获取适当的头部以从openai.fm获取所需格式

        基于测试，openai.fm返回：
        - MP3：使用简单/最小头部时
        - WAV：使用完整Chrome安全头部时

        Args:
            requested_format: 所需的音频格式

        Returns:
            Dict[str, str]: 为请求格式优化的HTTP头部
        """
        # 将请求格式映射到支持的格式
        target_format = get_supported_format(requested_format)

        if target_format == AudioFormat.MP3:
            # 使用最小头部获取MP3响应
            return {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                'Accept': 'audio/*,*/*;q=0.9'
            }
        else:
            # 使用完整真实头部获取WAV响应
            # 这适用于WAV、OPUS、AAC、FLAC、PCM格式
            return get_realistic_headers()

    def generate_speech(
        self,
        text: str,
        voice: Union[Voice, str] = Voice.ALLOY,
        response_format: Union[AudioFormat, str] = AudioFormat.MP3,
        instructions: Optional[str] = None,
        max_length: int = 4096,
        validate_length: bool = True,
        **kwargs
    ) -> TTSResponse:
        """
        从文本生成语音

        Args:
            text: 要转换为语音的文本
            voice: 用于生成的语音
            response_format: 输出的音频格式
            instructions: 语音调制的可选指令
            max_length: 最大允许文本长度（字符，默认：4096）
            validate_length: 是否验证文本长度（默认：True）
            **kwargs: 额外参数

        Returns:
            TTSResponse: 生成的音频响应

        Raises:
            TTSException: 如果生成失败
            ValueError: 如果文本超过max_length且validate_length为True
        """
        # 创建并验证请求
        request = TTSRequest(
            input=sanitize_text(text),
            voice=voice,
            response_format=response_format,
            instructions=instructions,
            max_length=max_length,
            validate_length=validate_length,
            **kwargs
        )

        return self._make_request(request)

    def generate_speech_from_request(self, request: TTSRequest) -> TTSResponse:
        """
        从TTSRequest对象生成语音

        Args:
            request: TTS请求对象

        Returns:
            TTSResponse: 生成的音频响应
        """
        return self._make_request(request)

    def generate_speech_batch(
        self,
        text: str,
        voice: Union[Voice, str] = Voice.ALLOY,
        response_format: Union[AudioFormat, str] = AudioFormat.MP3,
        instructions: Optional[str] = None,
        max_length: int = 4096,
        preserve_words: bool = True,
        **kwargs
    ) -> List[TTSResponse]:
        """
        通过将长文本分割成块来生成语音

        该方法自动将超过max_length的文本分割成较小的块，并为每个块单独生成语音。

        Args:
            text: 要转换为语音的文本
            voice: 用于生成的语音
            response_format: 输出的音频格式
            instructions: 语音调制的可选指令
            max_length: 每块的最大长度（默认：4096）
            preserve_words: 是否避免分割单词（默认：True）
            **kwargs: 额外参数

        Returns:
            List[TTSResponse]: 生成的音频响应列表

        Raises:
            TTSException: 如果任何块的生成失败
        """

        # 首先清理文本
        clean_text = sanitize_text(text)

        # 将文本分割成块
        chunks = split_text_by_length(clean_text, max_length, preserve_words)

        if not chunks:
            raise ValueError("No valid text chunks found after processing")

        responses = []

        for i, chunk in enumerate(chunks):
            logger.info(f"Processing chunk {i+1}/{len(chunks)} ({len(chunk)} characters)")

            # 为此块创建请求（禁用长度验证，因为我们已经分割了）
            request = TTSRequest(
                input=chunk,
                voice=voice,
                response_format=response_format,
                instructions=instructions,
                max_length=max_length,
                validate_length=False,  # 我们已经分割了文本
                **kwargs
            )

            response = self._make_request(request)
            responses.append(response)

        return responses

    def generate_speech_long_text(
        self,
        text: str,
        voice: Union[Voice, str] = Voice.ALLOY,
        response_format: Union[AudioFormat, str] = AudioFormat.MP3,
        instructions: Optional[str] = None,
        max_length: int = 4096,
        preserve_words: bool = True,
        **kwargs
    ) -> List[TTSResponse]:
        """
        通过将长文本分割成块来生成语音

        这是generate_speech_batch的别名，用于与AsyncTTSClient保持一致。
        自动将超过max_length的文本分割成较小的块，并为每个块单独生成语音。

        Args:
            text: 要转换为语音的文本
            voice: 用于生成的语音
            response_format: 输出的音频格式
            instructions: 语音调制的可选指令
            max_length: 每块的最大长度（默认：4096）
            preserve_words: 是否避免分割单词（默认：True）
            **kwargs: 额外参数

        Returns:
            List[TTSResponse]: 生成的音频响应列表

        Raises:
            TTSException: 如果任何块的生成失败
        """
        return self.generate_speech_batch(
            text=text,
            voice=voice,
            response_format=response_format,
            instructions=instructions,
            max_length=max_length,
            preserve_words=preserve_words,
            **kwargs
        )

    def _make_request(self, request: TTSRequest) -> TTSResponse:
        """
        向openai.fm TTS服务发出实际的HTTP请求

        Args:
            request: TTS请求对象

        Returns:
            TTSResponse: 生成的音频响应

        Raises:
            TTSException: 如果请求失败
        """
        url = build_url(self.base_url, "api/generate")

        # 为openai.fm API准备表单数据
        form_data = {
            'input': request.input,
            'voice': request.voice.value,
            'generation': str(uuid.uuid4()),
            'response_format': request.response_format.value if hasattr(request.response_format, 'value') else str(request.response_format)
        }

        # 如果提供了提示/指令则添加
        if request.instructions:
            form_data['prompt'] = request.instructions
        else:
            # 用于更好质量的默认提示
            form_data['prompt'] = (
                "Affect/personality: Natural and clear\n\n"
                "Tone: Friendly and professional, creating a pleasant listening experience.\n\n"
                "Pronunciation: Clear, articulate, and steady, ensuring each word is easily understood "
                "while maintaining a natural, conversational flow.\n\n"
                "Pause: Brief, purposeful pauses between sentences to allow time for the listener "
                "to process the information.\n\n"
                "Emotion: Warm and engaging, conveying the intended message effectively."
            )

        # 获取为请求格式优化的头部
        # 如果需要，将字符串格式转换为AudioFormat枚举
        requested_format = request.response_format
        if isinstance(requested_format, str):
            try:
                requested_format = AudioFormat(requested_format.lower())
            except ValueError:
                requested_format = AudioFormat.WAV  # 对于未知格式默认为WAV

        format_headers = self._get_headers_for_format(requested_format)

        logger.info(f"Generating speech for text: '{request.input[:50]}...' with voice: {request.voice}")
        logger.debug(f"Using headers optimized for {requested_format.value} format")

        # 带重试的请求
        for attempt in range(self.max_retries + 1):
            try:
                # 为速率限制添加随机延迟（除了第一次尝试）
                if attempt > 0:
                    delay = exponential_backoff(attempt - 1)
                    logger.info(f"Retrying request after {delay:.2f}s (attempt {attempt + 1})")
                    time.sleep(delay)

                # 使用openai.fm要求的多部分表单数据
                response = self.session.post(
                    url,
                    data=form_data,
                    headers=format_headers,
                    timeout=self.timeout,
                    verify=self.verify_ssl
                )

                # 处理不同的响应类型
                if response.status_code == 200:
                    return self._process_openai_fm_response(response, request)
                else:
                    # 尝试解析错误响应
                    try:
                        error_data = response.json()
                    except (json.JSONDecodeError, ValueError):
                        error_data = {"error": {"message": response.text or "Unknown error"}}

                    # 创建适当的异常
                    exception = create_exception_from_response(
                        response.status_code,
                        error_data,
                        f"TTS request failed with status {response.status_code}"
                    )

                    # 对于某些错误不重试
                    if response.status_code in [400, 401, 403, 404]:
                        raise exception

                    # 对于可重试的错误，继续下一次尝试
                    if attempt == self.max_retries:
                        raise exception

                    logger.warning(f"Request failed with status {response.status_code}, retrying...")
                    continue

            except requests.exceptions.Timeout:
                if attempt == self.max_retries:
                    raise NetworkException(
                        f"Request timed out after {self.timeout}s",
                        timeout=self.timeout,
                        retry_count=attempt
                    )
                logger.warning(f"Request timed out, retrying...")
                continue

            except requests.exceptions.ConnectionError as e:
                if attempt == self.max_retries:
                    raise NetworkException(
                        f"Connection error: {str(e)}",
                        retry_count=attempt
                    )
                logger.warning(f"Connection error, retrying...")
                continue

            except requests.exceptions.RequestException as e:
                if attempt == self.max_retries:
                    raise NetworkException(
                        f"Request error: {str(e)}",
                        retry_count=attempt
                    )
                logger.warning(f"Request error, retrying...")
                continue

        # 这应该永远不会到达，但以防万一
        raise TTSException("Maximum retries exceeded")

    def _process_openai_fm_response(self, response: requests.Response, request: TTSRequest) -> TTSResponse:
        """
        处理来自openai.fm TTS服务的成功响应

        Args:
            response: HTTP响应对象
            request: 原始TTS请求

        Returns:
            TTSResponse: 处理后的响应对象
        """
        # 从响应头获取内容类型
        content_type = response.headers.get("content-type", "audio/mpeg")

        # 获取音频数据
        audio_data = response.content

        if not audio_data:
            raise APIException("Received empty audio data from openai.fm")

        # 从内容类型确定格式
        if "audio/mpeg" in content_type or "audio/mp3" in content_type:
            actual_format = AudioFormat.MP3
        elif "audio/wav" in content_type:
            actual_format = AudioFormat.WAV
        elif "audio/opus" in content_type:
            actual_format = AudioFormat.OPUS
        elif "audio/aac" in content_type:
            actual_format = AudioFormat.AAC
        elif "audio/flac" in content_type:
            actual_format = AudioFormat.FLAC
        else:
            # 对于openai.fm默认为MP3
            actual_format = AudioFormat.MP3

        # 基于文本长度估算时长（粗略近似）
        estimated_duration = estimate_audio_duration(request.input)

        # 检查返回的格式是否与请求的格式不同
        requested_format = request.response_format
        if isinstance(requested_format, str):
            try:
                requested_format = AudioFormat(requested_format.lower())
            except ValueError:
                requested_format = AudioFormat.WAV  # 默认回退

        # 检查格式是否与请求不同
        if actual_format != requested_format:
            if maps_to_wav(requested_format.value) and actual_format.value == "wav":
                logger.debug(
                    f"Format '{requested_format.value}' requested, returning WAV format."
                )
            else:
                logger.warning(
                    f"Requested format '{requested_format.value}' but received '{actual_format.value}' "
                    f"from service."
                )

        # 创建响应对象
        tts_response = TTSResponse(
            audio_data=audio_data,
            content_type=content_type,
            format=actual_format,
            size=len(audio_data),
            duration=estimated_duration,
            metadata={
                "response_headers": dict(response.headers),
                "status_code": response.status_code,
                "url": str(response.url),
                "service": "openai.fm",
                "voice": request.voice.value,
                "original_text": request.input[:100] + "..." if len(request.input) > 100 else request.input,
                "requested_format": requested_format.value,
                "actual_format": actual_format.value
            }
        )

        logger.info(
            f"Successfully generated {format_file_size(len(audio_data))} "
            f"of {actual_format.value.upper()} audio from openai.fm using voice '{request.voice.value}'"
        )

        return tts_response

    def close(self):
        """关闭HTTP会话"""
        if hasattr(self, 'session'):
            self.session.close()

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


# ============================================================================
# 便利函数
# ============================================================================

def create_client(base_url: str = None, api_key: str = None, **kwargs) -> TTSClient:
    """
    创建新的TTS客户端实例

    Args:
        base_url: TTS服务的基础URL
        api_key: 认证用的API密钥（如果需要）
        **kwargs: 额外的客户端配置

    Returns:
        TTSClient: 配置好的客户端实例
    """
    return TTSClient(base_url=base_url, api_key=api_key, **kwargs)


def generate_speech(
    text: str,
    voice: Union[Voice, str] = Voice.ALLOY,
    response_format: Union[AudioFormat, str] = AudioFormat.MP3,
    base_url: str = "https://www.openai.fm",
    **kwargs
) -> TTSResponse:
    """
    使用默认客户端生成语音的便利函数

    Args:
        text: 要转换为语音的文本
        voice: 用于生成的语音
        response_format: 输出的音频格式
        base_url: TTS服务的基础URL
        **kwargs: 额外参数

    Returns:
        TTSResponse: 生成的音频响应
    """
    with TTSClient(base_url=base_url, **kwargs) as client:
        return client.generate_speech(text, voice, response_format)


def generate_speech_long_text(
    text: str,
    voice: Union[Voice, str] = Voice.ALLOY,
    response_format: Union[AudioFormat, str] = AudioFormat.MP3,
    base_url: str = "https://www.openai.fm",
    max_length: int = 4096,
    preserve_words: bool = True,
    **kwargs
) -> List[TTSResponse]:
    """
    为长文本生成语音的便利函数

    Args:
        text: 要转换为语音的文本
        voice: 用于生成的语音
        response_format: 输出的音频格式
        base_url: TTS服务的基础URL
        max_length: 每块的最大长度
        preserve_words: 是否避免分割单词
        **kwargs: 额外参数

    Returns:
        List[TTSResponse]: 生成的音频响应列表
    """
    with TTSClient(base_url=base_url, **kwargs) as client:
        return client.generate_speech_long_text(
            text, voice, response_format, max_length=max_length,
            preserve_words=preserve_words
        )


# ============================================================================
# 示例用法
# ============================================================================

if __name__ == "__main__":
    # 基本用法示例
    print("TTSFM独立客户端示例")
    print("=" * 50)

    # 创建客户端
    client = TTSClient()

    try:
        # 生成短文本语音
        print("生成短文本语音...")
        response = client.generate_speech(
            text="Hello, this is a test of the TTSFM standalone client!",
            voice=Voice.ALLOY,
            response_format=AudioFormat.MP3
        )

        # 保存音频文件
        filename = response.save_to_file("test_output")
        print(f"音频已保存到: {filename}")
        print(f"文件大小: {format_file_size(response.size)}")
        print(f"估算时长: {response.duration:.2f}秒")

        # 生成长文本语音（自动分块）
        long_text = """
        This is a longer text that will be automatically split into chunks.
        The TTSFM client can handle long texts by breaking them down into
        smaller pieces that fit within the API limits. Each chunk is processed
        separately and you get a list of audio responses that you can combine
        or use individually.
        """

        print("\n生成长文本语音（分块处理）...")
        responses = client.generate_speech_long_text(
            text=long_text,
            voice=Voice.NOVA,
            response_format=AudioFormat.WAV,
            max_length=100  # 小的块大小用于演示
        )

        print(f"生成了 {len(responses)} 个音频块")
        for i, resp in enumerate(responses):
            filename = resp.save_to_file(f"chunk_{i+1}")
            print(f"块 {i+1} 已保存到: {filename} ({format_file_size(resp.size)})")

    except Exception as e:
        print(f"错误: {e}")
    finally:
        client.close()

    print("\n示例完成！")
