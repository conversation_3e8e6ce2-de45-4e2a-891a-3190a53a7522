#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTSFM独立客户端测试脚本

这个脚本用于测试standalone_client.py的各种功能
"""

import os
import sys
import time
from standalone_client import (
    TTSClient, Voice, AudioFormat, 
    generate_speech, generate_speech_long_text,
    TTSException, NetworkException, ValidationException
)

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("测试基本功能")
    print("=" * 60)
    
    try:
        # 创建客户端
        print("1. 创建TTS客户端...")
        client = TTSClient()
        print("✓ 客户端创建成功")
        
        # 测试短文本生成
        print("\n2. 测试短文本语音生成...")
        response = client.generate_speech(
            text="Hello, this is a test of the TTSFM standalone client!",
            voice=Voice.ALLOY,
            response_format=AudioFormat.MP3
        )
        
        print(f"✓ 语音生成成功")
        print(f"  - 音频格式: {response.format.value}")
        print(f"  - 文件大小: {response.size} bytes")
        print(f"  - 内容类型: {response.content_type}")
        print(f"  - 估算时长: {response.duration:.2f}秒")
        
        # 保存文件
        filename = response.save_to_file("test_basic")
        print(f"✓ 音频已保存到: {filename}")
        
        # 关闭客户端
        client.close()
        print("✓ 客户端已关闭")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_context_manager():
    """测试上下文管理器"""
    print("\n" + "=" * 60)
    print("测试上下文管理器")
    print("=" * 60)
    
    try:
        print("1. 使用上下文管理器...")
        with TTSClient() as client:
            response = client.generate_speech(
                text="Testing context manager functionality",
                voice=Voice.NOVA,
                response_format=AudioFormat.WAV
            )
            
            filename = response.save_to_file("test_context")
            print(f"✓ 音频已保存到: {filename}")
            print(f"  - 格式: {response.format.value}")
            print(f"  - 大小: {response.size} bytes")
        
        print("✓ 上下文管理器测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 上下文管理器测试失败: {e}")
        return False

def test_different_voices():
    """测试不同语音"""
    print("\n" + "=" * 60)
    print("测试不同语音")
    print("=" * 60)
    
    voices_to_test = [Voice.ALLOY, Voice.ECHO, Voice.NOVA]
    
    try:
        with TTSClient() as client:
            for i, voice in enumerate(voices_to_test):
                print(f"{i+1}. 测试语音: {voice.value}")
                
                response = client.generate_speech(
                    text=f"This is a test using {voice.value} voice.",
                    voice=voice,
                    response_format=AudioFormat.MP3
                )
                
                filename = response.save_to_file(f"test_voice_{voice.value}")
                print(f"✓ {voice.value} 语音测试成功，保存到: {filename}")
        
        print("✓ 所有语音测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 语音测试失败: {e}")
        return False

def test_different_formats():
    """测试不同音频格式"""
    print("\n" + "=" * 60)
    print("测试不同音频格式")
    print("=" * 60)
    
    formats_to_test = [AudioFormat.MP3, AudioFormat.WAV, AudioFormat.OPUS]
    
    try:
        with TTSClient() as client:
            for i, format in enumerate(formats_to_test):
                print(f"{i+1}. 测试格式: {format.value}")
                
                response = client.generate_speech(
                    text=f"Testing {format.value} audio format.",
                    voice=Voice.ALLOY,
                    response_format=format
                )
                
                filename = response.save_to_file(f"test_format_{format.value}")
                print(f"✓ {format.value} 格式测试成功")
                print(f"  - 请求格式: {format.value}")
                print(f"  - 实际格式: {response.format.value}")
                print(f"  - 文件: {filename}")
        
        print("✓ 所有格式测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 格式测试失败: {e}")
        return False

def test_long_text():
    """测试长文本处理"""
    print("\n" + "=" * 60)
    print("测试长文本处理")
    print("=" * 60)
    
    long_text = """
    This is a longer text that will be automatically split into chunks.
    The TTSFM client can handle long texts by breaking them down into
    smaller pieces that fit within the API limits. Each chunk is processed
    separately and you get a list of audio responses that you can combine
    or use individually. This functionality is very useful when you need
    to convert large amounts of text to speech, such as articles, books,
    or long documents. The client will intelligently split the text at
    sentence boundaries to maintain natural speech flow.
    """
    
    try:
        print("1. 测试长文本自动分块...")
        with TTSClient() as client:
            responses = client.generate_speech_long_text(
                text=long_text,
                voice=Voice.ALLOY,
                response_format=AudioFormat.MP3,
                max_length=200  # 较小的块大小用于演示
            )
            
            print(f"✓ 长文本已分割为 {len(responses)} 个块")
            
            total_size = 0
            for i, resp in enumerate(responses):
                filename = resp.save_to_file(f"test_long_chunk_{i+1}")
                total_size += resp.size
                print(f"  - 块 {i+1}: {filename} ({resp.size} bytes)")
            
            print(f"✓ 总音频大小: {total_size} bytes")
        
        return True
        
    except Exception as e:
        print(f"✗ 长文本测试失败: {e}")
        return False

def test_convenience_functions():
    """测试便利函数"""
    print("\n" + "=" * 60)
    print("测试便利函数")
    print("=" * 60)
    
    try:
        print("1. 测试 generate_speech 便利函数...")
        response = generate_speech(
            text="Testing convenience function",
            voice=Voice.ECHO,
            response_format=AudioFormat.WAV
        )
        
        filename = response.save_to_file("test_convenience")
        print(f"✓ 便利函数测试成功，保存到: {filename}")
        
        print("\n2. 测试 generate_speech_long_text 便利函数...")
        responses = generate_speech_long_text(
            text="This is a test of the long text convenience function. It should work just like the client method.",
            voice=Voice.NOVA,
            response_format=AudioFormat.MP3,
            max_length=50
        )
        
        print(f"✓ 长文本便利函数测试成功，生成 {len(responses)} 个块")
        for i, resp in enumerate(responses):
            filename = resp.save_to_file(f"test_convenience_long_{i+1}")
            print(f"  - 块 {i+1}: {filename}")
        
        return True
        
    except Exception as e:
        print(f"✗ 便利函数测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("测试错误处理")
    print("=" * 60)
    
    try:
        print("1. 测试空文本验证...")
        try:
            with TTSClient() as client:
                client.generate_speech(text="")
        except ValidationException as e:
            print(f"✓ 空文本验证成功: {e}")
        except Exception as e:
            print(f"✗ 意外错误: {e}")
            return False
        
        print("\n2. 测试文本长度验证...")
        try:
            with TTSClient() as client:
                client.generate_speech(text="x" * 5000, max_length=100)
        except (ValidationException, ValueError) as e:
            print(f"✓ 文本长度验证成功: {e}")
        except Exception as e:
            print(f"✗ 意外错误: {e}")
            return False
        
        print("\n3. 测试无效语音...")
        try:
            with TTSClient() as client:
                client.generate_speech(text="test", voice="invalid_voice")
        except (ValidationException, ValueError) as e:
            print(f"✓ 无效语音验证成功: {e}")
        except Exception as e:
            print(f"✗ 意外错误: {e}")
            return False
        
        print("✓ 错误处理测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n" + "=" * 60)
    print("清理测试文件")
    print("=" * 60)
    
    test_files = [
        "test_basic.mp3", "test_context.wav", 
        "test_voice_alloy.mp3", "test_voice_echo.mp3", "test_voice_nova.mp3",
        "test_format_mp3.mp3", "test_format_wav.wav", "test_format_opus.wav",
        "test_convenience.wav", "test_convenience_long_1.mp3", "test_convenience_long_2.mp3"
    ]
    
    # 添加长文本测试文件
    for i in range(1, 10):  # 假设最多10个块
        test_files.append(f"test_long_chunk_{i}.mp3")
    
    cleaned = 0
    for filename in test_files:
        if os.path.exists(filename):
            try:
                os.remove(filename)
                cleaned += 1
                print(f"✓ 已删除: {filename}")
            except Exception as e:
                print(f"✗ 删除失败 {filename}: {e}")
    
    print(f"✓ 清理完成，删除了 {cleaned} 个文件")

def main():
    """主测试函数"""
    print("TTSFM独立客户端测试")
    print("=" * 60)
    
    tests = [
        ("基本功能", test_basic_functionality),
        ("上下文管理器", test_context_manager),
        ("不同语音", test_different_voices),
        ("不同格式", test_different_formats),
        ("长文本处理", test_long_text),
        ("便利函数", test_convenience_functions),
        ("错误处理", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    # 测试结果总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
    
    # 询问是否清理测试文件
    try:
        response = input("\n是否清理测试文件？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            cleanup_test_files()
    except KeyboardInterrupt:
        print("\n测试结束")

if __name__ == "__main__":
    main()
