# 轻量化Docker构建 - 忽略不必要的文件

# 版本控制
.git
.gitignore
.gitattributes

# Python缓存和虚拟环境
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/
.venv/
.env/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# 日志文件
*.log
logs/
*.out

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 数据文件（运行时生成）
data/browser_temp_*
data/session.json
data/articles/*.html
data/articles/*.json

# 构建产物
build/
dist/
*.egg-info/

# 测试文件
.pytest_cache/
.coverage
htmlcov/
.tox/

# 文档
docs/
*.md
README*

# 部署脚本（已在容器外使用）
deploy.sh
deploy.bat
start.sh
start.bat
install.sh
install_deps.bat

# Docker相关（避免递归）
Dockerfile
Dockerfile.*
docker-compose*.yml
.dockerignore

# 其他不必要文件
*.bak
*.backup
*.old
清除pycache.bat
