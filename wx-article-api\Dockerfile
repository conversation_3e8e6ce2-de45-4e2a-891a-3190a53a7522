# --- 阶段 1: 构建器 (Builder) ---
# 这个阶段只负责下载和准备文件
FROM debian:12-slim AS builder

# 设置版本号
ARG CHROME_VERSION="138.0.7204.92"

# 安装下载工具
RUN apt-get update && apt-get install -y --no-install-recommends wget unzip ca-certificates && rm -rf /var/lib/apt/lists/*

# 下载 Chrome 和 Chromedriver
WORKDIR /build
RUN wget -q -O google-chrome.deb "https://dl.google.com/linux/chrome/deb/pool/main/g/google-chrome-stable/google-chrome-stable_${CHROME_VERSION}-1_amd64.deb" && \
    wget -q -O chromedriver.zip "https://storage.googleapis.com/chrome-for-testing-public/${CHROME_VERSION}/linux64/chromedriver-linux64.zip" && \
    unzip chromedriver.zip && \
    mv chromedriver-linux64/chromedriver /usr/local/bin/chromedriver && \
    chmod +x /usr/local/bin/chromedriver


# --- 阶段 2: 最终镜像 (Final Image) ---
# 这个阶段负责构建最终的运行环境
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 安装 Chrome 的运行时依赖和 xvfb
# 注意：这里我们不再下载，而是直接安装
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    # Chrome 运行时必需的核心依赖库 (这是一个经过验证的最小集合)
    libasound2 libatk-bridge2.0-0 libatk1.0-0 libcairo2 libcups2 libdbus-1-3 \
    libexpat1 libgbm1 libglib2.0-0 libnspr4 libnss3 libpango-1.0-0 \
    libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 \
    libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 \
    libxkbcommon0 \
    # 字体相关
    libfontconfig1 fonts-liberation fonts-noto-cjk \
    # 其他工具
    lsb-release xdg-utils \
    # 虚拟显示服务器
    xvfb \
    xauth \
    && rm -rf /var/lib/apt/lists/*

# 从构建器阶段复制已准备好的文件
COPY --from=builder /build/google-chrome.deb /tmp/google-chrome.deb
COPY --from=builder /usr/local/bin/chromedriver /usr/local/bin/chromedriver

# 【核心】在最终镜像中，使用 apt 执行完整的安装过程
RUN apt-get update && \
    apt-get install -y --no-install-recommends /tmp/google-chrome.deb && \
    rm -f /tmp/google-chrome.deb && \
    rm -rf /var/lib/apt/lists/*

# 验证安装
RUN google-chrome --version && chromedriver --version

# --- Python 应用部分 ---

# 复制并安装依赖 (利用层缓存)
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制所有应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p data logs data/browser_login data/articles \
    && chmod -R 755 data logs && chmod +x entrypoint.sh

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
        PYTHONDONTWRITEBYTECODE=1
# 暴露端口
EXPOSE 8000

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 CMD curl -f http://localhost:8000/health || exit 1

# 使用相对路径指定 ENTRYPOINT，确保容器能找到启动脚本
ENTRYPOINT ["./entrypoint.sh"]