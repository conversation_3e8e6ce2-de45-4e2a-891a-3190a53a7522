#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文章相关API路由
"""

import os
import time
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import APIRouter, HTTPException, Query
from loguru import logger

from ..models.schemas import (
    ArticleContent,
    BatchDownloadRequest,
    BatchDownloadResponse,
    DownloadArticleRequest,
    DownloadArticleResponse,
    ErrorResponse,
    GetAllArticlesRequest,
    GetAllArticlesResponse,
    GetFirstPageArticlesRequest,
    GetFirstPageArticlesResponse,
    ListSavedArticlesResponse,
    SaveArticleHTMLRequest,
    SaveArticleHTMLResponse,
    SavedArticleFile,
    SearchAccountRequest,
    SearchAccountResponse,
    StatusCode,
    VideoInfo,
)
from ..services.file_service import FileService
from ..services.wechat_core import get_article_content
from ..utils.error_handler import handle_api_errors
from ..utils.time_filter import filter_articles_by_time, parse_time_filter
from .dependencies import require_login


# ==================== 配置常量 ====================
class ArticleAPIConfig:
    """文章API配置常量"""

    # 超时配置
    DEFAULT_TIMEOUT = 30
    DOWNLOAD_TIMEOUT = 30

    # 延时配置
    DOWNLOAD_DELAY = 2
    BATCH_DOWNLOAD_DELAY = 2

    # 分页配置
    DEFAULT_LIMIT = 5
    MAX_PAGES_LIMIT = 1000

    # 文件配置
    MAX_FILENAME_LENGTH = 100


# ==================== 辅助函数 ====================
async def _download_article_content(url: str, timeout: int = None) -> ArticleContent:
    """统一的文章下载逻辑

    Args:
        url: 文章链接
        timeout: 超时时间，默认使用配置值

    Returns:
        ArticleContent对象

    Raises:
        Exception: 下载失败时抛出异常
    """
    if timeout is None:
        timeout = ArticleAPIConfig.DOWNLOAD_TIMEOUT

    logger.info(f"下载文章内容（无cookies模式）: {url}")

    # 直接使用DrissionPage获取文章内容，不传递cookies，使用普通模式
    article_data = get_article_content(
        url=str(url),
        wait_timeout=timeout,
        cookies=None,    # 不使用cookies
        token=None,      # 不使用token
    )

    # 转换videos为VideoInfo对象列表
    video_objects = []
    for video_url in article_data.get('videos', []):
        video_objects.append(VideoInfo(url=video_url))

    return ArticleContent(
        title=article_data['title'],
        author=article_data['author'],
        publish_time=article_data['publish_time'],
        content=article_data['content'],
        url=str(url),
        videos=video_objects,
        images=article_data.get('images', []),
        biz=article_data.get('biz')
    )


def _create_error_response(status_code: int, message: str, error_code: StatusCode, details: Dict = None) -> HTTPException:
    """创建统一的错误响应

    Args:
        status_code: HTTP状态码
        message: 错误消息
        error_code: 错误代码
        details: 错误详情

    Returns:
        HTTPException对象
    """
    return HTTPException(
        status_code=status_code,
        detail=ErrorResponse(
            message=message,
            error_code=error_code,
            details=details or {}
        ).model_dump()
    )


async def _save_article_html(article_content: ArticleContent, filename: str = None) -> str:
    """保存文章为HTML文件

    Args:
        article_content: 文章内容对象
        filename: 自定义文件名

    Returns:
        保存的文件路径
    """
    file_service = FileService()
    saved_path = file_service.save_article_as_html(article_content, filename)
    logger.info(f"文章已保存为HTML文件: {saved_path}")
    return saved_path

# ==================== 路由定义 ====================
router = APIRouter(prefix="/articles", tags=["文章"])


# ==================== 公众号搜索相关API ====================

@router.post("/search-accounts", response_model=SearchAccountResponse, summary="搜索公众号")
@handle_api_errors("搜索公众号")
async def search_accounts(request: SearchAccountRequest):
    """搜索公众号

    - 根据关键词搜索公众号
    - 返回匹配的公众号列表
    """
    wechat_service = require_login()
    logger.info(f"搜索公众号: {request.keyword}")

    accounts = wechat_service.search_accounts(
        keyword=request.keyword,
        limit=request.limit,
        offset=request.offset
    )

    return SearchAccountResponse(
        success=True,
        message=f"找到 {len(accounts)} 个公众号",
        accounts=accounts,
        total=len(accounts)
    )

# ==================== 文章获取相关API ====================
@router.post("/get-first-page", response_model=GetFirstPageArticlesResponse, summary="获取公众号第一页文章")
@handle_api_errors("获取公众号第一页文章")
async def get_first_page_articles(request: GetFirstPageArticlesRequest):
    """获取公众号第一页文章

    - 通过公众号名称或fakeid获取第一页文章（通常是5篇最新文章）
    - account_name和fakeid二选一，优先使用account_name
    - 返回文章标题、链接、发布时间等基本信息
    """
    wechat_service = require_login()
    logger.info(f"获取公众号第一页文章: account_name={request.account_name}, fakeid={request.fakeid}")

    try:
        account_info, articles, actual_fakeid = wechat_service.get_first_page_articles(
            account_name=request.account_name,
            fakeid=request.fakeid
        )

        if not actual_fakeid:
            raise _create_error_response(
                status_code=404,
                message="未找到指定的公众号",
                error_code=StatusCode.ACCOUNT_NOT_FOUND,
                details={"account_name": request.account_name, "fakeid": request.fakeid}
            )

        return GetFirstPageArticlesResponse(
            success=True,
            message=f"成功获取第一页文章，共 {len(articles)} 篇",
            account_info=account_info,
            fakeid=actual_fakeid,
            articles=articles,
            total=len(articles)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取公众号第一页文章失败: {e}")
        raise _create_error_response(
            status_code=500,
            message=f"获取公众号第一页文章失败: {str(e)}",
            error_code=StatusCode.WECHAT_ERROR,
            details={"account_name": request.account_name, "fakeid": request.fakeid}
        )

@router.get("/get-first-page", response_model=GetFirstPageArticlesResponse, summary="获取公众号第一页文章（GET方式）")
@handle_api_errors("获取公众号第一页文章")
async def get_first_page_articles_get(
    account_name: Optional[str] = Query(None, description="公众号名称（与fakeid二选一）", example="奚晓乔"),
    fakeid: Optional[str] = Query(None, description="公众号fakeid（与account_name二选一）", example="MzU0MDk3NTUxMA==")
):
    """
    通过GET方式获取公众号第一页文章

    Args:
        account_name: 公众号名称（与fakeid二选一）
        fakeid: 公众号fakeid（与account_name二选一）
    """
    request = GetFirstPageArticlesRequest(account_name=account_name, fakeid=fakeid)
    return await get_first_page_articles(request)





# ==================== 文章下载相关API ====================
@router.post("/download", response_model=DownloadArticleResponse, summary="下载文章内容")
@handle_api_errors("下载文章")
async def download_article(request: DownloadArticleRequest):
    """下载文章内容（无cookies模式）

    - 支持通过文章标题或链接下载
    - 返回完整的文章内容HTML
    - 不需要登录状态，直接使用DrissionPage下载
    """
    article_content = await _handle_article_download(request)
    downloaded_articles = [article_content] if article_content else []

    # 保存HTML文件（如果需要）
    saved_file_path = None
    if request.save_html and article_content:
        # 如果custom_filename为空字符串，设置为None以使用文章标题
        final_filename = request.custom_filename if request.custom_filename and request.custom_filename.strip() else None
        saved_file_path = await _handle_html_saving(downloaded_articles, final_filename)

    # 准备响应
    return _create_download_response(article_content, downloaded_articles, saved_file_path)


async def _handle_article_download(request: DownloadArticleRequest) -> Optional[ArticleContent]:
    """处理文章下载逻辑

    Args:
        request: 下载请求对象

    Returns:
        下载的文章内容，失败时返回None
    """
    if request.url:
        try:
            return await _download_article_content(str(request.url))
        except Exception as e:
            logger.error(f"无cookies模式下载文章失败: {e}")
            raise _create_error_response(
                status_code=500,
                message=f"下载文章失败: {str(e)}",
                error_code=StatusCode.WECHAT_ERROR,
                details={"url": str(request.url), "mode": "no-cookies"}
            )

    elif request.title:
        # 通过标题下载暂时不支持无cookies模式（需要搜索功能）
        logger.warning("通过标题下载暂时不支持无cookies模式")
        raise _create_error_response(
            status_code=400,
            message="通过标题下载暂时不支持无cookies模式，请使用文章链接",
            error_code=StatusCode.INVALID_REQUEST,
            details={"title": request.title, "suggestion": "请提供文章URL"}
        )

    else:
        raise _create_error_response(
            status_code=404,
            message="未找到指定的文章",
            error_code=StatusCode.ARTICLE_NOT_FOUND,
            details={
                "title": request.title,
                "url": str(request.url) if request.url else None,
                "account_name": request.account_name
            }
        )


async def _handle_html_saving(downloaded_articles: List[ArticleContent], custom_filename: str = None) -> Optional[str]:
    """处理HTML文件保存逻辑

    Args:
        downloaded_articles: 下载的文章列表
        custom_filename: 自定义文件名

    Returns:
        保存的文件路径，失败时返回None
    """
    try:
        file_service = FileService()

        # 如果是多篇文章，保存所有文章
        if len(downloaded_articles) > 1:
            saved_paths = []
            for i, article in enumerate(downloaded_articles, 1):
                try:
                    # 为多篇文章生成不同的文件名
                    filename = f"{custom_filename}_{i}" if custom_filename else None
                    saved_path = file_service.save_article_as_html(article, filename)
                    saved_paths.append(saved_path)
                    logger.info(f"第 {i} 篇文章已保存: {saved_path}")
                except Exception as save_error:
                    logger.warning(f"保存第 {i} 篇文章失败: {save_error}")

            if saved_paths:
                if len(saved_paths) > 1:
                    logger.info(f"共保存 {len(saved_paths)} 篇文章")
                return saved_paths[0]

        # 单篇文章
        elif downloaded_articles:
            saved_path = file_service.save_article_as_html(
                downloaded_articles[0],
                custom_filename
            )
            logger.info(f"文章已保存为自包含HTML文件: {saved_path}")
            return saved_path

    except Exception as e:
        logger.warning(f"保存自包含HTML文件失败: {e}")

    return None


def _create_download_response(article_content: ArticleContent, downloaded_articles: List[ArticleContent],
                            saved_file_path: str = None) -> DownloadArticleResponse:
    """创建下载响应

    Args:
        article_content: 主要文章内容
        downloaded_articles: 下载的文章列表
        saved_file_path: 保存的文件路径

    Returns:
        下载响应对象
    """
    total_articles = len(downloaded_articles)
    articles_info = None

    # 如果下载了多篇文章，提供详细信息
    if downloaded_articles:
        articles_info = [
            {
                "title": article.title,
                "author": article.author or "未知",
                "publish_time": article.publish_time or "未知",
                "url": article.url or ""
            }
            for article in downloaded_articles
        ]

    message = "文章下载成功"
    if total_articles > 1:
        message += f"，共下载 {total_articles} 篇文章"
    if saved_file_path:
        message += "，已保存为自包含HTML格式"

    return DownloadArticleResponse(
        success=True,
        message=message,
        article=article_content,
        saved_file_path=saved_file_path,
        total_articles=total_articles,
        articles_info=articles_info
    )

@router.get("/download-by-url", response_model=DownloadArticleResponse, summary="通过URL下载文章（GET方式）")
@handle_api_errors("通过URL下载文章")
async def download_article_by_url(
    url: str = Query(..., description="文章链接", example=""),
    save_html: bool = Query(True, description="是否保存为自包含HTML文件"),
    custom_filename: Optional[str] = Query(None, description="自定义文件名（不含扩展名），为空时使用文章标题", example="")
):
    """通过URL下载文章（GET方式）

    Args:
        url: 文章链接
        save_html: 是否保存为自包含HTML文件
        custom_filename: 自定义文件名（不含扩展名），为空时使用文章标题

    注意：此接口不需要登录状态，直接使用DrissionPage下载
    """
    # 如果custom_filename为空字符串，设置为None以使用文章标题
    final_filename = custom_filename if custom_filename and custom_filename.strip() else None

    request = DownloadArticleRequest(
        url=url,
        save_html=save_html,
        custom_filename=final_filename
    )
    return await download_article(request)



@router.get("/download-by-title", response_model=DownloadArticleResponse, summary="通过标题下载文章（GET方式）")
@handle_api_errors("通过标题下载文章")
async def download_article_by_title(
    title: str,
    account_name: str = None,
    save_html: bool = True,
    custom_filename: str = None
):
    """
    通过GET方式使用标题下载文章内容

    Args:
        title: 文章标题
        account_name: 公众号名称（可选）
        save_html: 是否保存为HTML文件
        custom_filename: 自定义文件名（可选，为空时使用文章标题）
    """
    # 如果custom_filename为空字符串，设置为None以使用文章标题
    final_filename = custom_filename if custom_filename and custom_filename.strip() else None

    request = DownloadArticleRequest(
        title=title,
        account_name=account_name,
        save_html=save_html,
        custom_filename=final_filename
    )
    return await download_article(request)

@router.get("/saved-files", response_model=ListSavedArticlesResponse, summary="获取已保存的文章文件列表")
@handle_api_errors("获取已保存文章列表")
async def list_saved_articles():
    """
    获取已保存的自包含HTML文章文件列表
    """
    file_service = FileService()
    files_data = file_service.get_saved_articles()

    files = [SavedArticleFile(**file_data) for file_data in files_data]

    return ListSavedArticlesResponse(
        success=True,
        message=f"找到 {len(files)} 个已保存的文章文件",
        files=files,
        total=len(files)
    )

@router.delete("/saved-files/{filename}", summary="删除已保存的文章文件")
@handle_api_errors("删除文章文件")
async def delete_saved_article(
    filename: str
):
    """
    删除指定的已保存文章文件
    """
    file_service = FileService()
    success = file_service.delete_article_file(filename)

    if success:
        return {
            "success": True,
            "message": f"文件 {filename} 删除成功"
        }
    else:
        raise HTTPException(
            status_code=404,
            detail=ErrorResponse(
                message=f"文件 {filename} 不存在或删除失败",
                error_code=StatusCode.FILE_NOT_FOUND
            ).model_dump()
        )

# ==================== 文章保存相关API ====================
@router.post("/save", response_model=SaveArticleHTMLResponse, summary="保存文章为自包含HTML格式（无cookies模式）", include_in_schema=False)
@handle_api_errors("保存文章")
async def save_article_as_html(request: SaveArticleHTMLRequest):
    """保存文章到本地自包含HTML文件（无cookies模式）

    - 使用自包含HTML格式，完美的浏览器兼容性
    - 图片Base64嵌入，完全离线可用
    - 支持通过文章链接保存（标题搜索暂不支持无cookies模式）
    - 可自定义文件名
    - 不需要登录状态，直接使用DrissionPage下载
    """
    # 下载文章内容
    article_content = await _handle_save_article_download(request)

    # 保存为HTML文件
    saved_path, file_size = await _handle_save_article_to_file(article_content, request.filename)

    logger.info(f"文章保存成功: {saved_path} (自包含HTML格式, {file_size} bytes)")

    return SaveArticleHTMLResponse(
        success=True,
        message="文章已成功保存为自包含HTML格式",
        file_path=saved_path,
        file_size=file_size
    )


async def _handle_save_article_download(request: SaveArticleHTMLRequest) -> ArticleContent:
    """处理保存文章的下载逻辑"""
    if request.url:
        try:
            return await _download_article_content(str(request.url))
        except Exception as e:
            logger.error(f"无cookies模式下载文章失败: {e}")
            raise _create_error_response(
                status_code=500,
                message=f"下载文章失败: {str(e)}",
                error_code=StatusCode.WECHAT_ERROR,
                details={"url": str(request.url), "mode": "no-cookies"}
            )

    elif request.title:
        # 通过标题下载暂时不支持无cookies模式（需要搜索功能）
        logger.warning("通过标题下载暂时不支持无cookies模式")
        raise _create_error_response(
            status_code=400,
            message="通过标题下载暂时不支持无cookies模式，请使用文章链接",
            error_code=StatusCode.INVALID_REQUEST,
            details={"title": request.title, "suggestion": "请提供文章URL"}
        )

    else:
        raise _create_error_response(
            status_code=404,
            message="未找到指定的文章",
            error_code=StatusCode.ARTICLE_NOT_FOUND,
            details={"url": request.url, "title": request.title}
        )


async def _handle_save_article_to_file(article_content: ArticleContent, filename: str = None) -> tuple:
    """处理文章保存到文件的逻辑"""
    file_service = FileService()

    # 保存为自包含HTML格式
    saved_path = file_service.save_article_as_html(
        article=article_content,
        filename=filename
    )

    # 获取文件大小
    file_size = os.path.getsize(saved_path)

    return saved_path, file_size

@router.post("/get-all-articles", response_model=GetAllArticlesResponse, summary="获取公众号全部文章列表")
@handle_api_errors("获取公众号全部文章列表")
async def get_all_articles_by_fakeid(
    request: GetAllArticlesRequest
):
    """
    根据 account_name 或 faker_id 获取公众号的全部文章列表

    - 通过 account_name 或 faker_id 获取指定公众号的所有历史文章
    - account_name 和 faker_id 二选一，优先使用 account_name
    - max_pages 不指定时自动获取所有文章，指定时作为页数限制
    - 程序会在没有更多文章时自动停止，无需担心设置过大的页数
    - 返回文章标题、链接、发布时间等基本信息
    """
    # 获取微信服务并检查登录状态
    wechat_service = require_login()

    logger.info(f"获取公众号全部文章: account_name={request.account_name}, faker_id={request.faker_id}, max_pages={request.max_pages}")

    try:
        # 1. 确定公众号信息和fakeid
        account_info = None
        actual_fakeid = request.faker_id

        if request.account_name and request.account_name.strip():
            # 通过公众号名称查找
            logger.info(f"通过公众号名称查找: {request.account_name}")
            accounts = wechat_service.search_accounts(request.account_name.strip(), limit=10)

            # 精确匹配公众号名称
            matched_account = None
            for account in accounts:
                if account.nickname == request.account_name.strip():
                    matched_account = account
                    break

            if not matched_account:
                raise HTTPException(
                    status_code=404,
                    detail=ErrorResponse(
                        message=f"未找到公众号: {request.account_name}",
                        error_code=StatusCode.ACCOUNT_NOT_FOUND,
                        details={"account_name": request.account_name}
                    ).model_dump()
                )

            account_info = matched_account
            actual_fakeid = matched_account.fakeid
            logger.info(f"找到公众号: {matched_account.nickname} (fakeid: {actual_fakeid})")

        elif request.faker_id and request.faker_id.strip():
            # 直接使用提供的fakeid
            actual_fakeid = request.faker_id.strip()
            logger.info(f"使用提供的fakeid: {actual_fakeid}")

        else:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    message="必须提供有效的account_name或faker_id",
                    error_code=StatusCode.INVALID_REQUEST
                ).model_dump()
            )

        # 2. 获取文章列表
        articles = wechat_service.get_mp_articles_by_fakeid(
            faker_id=actual_fakeid,
            max_pages=request.max_pages
        )

        # 计算实际获取的页数
        calculated_pages = (len(articles) + 4) // 5  # 每页5篇文章
        if request.max_pages is None:
            pages_fetched = calculated_pages
        else:
            pages_fetched = min(request.max_pages, calculated_pages)

        return GetAllArticlesResponse(
            success=True,
            message=f"成功获取 {len(articles)} 篇文章",
            account_info=account_info,
            faker_id=actual_fakeid,
            articles=articles,
            total=len(articles),
            pages_fetched=pages_fetched
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取公众号全部文章失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                message=f"获取公众号文章失败: {str(e)}",
                error_code=StatusCode.WECHAT_ERROR,
                details={"account_name": request.account_name, "faker_id": request.faker_id, "max_pages": request.max_pages}
            ).model_dump()
        )
@router.get("/get-all-articles", response_model=GetAllArticlesResponse, summary="获取公众号全部文章列表（GET方式）")
@handle_api_errors("获取公众号全部文章列表")
async def get_all_articles_by_query_params(
    account_name: Optional[str] = Query(None, description="公众号名称（与faker_id二选一）", example="奚晓乔"),
    faker_id: Optional[str] = Query(None, description="公众号faker_id（与account_name二选一）", example="MzU0MDk3NTUxMA=="),
    max_pages: Optional[int] = Query(None, ge=1, le=1000, description="最大页数限制，不指定则获取所有文章")
):
    """
    通过GET方式根据查询参数获取公众号的全部文章列表

    Args:
        account_name: 公众号名称（与faker_id二选一）
        faker_id: 公众号的 faker_id（与account_name二选一）
        max_pages: 最大页数限制（1-1000），不指定则自动获取所有文章
    """
    request = GetAllArticlesRequest(account_name=account_name, faker_id=faker_id, max_pages=max_pages)
    return await get_all_articles_by_fakeid(request)

@router.post("/batch-download-by-account", response_model=BatchDownloadResponse, summary="批量下载公众号文章")
@handle_api_errors("批量下载公众号文章")
async def batch_download_by_account(
    request: BatchDownloadRequest
):
    """
    根据公众号名称或fakeid批量下载文章，支持时间范围过滤

    请求参数：
    - account_name: 公众号名称（与fakeid二选一）
    - fakeid: 公众号fakeid（与account_name二选一，如：MzU0MDk3NTUxMA==）
    - time_filter: 时间过滤器（可选）
    - save_html: 是否保存为HTML文件（默认true）

    时间过滤器格式：
    月份级别：
    - '2025': 下载2025年全年文章
    - '2025/06': 下载2025年6月文章
    - '2025/06,2025/08': 下载2025年6月和8月文章
    - '2025/06-2025/08': 下载2025年6月到8月文章
    - '2025/06/15': 下载2025年6月15日文章
    - '2025/06/01-2025/06/27': 下载2025年6月1日到27日文章
    - '2025/06/01,2025/08/15': 下载指定日期文章
    - '2025/06/01-2025/06/15,2025/08': 混合日期和月份过滤
    """
    # 获取微信服务并检查登录状态
    wechat_service = require_login()

    # 1. 获取fakeid和公众号信息
    account_info = None
    if request.fakeid and request.fakeid.strip():
        fakeid = request.fakeid.strip()
        logger.info(f"使用提供的fakeid: {fakeid}")
    elif request.account_name and request.account_name.strip():
        account_name = request.account_name.strip()
        logger.info(f"搜索公众号: {account_name}")
        accounts = wechat_service.search_accounts(account_name, limit=1)
        if not accounts:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    message=f"未找到公众号: {account_name}",
                    error_code=StatusCode.ACCOUNT_NOT_FOUND
                ).model_dump()
            )
        account_info = accounts[0]
        fakeid = account_info.fakeid
        logger.info(f"找到公众号: {account_info.nickname} (fakeid: {fakeid})")
    else:
        raise HTTPException(
            status_code=400,
            detail=ErrorResponse(
                message="必须提供有效的account_name或fakeid",
                error_code=StatusCode.INVALID_REQUEST
            ).model_dump()
        )

    # 2. 获取文章列表（优化：在获取过程中就进行时间过滤）
    logger.info(f"开始获取公众号文章...")
    if request.time_filter:
        logger.info(f"将在获取过程中应用时间过滤器: {request.time_filter}")

    try:
        # 优化：直接在获取过程中进行时间过滤，避免获取不需要的文章
        filtered_articles = wechat_service.get_mp_articles_by_fakeid(
            faker_id=fakeid,
            max_pages=request.max_pages,
            time_filter=request.time_filter  # 传递时间过滤器
        )

        if request.time_filter:
            logger.info(f"获取并过滤后得到 {len(filtered_articles)} 篇文章")
        else:
            logger.info(f"获取到 {len(filtered_articles)} 篇文章")

    except Exception as e:
        logger.error(f"获取文章列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                message=f"获取文章列表失败: {str(e)}",
                error_code=StatusCode.WECHAT_ERROR,
                details={"fakeid": fakeid, "time_filter": request.time_filter}
            ).model_dump()
        )

    if not filtered_articles:
        return BatchDownloadResponse(
            success=True,
            message="没有符合条件的文章",
            account_info=account_info,
            fakeid=fakeid,
            total_articles=len(filtered_articles),
            filtered_articles=0,
            downloaded_articles=0,
            failed_articles=0,
            time_filter_applied=request.time_filter
        )

    # 4. 批量下载文章（每篇文章独立打开Chrome）
    logger.info(f"开始批量下载 {len(filtered_articles)} 篇文章...")
    downloaded_count = 0
    failed_count = 0
    saved_files = []
    download_summary = []

    file_service = FileService() if request.save_html else None

    for i, article in enumerate(filtered_articles, 1):
        article_title = article.get('title', '未知标题')
        article_link = article.get('link', '')
        logger.info(f"下载第 {i}/{len(filtered_articles)} 篇文章: {article_title[:50]}...")
        try:
            if not article_link:
                raise Exception("文章链接为空")
            # 下载文章内容（无cookies模式，每篇文章独立打开Chrome）
            article_data = get_article_content(
                url=article_link,
                wait_timeout=30,
                cookies=None,    # 不使用cookies
                token=None,      # 不使用token
            )

            # 转换为ArticleContent对象
            video_objects = []
            for video_url in article_data.get('videos', []):
                video_objects.append(VideoInfo(url=video_url))

            article_content = ArticleContent(
                title=article_data['title'],
                author=article_data['author'],
                publish_time=article_data['publish_time'],
                content=article_data['content'],
                url=article_link,
                videos=video_objects,
                images=article_data.get('images', []),
                biz=article_data.get('biz')
            )

            # 保存为HTML文件
            saved_path = None
            if request.save_html and file_service:
                saved_path = file_service.save_article_as_html(
                    article=article_content,
                    filename=None  # 使用默认文件名
                )
                saved_files.append(saved_path)
                logger.info(f"文章已保存: {saved_path}")

            downloaded_count += 1
            download_summary.append({
                "title": article_title,
                "url": article_link,
                "status": "success",
                "saved_path": saved_path,
                "create_time": article.get('create_time_formatted', 'N/A')
            })

        except Exception as e:
            failed_count += 1
            error_msg = str(e)
            logger.error(f"下载文章失败: {article_title} - {error_msg}")

            download_summary.append({
                "title": article_title,
                "url": article_link,
                "status": "failed",
                "error": error_msg,
                "create_time": article.get('create_time_formatted', 'N/A')
            })

        # 添加延时避免被限制
        if i < len(filtered_articles):
            time.sleep(2)  # 每篇文章间隔2秒

    # 5. 返回结果
    success_rate = (downloaded_count / len(filtered_articles)) * 100 if filtered_articles else 0
    message = f"批量下载完成，成功 {downloaded_count} 篇，失败 {failed_count} 篇，成功率 {success_rate:.1f}%"

    logger.info(message)

    return BatchDownloadResponse(
        success=True,
        message=message,
        account_info=account_info,
        fakeid=fakeid,
        total_articles=len(filtered_articles),  # 修正：使用filtered_articles
        filtered_articles=len(filtered_articles),
        downloaded_articles=downloaded_count,
        failed_articles=failed_count,
        saved_files=saved_files,
        download_summary=download_summary,
        time_filter_applied=request.time_filter
    )