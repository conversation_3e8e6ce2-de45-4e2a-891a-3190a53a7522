#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证相关API路由
"""

from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends
from loguru import logger
from ..models.schemas import (
    LoginRequest, LoginResponse, SessionStatusResponse,
    ErrorResponse, StatusCode
)
from ..services.wechat_manager import WeChatTokenManager
from ..services.telegram_service import TelegramService
from .dependencies import get_token_manager, get_telegram_service

router = APIRouter(prefix="/auth", tags=["认证"])

@router.post("/login", response_model=LoginResponse, summary="微信公众平台登录")
async def login(
    request: LoginRequest,
    token_manager: WeChatTokenManager = Depends(get_token_manager),
    telegram_service: TelegramService = Depends(get_telegram_service)
):
    """
    启动微信公众平台登录流程
    
    - 生成二维码并通过Telegram发送
    - 等待用户扫码登录
    - 返回登录结果和会话信息
    """
    try:
        logger.info(f"开始登录流程，等待时间: {request.wait_time}秒")

        # 检查是否已经登录
        if token_manager.is_session_valid():
            session_data = token_manager.get_current_session()
            expiry_info = session_data.get('expiry') if session_data else None

            return LoginResponse(
                success=True,
                message="已经登录，无需重复登录",
                data=session_data,
                token=session_data.get('token') if session_data else None,
                expires_at=datetime.fromtimestamp(expiry_info['expiry_timestamp']) if expiry_info else None
            )

        # 启动登录会话
        session_data = token_manager.start_session(
            wait_time=request.wait_time
        )

        if session_data:
            expiry_info = session_data.get('expiry')
            return LoginResponse(
                success=True,
                message="登录成功",
                data=session_data,
                token=session_data.get('token'),
                expires_at=datetime.fromtimestamp(expiry_info['expiry_timestamp']) if expiry_info else None
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    message="登录失败",
                    error_code=StatusCode.WECHAT_ERROR,
                    details={"reason": "扫码超时或登录过程中出现错误"}
                ).model_dump()
            )

    except Exception as e:
        logger.error(f"登录失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                message=f"登录失败: {str(e)}",
                error_code=StatusCode.INTERNAL_ERROR
            ).model_dump()
        )

@router.get("/status", response_model=SessionStatusResponse, summary="获取登录状态")
async def get_login_status(
    token_manager: WeChatTokenManager = Depends(get_token_manager)
):
    """
    获取当前登录状态和会话信息
    """
    try:
        is_logged_in = token_manager.is_session_valid()
        session_data = token_manager.get_current_session()

        if is_logged_in and session_data:
            expiry_info = session_data.get('expiry')
            return SessionStatusResponse(
                success=True,
                message="会话有效",
                is_logged_in=True,
                token=session_data.get('token'),
                expires_at=datetime.fromtimestamp(expiry_info['expiry_timestamp']) if expiry_info else None,
                remaining_seconds=expiry_info.get('remaining_seconds') if expiry_info else None
            )
        else:
            return SessionStatusResponse(
                success=True,
                message="未登录或会话已过期",
                is_logged_in=False
            )

    except Exception as e:
        logger.error(f"获取登录状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                message=f"获取状态失败: {str(e)}",
                error_code=StatusCode.INTERNAL_ERROR
            ).model_dump()
        )

@router.post("/refresh", response_model=SessionStatusResponse, summary="刷新会话")
async def refresh_session(
    token_manager: WeChatTokenManager = Depends(get_token_manager)
):
    """
    手动刷新当前会话
    """
    try:
        if not token_manager.is_session_valid():
            raise HTTPException(
                status_code=401,
                detail=ErrorResponse(
                    message="未登录或会话已过期",
                    error_code=StatusCode.LOGIN_REQUIRED
                ).model_dump()
            )

        success = token_manager.refresh_session()
        if success:
            session_data = token_manager.get_current_session()
            expiry_info = session_data.get('expiry') if session_data else None

            return SessionStatusResponse(
                success=True,
                message="会话刷新成功",
                is_logged_in=True,
                token=session_data.get('token') if session_data else None,
                expires_at=datetime.fromtimestamp(expiry_info['expiry_timestamp']) if expiry_info else None,
                remaining_seconds=expiry_info.get('remaining_seconds') if expiry_info else None
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    message="会话刷新失败",
                    error_code=StatusCode.WECHAT_ERROR
                ).model_dump()
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新会话失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                message=f"刷新失败: {str(e)}",
                error_code=StatusCode.INTERNAL_ERROR
            ).model_dump()
        )

@router.post("/logout", summary="登出")
async def logout(
    token_manager: WeChatTokenManager = Depends(get_token_manager)
):
    """
    登出并清理会话
    """
    try:
        token_manager.close()
        return {
            "success": True,
            "message": "登出成功",
            "timestamp": datetime.now()
        }

    except Exception as e:
        logger.error(f"登出失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                message=f"登出失败: {str(e)}",
                error_code=StatusCode.INTERNAL_ERROR
            ).model_dump()
        )
