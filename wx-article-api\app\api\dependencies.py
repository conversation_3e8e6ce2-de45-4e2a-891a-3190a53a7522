#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API依赖注入
"""

from functools import lru_cache
from fastapi import HTTPEx<PERSON>, Header, Query, Request
from typing import Optional
from loguru import logger
from ..services.wechat_manager import WeChatTokenManager
from ..services.telegram_service import TelegramService
from ..services.wechat_service import WeChatService
from ..models.schemas import ErrorResponse, StatusCode
from ..core.config import get_settings

# 全局实例
_token_manager = None
_telegram_service = None
_wechat_service = None

@lru_cache()
def get_settings_cached():
    """获取缓存的设置"""
    return get_settings()

def get_telegram_service() -> TelegramService:
    """获取Telegram服务实例"""
    global _telegram_service
    if _telegram_service is None:
        settings = get_settings_cached()
        _telegram_service = TelegramService(
            bot_token=settings.telegram.bot_token,
            chat_id=settings.telegram.chat_id
        )
    return _telegram_service

def get_token_manager() -> WeChatTokenManager:
    """获取微信Token管理器实例"""
    global _token_manager
    if _token_manager is None:
        telegram_service = get_telegram_service()
        _token_manager = WeChatTokenManager(telegram_service)

        # 应用启动时自动尝试加载已保存的会话数据
        # 这样可以在API服务重启后自动恢复登录状态
        try:
            saved_session = _token_manager.session_manager.load_session()
            if saved_session:
                logger.info("应用启动时成功加载已保存的会话数据")
                # 验证会话数据是否有效
                if _token_manager._validate_session_data(saved_session):
                    # 恢复会话状态
                    _token_manager.session_data = saved_session
                    _token_manager.is_logged_in = True

                    # 如果配置了自动刷新，启动定时刷新
                    settings = get_settings()
                    if (settings.wechat.token.auto_refresh and
                        settings.wechat.token.refresh_interval > 0):
                        _token_manager.start_refresh_schedule()
                        logger.info(f"已启动会话自动刷新，间隔: {settings.wechat.token.refresh_interval}秒")
                else:
                    logger.warning("应用启动时发现会话数据已过期")
            else:
                logger.debug("应用启动时未找到有效的已保存会话")
        except Exception as e:
            logger.warning(f"应用启动时加载会话数据失败: {e}")
    return _token_manager

def get_wechat_service() -> WeChatService:
    """获取微信服务实例"""
    global _wechat_service
    if _wechat_service is None:
        token_manager = get_token_manager()
        _wechat_service = WeChatService(token_manager)
    return _wechat_service

def require_login() -> WeChatService:
    """要求登录的依赖"""
    wechat_service = get_wechat_service()
    token_manager = wechat_service.token_manager
    if not token_manager.is_session_valid():
        raise HTTPException(
            status_code=401,
            detail=ErrorResponse(
                message="未登录或会话已过期，请先登录",
                error_code=StatusCode.LOGIN_REQUIRED
            ).model_dump()
        )

    return wechat_service



def cleanup_services():
    """清理服务实例"""
    global _token_manager, _telegram_service, _wechat_service

    if _token_manager:
        _token_manager.close()
        _token_manager = None

    _telegram_service = None
    _wechat_service = None

    logger.info("服务实例已清理")
