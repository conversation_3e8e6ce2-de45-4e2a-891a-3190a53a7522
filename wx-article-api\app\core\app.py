#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import json
from datetime import datetime
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from loguru import logger
from .config import get_settings
from ..models.schemas import HealthCheckResponse, ErrorResponse, StatusCode
from ..api import auth, articles
from ..api.dependencies import cleanup_services

class CustomJSONResponse(JSONResponse):

    def render(self, content) -> bytes:
        return json.dumps(
            content,
            ensure_ascii=False,
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
            default=self._json_serializer
        ).encode("utf-8")

    def _json_serializer(self, obj):
        """自定义JSON序列化器"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")


# 应用启动时间
start_time = time.time()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时
    logger.info("微信公众号文章查询API启动")
    yield
    # 关闭时
    logger.info("微信公众号文章查询API关闭")
    cleanup_services()

def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    settings = get_settings()



    app = FastAPI(
        title="微信公众号文章查询API",
        description="基于微信公众平台的文章查询和下载API服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )

    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.security.allowed_origins,
        allow_credentials=True,
        allow_methods=settings.security.allowed_methods,
        allow_headers=settings.security.allowed_headers,
    )



    # 添加请求日志中间件
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        start_time = time.time()

        # 记录请求
        logger.info(f"请求开始: {request.method} {request.url}")

        try:
            response = await call_next(request)
            process_time = time.time() - start_time

            # 记录响应
            logger.info(
                f"请求完成: {request.method} {request.url} - "
                f"状态码: {response.status_code} - "
                f"耗时: {process_time:.3f}s"
            )

            # 添加响应头
            response.headers["X-Process-Time"] = str(process_time)
            return response

        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                f"请求失败: {request.method} {request.url} - "
                f"错误: {str(e)} - "
                f"耗时: {process_time:.3f}s"
            )
            raise

    # 全局异常处理
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理"""
        return CustomJSONResponse(
            status_code=exc.status_code,
            content=exc.detail if isinstance(exc.detail, dict) else {
                "success": False,
                "message": str(exc.detail),
                "timestamp": datetime.now().isoformat()
            }
        )

    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """通用异常处理"""
        logger.error(f"未处理的异常: {str(exc)}", exc_info=True)

        error_response = ErrorResponse(
            message="服务器内部错误",
            error_code=StatusCode.INTERNAL_ERROR,
            details={"error": str(exc)} if settings.server.debug else None
        )

        return CustomJSONResponse(
            status_code=500,
            content=error_response.model_dump()
        )

    # 健康检查端点
    @app.get("/health", response_model=HealthCheckResponse, tags=["系统"])
    async def health_check():
        """健康检查"""
        uptime_seconds = int(time.time() - start_time)
        uptime_hours = uptime_seconds // 3600
        uptime_minutes = (uptime_seconds % 3600) // 60
        uptime_str = f"{uptime_hours}h {uptime_minutes}m {uptime_seconds % 60}s"

        return HealthCheckResponse(
            success=True,
            message="服务运行正常",
            version="1.0.0",
            uptime=uptime_str,
            status="healthy"
        )

    @app.get("/", tags=["系统"])
    async def root():
        """根路径"""
        return {
            "message": "微信公众号文章查询API",
            "version": "1.0.0",
            "docs": "/docs",
            "health": "/health"
        }

    # 注册路由
    app.include_router(auth.router, prefix="/api/v1")
    app.include_router(articles.router, prefix="/api/v1")

    logger.info("FastAPI应用创建完成")
    return app
