#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
import yaml
from pathlib import Path
from typing import List, Optional, Union
from pydantic import BaseModel
from functools import lru_cache
from dotenv import load_dotenv

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

class ServerConfig(BaseModel):
    """服务器配置"""
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    reload: bool = False

class WeChatLoginConfig(BaseModel):
    """微信登录配置"""
    wait_time: int = 120
    qrcode_path: str = "data/wx_qrcode.png"

class WeChatTokenConfig(BaseModel):
    """微信Token配置"""
    refresh_interval: int = 3600
    auto_refresh: bool = True
    auto_relogin_on_failure: bool = True  # 刷新失败时自动重新登录

class WeChatBrowserConfig(BaseModel):
    """浏览器配置"""
    timeout: int = 30
    implicit_wait: int = 10

class WeChatImageRecognitionConfig(BaseModel):
    """图像识别配置"""
    worker_url: str
    authorization: str
    custom_prompt: str
    timeout: int

class WeChatConfig(BaseModel):
    """微信配置"""
    login: WeChatLoginConfig = WeChatLoginConfig()
    token: WeChatTokenConfig = WeChatTokenConfig()
    browser: WeChatBrowserConfig = WeChatBrowserConfig()
    image_recognition: Optional[WeChatImageRecognitionConfig] = None

class TelegramConfig(BaseModel):
    """Telegram配置"""
    enabled: bool = True
    bot_token: str = ""
    chat_id: str = ""

class CacheConfig(BaseModel):
    """缓存配置"""
    enabled: bool = True
    expire_hours: int = 24
    max_size: int = 1000

class StorageConfig(BaseModel):
    """存储配置"""
    session_file: str = "data/session.json"
    cache: CacheConfig = CacheConfig()

class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = "INFO"
    file: str = "logs/app.log"
    max_size: str = "10MB"
    backup_count: int = 5
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

class RateLimitConfig(BaseModel):
    """请求限制配置"""
    enabled: bool = True
    requests_per_minute: int = 60

class ResponseConfig(BaseModel):
    """响应配置"""
    timeout: int = 30
    max_articles: int = 20

class APIConfig(BaseModel):
    """API配置"""
    rate_limit: RateLimitConfig = RateLimitConfig()
    response: ResponseConfig = ResponseConfig()

class SecurityConfig(BaseModel):
    """安全配置"""
    allowed_origins: List[str] = ["*"]
    allowed_methods: List[str] = ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: List[str] = ["*"]

class Settings(BaseModel):
    """应用设置"""
    server: ServerConfig = ServerConfig()
    wechat: WeChatConfig = WeChatConfig()
    telegram: TelegramConfig = TelegramConfig()
    storage: StorageConfig = StorageConfig()
    logging: LoggingConfig = LoggingConfig()
    api: APIConfig = APIConfig()
    security: SecurityConfig = SecurityConfig()

    # 环境变量
    telegram_bot_token: Optional[str] = None
    telegram_chat_id: Optional[str] = None
    environment: str = "development"
    debug: bool = False
    log_level: str = "INFO"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._load_config_file()
        self._load_env_vars()

    def _load_config_file(self):
        """加载YAML配置文件"""
        config_file = PROJECT_ROOT / "config" / "config.yaml"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            # 更新配置
            if config_data:
                self._update_nested_config(config_data)

    def _update_nested_config(self, config_data: dict, target_obj=None):
        """递归更新嵌套配置"""
        if target_obj is None:
            target_obj = self

        for key, value in config_data.items():
            if hasattr(target_obj, key):
                current_attr = getattr(target_obj, key)
                if isinstance(current_attr, BaseModel):
                    if isinstance(value, dict):
                        # 递归处理嵌套的BaseModel
                        self._update_nested_config(value, current_attr)
                    else:
                        # 直接设置值
                        setattr(target_obj, key, value)
                elif current_attr is None and isinstance(value, dict):
                    # 处理Optional[BaseModel]的情况，需要创建实例
                    field_type = target_obj.__annotations__.get(key)
                    if hasattr(field_type, '__origin__') and field_type.__origin__ is Union:
                        # 获取Optional类型的实际类型
                        actual_type = field_type.__args__[0]
                        if issubclass(actual_type, BaseModel):
                            # 创建BaseModel实例
                            new_instance = actual_type(**value)
                            setattr(target_obj, key, new_instance)
                else:
                    # 非BaseModel属性直接设置
                    setattr(target_obj, key, value)

    def _load_env_vars(self):
        """加载环境变量"""
        # 加载.env文件
        env_file = PROJECT_ROOT / ".env"
        if env_file.exists():
            load_dotenv(env_file)

        # 读取环境变量
        self.telegram_bot_token = os.getenv('TELEGRAM_BOT_TOKEN', self.telegram_bot_token)
        self.telegram_chat_id = os.getenv('TELEGRAM_CHAT_ID', self.telegram_chat_id)
        self.environment = os.getenv('ENVIRONMENT', self.environment)
        self.debug = os.getenv('DEBUG', str(self.debug)).lower() in ('true', '1', 'yes')
        self.log_level = os.getenv('LOG_LEVEL', self.log_level)

        # 使用环境变量覆盖配置
        if self.telegram_bot_token:
            self.telegram.bot_token = self.telegram_bot_token
        if self.telegram_chat_id:
            self.telegram.chat_id = self.telegram_chat_id
        if self.debug:
            self.server.debug = self.debug
        if self.log_level:
            self.logging.level = self.log_level

    def get_absolute_path(self, relative_path: str) -> Path:
        """获取相对于项目根目录的绝对路径"""
        return PROJECT_ROOT / relative_path

@lru_cache()
def get_settings() -> Settings:
    """获取应用设置（单例模式）"""
    return Settings()
