#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API数据模型定义
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, ConfigDict, Field
try:
    from pydantic import HttpUrl
except ImportError:
    HttpUrl = str

class BaseResponse(BaseModel):
    """基础响应模型"""
    model_config = ConfigDict(
        # 启用JSON序列化时的datetime格式化
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )

    success: bool
    message: str
    timestamp: datetime = None

    def __init__(self, **data):
        if 'timestamp' not in data:
            data['timestamp'] = datetime.now()
        super().__init__(**data)

class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = False
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

class LoginRequest(BaseModel):
    """登录请求模型"""
    wait_time: Optional[int] = 120
    auto_refresh: Optional[bool] = True
    refresh_interval: Optional[int] = 3600

class LoginResponse(BaseResponse):
    """登录响应模型"""
    data: Optional[Dict[str, Any]] = None
    token: Optional[str] = None
    expires_at: Optional[datetime] = None

class SessionStatusResponse(BaseResponse):
    """会话状态响应模型"""
    is_logged_in: bool
    token: Optional[str] = None
    expires_at: Optional[datetime] = None
    remaining_seconds: Optional[int] = None

class AccountInfo(BaseModel):
    """公众号信息模型"""
    fakeid: str
    nickname: str
    alias: Optional[str] = None
    round_head_img: Optional[str] = None
    service_type: Optional[int] = None

class SearchAccountRequest(BaseModel):
    """搜索公众号请求模型"""
    keyword: str
    limit: Optional[int] = 5
    offset: Optional[int] = 0

class SearchAccountResponse(BaseResponse):
    """搜索公众号响应模型"""
    accounts: List[AccountInfo] = []
    total: int = 0

class ArticleInfo(BaseModel):
    """文章信息模型"""
    title: str
    digest: Optional[str] = None
    author: Optional[str] = None
    link: Optional[str] = None
    cover: Optional[str] = None
    create_time: Optional[int] = None
    update_time: Optional[int] = None
    publish_time: Optional[str] = None

class VideoInfo(BaseModel):
    """视频信息模型"""
    url: str
    cover: Optional[str] = None
    nickname: Optional[str] = None
    desc: Optional[str] = None
    width: Optional[str] = None
    height: Optional[str] = None

class ArticleContent(BaseModel):
    """文章内容模型"""
    title: str
    author: Optional[str] = None
    publish_time: Optional[str] = None
    content: str
    images: List[str] = []
    videos: List[VideoInfo] = []
    url: str
    biz: Optional[str] = None

class DownloadArticleRequest(BaseModel):
    """下载文章请求模型"""
    title: Optional[str] = None
    url: Optional[str] = None  # 改为str类型
    account_name: Optional[str] = None
    save_html: bool = True  # 是否保存为自包含HTML文件（保持字段名兼容性）
    custom_filename: Optional[str] = None  # 自定义文件名（默认使用文章标题）

    def __init__(self, **data):
        super().__init__(**data)
        # 验证至少提供一个查询条件
        if not self.title and not self.url:
            raise ValueError("必须提供文章标题或文章链接")

class SaveArticleHTMLRequest(BaseModel):
    """保存文章为HTML请求模型"""
    title: Optional[str] = None
    url: Optional[str] = None
    account_name: Optional[str] = None
    custom_filename: Optional[str] = None

    def __init__(self, **data):
        super().__init__(**data)
        # 验证至少提供一个查询条件
        if not self.title and not self.url:
            raise ValueError("必须提供文章标题或文章链接")

class DownloadArticleResponse(BaseResponse):
    """下载文章响应模型"""
    article: Optional[ArticleContent] = None
    saved_file_path: Optional[str] = None  # 保存的自包含HTML文件路径
    total_articles: int = 1  # 总共下载的文章数量
    articles_info: Optional[List[Dict[str, str]]] = None  # 所有下载文章的基本信息

class SavedArticleFile(BaseModel):
    """已保存的文章文件信息"""
    filename: str
    path: str
    size: int
    created_time: str
    modified_time: str

class ListSavedArticlesResponse(BaseResponse):
    """获取已保存文章列表响应模型"""
    files: List[SavedArticleFile] = []
    total: int = 0

class SaveArticleHTMLResponse(BaseResponse):
    """保存文章为HTML响应模型"""
    file_path: Optional[str] = None
    file_size: Optional[int] = None

class HealthCheckResponse(BaseResponse):
    """健康检查响应模型"""
    version: str
    uptime: str
    status: str

# API响应状态码
class GetAllArticlesRequest(BaseModel):
    """获取公众号全部文章请求模型"""
    account_name: Optional[str] = Field(None, description="公众号名称（与faker_id二选一）", example="奚晓乔")
    faker_id: Optional[str] = Field(None, description="公众号faker_id（与account_name二选一）", example="MzU0MDk3NTUxMA==")
    max_pages: Optional[int] = Field(None, ge=1, le=1000, description="最大页数限制，不指定则获取所有文章")

    def __init__(self, **data):
        super().__init__(**data)
        # 验证至少提供一个查询条件（检查是否为空字符串）
        account_name_valid = self.account_name and self.account_name.strip()
        faker_id_valid = self.faker_id and self.faker_id.strip()

        if not account_name_valid and not faker_id_valid:
            raise ValueError("必须提供account_name或faker_id")

    class Config:
        json_schema_extra = {
            "example": {
                "account_name": "奚晓乔",
                "faker_id": "",
                "max_pages": 10
            }
        }

class GetAllArticlesResponse(BaseResponse):
    """获取公众号全部文章响应模型"""
    account_info: Optional[AccountInfo] = None
    faker_id: str
    articles: List[Dict[str, Any]] = []
    total: int = 0
    pages_fetched: int = 0

class BatchDownloadRequest(BaseModel):
    """批量下载公众号文章请求模型"""
    account_name: Optional[str] = Field(None, description="公众号名称（与fakeid二选一）", example="奚晓乔")
    fakeid: Optional[str] = Field(None, description="公众号fakeid（与account_name二选一）", example="MzU0MDk3NTUxMA==")
    time_filter: Optional[str] = Field(None, description="时间过滤器，支持年/月/日级别过滤", example="2025/06-2025/08")
    save_html: bool = Field(True, description="是否保存为HTML文件")
    max_pages: Optional[int] = Field(None, description="最大页数限制，使用时间过滤时通常不需要此参数", example=10)

    def __init__(self, **data):
        super().__init__(**data)
        # 验证至少提供一个查询条件（检查是否为空字符串）
        account_name_valid = self.account_name and self.account_name.strip()
        fakeid_valid = self.fakeid and self.fakeid.strip()

        if not account_name_valid and not fakeid_valid:
            raise ValueError("必须提供account_name或fakeid")

    class Config:
        json_schema_extra = {
            "example": {
                "account_name": "",
                "fakeid": "",
                "time_filter": "2025/06-2025/08",
                "save_html": True
            }
        }

class BatchDownloadResponse(BaseResponse):
    """批量下载公众号文章响应模型"""
    account_info: Optional[AccountInfo] = None
    fakeid: str
    total_articles: int = 0
    filtered_articles: int = 0
    downloaded_articles: int = 0
    failed_articles: int = 0
    saved_files: List[str] = []
    download_summary: List[Dict[str, Any]] = []
    time_filter_applied: Optional[str] = None

class GetFirstPageArticlesRequest(BaseModel):
    """获取公众号第一页文章请求模型"""
    account_name: Optional[str] = Field(None, description="公众号名称（与fakeid二选一）", example="奚晓乔")
    fakeid: Optional[str] = Field(None, description="公众号fakeid（与account_name二选一）", example="MzU0MDk3NTUxMA==")

    def __init__(self, **data):
        super().__init__(**data)
        # 验证至少提供一个查询条件（检查是否为空字符串）
        account_name_valid = self.account_name and self.account_name.strip()
        fakeid_valid = self.fakeid and self.fakeid.strip()

        if not account_name_valid and not fakeid_valid:
            raise ValueError("必须提供account_name或fakeid")

    class Config:
        json_schema_extra = {
            "example": {
                "account_name": "奚晓乔",
                "fakeid": ""
            }
        }

class GetFirstPageArticlesResponse(BaseResponse):
    """获取公众号第一页文章响应模型"""
    account_info: Optional[AccountInfo] = None
    fakeid: str
    articles: List[Dict[str, Any]] = []
    total: int = 0

class StatusCode:
    """API状态码定义"""
    SUCCESS = "SUCCESS"
    LOGIN_REQUIRED = "LOGIN_REQUIRED"
    INVALID_REQUEST = "INVALID_REQUEST"
    ACCOUNT_NOT_FOUND = "ACCOUNT_NOT_FOUND"
    ARTICLE_NOT_FOUND = "ARTICLE_NOT_FOUND"
    FILE_NOT_FOUND = "FILE_NOT_FOUND"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    WECHAT_ERROR = "WECHAT_ERROR"
