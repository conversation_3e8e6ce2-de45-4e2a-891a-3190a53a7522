#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双浏览器管理器
管理1号浏览器（登录专用）和2号浏览器（下载专用）
"""

import os
import threading
from pathlib import Path
from contextlib import contextmanager
from DrissionPage import ChromiumPage, ChromiumOptions
from loguru import logger
from ..utils.temp_manager import create_temp_browser_dir, cleanup_temp_browser_dir


class BrowserManager:
    """双浏览器管理器"""

    def __init__(self, base_data_dir: str = "./data"):
        self.base_data_dir = Path(base_data_dir)
        self.base_data_dir.mkdir(parents=True, exist_ok=True)

        # 1号浏览器（登录专用）
        self.login_data_dir = self.base_data_dir / "browser_login"
        self.login_data_dir.mkdir(parents=True, exist_ok=True)

        # 2号浏览器配置
        self.download_port_start = 9222
        self._port_counter = 0
        self._port_lock = threading.Lock()

    def _create_browser_options(self, port: int, user_data_path: str) -> ChromiumOptions:
        co = ChromiumOptions()

        # 设置端口和用户数据目录
        co.set_local_port(port)
        co.set_user_data_path(user_data_path)

        # 检测并设置浏览器路径（优先使用google-chrome）
        browser_paths = [
            '/usr/bin/google-chrome',         # 标准 Ubuntu 路径（优先）
            '/usr/bin/chrome',                # 软链接路径
            'google-chrome',                  # 命令名
            'chrome',                         # 命令名
        ]

        browser_path = None
        for path in browser_paths:
            if os.path.exists(path) or (not path.startswith('/') and os.system(f'which {path} > /dev/null 2>&1') == 0):
                browser_path = path
                logger.info(f"找到浏览器路径: {path}")
                break

        if browser_path:
            co.set_browser_path(browser_path)
        else:
            logger.warning("未找到浏览器路径，使用默认配置")

        co.headless(False)
        # Docker 容器必需的参数
        co.set_argument('--no-sandbox')
        co.set_argument('--disable-dev-shm-usage')
        co.set_argument('--disable-gpu')
        co.set_argument('--window-size=1920,1080')

        # 虚拟显示相关参数（xvfb-run会自动处理DISPLAY变量）
        co.set_argument('--use-gl=swiftshader')
        co.set_argument('--disable-software-rasterizer')

        # 根据浏览器类型设置不同的配置
        logger.info(f"创建登录浏览器配置 (端口: {port}, 普通模式)")
        return co

    def get_login_browser(self) -> ChromiumPage:
        try:
            logger.info("创建1号浏览器（登录专用）...")
            # 创建浏览器配置
            co = self._create_browser_options(
                port=9111,
                user_data_path=str(self.login_data_dir)
            )
            # 创建浏览器实例
            logger.info(f"1号浏览器实例创建成功")
            return ChromiumPage(co)
        except Exception as e:
            logger.error(f"创建1号浏览器失败: {e}", exc_info=True)
            raise

    def _get_next_port(self) -> int:
        """获取下一个可用端口"""
        with self._port_lock:
            port = self.download_port_start + self._port_counter
            self._port_counter += 1
            return port

    def create_download_browser(self, temp_dir: str) -> ChromiumPage:
        try:
            port = self._get_next_port()
            logger.info(f"创建2号浏览器（下载专用）端口: {port}, 临时目录: {temp_dir}")

            # 创建浏览器配置
            co = self._create_browser_options(
                port=port,
                user_data_path=temp_dir
            )
            # 创建浏览器实例
            browser = ChromiumPage(co)
            logger.info(f"2号浏览器创建成功 (端口: {port})")

            return browser

        except Exception as e:
            logger.error(f"创建2号浏览器失败: {e}")
            raise

    @contextmanager
    def download_browser_context(self):
        browser = None
        temp_dir = None
        try:
            # 创建临时目录
            temp_dir = create_temp_browser_dir()
            # 创建浏览器
            browser = self.create_download_browser(temp_dir)
            yield browser
        finally:
            # 先关闭浏览器
            if browser:
                try:
                    # ChromiumPage 可能没有 quit() 方法，尝试其他关闭方法
                    if hasattr(browser, 'quit'):
                        browser.quit()
                    elif hasattr(browser, 'close'):
                        browser.close()
                    else:
                        # 关闭所有标签页
                        browser.close_tabs()
                    logger.info("2号浏览器已关闭")
                except Exception as e:
                    logger.warning(f"关闭2号浏览器时出错: {e}")

            # 再清理临时目录
            if temp_dir:
                try:
                    cleanup_temp_browser_dir(temp_dir)
                except Exception as e:
                    logger.warning(f"清理临时目录时出错: {e}")


# 全局浏览器管理器实例
_browser_manager = None


def get_browser_manager() -> BrowserManager:
    """获取全局浏览器管理器实例"""
    global _browser_manager
    if _browser_manager is None:
        _browser_manager = BrowserManager()
    return _browser_manager
