#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件保存服务 - 只支持自包含HTML格式
"""

import os
import re
import base64
import requests
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Tuple
from loguru import logger
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from bs4 import BeautifulSoup
from ..models.schemas import ArticleContent

class FileService:
    """文件保存服务类 - 专注于自包含HTML格式"""

    def __init__(self, base_dir: str = "data/articles"):
        """初始化文件服务
        
        Args:
            base_dir: 文章保存的基础目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)

    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除不合法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # 移除或替换不合法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 移除多余的空格和点
        filename = re.sub(r'\s+', ' ', filename).strip()
        filename = filename.strip('.')
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100]
        return filename

    def _download_image_for_html(self, image_url: str, timeout: int = 30, max_retries: int = 3) -> Optional[Tuple[bytes, str]]:
        """下载图片用于HTML格式

        Args:
            image_url: 图片URL
            timeout: 请求超时时间
            max_retries: 最大重试次数

        Returns:
            (图片数据, 内容类型) 的元组，如果失败返回None
        """
        # 清理URL
        if not image_url or not image_url.startswith(('http://', 'https://')):
            logger.warning(f"无效的图片URL: {image_url}")
            return None

        # 清理URL中的多余参数（保留基本参数）
        try:
            parsed = urlparse(image_url)
            # 保留一些常见的图片参数
            if parsed.query:
                query_params = parse_qs(parsed.query)
                # 保留图片相关参数
                keep_params = {}
                for key, values in query_params.items():
                    if key.lower() in ['w', 'width', 'h', 'height', 'q', 'quality', 'format', 'f']:
                        keep_params[key] = values[0] if values else ''

                if keep_params:
                    new_query = urlencode(keep_params)
                    image_url = urlunparse((parsed.scheme, parsed.netloc, parsed.path, parsed.params, new_query, ''))
                else:
                    image_url = urlunparse((parsed.scheme, parsed.netloc, parsed.path, parsed.params, '', ''))
        except:
            pass  # 如果URL解析失败，使用原URL

        logger.debug(f"正在下载图片用于HTML: {image_url}")

        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Referer': 'https://mp.weixin.qq.com/'
        }

        # 重试下载
        for attempt in range(max_retries):
            try:
                # 下载图片
                response = requests.get(image_url, headers=headers, timeout=timeout, stream=True)
                response.raise_for_status()

                # 检查内容类型
                content_type = response.headers.get('content-type', '').lower()
                if not content_type.startswith('image/'):
                    # 尝试从URL扩展名推断类型
                    if image_url.lower().endswith(('.jpg', '.jpeg')):
                        content_type = 'image/jpeg'
                    elif image_url.lower().endswith('.png'):
                        content_type = 'image/png'
                    elif image_url.lower().endswith('.gif'):
                        content_type = 'image/gif'
                    elif image_url.lower().endswith('.webp'):
                        content_type = 'image/webp'
                    else:
                        if attempt < max_retries - 1:
                            logger.warning(f"URL返回的不是图片内容 (尝试 {attempt + 1}/{max_retries}): {image_url}, content-type: {content_type}")
                            continue
                        else:
                            logger.error(f"URL返回的不是图片内容 (最终失败): {image_url}, content-type: {content_type}")
                            return None

                # 读取图片数据
                image_data = response.content
                if len(image_data) == 0:
                    if attempt < max_retries - 1:
                        logger.warning(f"图片数据为空 (尝试 {attempt + 1}/{max_retries}): {image_url}")
                        continue
                    else:
                        logger.error(f"图片数据为空 (最终失败): {image_url}")
                        return None

                # 检查图片大小限制（避免过大的图片）
                max_size = 10 * 1024 * 1024  # 10MB
                if len(image_data) > max_size:
                    logger.warning(f"图片过大，跳过: {image_url} ({len(image_data)} bytes)")
                    return None

                logger.debug(f"图片下载成功: {image_url} -> {len(image_data)} bytes, type: {content_type}")
                return image_data, content_type

            except requests.exceptions.Timeout:
                if attempt < max_retries - 1:
                    logger.warning(f"下载图片超时 (尝试 {attempt + 1}/{max_retries}): {image_url}")
                    continue
                else:
                    logger.error(f"下载图片超时 (最终失败): {image_url}")
                    break
            except requests.exceptions.RequestException as e:
                if attempt < max_retries - 1:
                    logger.warning(f"下载图片失败 (尝试 {attempt + 1}/{max_retries}): {image_url} - {e}")
                    continue
                else:
                    logger.error(f"下载图片失败 (最终失败): {image_url} - {e}")
                    break
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.error(f"处理图片时出错 (尝试 {attempt + 1}/{max_retries}): {image_url} - {e}")
                    continue
                else:
                    logger.error(f"处理图片时出错 (最终失败): {image_url} - {e}")
                    break

        return None

    def _extract_all_image_urls(self, article: ArticleContent, content: str) -> set:
        """提取所有图片URL

        Args:
            article: 文章对象
            content: HTML内容

        Returns:
            所有图片URL的集合
        """
        # 优先使用wechat_core提供的图片列表
        all_image_urls = set(article.images) if article.images else set()

        # 从HTML内容中补充提取图片
        try:
            soup = BeautifulSoup(content, 'html.parser')
            # 定义所有可能的图片属性
            img_attributes = ['src', 'data-src', 'data-original', 'data-lazy-src', 'data-original-src', 'data-croporisrc']

            for img_tag in soup.find_all('img'):
                for attr in img_attributes:
                    attr_src = img_tag.get(attr)
                    if attr_src and attr_src.startswith(('http://', 'https://', 'data:')):
                        # 添加原始URL
                        all_image_urls.add(attr_src)

                        # 同时添加清理后的URL（去掉HTML实体编码）
                        clean_url = attr_src.replace('&amp;', '&')
                        if clean_url != attr_src:
                            all_image_urls.add(clean_url)

                        # 添加基础URL（去掉查询参数）
                        base_url = attr_src.split('?')[0]
                        if base_url != attr_src:
                            all_image_urls.add(base_url)

        except Exception as e:
            logger.warning(f"从HTML补充提取图片时出错: {e}")

        return all_image_urls

    def _replace_image_urls_in_content(self, content: str, url_mapping: dict) -> str:
        """替换内容中的图片URL（优化版本）

        Args:
            content: HTML内容
            url_mapping: URL映射字典 {原始URL: 新URL}

        Returns:
            替换后的内容
        """
        if not url_mapping:
            return content

        logger.debug(f"开始替换图片URL，共 {len(url_mapping)} 个映射")
        start_time = time.time()

        processed_content = content

        # 定义需要处理的属性
        attributes = ['src', 'data-src', 'data-original', 'data-lazy-src', 'data-original-src', 'data-croporisrc']

        # 预编译所有可能的正则表达式模式（一次性编译，避免重复编译）
        compiled_patterns = {}
        for attr in attributes:
            # 匹配该属性的通用模式：attr="任意内容" 或 attr='任意内容'
            compiled_patterns[attr] = re.compile(rf'{attr}=(["\'])([^"\']*?)\1', re.IGNORECASE)

        # 创建URL基础部分到Base64的映射（去掉参数进行匹配）
        base_url_mapping = {}
        for original_url, new_url in url_mapping.items():
            base_url = original_url.split('?')[0]
            base_url_mapping[base_url] = new_url

        # 使用单次扫描替换所有URL
        def replace_url_in_attribute(match):
            quote = match.group(1)
            url = match.group(2)

            # 首先尝试精确匹配
            if url in url_mapping:
                return f'src={quote}{url_mapping[url]}{quote}'

            # 然后尝试基础URL匹配
            base_url = url.split('?')[0]
            if base_url in base_url_mapping:
                return f'src={quote}{base_url_mapping[base_url]}{quote}'

            # 如果没有匹配，保持原样但转换为src属性
            return f'src={quote}{url}{quote}'

        # 对每个属性进行一次性替换
        for attr in attributes:
            pattern = compiled_patterns[attr]
            processed_content = pattern.sub(replace_url_in_attribute, processed_content)

        elapsed_time = time.time() - start_time
        logger.debug(f"图片URL替换完成，耗时 {elapsed_time:.2f} 秒")

        return processed_content

    def _cleanup_image_attributes(self, content: str) -> str:
        """清理图片属性（优化版本）

        Args:
            content: HTML内容

        Returns:
            清理后的内容
        """
        logger.debug("开始清理图片属性")
        start_time = time.time()

        # 使用一次性正则表达式清理所有懒加载属性
        lazy_attributes = ['data-src', 'data-original', 'data-lazy-src', 'data-original-src', 'data-croporisrc']

        # 构建一个匹配所有懒加载属性的正则表达式
        attrs_pattern = '|'.join(lazy_attributes)
        # 一次性删除所有懒加载属性（双引号和单引号）
        content = re.sub(rf'\s*(?:{attrs_pattern})=(["\'])[^"\']*\1', '', content)

        # 清理可能的重复src属性（一次性处理）
        content = re.sub(r'(<img[^>]*?)src="[^"]*"([^>]*?)src="([^"]*)"', r'\1src="\3"\2', content)
        content = re.sub(r"(<img[^>]*?)src='[^']*'([^>]*?)src='([^']*)'", r"\1src='\3'\2", content)

        elapsed_time = time.time() - start_time
        logger.debug(f"图片属性清理完成，耗时 {elapsed_time:.2f} 秒")

        return content

    def _ensure_no_external_urls(self, content: str) -> str:
        """确保没有剩余的外部图片URL，将它们替换为占位符

        Args:
            content: HTML内容

        Returns:
            处理后的内容
        """
        try:
            # 占位符图片（SVG格式的"图片加载失败"提示）
            placeholder_svg = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4="

            # 查找所有剩余的外部图片URL并替换为占位符
            # 匹配 src="http..." 或 src='http...' 的模式
            external_url_pattern = r'src=(["\'])(https?://[^"\']*)\1'

            def replace_external_url(match):
                quote = match.group(1)
                original_url = match.group(2)
                logger.warning(f"发现未处理的外部图片URL，替换为占位符: {original_url}")
                return f'src={quote}{placeholder_svg}{quote}'

            content = re.sub(external_url_pattern, replace_external_url, content)

            return content

        except Exception as e:
            logger.warning(f"检查外部URL时出错: {e}")
            return content

    def save_article_as_html(self, article: ArticleContent, filename: str = None) -> str:
        """将文章保存为自包含HTML文件（更好的兼容性）

        Args:
            article: 文章内容对象
            filename: 文件名（不包含扩展名），如果为None则使用文章标题

        Returns:
            保存的文件路径
        """
        try:
            # 确保保存目录存在
            os.makedirs(self.base_dir, exist_ok=True)
            # 生成文件名
            if filename is None:
                filename = article.title
            # 清理文件名
            clean_filename = self._sanitize_filename(filename)
            file_path = os.path.join(self.base_dir, f"{clean_filename}.html")
            # 生成自包含HTML内容
            html_content = self._generate_self_contained_html(article)
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            # 获取文件大小
            file_size = os.path.getsize(file_path)
            logger.info(f"文章已保存为HTML文件: {file_path} ({file_size} bytes)")
            return file_path
        except Exception as e:
            logger.error(f"保存HTML文件时出错: {e}")
            raise Exception(f"保存HTML文件失败: {str(e)}")

    def _generate_self_contained_html(self, article: ArticleContent) -> str:
        """生成自包含的HTML文件（包含Base64图片）

        Args:
            article: 文章内容对象

        Returns:
            完整的HTML内容
        """
        try:
            # 下载并转换图片为Base64
            processed_content = article.content
            image_base64_map = {}
            # 使用文章中的作者信息，如果为空则显示"未知"
            final_author = article.author or "未知"
            # 提取所有图片URL（优先使用wechat_core提供的列表，补充HTML中的图片）
            all_image_urls = self._extract_all_image_urls(article, processed_content)

            # 去重并按基础URL分组，确保每个图片只下载一次
            unique_urls = {}  # base_url -> actual_download_url
            url_variants = {}  # base_url -> [all_variant_urls]

            for url in all_image_urls:
                if url.startswith('data:'):
                    continue  # 跳过已经是Base64的图片

                # 清理HTML实体编码
                clean_url = url.replace('&amp;', '&')
                base_url = clean_url.split('?')[0]

                if base_url not in unique_urls:
                    unique_urls[base_url] = clean_url
                    url_variants[base_url] = []

                url_variants[base_url].append(url)

            logger.info(f"开始处理 {len(unique_urls)} 个唯一图片")

            for i, (base_url, download_url) in enumerate(unique_urls.items(), 1):
                logger.info(f"正在下载第 {i}/{len(unique_urls)} 个图片: {download_url[:100]}...")
                image_result = self._download_image_for_html(download_url)

                if image_result:
                    image_data, content_type = image_result
                    # 转换为Base64
                    base64_data = base64.b64encode(image_data).decode('utf-8')
                    data_url = f"data:{content_type};base64,{base64_data}"

                    # 为所有变体URL创建映射
                    for variant_url in url_variants[base_url]:
                        image_base64_map[variant_url] = data_url

                    logger.info(f"图片 {i} 处理成功: {len(image_data)} bytes -> Base64 (映射到 {len(url_variants[base_url])} 个变体)")
                else:
                    logger.warning(f"图片 {i} 下载失败: {download_url}")
                    # 对于下载失败的图片，使用占位符图片
                    placeholder_svg = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4="

                    # 为所有变体URL创建占位符映射
                    for variant_url in url_variants[base_url]:
                        image_base64_map[variant_url] = placeholder_svg

                    logger.info(f"图片 {i} 使用占位符替换 (映射到 {len(url_variants[base_url])} 个变体)")

            # 替换内容中的图片URL（使用优化的方法）
            logger.info("开始替换内容中的图片URL...")
            url_replace_start = time.time()
            processed_content = self._replace_image_urls_in_content(processed_content, image_base64_map)
            url_replace_time = time.time() - url_replace_start
            logger.info(f"图片URL替换完成，耗时 {url_replace_time:.2f} 秒")

            # 清理图片属性
            logger.info("开始清理图片属性...")
            cleanup_start = time.time()
            processed_content = self._cleanup_image_attributes(processed_content)
            cleanup_time = time.time() - cleanup_start
            logger.info(f"图片属性清理完成，耗时 {cleanup_time:.2f} 秒")

            # 最终检查：确保没有剩余的外部图片URL
            processed_content = self._ensure_no_external_urls(processed_content)


            # 生成完整的HTML
            logger.info("开始生成HTML模板...")
            html_gen_start = time.time()
            html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{article.title}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }}
        .article-header {{
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }}
        .article-title {{
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            line-height: 1.3;
        }}
        .article-meta {{
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
            text-align: center;
        }}
        .article-meta-line {{
            margin-bottom: 8px;
            line-height: 1.5;
        }}
        .article-meta span {{
            margin: 0 15px;
            display: inline-block;
        }}
        .article-url-line {{
            text-align: center;
            margin-top: 15px;
        }}
        .article-url {{
            color: #3498db;
            text-decoration: none;
            word-break: break-all;
        }}
        .article-url:hover {{
            text-decoration: underline;
        }}
        .article-content {{
            font-size: 16px;
            line-height: 1.8;
            margin-top: 30px;
        }}
        .article-content img {{
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            /* 确保Base64图片正确显示 */
            object-fit: contain;
            background-color: #f8f9fa;
        }}
        /* 处理微信公众号特殊图片样式 */
        .article-content img[data-ratio] {{
            width: auto !important;
            height: auto !important;
        }}
        /* 处理懒加载图片 */
        .article-content img[data-src] {{
            opacity: 1 !important;
            visibility: visible !important;
        }}
        .article-content p {{
            margin-bottom: 16px;
        }}
        .article-content h1, .article-content h2, .article-content h3 {{
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
        }}
        .article-content blockquote {{
            border-left: 4px solid #3498db;
            padding-left: 20px;
            margin: 20px 0;
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-radius: 4px;
        }}
        .article-content ul, .article-content ol {{
            padding-left: 30px;
            margin-bottom: 16px;
        }}
        .article-content li {{
            margin-bottom: 8px;
        }}
        .html-info {{
            background-color: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
            font-size: 14px;
            color: #1565c0;
        }}
        .wechat-video-player {{
            border: 2px solid #1aad19;
            border-radius: 8px;
            margin: 20px 0;
            padding: 15px;
            background: #f8f8f8;
            text-align: center;
            position: relative;
        }}
    </style>
</head>
<body>
    <div class="article-header">
        <h1 class="article-title">{article.title}</h1>
        <div class="article-meta">
            <div class="article-meta-line">
                {f'<span>👤 作者: {final_author}</span>' if final_author else '<span>👤 作者: 未知</span>'}
                {f'<span>📅 发布时间: {article.publish_time}</span>' if article.publish_time else '<span>📅 发布时间: 未知</span>'}
            </div>
            <div class="article-meta-line">
                <span>🖼️ 图片数量: {len(article.images)}</span>
                <span>🎬 视频数量: {len(article.videos)}</span>
                {f'<span>🔗 BIZ: {article.biz}</span>' if article.biz else '<span>🔗 BIZ: 未知</span>'}
            </div>
        </div>
        <div class="article-url-line">
            <span>📄 原文链接: <a href="{article.url}" class="article-url" target="_blank">{article.url}</a></span>
        </div>
    </div>
    <div class="article-content">
        {processed_content}
    </div>
</body>
</html>"""

            html_gen_time = time.time() - html_gen_start
            logger.info(f"HTML模板生成完成，耗时 {html_gen_time:.2f} 秒")

            logger.info(f"自包含HTML生成完成，包含 {len(image_base64_map)} 个Base64嵌入图片")
            return html_template

        except Exception as e:
            logger.error(f"生成自包含HTML时出错: {e}")

    def get_saved_articles(self) -> list:
        """获取已保存的文章列表

        Returns:
            文章文件列表
        """
        try:
            articles = []
            if self.base_dir.exists():
                # 查找HTML文件（现在保存为HTML格式）
                for file_path in self.base_dir.glob("*.html"):
                    file_stat = file_path.stat()
                    articles.append({
                        "filename": file_path.name,
                        "title": file_path.stem,
                        "size": file_stat.st_size,
                        "created_time": datetime.fromtimestamp(file_stat.st_ctime).strftime("%Y-%m-%d %H:%M:%S"),
                        "modified_time": datetime.fromtimestamp(file_stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                    })

            # 按修改时间倒序排列
            articles.sort(key=lambda x: x["modified_time"], reverse=True)
            return articles

        except Exception as e:
            logger.error(f"获取已保存文章列表失败: {e}")
            return []

    def delete_article_file(self, filename: str) -> bool:
        """删除指定的文章文件

        Args:
            filename: 文件名

        Returns:
            删除是否成功
        """
        try:
            file_path = self.base_dir / filename
            if file_path.exists() and file_path.suffix == '.html':
                file_path.unlink()
                logger.info(f"文章文件删除成功: {filename}")
                return True
            else:
                logger.warning(f"文章文件不存在或不是HTML文件: {filename}")
                return False

        except Exception as e:
            logger.error(f"删除文章文件失败: {e}")
            return False
