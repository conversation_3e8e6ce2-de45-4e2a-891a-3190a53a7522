#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像识别服务
使用Cloudflare Worker API分析微信文章截图，提取title、author、time、place信息
"""

import base64
import json
import os
import re
import requests
from pathlib import Path
from typing import Dict, Optional, Union
from urllib.parse import quote
from loguru import logger
from ..core.config import get_settings

class ImageRecognitionService:
    """图像识别服务"""

    def __init__(self):
        """初始化图像识别服务"""
        self.settings = get_settings()

        # 从配置文件读取图像识别配置
        image_config = self.settings.wechat.image_recognition

        # Cloudflare Worker API配置（固定不变）
        self.worker_url = image_config.worker_url

        # 自定义提示词（固定不变）
        self.custom_prompt = image_config.custom_prompt

        # authorization（从配置文件读取，可通过修改config.yaml更新）
        self.authorization = image_config.authorization

        # API请求超时时间
        self.timeout = image_config.timeout

    def encode_prompt_for_worker(self, prompt: str) -> str:
        """
        对自定义提示词进行双重编码 (URI + Base64)，以匹配 worker.js 的解码逻辑。
        """
        uri_encoded_prompt = quote(prompt, safe='')
        base64_encoded_prompt = base64.b64encode(uri_encoded_prompt.encode('utf-8')).decode('utf-8')
        return base64_encoded_prompt

    def recognize_image_via_worker(self, image_path: Union[str, Path]) -> Optional[Dict]:
        """
        通过调用Cloudflare Worker API识别图片中的文字

        Args:
            image_path: 图片文件路径

        Returns:
            识别结果字典，失败返回None
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                logger.error(f"图片文件不存在: {image_path}")
                return None

            # 读取本地图片并进行Base64编码
            with open(image_path, "rb") as image_file:
                base64_image_string = base64.b64encode(image_file.read()).decode('utf-8')

            # 构造请求头
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f"Bearer {self.authorization}",
                'x-advanced-mode': 'true',
                'x-custom-prompt': self.encode_prompt_for_worker(self.custom_prompt),
            }

            # 构造请求体
            payload = {
                'base64Image': base64_image_string
            }

            logger.info(f"正在向Cloudflare Worker发送识别请求: {self.worker_url}")

            # 发送POST请求
            response = requests.post(self.worker_url, headers=headers, json=payload, timeout=self.timeout)
            response.raise_for_status()

            return response.json()

        except requests.exceptions.HTTPError as http_err:
            logger.error(f"HTTP错误: {http_err.response.status_code}")
            logger.error(f"Worker返回的错误信息: {http_err.response.text}")
        except requests.exceptions.RequestException as req_err:
            logger.error(f"网络或请求错误: {req_err}")
        except (IOError, FileNotFoundError) as file_err:
            logger.error(f"文件处理错误: {file_err}")
        except Exception as e:
            logger.error(f"图片识别失败: {e}")

        return None

    def parse_worker_response(self, response_data: Dict) -> Optional[Dict]:
        """解析Worker API响应并提取信息（temp数组格式）"""
        try:
            if not response_data.get('success'):
                logger.error(f"Worker返回失败响应: {response_data.get('error', '未知错误')}")
                return None

            # 获取识别结果
            result_text = response_data.get('result', '')
            if not result_text:
                logger.error("Worker返回空结果")
                return None

            logger.debug(f"Worker返回的原始结果: {result_text}")

            # 尝试解析JSON
            try:
                # 直接尝试解析JSON
                result_json = json.loads(result_text)

                # 转换temp数组格式
                return self._convert_temp_array_format(result_json)

            except json.JSONDecodeError:
                # 如果不是标准JSON，尝试提取关键信息
                logger.warning("Worker返回的不是标准JSON，尝试解析文本")
                return self.extract_info_from_text(result_text)

        except Exception as e:
            logger.error(f"解析Worker响应失败: {e}")
            return None

    def _convert_temp_array_format(self, result_json: Dict) -> Dict:
        """转换temp数组格式为标准格式

        Args:
            result_json: 包含temp数组的结果

        Returns:
            转换后的标准格式结果
        """
        try:
            temp_array = result_json.get('temp', [])
            title = result_json.get('title', '')

            # 确保temp数组有足够的元素
            while len(temp_array) < 4:
                temp_array.append('')

            # 根据temp数组格式提取信息
            # temp[0] = 不用理会
            # temp[1] = 作者
            # temp[2] = 发布日期
            # temp[3] = 城市
            converted_result = {
                "title": title,
                "author": temp_array[1] if len(temp_array) > 1 else '',
                "time": temp_array[2] if len(temp_array) > 2 else '',
                "place": temp_array[3] if len(temp_array) > 3 else ''
            }

            logger.debug(f"temp数组格式转换: {temp_array} -> {converted_result}")
            return converted_result

        except Exception as e:
            logger.error(f"转换temp数组格式失败: {e}")
            # 返回默认格式
            return {
                "title": result_json.get('title', ''),
                "author": '',
                "time": '',
                "place": ''
            }

    def extract_info_from_text(self, text: str) -> Dict:
        """从文本中提取信息（备用解析方法，temp数组格式）"""
        try:
            # 尝试解析temp数组格式
            if 'temp' in text and '[' in text and ']' in text:
                result_from_temp = self._extract_from_temp_text(text)
                if result_from_temp:
                    return result_from_temp

            # 如果没有temp数组，返回默认结果
            logger.warning("文本中未找到temp数组格式")
            return {
                "title": "",
                "author": "",
                "time": "",
                "place": ""
            }

        except Exception as e:
            logger.error(f"从文本提取信息失败: {e}")
            return {
                "title": "",
                "author": "",
                "time": "",
                "place": ""
            }

    def _extract_from_temp_text(self, text: str) -> Optional[Dict]:
        """从包含temp数组的文本中提取信息"""
        try:
            # 提取title
            title_match = re.search(r'title:\s*["\']([^"\']*)["\']', text, re.IGNORECASE)
            title = title_match.group(1) if title_match else ''

            # 提取temp数组
            temp_match = re.search(r'temp:\s*\[(.*?)\]', text, re.DOTALL | re.IGNORECASE)
            if not temp_match:
                return None

            temp_content = temp_match.group(1)

            # 解析数组元素
            temp_elements = []
            # 匹配引号内的内容
            element_matches = re.findall(r'["\']([^"\']*)["\']', temp_content)
            temp_elements = element_matches

            # 确保有足够的元素
            while len(temp_elements) < 4:
                temp_elements.append('')

            result = {
                "title": title,
                "author": temp_elements[1] if len(temp_elements) > 1 else '',
                "time": temp_elements[2] if len(temp_elements) > 2 else '',
                "place": temp_elements[3] if len(temp_elements) > 3 else ''
            }

            logger.debug(f"从temp文本提取的信息: {temp_elements} -> {result}")
            return result

        except Exception as e:
            logger.error(f"从temp文本提取信息失败: {e}")
            return None

    async def analyze_image(self, image_path: Union[str, Path], custom_prompt: Optional[str] = None, return_json: bool = True) -> Optional[Dict]:
        """异步版本的图片分析（调用同步方法）"""
        return self.analyze_image_sync(image_path, custom_prompt, return_json)

    def analyze_image_sync(self, image_path: Union[str, Path], custom_prompt: Optional[str] = None, return_json: bool = True) -> Optional[Dict]:
        """同步版本的图片分析"""
        try:
            logger.info(f"开始分析图片: {image_path}")

            # 调用Worker API进行图片识别
            worker_response = self.recognize_image_via_worker(image_path)

            if not worker_response:
                logger.error("Worker API调用失败")
                return None

            # 解析Worker响应
            result = self.parse_worker_response(worker_response)

            if result:
                logger.info(f"图片分析成功: {result}")
                return result
            else:
                logger.error("解析Worker响应失败")
                return None

        except Exception as e:
            logger.error(f"图片分析失败: {e}")
            return None


# 全局图像识别服务实例
_image_recognition_service = None


def get_image_recognition_service() -> ImageRecognitionService:
    """获取全局图像识别服务实例"""
    global _image_recognition_service
    if _image_recognition_service is None:
        _image_recognition_service = ImageRecognitionService()
    return _image_recognition_service
