#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram通知服务
"""

import os
import html
import requests
from typing import Optional
from loguru import logger

class TelegramService:
    """Telegram通知服务类"""
    
    def __init__(self, bot_token: str, chat_id: str):
        """初始化Telegram服务
        
        Args:
            bot_token: Telegram机器人token
            chat_id: 聊天ID
        """
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.enabled = bool(bot_token and chat_id)
        
        if not self.enabled:
            logger.warning("Telegram配置不完整，通知功能已禁用")

    def _escape_html(self, text: str) -> str:
        """转义HTML特殊字符

        Args:
            text: 需要转义的文本

        Returns:
            转义后的文本
        """
        return html.escape(text)

    def send_message(self, message: str, parse_mode: str = "HTML") -> bool:
        """发送文本消息到Telegram
        
        Args:
            message: 要发送的消息
            parse_mode: 解析模式，支持HTML或Markdown
            
        Returns:
            发送是否成功
        """
        if not self.enabled:
            logger.debug("Telegram未启用，跳过消息发送")
            return False
        
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            data = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': parse_mode
            }
            response = requests.post(url, data=data, timeout=10)
            
            if response.status_code == 200:
                logger.debug("Telegram消息发送成功")
                return True
            else:
                logger.error(f"Telegram消息发送失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"发送Telegram消息失败: {e}")
            return False
    
    def send_photo(self, photo_path: str, caption: str = "", parse_mode: str = "HTML") -> bool:
        """发送图片到Telegram
        
        Args:
            photo_path: 图片文件路径
            caption: 图片说明文字
            parse_mode: 解析模式
            
        Returns:
            发送是否成功
        """
        if not self.enabled:
            logger.debug("Telegram未启用，跳过图片发送")
            return False
        
        if not os.path.exists(photo_path):
            logger.error(f"图片文件不存在: {photo_path}")
            return False
        
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/sendPhoto"
            with open(photo_path, 'rb') as photo:
                files = {'photo': photo}
                data = {
                    'chat_id': self.chat_id,
                    'caption': caption,
                    'parse_mode': parse_mode
                }
                response = requests.post(url, files=files, data=data, timeout=30)
                
            if response.status_code == 200:
                logger.debug("Telegram图片发送成功")
                return True
            else:
                logger.error(f"Telegram图片发送失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"发送Telegram图片失败: {e}")
            return False
    
    def send_login_qrcode(self, qrcode_path: str, wait_time: int) -> bool:
        """发送登录二维码
        
        Args:
            qrcode_path: 二维码图片路径
            wait_time: 等待时间（秒）
            
        Returns:
            发送是否成功
        """
        message = (
            "🔐 <b>微信公众平台登录</b>\n\n"
            "请扫描二维码登录微信公众平台\n"
            f"⏰ 等待时间: {wait_time} 秒"
        )
        return self.send_photo(qrcode_path, message)
    
    def send_login_success(self, token: str, expiry_info: Optional[dict] = None, 
                          refresh_interval: int = 0) -> bool:
        """发送登录成功通知
        
        Args:
            token: 登录token
            expiry_info: 过期时间信息
            refresh_interval: 刷新间隔（秒）
            
        Returns:
            发送是否成功
        """
        message = f"✅ <b>登录成功！</b>\n\n🎯 Token: <code>{token[:20]}...</code>"
        
        if expiry_info:
            expiry_time = expiry_info.get('expiry_time', '未知')
            remaining_seconds = expiry_info.get('remaining_seconds', 0)
            remaining_hours = remaining_seconds // 3600
            message += f"\n⏰ 过期时间: {expiry_time}\n⏳ 剩余时间: {remaining_hours} 小时"
        
        if refresh_interval > 0:
            refresh_hours = refresh_interval // 3600
            message += f"\n🔄 自动刷新: 每 {refresh_hours} 小时"
        
        return self.send_message(message)
    
    def send_login_failed(self, error_msg: str) -> bool:
        """发送登录失败通知

        Args:
            error_msg: 错误信息

        Returns:
            发送是否成功
        """
        # 转义错误消息中的HTML特殊字符
        escaped_error_msg = self._escape_html(str(error_msg))
        message = (
            f"❌ <b>登录失败</b>\n\n"
            f"🚫 错误信息: {escaped_error_msg}\n"
            f"🔄 请检查网络连接或重新尝试登录"
        )
        return self.send_message(message)
    
    def send_session_expired(self) -> bool:
        """发送会话过期通知
        
        Returns:
            发送是否成功
        """
        message = (
            "⚠️ <b>Cookie 已过期</b>\n\n"
            "🔐 微信公众平台登录已过期\n"
            "📱 请重新扫码登录\n"
            "🔄 自动刷新已停止"
        )
        return self.send_message(message)
    
    def send_session_refreshed(self, expiry_info: Optional[dict] = None) -> bool:
        """发送会话刷新成功通知
        
        Args:
            expiry_info: 过期时间信息
            
        Returns:
            发送是否成功
        """
        message = "🔄 <b>会话已刷新</b>\n\n"
        
        if expiry_info:
            expiry_time = expiry_info.get('expiry_time', '未知')
            remaining_seconds = expiry_info.get('remaining_seconds', 0)
            remaining_hours = remaining_seconds // 3600
            message += f"⏰ 新的过期时间: {expiry_time}\n⏳ 剩余时间: {remaining_hours} 小时"
        
        return self.send_message(message)

    def send_session_restored(self, token: str, expiry_info: Optional[dict] = None) -> bool:
        """发送会话恢复通知

        Args:
            token: 登录token
            expiry_info: 过期时间信息

        Returns:
            发送是否成功
        """
        message = f"🔄 <b>会话已恢复</b>\n\n🎯 Token: <code>{token[:20]}...</code>"

        if expiry_info:
            expiry_time = expiry_info.get('expiry_time', '未知')
            remaining_seconds = expiry_info.get('remaining_seconds', 0)
            remaining_hours = remaining_seconds // 3600
            message += f"\n⏰ 过期时间: {expiry_time}\n⏳ 剩余时间: {remaining_hours} 小时"

        message += "\n✅ 无需重新扫码，自动使用已保存的登录状态"

        return self.send_message(message)

    def send_refresh_failed(self, error_msg: str) -> bool:
        """发送会话刷新失败通知

        Args:
            error_msg: 错误信息

        Returns:
            发送是否成功
        """
        # 转义错误消息中的HTML特殊字符
        escaped_error_msg = self._escape_html(str(error_msg))
        message = (
            f"❌ <b>会话刷新失败</b>\n\n"
            f"🚫 错误信息: {escaped_error_msg}\n"
            f"🔄 自动刷新已停止\n"
            f"📱 可能需要重新登录"
        )
        return self.send_message(message)
