#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信核心功能模块 - 使用DrissionPage
基于原始wxarticle.py的核心功能，使用更稳定的DrissionPage
支持双浏览器架构：1号浏览器（登录）+ 2号浏览器（下载）
"""

import json
import os
import re
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

import requests
from bs4 import BeautifulSoup
from DrissionPage import ChromiumOptions, ChromiumPage
from loguru import logger

from .browser_manager import get_browser_manager
from .image_recognition_service import get_image_recognition_service


# ==================== 配置常量 ====================
class WeChatCoreConfig:
    """微信核心功能配置常量"""

    # 浏览器路径配置
    BROWSER_PATHS = [
        '/usr/bin/google-chrome',  # 标准 Ubuntu 路径（优先）
        '/usr/bin/chrome',         # 软链接路径
        'google-chrome',           # 命令名
        'chrome',                  # 命令名
    ]

    # CSS选择器配置

    AUTHOR_SELECTORS = [
        '#js_wx_follow_nickname',  # 原有选择器
        '.rich_media_meta_nickname'
    ]

    TIME_SELECTORS = [
        "#publish_time",  # ID选择器
    ]

    # 超时配置
    DEFAULT_TIMEOUT = 30
    ELEMENT_TIMEOUT = 5
    PAGE_LOAD_TIMEOUT = 30
    SCRIPT_TIMEOUT = 15
    BASE_TIMEOUT = 10

    # 截图配置
    SCREENSHOT_REGION = (0, 0, 1920, 120)  # 标题区域截图范围

    # 验证页面特征
    VERIFICATION_INDICATORS = [
        "wappoc_appmsgcaptcha",  # 验证页面URL特征
        "当前环境异常",           # 页面文本特征
        "完成验证后即可继续访问",   # 页面文本特征
        "去验证"                 # 按钮文本特征
    ]

    # 图片和视频选择器
    IMAGE_SELECTORS = [
        'img[src]',
        'img[data-src]',
        'img[data-original]',
        'img[data-lazy-src]'
    ]

    VIDEO_SELECTORS = [
        'video',  # 标准video标签
        '.video_iframe',  # 微信视频iframe
        '.weapp_display_element',  # 小程序视频
        '[data-mpvid]',  # 微信视频
        '.js_mpvideo',  # 微信视频播放器
        'iframe[src*="video"]',  # 视频iframe
        'iframe[src*="mp.weixin.qq.com/mp/readtemplate"]'  # 微信视频模板
    ]

    # 默认用户代理
    DEFAULT_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"


# ==================== 工具类 ====================
class ElementExtractor:
    """元素提取器，统一处理页面元素提取逻辑"""

    @staticmethod
    def extract_title(page) -> str:
        """提取文章标题"""
        try:
            element = page.title
            if element:
                logger.debug(f"成功获取标题: {element}")
                return element
        except Exception as e:
            logger.debug(f"获取标题失败: {e}")

        return "未知标题"

    @staticmethod
    def extract_author(page) -> str:
        """提取文章作者"""
        try:
            # 优先尝试CSS选择器
            for selector in WeChatCoreConfig.AUTHOR_SELECTORS:
                try:
                    # 处理普通元素
                    element = page.ele(selector, timeout=1)
                    if element and element.text.strip():
                        author = element.text.strip()
                        logger.debug(f"从元素 '{selector}' 提取作者: {author}")
                        return author
                except Exception as e:
                    logger.debug(f"选择器 '{selector}' 提取作者失败: {e}")
                    continue

            # 尝试从完整页面HTML中提取
            full_page_html = page.html
            if full_page_html:
                soup = BeautifulSoup(full_page_html, 'html.parser')
                for selector in WeChatCoreConfig.AUTHOR_SELECTORS:
                    try:
                        if selector.startswith('meta'):
                            author_element = soup.select_one(selector)
                            if author_element:
                                author = author_element.get('content', '').strip()
                                if author:
                                    logger.debug(f"从HTML meta标签 '{selector}' 提取作者: {author}")
                                    return author
                        else:
                            author_element = soup.select_one(selector)
                            if author_element:
                                author = author_element.get_text().strip()
                                if author:
                                    logger.debug(f"从HTML元素 '{selector}' 提取作者: {author}")
                                    return author
                    except Exception as e:
                        logger.debug(f"HTML选择器 '{selector}' 提取作者失败: {e}")
                        continue

        except Exception as e:
            logger.debug(f"提取作者信息失败: {e}")

        return "未知作者"

    @staticmethod
    def extract_publish_time(page) -> str:
        """提取发布时间"""
        for selector in WeChatCoreConfig.TIME_SELECTORS:
            try:
                element = page.ele(selector, timeout=2)
                if element and element.text.strip():
                    publish_time = element.text.strip()
                    logger.debug(f"使用选择器 '{selector}' 成功获取发布时间: {publish_time}")
                    return publish_time
            except Exception as e:
                logger.debug(f"选择器 '{selector}' 获取发布时间失败: {e}")
                continue

        return "未知时间"

# ==================== 核心功能函数 ====================
def create_chromium_page() -> ChromiumPage:
    """创建ChromiumPage实例"""
    try:
        # 创建配置对象，不读取默认配置文件以避免路径问题
        options = ChromiumOptions()

        # 检测并设置浏览器路径（优先使用google-chrome）
        browser_path = _find_browser_path()
        if browser_path:
            options.set_browser_path(browser_path)
        else:
            logger.warning("未找到浏览器路径，使用默认配置")

        # 设置浏览器参数
        _configure_browser_options(options)

        logger.info("正在初始化ChromiumPage...")
        page = ChromiumPage(addr_or_opts=options)
        page.set.timeouts(
            base=WeChatCoreConfig.BASE_TIMEOUT,
            page_load=WeChatCoreConfig.PAGE_LOAD_TIMEOUT,
            script=WeChatCoreConfig.SCRIPT_TIMEOUT
        )

        logger.info("ChromiumPage初始化成功")
        return page

    except Exception as e:
        logger.error(f"ChromiumPage初始化失败: {e}")
        raise


def _find_browser_path() -> Optional[str]:
    """查找可用的浏览器路径"""
    for path in WeChatCoreConfig.BROWSER_PATHS:
        if os.path.exists(path) or (not path.startswith('/') and os.system(f'which {path} > /dev/null 2>&1') == 0):
            logger.info(f"找到浏览器路径: {path}")
            return path
    return None


def _configure_browser_options(options: ChromiumOptions) -> None:
    """配置浏览器选项"""
    # Docker 容器必需的参数
    docker_args = [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--window-size=1920,1080',
        '--use-gl=swiftshader',
        '--disable-software-rasterizer'
    ]

    for arg in docker_args:
        options.set_argument(arg)

    logger.info("使用普通模式 + xvfb-run虚拟显示")

def expire_cookie(cookies: List[Dict]) -> Optional[Dict]:
    """计算cookie过期时间"""
    for cookie in cookies:
        if cookie.get('name') == 'slave_sid' and 'expiry' in cookie:
            try:
                expiry_time = float(cookie['expiry'])
                remaining_time = expiry_time - time.time()
                if remaining_time > 0:
                    return {
                        'expiry_timestamp': expiry_time,
                        'remaining_seconds': int(remaining_time),
                        'expiry_time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expiry_time))
                    }
            except ValueError:
                logger.warning(f"slave_sid 的过期时间戳无效: {cookie['expiry']}")
            break
    return None


def dateformat(timestamp: int) -> str:
    """格式化时间戳为本地时间字符串

    Args:
        timestamp: 时间戳

    Returns:
        格式化后的时间字符串
    """
    # UTC时间对象
    utc_dt = datetime.fromtimestamp(int(timestamp), timezone.utc)
    # UTC转本地时区
    local_dt = utc_dt.astimezone()
    return local_dt.strftime("%Y-%m-%d %H:%M:%S")

def search_biz(kw: str = "", limit: int = 5, offset: int = 0,
               token: str = "", cookie: str = "", user_agent: str = "") -> Dict:
    """通过公众号平台接口查询公众号

    Args:
        kw: 搜索关键词
        limit: 返回数量限制
        offset: 偏移量
        token: 微信平台token
        cookie: 请求cookie
        user_agent: 用户代理

    Returns:
        搜索结果数据
    """
    url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"
    params = {
        "action": "search_biz",
        "begin": offset,
        "count": limit,
        "query": kw,
        "token": token,
        "lang": "zh_CN",
        "f": "json",
        "ajax": "1"
    }
    headers = {
        "Cookie": cookie,
        "User-Agent": user_agent or WeChatCoreConfig.DEFAULT_USER_AGENT
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=WeChatCoreConfig.DEFAULT_TIMEOUT)
        response.raise_for_status()
        data = json.loads(response.text)

        # 处理嵌套的JSON字符串
        if 'publish_page' in data:
            data['publish_page'] = json.loads(data['publish_page'])

        return data
    except Exception as e:
        logger.error(f"搜索公众号失败: {e}")
        return {}


def get_articles(faker_id: str, token: str = "", cookie: str = "",
                user_agent: str = "", count: int = 5) -> Dict:
    """通过公众号接口获取公众号文章列表

    Args:
        faker_id: 公众号的faker_id
        token: 微信平台token
        cookie: 请求cookie
        user_agent: 用户代理
        count: 获取文章数量

    Returns:
        文章列表数据
    """
    url = "https://mp.weixin.qq.com/cgi-bin/appmsgpublish"
    headers = {
        "Cookie": cookie,
        "User-Agent": user_agent or WeChatCoreConfig.DEFAULT_USER_AGENT
    }
    params = {
        "sub": "list",
        "sub_action": "list_ex",
        "begin": 0,
        "count": count,
        "fakeid": faker_id,
        "token": token,
        "lang": "zh_CN",
        "f": "json",
        "ajax": 1
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=WeChatCoreConfig.DEFAULT_TIMEOUT)
        response.raise_for_status()
        data = json.loads(response.text)

        # 处理嵌套的JSON字符串
        if 'publish_page' in data:
            data['publish_page'] = json.loads(data['publish_page'])
        if 'publish_info' in data:
            data['publish_info'] = json.loads(data['publish_info'])

        return data
    except Exception as e:
        logger.error(f"获取文章列表失败: {e}")
        return {}

def extract_token_from_page(page: ChromiumPage) -> Optional[str]:
    """从页面中提取token

    Args:
        page: ChromiumPage实例

    Returns:
        提取的token，如果未找到返回None
    """
    try:
        # 从URL中提取token
        token_match = re.search(r'token=([^&]+)', page.url)
        if token_match:
            return token_match.group(1)

        # 从localStorage获取
        token = page.run_js("return localStorage.getItem('token');")
        if token:
            return token

        # 从sessionStorage获取
        token = page.run_js("return sessionStorage.getItem('token');")
        if token:
            return token

        # 从cookies获取
        for cookie in page.cookies():
            if 'token' in cookie['name'].lower():
                return cookie['value']

        return None
    except Exception as e:
        logger.error(f"提取token时出错: {str(e)}")
        return None


def format_token(cookies: List[Dict], token: str = "") -> Dict:
    """格式化token和cookies信息"""
    # 导入CookieExpiry类
    from .wechat_manager import CookieExpiry

    cookies_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])

    if not token:
        for cookie in cookies:
            if 'token' in cookie['name'].lower():
                token = cookie['value']
                break

    return {
        'cookies': cookies,
        'cookies_str': cookies_str,
        'token': token,
        'expiry': CookieExpiry.check_expiry(cookies)
    }

def _handle_wechat_verification(page, wait_timeout: int = 30) -> bool:
    """处理微信验证页面

    Args:
        page: DrissionPage页面对象
        wait_timeout: 等待超时时间

    Returns:
        bool: 是否成功处理验证
    """
    try:
        logger.info("开始处理微信验证页面...")

        # 保存验证页面截图和HTML
        screenshots = _save_verification_evidence(page)

        # 尝试模拟人工操作
        verification_clicked = _simulate_verification_click(page)

        if not verification_clicked:
            return False

        # 等待页面跳转并检查结果
        success = _wait_for_verification_result(page, wait_timeout)

        # 清理临时文件
        if success:
            _cleanup_verification_files(screenshots)

        return success

    except Exception as e:
        logger.error(f"处理微信验证页面时发生错误: {e}")
        return False


def _save_verification_evidence(page) -> List[str]:
    """保存验证页面的截图和HTML证据"""
    screenshots = []
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存截图
        screenshot_name = f'{timestamp}.png'
        page.get_screenshot(path='./data', name=screenshot_name, full_page=True)
        screenshots.append(screenshot_name)
        logger.debug("已保存验证页面截图")

        # 保存HTML源码
        html_name = f'{timestamp}.mhtml'
        page.save(path='./data', name=html_name)
        screenshots.append(html_name)
        logger.debug("已保存验证页面HTML")

    except Exception as e:
        logger.warning(f"保存验证证据失败: {e}")

    return screenshots


def _simulate_verification_click(page) -> bool:
    """模拟人工点击验证按钮"""
    try:
        logger.info("尝试模拟人工操作...")

        # 随机滚动页面
        page.scroll.to_bottom()
        time.sleep(1)
        page.scroll.to_top()
        time.sleep(1)

        # 查找并点击验证按钮
        button = page.ele('#js_verify')
        if button:
            button.click(by_js=None)
            time.sleep(2)

            # 保存点击后的截图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            page.get_screenshot(path='./data', name=f'{timestamp}_after_click.png', full_page=True)
            logger.debug("已保存点击后页面截图")
            return True
        else:
            logger.warning("未找到验证按钮")
            return False

    except Exception as e:
        logger.debug(f"模拟人工操作失败: {e}")
        return False


def _wait_for_verification_result(page, wait_timeout: int) -> bool:
    """等待验证结果"""
    logger.info("已点击验证按钮，等待页面跳转...")

    max_wait = min(wait_timeout, 9) // 3  # 最多等待3次，每次6秒
    wait_count = 0

    while wait_count < max_wait:
        current_url = page.url
        if "wappoc_appmsgcaptcha" not in current_url:
            logger.info(f"验证成功，已跳转到: {current_url}")
            return True
        time.sleep(3)
        wait_count += 1

    logger.warning("验证后等待超时，可能验证失败")
    return False


def _cleanup_verification_files(file_list: List[str]) -> None:
    """清理验证过程中产生的临时文件"""
    try:
        for filename in file_list:
            file_path = f"./data/{filename}"
            if os.path.exists(file_path):
                os.remove(file_path)
        logger.debug("临时截图和源代码已删除")
    except Exception as e:
        logger.warning(f"清理临时文件失败: {e}")

def _extract_info_by_css_selectors(page) -> Dict[str, Any]:
    """使用CSS选择器提取文章基本信息

    Args:
        page: DrissionPage页面对象

    Returns:
        包含提取结果的字典：
        {
            "success": bool,  # 是否所有字段都成功提取
            "title": str,
            "author": str,
            "publish_time": str
        }
    """
    logger.info("尝试使用CSS选择器获取文章信息...")

    # 使用ElementExtractor统一提取
    extractor = ElementExtractor()

    result = {
        "success": False,
        "title": extractor.extract_title(page),
        "author": extractor.extract_author(page),
        "publish_time": extractor.extract_publish_time(page)
    }

    # 检查是否所有字段都成功提取
    if result["title"] != "未知标题" and result["author"] != "未知作者" and result["publish_time"] != "未知时间":
        result["success"] = True
        logger.info(f"CSS选择器提取成功 - 标题: {result['title'][:30]}..., 作者: {result['author']}, 时间: {result['publish_time']}")
    else:
        missing_fields = []
        if result["title"] == "未知标题":
            missing_fields.append("标题")
        if result["author"] == "未知作者":
            missing_fields.append("作者")
        if result["publish_time"] == "未知时间":
            missing_fields.append("发布时间")
        logger.info(f"CSS选择器提取不完整，缺少: {', '.join(missing_fields)}")

    return result

def _extract_article_content(page) -> Dict[str, Any]:
    """从页面中提取文章内容

    Args:
        page: DrissionPage页面对象（可以是ChromiumPage或MixTab）

    Returns:
        文章内容字典
    """
    try:
        # 提取文章基本信息（标题、作者、发布时间）
        article_info = _extract_article_basic_info(page)

        # 滚动加载图片
        _scroll_and_load_images_dp(page)

        # 获取文章内容
        content = _extract_article_html_content(page)

        # 提取媒体资源（图片和视频）
        media_info = _extract_media_resources(content)

        # 提取biz参数
        biz = _extract_biz_parameter(page)

        return {
            **article_info,
            "content": content,
            **media_info,
            "biz": biz
        }

    except Exception as e:
        logger.error(f"提取文章内容失败: {e}")
        raise


def _extract_article_basic_info(page) -> Dict[str, str]:
    """提取文章基本信息（标题、作者、发布时间）"""
    # 第一步：尝试CSS选择器提取
    css_result = _extract_info_by_css_selectors(page)

    if css_result and css_result.get('success'):
        # CSS选择器提取成功
        title = css_result.get('title', '').strip() or "未知标题"
        author = css_result.get('author', '').strip() or "未知作者"
        publish_time = css_result.get('publish_time', '').strip() or "未知时间"

        logger.info(f"CSS选择器提取成功 - 标题: {title[:30]}..., 作者: {author}, 时间: {publish_time}")
        return {"title": title, "author": author, "publish_time": publish_time}

    # CSS选择器提取失败，使用截图识别作为备用方案
    try:
        logger.info("CSS选择器提取失败，尝试使用截图识别获取文章信息...")
        screenshot_result = _extract_info_by_screenshot(page)

        if screenshot_result and screenshot_result.get('success'):
            title = screenshot_result.get('title', '').strip() or "未知标题"
            author = screenshot_result.get('author', '').strip() or "未知作者"
            publish_time = screenshot_result.get('publish_time', '').strip() or "未知时间"

            logger.info(f"截图识别成功 - 标题: {title[:30]}..., 作者: {author}, 时间: {publish_time}")
            return {"title": title, "author": author, "publish_time": publish_time}
        else:
            logger.warning("截图识别也失败，使用默认值")
    except Exception as e:
        logger.error(f"截图识别出错，使用默认值: {e}")

    # 两种方法都失败，使用默认值
    return {"title": "未知标题", "author": "未知作者", "publish_time": "未知时间"}


def _extract_article_html_content(page) -> str:
    """提取文章HTML内容"""
    try:
        # 设置较短的超时时间
        content_element = page.ele("#js_content", timeout=3)
        if content_element:
            content = content_element.html
            return _ensure_content_visibility(content)
    except Exception as e:
        logger.error(f"获取文章内容时发生错误: {e}")

    return ""


def _extract_media_resources(content: str) -> Dict[str, List[str]]:
    """提取媒体资源（图片和视频）"""
    images = []
    videos = []

    try:
        # 确保内容经过完整的懒加载处理后再统计图片和视频
        final_content = _ensure_lazy_loading_processed(content)
        soup = BeautifulSoup(final_content, 'html.parser')

        # 提取图片
        for selector in WeChatCoreConfig.IMAGE_SELECTORS:
            img_elements = soup.select(selector)
            for img_element in img_elements:
                img_src = (img_element.get('src') or
                         img_element.get('data-src') or
                         img_element.get('data-original') or
                         img_element.get('data-lazy-src'))
                if img_src and img_src.startswith(('http://', 'https://')):
                    images.append(img_src)

        # 提取视频
        for selector in WeChatCoreConfig.VIDEO_SELECTORS:
            video_elements = soup.select(selector)
            for video_element in video_elements:
                video_src = (video_element.get('src') or
                           video_element.get('data-src') or
                           video_element.get('data-mpvid') or
                           video_element.get('data-vid'))
                if video_src:
                    videos.append(video_src)

        logger.info(f"成功提取 {len(images)} 张图片和 {len(videos)} 个视频（懒加载处理后）")
    except Exception as e:
        logger.warning(f"提取图片和视频列表失败: {e}")

    return {"images": images, "videos": videos}


def _extract_biz_parameter(page) -> str:
    """提取biz参数"""
    try:
        # 尝试从URL中提取
        current_url = page.url
        match = re.search(r'[?&]__biz=([^&]+)', current_url)
        if match:
            return match.group(1)

        # 从页面源码中提取
        page_source = page.html
        biz_match = re.search(r'var biz = "([^"]+)"', page_source)
        if biz_match:
            return biz_match.group(1)
    except Exception as e:
        logger.debug(f"提取biz参数失败: {e}")

    return ""

def get_article_content_with_download_browser(url: str, wait_timeout: int = 30) -> Dict[str, Any]:
    """使用2号浏览器（下载专用）获取文章内容

    Args:
        url: 文章链接
        wait_timeout: 等待超时时间

    Returns:
        文章内容字典
    """
    browser_manager = get_browser_manager()

    with browser_manager.download_browser_context() as browser:
        try:
            logger.info(f"使用2号浏览器获取文章内容: {url}")

            # 获取或创建标签页
            tab = _get_or_create_tab(browser)

            # 访问文章页面
            _navigate_to_article(tab, url, wait_timeout)

            # 检查并处理验证页面
            _handle_verification_if_needed(tab, wait_timeout)

            # 提取文章内容
            article_data = _extract_article_content(tab)
            article_data['url'] = url

            logger.info(f"文章内容获取成功: {article_data.get('title', 'Unknown')}")
            return article_data

        except Exception as e:
            logger.error(f"使用2号浏览器获取文章内容失败: {e}")
            raise


def _get_or_create_tab(browser):
    """获取或创建浏览器标签页"""
    tab = browser.latest_tab
    if not tab:
        tab = browser.new_tab()
    logger.info(f"使用标签页: {type(tab)}")
    return tab


def _navigate_to_article(tab, url: str, timeout: int):
    """导航到文章页面"""
    tab.get(url, timeout=timeout)
    time.sleep(3)  # 等待页面加载

    logger.debug(f"页面加载完成，当前URL: {tab.url}")
    logger.debug(f"页面标题: {tab.title}")


def _handle_verification_if_needed(tab, wait_timeout: int):
    """检查并处理验证页面"""
    needs_verification = _check_verification_needed(tab)

    if needs_verification:
        logger.info("检测到微信验证页面，尝试处理验证...")
        success = _handle_wechat_verification(tab, wait_timeout)
        if success:
            logger.info("验证处理成功，继续获取文章内容")
            # 重新记录页面状态
            tab.refresh()
            tab.wait.eles_loaded('.rich_media_title', timeout=5)
            logger.debug(f"验证后页面URL: {tab.url}")
            logger.debug(f"验证后页面标题: {tab.title}")
        else:
            logger.warning("验证处理失败，可能影响文章内容获取")
            raise Exception("微信验证失败")
    else:
        logger.info("页面正常，无需验证处理")


def _check_verification_needed(tab) -> bool:
    """检查是否需要处理验证页面"""
    for indicator in WeChatCoreConfig.VERIFICATION_INDICATORS:
        if indicator in tab.url or indicator in tab.html:
            logger.info(f"检测到验证页面特征: {indicator}")
            return True
    return False

def get_article_content(url: str, wait_timeout: int = 30,
                       cookies: List[Dict] = None, token: str = None) -> Dict:
    """获取单篇文章详细内容（兼容原有接口）

    Args:
        url: 文章链接
        wait_timeout: 等待超时时间
        cookies: cookies列表
        token: 访问token
    """
    # 如果没有提供cookies和token，使用2号浏览器（下载专用）
    if not cookies and not token:
        logger.info("未提供cookies和token，使用2号浏览器（下载专用）")
        return get_article_content_with_download_browser(url, wait_timeout)

    # 否则使用原有的方式（兼容性）
    page = None
    try:
        page = create_chromium_page()

        # 设置cookies
        _setup_cookies(page, cookies)

        # 访问文章页面并处理验证
        _navigate_and_handle_verification(page, url, wait_timeout)

        # 提取文章信息和内容
        return _extract_legacy_article_content(page, url, cookies)

    except Exception as e:
        logger.error(f"获取文章内容失败: {str(e)}")
        raise
    finally:
        if page:
            try:
                page.quit()
            except:
                pass


def _setup_cookies(page, cookies: List[Dict]):
    """设置cookies"""
    if cookies:
        page.get("https://mp.weixin.qq.com/")
        for cookie in cookies:
            try:
                page.set.cookies({
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain', '.weixin.qq.com'),
                    'path': cookie.get('path', '/'),
                })
            except Exception:
                continue


def _navigate_and_handle_verification(page, url: str, wait_timeout: int):
    """导航到页面并处理验证"""
    # 访问文章页面
    page.get(url, timeout=wait_timeout)
    time.sleep(3)  # 等待页面加载

    # 记录页面加载状态
    logger.debug(f"页面加载完成，当前URL: {page.url}")
    logger.debug(f"页面标题: {page.title}")

    # 检查并处理验证页面
    needs_verification = _check_verification_needed(page)

    if needs_verification:
        logger.info("检测到微信验证页面，尝试处理验证...")
        success = _handle_wechat_verification(page, wait_timeout)
        if success:
            logger.info("验证处理成功，继续获取文章内容")
            logger.debug(f"验证后页面URL: {page.url}")
            logger.debug(f"验证后页面标题: {page.title}")
        else:
            logger.warning("验证处理失败，可能影响文章内容获取")

def _extract_legacy_article_content(page, url: str, cookies: List[Dict]) -> Dict[str, Any]:
    """提取文章内容（兼容旧版本接口）"""
    # 使用ElementExtractor提取基本信息
    extractor = ElementExtractor()
    title = extractor.extract_title(page)
    author = extractor.extract_author(page)
    publish_time = extractor.extract_publish_time(page)

    # 简单滚动加载图片
    _scroll_and_load_images_dp(page)

    # 获取文章内容
    content = _process_lazy_images_with_requests(url, cookies)
    if not content:
        try:
            content_element = page.ele("#js_content")
            if content_element:
                content = content_element.html
                content = _ensure_content_visibility(content)
        except:
            content = ""

    # 确保内容经过完整的懒加载处理后再统计图片和视频
    final_content = _ensure_lazy_loading_processed(content)

    # 获取图片和视频列表（懒加载完成后统计）
    images = []
    videos = []
    try:
        soup = BeautifulSoup(final_content, 'html.parser')

        # 统计图片
        all_img_tags = soup.find_all('img')
        logger.debug(f"HTML中找到 {len(all_img_tags)} 个img标签")

        for img_tag in all_img_tags:
            img_src = img_tag.get('src') or img_tag.get('data-src')
            if img_src and img_src.startswith(('http://', 'https://', 'data:')):
                images.append(img_src)

        # 统计视频
        for selector in WeChatCoreConfig.VIDEO_SELECTORS:
            video_elements = soup.select(selector)
            for video_element in video_elements:
                # 提取视频相关信息
                video_src = (video_element.get('src') or
                           video_element.get('data-src') or
                           video_element.get('data-mpvid') or
                           video_element.get('data-vid'))
                if video_src:
                    videos.append(video_src)

        logger.info(f"成功提取 {len(images)} 张图片和 {len(videos)} 个视频（懒加载处理后）")
    except Exception as e:
        logger.warning(f"提取图片和视频列表失败: {e}")

    # 提取biz参数
    biz = extract_biz_from_page(page, url)

    return {
        "title": title,
        "author": author,
        "publish_time": publish_time,
        "content": content,
        "images": images,
        "videos": videos,
        "biz": biz,
        "url": url
    }

# ==================== 工具函数 ====================
def _ensure_lazy_loading_processed(content_html: str) -> str:
    """确保懒加载图片已完全处理，用于准确统计图片数量

    Args:
        content_html: 原始HTML内容

    Returns:
        处理后的HTML内容，所有data-src都转换为src
    """
    try:
        soup = BeautifulSoup(content_html, 'html.parser')

        # 处理所有懒加载图片
        for img_tag in soup.find_all('img'):
            # 如果有data-src但没有src，或者src为空，则使用data-src
            data_src = img_tag.get('data-src')
            current_src = img_tag.get('src')

            if data_src and (not current_src or current_src.strip() == ''):
                img_tag['src'] = data_src

            # 处理其他可能的懒加载属性
            for attr in ['data-original', 'data-lazy-src', 'data-original-src']:
                if img_tag.get(attr) and (not current_src or current_src.strip() == ''):
                    img_tag['src'] = img_tag[attr]
                    break

        return str(soup)

    except Exception as e:
        logger.warning(f"处理懒加载图片时出错: {e}")
        return content_html


def _ensure_content_visibility(content_html: str) -> str:
    """确保HTML内容可见，移除所有隐藏样式

    Args:
        content_html: 原始HTML内容

    Returns:
        处理后的可见HTML内容
    """
    try:
        soup = BeautifulSoup(content_html, 'html.parser')

        # 处理所有可能隐藏内容的元素
        for element in soup.find_all(True):  # 查找所有标签
            if 'style' in element.attrs:
                style = element.attrs['style']

                # 移除所有隐藏样式
                style = re.sub(r'visibility\s*:\s*hidden\s*;?', '', style)
                style = re.sub(r'opacity\s*:\s*0\s*;?', '', style)
                style = re.sub(r'display\s*:\s*none\s*;?', '', style)

                # 清理多余的分号和空格
                style = re.sub(r';\s*;', ';', style)
                style = style.strip('; ')

                if style:
                    element.attrs['style'] = style
                else:
                    del element.attrs['style']

        return str(soup)

    except Exception as e:
        logger.warning(f"处理内容可见性时出错: {e}")
        return content_html

def _process_lazy_images_with_requests(url: str, cookies: List[Dict] = None) -> str:
    """使用requests方式处理懒加载图片"""
    try:
        headers = {
            "User-Agent": WeChatCoreConfig.DEFAULT_USER_AGENT
        }

        if cookies:
            cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
            headers["Cookie"] = cookie_str

        response = requests.get(url, headers=headers, timeout=WeChatCoreConfig.DEFAULT_TIMEOUT)
        if response.status_code != 200:
            return ""

        soup = BeautifulSoup(response.text, 'html.parser')
        js_content_div = soup.find('div', {'id': 'js_content'})

        if not js_content_div:
            return ""

        # 确保内容可见
        js_content_div.attrs['style'] = 'visibility: visible; opacity: 1; display: block'

        # 处理懒加载图片
        for img_tag in js_content_div.find_all('img'):
            if 'data-src' in img_tag.attrs:
                img_tag['src'] = img_tag['data-src']
                del img_tag['data-src']
            img_tag.attrs['style'] = 'visibility: visible; opacity: 1'

        return _ensure_content_visibility(str(js_content_div))

    except Exception as e:
        logger.error(f"requests方式处理图片失败: {e}")
        return ""


def _scroll_and_load_images_dp(page: ChromiumPage) -> None:
    """简单的页面滚动以触发懒加载图片"""
    try:
        # 滚动到页面底部
        page.run_js("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)

        # 滚动回顶部
        page.run_js("window.scrollTo(0, 0);")
        time.sleep(1)

        # 强制加载懒加载图片
        page.run_js("""
            document.querySelectorAll('img[data-src]').forEach(img => {
                if (img.dataset.src) img.src = img.dataset.src;
            });
        """)

        logger.debug("页面滚动和图片加载完成")
    except Exception as e:
        logger.warning(f"滚动加载图片时出错: {e}")


def extract_biz_from_page(page: ChromiumPage, url: str) -> str:
    """从URL或页面源码中提取biz参数

    Args:
        page: ChromiumPage实例
        url: 文章URL

    Returns:
        提取的biz参数
    """
    # 尝试从URL中提取
    match = re.search(r'[?&]__biz=([^&]+)', url)
    if match:
        return match.group(1)

    # 从页面源码中提取
    try:
        page_source = page.html
        biz_match = re.search(r'var biz = "([^"]+)"', page_source)
        if biz_match:
            return biz_match.group(1)

        # 尝试其他可能的biz存储位置
        biz_match = re.search(r'window\.__biz=([^&]+)', page_source)
        if biz_match:
            return biz_match.group(1)

        return ""
    except Exception:
        return ""


# ==================== 截图识别功能 ====================
def get_article_info_by_screenshot(url: str) -> Dict[str, any]:
    """通过截图识别获取微信文章信息

    使用浏览器截图 + AI图像识别的方式提取文章的title、author、publish_time信息

    Args:
        url: 微信文章URL

    Returns:
        包含文章信息的字典
    """
    logger.info(f"开始通过截图识别获取文章信息: {url}")

    result = {
        "success": False,
        "title": "",
        "author": "",
        "publish_time": "",
        "error": ""
    }

    try:
        browser_manager = get_browser_manager()
        image_recognition = get_image_recognition_service()

        # 使用下载浏览器进行截图
        with browser_manager.download_browser_context() as browser:
            logger.info("浏览器已启动，正在访问文章...")

            tab = browser.new_tab()
            tab.get(url, timeout=WeChatCoreConfig.DEFAULT_TIMEOUT)
            time.sleep(5)  # 等待页面加载

            # 检查页面是否正确加载
            if _is_valid_wechat_page(tab):
                screenshot_path = _take_article_screenshot(tab)

                if screenshot_path:
                    # 使用图像识别服务分析截图
                    recognition_result = image_recognition.analyze_image_sync(str(screenshot_path))

                    # 清理截图文件
                    _cleanup_screenshot_file(screenshot_path)

                    if recognition_result:
                        result.update({
                            "success": True,
                            "title": recognition_result.get("title", "").strip(),
                            "author": recognition_result.get("author", "").strip(),
                            "publish_time": recognition_result.get("time", "").strip()
                        })
                        logger.info(f"提取成功 - 标题: {result['title'][:30]}..., 作者: {result['author']}, 时间: {result['publish_time']}")
                    else:
                        result["error"] = "图像识别失败"
                        logger.error("图像识别失败")
                else:
                    result["error"] = "截图失败"
            else:
                result["error"] = f"页面加载失败，当前URL: {tab.url}"
                logger.warning(f"页面可能未正确加载，当前URL: {tab.url}")

    except Exception as e:
        result["error"] = str(e)
        logger.error(f"截图识别过程中出错: {e}")

    return result


def _is_valid_wechat_page(tab) -> bool:
    """检查是否为有效的微信页面"""
    page_title = tab.title
    logger.info(f"页面标题: {page_title}")
    return "微信公众平台" in page_title or "mp.weixin.qq.com" in tab.url


def _take_article_screenshot(tab) -> Optional[Path]:
    """截取文章标题区域"""
    try:
        # 滚动到页面顶部确保截图完整
        tab.scroll.to_top()
        time.sleep(2)

        # 创建截图目录和文件名
        screenshots_dir = Path("./data")
        screenshots_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_filename = f"article_info_{timestamp}.png"
        screenshot_path = screenshots_dir / screenshot_filename

        # 截取标题区域
        tab.get_screenshot(
            path=str(screenshot_path),
            left_top=WeChatCoreConfig.SCREENSHOT_REGION[:2],
            right_bottom=WeChatCoreConfig.SCREENSHOT_REGION[2:]
        )

        logger.info(f"截图已保存: {screenshot_path}")
        return screenshot_path
    except Exception as e:
        logger.error(f"截图失败: {e}")
        return None


def _cleanup_screenshot_file(screenshot_path: Path):
    """清理截图文件"""
    try:
        if screenshot_path and os.path.exists(screenshot_path):
            os.remove(screenshot_path)
            logger.debug(f"截图文件已删除: {screenshot_path}")
    except Exception as delete_error:
        logger.warning(f"删除截图文件失败: {delete_error}")


def _extract_info_by_screenshot(page) -> Dict[str, any]:
    """使用截图识别提取文章信息的辅助函数"""
    screenshot_path = None
    try:
        screenshot_path = _take_article_screenshot(page)

        if not screenshot_path:
            return {"success": False}

        # 使用图像识别服务分析截图
        image_recognition = get_image_recognition_service()
        recognition_result = image_recognition.analyze_image_sync(str(screenshot_path))

        # 清理截图文件
        _cleanup_screenshot_file(screenshot_path)

        if recognition_result:
            return {
                "success": True,
                "title": recognition_result.get("title", ""),
                "author": recognition_result.get("author", ""),
                "publish_time": recognition_result.get("time", "")
            }
        else:
            return {"success": False}

    except Exception as e:
        logger.error(f"截图识别提取信息失败: {e}")

        # 确保在异常情况下也删除截图文件
        if screenshot_path:
            _cleanup_screenshot_file(screenshot_path)

        return {"success": False}