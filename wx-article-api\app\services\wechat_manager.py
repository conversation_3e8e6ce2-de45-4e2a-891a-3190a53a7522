#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信Token管理器
支持定时刷新和Telegram通知
"""

import os
import time
import json
import base64
from threading import Timer
from datetime import datetime
from typing import Optional, Dict, Callable, Any
from loguru import logger
from DrissionPage import ChromiumPage
from .wechat_core import extract_token_from_page,format_token
from .browser_manager import get_browser_manager
from .telegram_service import TelegramService
from ..core.config import get_settings


class CookieExpiry:
    """Cookie过期时间管理"""

    @staticmethod
    def check_expiry(cookies) -> Optional[Dict[str, Any]]:
        """检查Cookie过期时间

        Args:
            cookies: Cookie列表

        Returns:
            Dict: 包含过期信息的字典，如果无法确定则返回None
        """
        if not isinstance(cookies, list):
            return None

        for cookie in cookies:
            if not isinstance(cookie, dict):
                continue

            if cookie.get('name') == 'slave_sid' and 'expiry' in cookie:
                try:
                    expiry_time = float(cookie['expiry'])
                    remaining_time = expiry_time - time.time()

                    if remaining_time > 0:
                        return {
                            'expiry_timestamp': expiry_time,
                            'remaining_seconds': int(remaining_time),
                            'expiry_time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expiry_time)),
                            'is_valid': True
                        }
                    else:
                        return {
                            'expiry_timestamp': expiry_time,
                            'remaining_seconds': 0,
                            'expiry_time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expiry_time)),
                            'is_valid': False
                        }
                except ValueError:
                    logger.warning(f"slave_sid 的过期时间戳无效: {cookie['expiry']}")
                    break

        return None


class SessionManager:
    """会话管理类 - 统一管理Cookie、Token等所有会话数据"""

    def __init__(self, session_file: str = "data/session.json"):
        """初始化会话管理器

        Args:
            session_file: 会话文件路径
        """
        self.session_file = session_file
        os.makedirs(os.path.dirname(session_file), exist_ok=True)

    def save_session(self, cookies, token: str = "", expiry_info: Dict = None, additional_data: Dict = None):
        """保存完整的会话信息

        Args:
            cookies: Cookie列表
            token: Token字符串
            expiry_info: 过期信息字典
            additional_data: 额外数据字典
        """
        if isinstance(cookies, str):
            return
        # 过滤Cookie
        filtered_cookies = []
        for item in cookies:
            # 过滤掉QQ域名的cookie
            if item.get("domain") == ".qq.com":
                continue
            # 过滤掉无用的cookie
            if item.get('name') == "_clck":
                continue
            filtered_cookies.append(item)
        # 构建完整的会话数据
        session_data = {
            "cookies": filtered_cookies,
            "token": token,
            "expiry": expiry_info or {},
            "save_time": datetime.now().isoformat(),
            "cookie_count": len(filtered_cookies)
        }
        if additional_data:
            session_data.update(additional_data)
        with open(self.session_file, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, ensure_ascii=False, indent=2)
        logger.info(f"会话已保存到 {self.session_file} ({len(filtered_cookies)}个Cookie)")

    def load_cookies(self):
        """加载Cookie列表

        Returns:
            List: Cookie列表
        """
        try:
            session_data = self.load_session()
            if not session_data:
                return []

            cookies = session_data.get("cookies", [])
            new_items = []

            for item in cookies:
                # 移除domain字段，避免添加cookie时出错
                if "domain" in item:
                    del item["domain"]
                new_items.append(item)

            save_time = session_data.get("save_time", "未知")
            logger.info(f"从 {self.session_file} 加载了 {len(new_items)} 个Cookie (保存时间: {save_time})")
            return new_items

        except Exception as e:
            logger.error(f"加载Cookie失败: {str(e)}")
            return []

    def load_session(self):
        """加载完整的会话数据

        Returns:
            Dict: 会话数据字典
        """
        try:
            if not os.path.exists(self.session_file):
                logger.warning(f"会话文件不存在: {self.session_file}")
                return {}

            with open(self.session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)

            return session_data

        except Exception as e:
            logger.error(f"加载会话数据失败: {str(e)}")
            return {}

    def get_token(self, default: str = ""):
        """获取Token

        Args:
            default: 默认值

        Returns:
            str: Token字符串
        """
        session_data = self.load_session()
        return session_data.get("token", default)

    def get_cookies_str(self, default: str = ""):
        """获取Cookie字符串（动态生成）"""
        session_data = self.load_session()
        cookies = session_data.get("cookies", [])
        if not cookies:
            return default
        # 动态拼接字符串
        cookies_str = ""
        for cookie in cookies:
            cookies_str += f"{cookie['name']}={cookie['value']}; "
        return cookies_str

    def get_expiry_info(self):
        """获取过期信息

        Returns:
            Dict: 过期信息字典
        """
        session_data = self.load_session()
        return session_data.get("expiry", {})

    def clear(self):
        """清除会话文件"""
        try:
            if os.path.exists(self.session_file):
                os.remove(self.session_file)
                logger.info(f"已清除会话文件: {self.session_file}")
        except Exception as e:
            logger.error(f"清除会话文件失败: {str(e)}")

    def is_session_valid(self):
        """检查会话是否存在且有效

        Returns:
            bool: 会话是否有效
        """
        if not os.path.exists(self.session_file):
            return False

        session_data = self.load_session()
        if not session_data:
            return False

        expiry_info = session_data.get("expiry", {})
        return expiry_info.get("is_valid", False)


class WeChatTokenManager:
    """微信Token管理器，支持定时刷新和Telegram通知"""

    def __init__(self, telegram_service: Optional[TelegramService] = None):
        """初始化Token管理器

        Args:
            telegram_service: Telegram通知服务实例
        """
        self.settings = get_settings()
        self.driver = None
        self.session_data = None
        self.is_logged_in = False
        self.refresh_timer = None
        self.callback = None
        self.telegram = telegram_service


        # 确保数据目录存在
        data_dir = self.settings.get_absolute_path("data")
        data_dir.mkdir(exist_ok=True)

        # 确保日志目录存在
        logs_dir = self.settings.get_absolute_path("logs")
        logs_dir.mkdir(exist_ok=True)

        # 初始化会话管理器
        session_file = self.settings.get_absolute_path(self.settings.storage.session_file)
        self.session_manager = SessionManager(str(session_file)) # 统一路径来源

        # 配置参数
        self.refresh_interval = self.settings.wechat.token.refresh_interval
        self.auto_refresh = self.settings.wechat.token.auto_refresh

    def capture_wechat_qr_with_canvas(self, page: ChromiumPage, save_path: str) -> bool:
        """使用 Canvas 方法截取微信二维码（更可靠的方法）

        Args:
            page: DrissionPage 的 ChromiumPage 实例
            save_path: 保存路径

        Returns:
            是否截取成功
        """
        try:
            logger.info("使用 Canvas 方法截取微信二维码...")

            # 使用Canvas API直接绘制图片
            canvas_data = page.run_js("""
            return new Promise((resolve, reject) => {
                const img = document.querySelector('.login__type__container__scan__qrcode');
                if (!img) {
                    reject(new Error('未找到二维码元素'));
                    return;
                }

                if (!img.complete) {
                    reject(new Error('图片未加载完成'));
                    return;
                }

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                canvas.width = img.naturalWidth || img.width;
                canvas.height = img.naturalHeight || img.height;

                try {
                    ctx.drawImage(img, 0, 0);
                    const dataURL = canvas.toDataURL('image/png');
                    resolve(dataURL);
                } catch (error) {
                    reject(error);
                }
            });
            """)

            if canvas_data and canvas_data.startswith('data:image/png;base64,'):
                # 保存Canvas截取的图片
                image_data = base64.b64decode(canvas_data.split(',')[1])
                with open(save_path, 'wb') as f:
                    f.write(image_data)

                logger.info(f"Canvas截取成功，已保存到: {save_path}")
                return True
            else:
                logger.error("Canvas截取失败，未获取到有效数据")
                return False

        except Exception as e:
            logger.error(f"Canvas截取时出错: {e}")
            return False

    def _cleanup_driver(self):
        """安全地关闭和清理浏览器驱动实例"""
        if not self.driver:
            return
        try:
            # DrissionPage 推荐使用 close_tabs() 或 close()
            self.driver.close()
        except Exception as e:
            logger.warning(f"清理浏览器时出错: {e}")
        finally:
            self.driver = None

    def start_session(self, wait_time: Optional[int] = None,
                     qrcode_path: Optional[str] = None,
                     force_new_login: bool = False) -> Optional[Dict]:
        # 1. 尝试从文件恢复会话，如果有效则无需启动浏览器
        if not force_new_login:
            logger.info("检查是否存在有效的本地会话...")
            saved_session = self.session_manager.load_session()
            if self._validate_session_data(saved_session):
                logger.info("找到有效的本地会话，将直接启动定时刷新。")
                self.session_data = saved_session
                self.is_logged_in = True
                self.start_refresh_schedule()
                if self.telegram:
                    self.telegram.send_session_restored(
                        saved_session.get('token', '未获取'),
                        saved_session.get('expiry')
                    )
                return self.session_data
            else:
                logger.info("本地会话无效或不存在，将启动新的登录流程。")
        # 2. 将所有浏览器操作封装在 try...finally 中，确保浏览器被关闭
        self.driver = None  # 确保初始状态干净
        try:
            # --- 配置和常量定义 ---
            wait_time = wait_time or self.settings.wechat.login.wait_time
            qrcode_path_str = str(self.settings.get_absolute_path(qrcode_path or self.settings.wechat.login.qrcode_path))
            WX_LOGIN = "https://mp.weixin.qq.com/"
            WX_HOME = "https://mp.weixin.qq.com/cgi-bin/home"
            MIN_QR_CODE_SIZE = 364 # 避免魔法数字
            # --- 启动浏览器 ---
            logger.info("正在启动浏览器（登录专用）...")
            browser_manager = get_browser_manager()
            self.driver = browser_manager.get_login_browser()
            # 如果强制重新登录，清理浏览器状态
            if force_new_login:
                logger.info("强制重新登录，正在清除浏览器状态...")
                self.driver.get(WX_LOGIN)
                time.sleep(1)
                self.driver.clear_cache()
                logger.info("已清除浏览器缓存和Cookies。")
            # --- 打开登录页面 ---
            logger.info("正在打开微信公众平台登录页面...")
            self.driver.get(WX_LOGIN)
            self.driver.wait.load_start()
            time.sleep(2) # 等待页面稳定
            # --- 检查是否已登录 ---
            if WX_HOME in self.driver.url or "home" in self.driver.url:
                logger.info("检测到已存在登录状态，直接获取会话数据。")
            else:
                # --- 执行扫码登录流程 ---
                logger.info("等待页面图片加载...")
                self.driver.wait.ele_displayed('tag:img', timeout=30)
                time.sleep(2) # 额外等待图片渲染
                logger.info("正在截取二维码...")
                if not self.capture_wechat_qr_with_canvas(page=self.driver, save_path=qrcode_path_str):
                    raise Exception("使用Canvas截取二维码失败")
                if os.path.getsize(qrcode_path_str) <= MIN_QR_CODE_SIZE:
                    raise Exception("二维码图片获取失败，文件过小或无效")
                logger.info(f"二维码已保存至 {qrcode_path_str}，请扫码。")
                if self.telegram:
                    self.telegram.send_login_qrcode(qrcode_path_str, wait_time)
                logger.info(f"等待用户扫码登录，超时时间：{wait_time}秒...")
                start_wait_time = time.time()
                login_success = False
                while time.time() - start_wait_time < wait_time:
                    if WX_HOME in self.driver.url or "home" in self.driver.url:
                        logger.info("检测到URL变化，用户登录成功！")
                        login_success = True
                        break
                    time.sleep(2) # 每2秒检查一次
                if not login_success:
                    raise Exception(f"登录超时，在 {wait_time} 秒内未检测到成功登录。")
                    logger.info("用户登录成功！")
            # --- 登录成功后的统一处理 ---
            self.save_session_success() # 调用统一的保存方法
            if self.telegram:
                self.telegram.send_login_success(
                    self.session_data.get('token', '未获取'),
                    self.session_data.get('expiry'),
                    self.refresh_interval
                )
            # 清理二维码文件
            if os.path.exists(qrcode_path_str):
                try:
                    os.remove(qrcode_path_str)
                    logger.info("已清理二维码临时文件。")
                except Exception as e:
                    logger.warning(f"清理二维码文件失败: {e}")
            # 启动定时刷新
            self.start_refresh_schedule()
            return self.session_data
        except Exception as e:
            error_msg = str(e)
            logger.error(f"启动会话失败: {error_msg}", exc_info=True) # exc_info=True可以记录更详细的堆栈信息
            if self.telegram:
                self.telegram.send_login_failed(error_msg)
            return None
        finally:
            # 无论成功还是失败，都确保清理浏览器实例
            logger.info("登录流程结束，正在清理浏览器实例...")
            self._cleanup_driver()

    def _get_cookies_with_expiry(self, browser: ChromiumPage) -> list:
        try:
            logger.debug("尝试使用 CDP 'Network.getAllCookies' 命令获取 Cookies...")
            # DrissionPage 5.0+ 的 CDP 调用方式
            if hasattr(browser, 'run_cdp'):
                result = browser.run_cdp('Network.getAllCookies')
                cookies_from_cdp = result.get('cookies', [])
            # 兼容旧版或不同实现
            elif hasattr(browser, 'driver'): 
                cookies_from_cdp = browser.driver.execute_cdp_cmd('Network.getAllCookies', {}).get('cookies', [])
            else:
                raise RuntimeError("无法找到执行 CDP 命令的方法。")
            if not cookies_from_cdp:
                logger.warning("CDP 未返回任何 Cookies，将回退到 driver.cookies()。")
                return browser.cookies()
            logger.info("成功通过 CDP 获取到详细的 Cookie 信息。")
            formatted_cookies = []
            for cookie in cookies_from_cdp:
                if 'mp.weixin.qq.com' not in cookie.get('domain', ''):
                    continue
                if 'expires' in cookie:
                    cookie['expiry'] = cookie.pop('expires')
                formatted_cookies.append(cookie)
            return formatted_cookies
        except Exception as e:
            logger.error(f"通过 CDP 获取 Cookies 失败: {e}。将回退到 driver.cookies()。")
            return browser.cookies()

    def _update_and_save_session_from_browser(self, additional_data: Dict = None):
        if not self.driver:
            raise Exception("浏览器实例不存在，无法更新会话")
        try:
            token = extract_token_from_page(self.driver)
            cookies = self._get_cookies_with_expiry(self.driver)
            formatted_cookies = [
                {
                    'name': c.get('name', ''), 'value': c.get('value', ''),
                    'domain': c.get('domain', ''), 'path': c.get('path', '/'),
                    'secure': c.get('secure', False), 'httpOnly': c.get('httpOnly', False),
                    **({'expiry': c['expiry']} if 'expiry' in c else {})
                }
                for c in cookies
            ]
            cookie_expiry = CookieExpiry.check_expiry(formatted_cookies)
            self.session_manager.save_session(
                cookies=formatted_cookies,
                token=token or "",
                expiry_info=cookie_expiry,
                additional_data=additional_data
            )
            self.is_logged_in = cookie_expiry is not None and cookie_expiry.get('is_valid', False)
            self.session_data = {
                'cookies': formatted_cookies,
                'cookies_str': self.session_manager.get_cookies_str(),
                'token': token or "",
                'expiry': cookie_expiry,
                **(additional_data or {})
            }
            logger.info("会话信息已从浏览器更新并成功保存。")
        except Exception as e:
            logger.error(f"从浏览器更新并保存会话信息失败: {e}")
            self.is_logged_in = False
            raise # 将异常抛出，让调用方处理
        
    def save_session_success(self):
        """登录成功后保存会话信息"""
        try:
            # 只需调用统一的方法，并传入登录特定的附加数据
            additional_data = {
                'qr_code_path': self.settings.wechat.login.qrcode_path,
                'login_time': datetime.now().isoformat()
            }
            self._update_and_save_session_from_browser(additional_data)
            logger.info("登录成功，会话已保存。")
        except Exception as e:
            # 日志已在内部记录，这里只向上抛出异常
            raise e

    def refresh_session(self) -> bool:
        """刷新会话，并在每个关键点检查Cookie有效性"""
        # 1.【前置被动检查】在执行任何操作前，先检查内存中的会话是否已过期
        if not self.is_session_valid():
            self._handle_session_expiry("主动刷新前检测到Cookie已过期")
            return False
        self.driver = None
        try:
            logger.info("正在刷新会话（按需启动浏览器）...")
            browser_manager = get_browser_manager()
            self.driver = browser_manager.get_login_browser()
            if not self.driver:
                raise Exception("为刷新会话启动临时浏览器失败")
            self.driver.get("https://mp.weixin.qq.com/")
            time.sleep(3)
            # 2.【URL变化被动检查】这是最可靠的失效判断方式
            current_url = self.driver.url
            if "home" not in current_url:
                # 调用统一的失效处理流程
                self._handle_session_expiry("刷新时页面跳转，登录状态已失效")
                return False
            # 3.【主动检查】登录状态确认后，获取新Cookie并保存
            # 该方法内部会重新计算并保存新的失效时间
            self._update_and_save_session_from_browser(
                additional_data={'refresh_time': datetime.now().isoformat()}
            )
            logger.info("会话刷新成功！")
            if self.callback and self.session_data:
                self.callback(self.session_data)
            return True
        except Exception as e:
            error_msg = str(e)
            logger.error(f"刷新会话时发生异常: {error_msg}", exc_info=True)
            # 异常也可能意味着会话问题，执行部分清理
            self.is_logged_in = False
            self.stop_refresh_schedule()
            if self.telegram:
                self.telegram.send_refresh_failed(error_msg)
            if self.settings.wechat.token.auto_relogin_on_failure:
                self._auto_relogin_after_failure(error_msg)
            return False
        finally:
            logger.info("刷新操作完成，正在清理临时浏览器...")
            self._cleanup_driver()

    def stop_refresh_schedule(self):
        if self.refresh_timer:
            self.refresh_timer.cancel()
            self.refresh_timer = None
            logger.info("已停止定时刷新")

    def set_refresh_callback(self, callback: Callable[[Dict], None]):
        self.callback = callback

    def get_current_session(self) -> Optional[Dict]:
        return self.session_data

    def _validate_session_data(self, session_data: Dict) -> bool:
        if not session_data:
            logger.debug("验证失败：会话数据为空。")
            return False
        if not session_data.get('cookies') or not session_data.get('token'):
            logger.debug("验证失败：会话数据缺少 'cookies' 或 'token' 关键字段。")
            return False
        # 使用 CookieExpiry.check_expiry 进行判断
        expiry_info = CookieExpiry.check_expiry(session_data.get('cookies'))
        if expiry_info and expiry_info.get('is_valid'):
            remaining = expiry_info.get('remaining_seconds', 0)
            logger.debug(f"会话验证成功，剩余有效期: {remaining // 60} 分钟。")
            return True
        else:
            expiry_time_str = expiry_info.get('expiry_time') if expiry_info else '未知'
            logger.warning(f"会话验证失败：Cookie已过期或无法确定有效期。过期时间: {expiry_time_str}")
            return False

    def is_session_valid(self) -> bool:
        if not self.is_logged_in or not self.session_data:
            return False
        return self._validate_session_data(self.session_data)

    def _handle_session_expiry(self, reason: str):
        logger.warning(f"会话失效：{reason}。正在执行清理流程...")
        # 1. 状态标记
        self.is_logged_in = False
        # 2. 停止定时刷新
        self.stop_refresh_schedule()
        # 3. 通知机制
        if self.telegram:
            self.telegram.send_session_expired(reason)
        # 4. 清理会话文件
        self.session_manager.clear()
        # 5. 检查是否需要自动重新登录（代替直接抛出异常）
        if self.settings.wechat.token.auto_relogin_on_failure:
            logger.info("检测到会话失效，将尝试自动重新登录...")
            self._auto_relogin_after_failure(reason)
        else:
            # 如果不自动重连，可以考虑在这里做更柔和的处理，而不是让整个应用崩溃
            logger.error("自动重新登录已禁用，请手动干预。")

    def _auto_relogin_after_failure(self, error_msg: str):
        try:
            logger.info("开始自动重新登录流程...")
            if self.telegram:
                # 格式化字符串时使用 f-string 更直观
                message = (
                    f"🔄 <b>自动重新登录</b>\n\n"
                    f"会话刷新失败: {self.telegram._escape_html(error_msg)}\n"
                    f"正在尝试自动重新登录..."
                )
                self.telegram.send_message(message)
            self.stop_refresh_schedule()
            self._cleanup_driver()  # 确保任何残留的浏览器实例被关闭
            self.is_logged_in = False
            self.session_data = None
            logger.info("旧状态已清理，5秒后尝试重新登录...")
            time.sleep(5)
            # 启动新的登录会话，它会自己处理所有设置
            result = self.start_session(force_new_login=True)
            if result:
                logger.info("自动重新登录成功！")
                if self.telegram:
                    self.telegram.send_message("✅ <b>自动重新登录成功</b>\n\n会话已恢复正常")
            else:
                logger.error("自动重新登录失败")
                if self.telegram:
                    self.telegram.send_message("❌ <b>自动重新登录失败</b>\n\n请手动重新启动登录")
        except Exception as e:
            logger.error(f"自动重新登录过程中出错: {e}", exc_info=True)
            if self.telegram:
                self.telegram.send_message(f"❌ <b>自动重新登录异常</b>\n\n错误: {self.telegram._escape_html(str(e))}")

    def _schedule_next_refresh(self):
        """安排下一次的定时刷新任务"""
        if not self.is_logged_in or not self.auto_refresh or self.refresh_interval <= 0:
            return
        self.stop_refresh_schedule()
        self.refresh_timer = Timer(self.refresh_interval, self.schedule_refresh)
        self.refresh_timer.daemon = True
        self.refresh_timer.start()
        logger.info(f"已安排下一次刷新任务，将在 {self.refresh_interval // 60} 分钟后执行。")
    
    def schedule_refresh(self):
        try:
            refreshed = self.refresh_session()
        finally:
            self._schedule_next_refresh()
    
    def start_refresh_schedule(self):
        try:
            logger.info(f"启动定时刷新，间隔: {self.refresh_interval // 60} 分钟")
            self._schedule_next_refresh()
        except Exception as e:
            logger.error(f"启动定时刷新失败: {e}")
            self.is_logged_in = False

    def close(self):
        try:
            logger.info("正在关闭微信Token管理器...")
            # 1. 停止定时刷新
            self.stop_refresh_schedule()
            # 2. 清理浏览器实例
            self._cleanup_driver()
            # 3. 重置登录状态和会话数据
            self.is_logged_in = False
            self.session_data = None
            # 4. 清理回调函数
            self.callback = None
            logger.info("微信Token管理器已关闭")
        except Exception as e:
            logger.error(f"关闭Token管理器时出错: {e}")
            # 即使出错也要确保基本状态被重置
            self.is_logged_in = False
            self.session_data = None
            self.callback = None
