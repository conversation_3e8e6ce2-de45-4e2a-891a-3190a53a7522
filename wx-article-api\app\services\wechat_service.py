#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信公众号查询服务
"""

import json
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import requests
from loguru import logger

from ..core.config import get_settings
from ..models.schemas import AccountInfo, ArticleContent, ArticleInfo, VideoInfo
from ..services.file_service import FileService
from ..utils.time_filter import calculate_earliest_target_time, parse_time_filter
from .wechat_core import dateformat, get_article_content, get_articles, search_biz
from .wechat_manager import WeChatTokenManager


# ==================== 配置常量 ====================
class WeChatServiceConfig:
    """微信服务配置常量"""

    # API URLs
    APPMSG_URL = "https://mp.weixin.qq.com/cgi-bin/appmsg"
    MP_BASE_URL = "https://mp.weixin.qq.com/"

    # 默认配置
    DEFAULT_USER_AGENT = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    )
    DEFAULT_TIMEOUT = 15
    DEFAULT_COUNT = 5
    DEFAULT_SEARCH_LIMIT = 5
    DEFAULT_ARTICLE_LIMIT = 20
    BATCH_SEARCH_LIMIT = 50

    # 分页配置
    MAX_CONSECUTIVE_EMPTY_PAGES = 1
    DEFAULT_MAX_PAGES = 1000
    PAGE_DELAY = 3

    # HTTP请求头
    DEFAULT_HEADERS = {
        "Referer": "https://mp.weixin.qq.com/",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "X-Requested-With": "XMLHttpRequest"
    }

    # API错误码
    ERROR_CODES = {
        200013: "触发频率限制，请稍后再试",
        200003: "登录会话失效，需要重新登录",
    }


# ==================== 数据处理器 ====================
class ArticleProcessor:
    """文章数据处理器"""

    @staticmethod
    def create_article_info_from_appmsg(item: Dict, create_time: int = None, update_time: int = None) -> ArticleInfo:
        """从appmsgex数据创建ArticleInfo对象"""
        return ArticleInfo(
            title=item.get('title', ''),
            digest=item.get('digest', ''),
            author=item.get('author', ''),
            link=item.get('link', ''),
            cover=item.get('cover', ''),
            create_time=create_time,
            update_time=update_time,
            publish_time=dateformat(create_time) if create_time else None
        )

    @staticmethod
    def create_article_dict(item: Dict) -> Dict[str, Any]:
        """创建标准化的文章字典"""
        return {
            "id": item.get("aid"),
            "title": item.get("title", ""),
            "link": item.get("link", ""),
            "cover": item.get("cover", ""),
            "digest": item.get("digest", ""),
            "create_time": item.get("create_time", 0),
            "update_time": item.get("update_time", 0),
            "author": item.get("author", ""),
        }

    @staticmethod
    def format_article_time(timestamp: int) -> str:
        """格式化文章时间戳为字符串"""
        try:
            dt = datetime.fromtimestamp(int(timestamp))
            return dt.strftime('%Y/%m/%d %H:%M:%S')
        except (ValueError, TypeError):
            return 'N/A'

    @staticmethod
    def add_formatted_times(article: Dict) -> None:
        """为文章添加格式化的时间字段"""
        # 转换 create_time
        if article.get('create_time'):
            article['create_time_formatted'] = ArticleProcessor.format_article_time(article['create_time'])
        else:
            article['create_time_formatted'] = 'N/A'

        # 转换 update_time
        if article.get('update_time'):
            article['update_time_formatted'] = ArticleProcessor.format_article_time(article['update_time'])
        else:
            article['update_time_formatted'] = 'N/A'

    @staticmethod
    def deduplicate_articles_by_title(articles: List[Dict]) -> List[Dict]:
        """根据标题去重文章，保留create_time最新的"""
        title_map = {}

        for article in articles:
            title = article.get('title', '').strip()
            if not title:
                continue

            create_time = article.get('create_time', 0)

            if title not in title_map:
                title_map[title] = article
            else:
                # 比较 create_time，保留更新的文章
                existing_time = title_map[title].get('create_time', 0)
                if create_time > existing_time:
                    logger.info(f"发现重复标题文章，保留更新版本: {title}")
                    title_map[title] = article
                else:
                    logger.info(f"发现重复标题文章，跳过旧版本: {title}")

        # 转换为列表并按 create_time 降序排序
        processed_articles = list(title_map.values())
        processed_articles.sort(key=lambda x: x.get('create_time', 0), reverse=True)

        return processed_articles

# ==================== 主服务类 ====================
class WeChatService:
    """微信公众号查询服务"""

    def __init__(self, token_manager: WeChatTokenManager):
        """初始化服务

        Args:
            token_manager: 微信Token管理器实例
        """
        self.token_manager = token_manager
        self.settings = get_settings()
        self.config = WeChatServiceConfig()
        self.processor = ArticleProcessor()

    def _get_session_info(self) -> Tuple[str, str, str]:
        """获取当前会话信息

        Returns:
            (token, cookies_str, user_agent) 元组

        Raises:
            Exception: 当未登录或会话信息不完整时
        """
        session_data = self.token_manager.get_current_session()
        if not session_data:
            raise Exception("未登录或会话已过期，请先登录")

        token = session_data.get('token', '')
        cookies_str = session_data.get('cookies_str', '')

        if not token or not cookies_str:
            raise Exception("会话信息不完整，请重新登录")

        return token, cookies_str, self.config.DEFAULT_USER_AGENT

    def _create_request_headers(self, cookies_str: str, user_agent: str) -> Dict[str, str]:
        """创建请求头

        Args:
            cookies_str: Cookie字符串
            user_agent: 用户代理

        Returns:
            请求头字典
        """
        headers = self.config.DEFAULT_HEADERS.copy()
        headers.update({
            "Cookie": cookies_str,
            "User-Agent": user_agent
        })
        return headers

    # ==================== 公众号搜索相关方法 ====================
    def search_accounts(self, keyword: str, limit: int = None, offset: int = 0) -> List[AccountInfo]:
        """搜索公众号

        Args:
            keyword: 搜索关键词
            limit: 返回数量限制，默认使用配置值
            offset: 偏移量

        Returns:
            公众号信息列表
        """
        try:
            if limit is None:
                limit = self.config.DEFAULT_SEARCH_LIMIT

            token, cookies_str, user_agent = self._get_session_info()

            logger.info(f"搜索公众号: {keyword} (limit: {limit}, offset: {offset})")
            result = search_biz(
                kw=keyword,
                limit=limit,
                offset=offset,
                token=token,
                cookie=cookies_str,
                user_agent=user_agent
            )

            accounts = self._parse_account_search_result(result)
            logger.info(f"找到 {len(accounts)} 个公众号")
            return accounts

        except Exception as e:
            logger.error(f"搜索公众号失败: {e}")
            raise

    def find_account_by_name(self, account_name: str) -> Optional[AccountInfo]:
        """根据名称查找公众号（精确匹配）

        Args:
            account_name: 公众号名称

        Returns:
            公众号信息，如果未找到返回None
        """
        try:
            # 先搜索公众号
            accounts = self.search_accounts(account_name, limit=10)

            # 精确匹配名称
            for account in accounts:
                if account.nickname == account_name:
                    logger.info(f"找到公众号: {account_name} (fakeid: {account.fakeid})")
                    return account

            logger.warning(f"未找到公众号: {account_name}")
            return None

        except Exception as e:
            logger.error(f"查找公众号失败: {e}")
            raise

    def _parse_account_search_result(self, result: Dict) -> List[AccountInfo]:
        """解析公众号搜索结果

        Args:
            result: 搜索结果字典

        Returns:
            公众号信息列表
        """
        accounts = []
        if result and 'list' in result:
            for item in result['list']:
                account = AccountInfo(
                    fakeid=item.get('fakeid', ''),
                    nickname=item.get('nickname', ''),
                    alias=item.get('alias', ''),
                    round_head_img=item.get('round_head_img', ''),
                    service_type=item.get('service_type')
                )
                accounts.append(account)
        return accounts

    # ==================== 文章获取相关方法 ====================
    def get_account_articles(self, account_name: str, count: int = None) -> Tuple[Optional[AccountInfo], List[ArticleInfo]]:
        """获取公众号的文章列表

        Args:
            account_name: 公众号名称
            count: 获取文章数量，默认使用配置值

        Returns:
            (公众号信息, 文章列表) 元组
        """
        try:
            if count is None:
                count = self.config.DEFAULT_COUNT

            # 先查找公众号
            account = self.find_account_by_name(account_name)
            if not account:
                return None, []

            token, cookies_str, user_agent = self._get_session_info()

            logger.info(f"获取公众号文章: {account_name} (count: {count})")
            result = get_articles(
                faker_id=account.fakeid,
                token=token,
                cookie=cookies_str,
                user_agent=user_agent,
                count=count
            )

            articles = self._parse_article_list_result(result)
            logger.info(f"获取到 {len(articles)} 篇文章")
            return account, articles

        except Exception as e:
            logger.error(f"获取文章列表失败: {e}")
            raise

    def _parse_article_list_result(self, result: Dict) -> List[ArticleInfo]:
        """解析文章列表结果

        Args:
            result: API返回的结果字典

        Returns:
            文章信息列表
        """
        articles = []
        if not (result and 'publish_page' in result and 'publish_list' in result['publish_page']):
            return articles

        for item in result['publish_page']['publish_list']:
            # 处理文章信息
            publish_info = item.get('publish_info', {})
            if isinstance(publish_info, str):
                try:
                    publish_info = json.loads(publish_info)
                except:
                    publish_info = {}

            appmsgex = publish_info.get('appmsgex', [])
            if appmsgex:
                create_time = item.get('create_time')
                update_time = item.get('update_time')

                # 主文章
                main_article = appmsgex[0]
                article = self.processor.create_article_info_from_appmsg(
                    main_article, create_time, update_time
                )
                articles.append(article)

                # 副文章（如果有）
                for sub_article in appmsgex[1:]:
                    article = self.processor.create_article_info_from_appmsg(
                        sub_article, create_time, update_time
                    )
                    articles.append(article)

        return articles


    # ==================== 文章查找相关方法 ====================
    def find_article_by_title(self, title: str, account_name: Optional[str] = None) -> Optional[ArticleInfo]:
        """根据标题查找文章（完全匹配）

        Args:
            title: 文章标题
            account_name: 公众号名称（可选，用于缩小搜索范围）

        Returns:
            文章信息，如果未找到返回None
        """
        try:
            if account_name:
                # 在指定公众号中查找
                _, articles = self.get_account_articles(account_name, count=self.config.DEFAULT_ARTICLE_LIMIT)
                for article in articles:
                    if article.title == title:
                        logger.info(f"在公众号 {account_name} 中找到文章: {title}")
                        return article
            else:
                # TODO: 实现全局搜索功能
                logger.warning("暂不支持全局文章搜索，请指定公众号名称")
                return None

            logger.warning(f"未找到文章: {title}")
            return None

        except Exception as e:
            logger.error(f"查找文章失败: {e}")
            raise

    def find_articles_by_title_contains(self, title: str, account_name: Optional[str] = None) -> List[ArticleInfo]:
        """根据标题查找文章（包含匹配）

        Args:
            title: 文章标题关键词
            account_name: 公众号名称（可选，用于缩小搜索范围）

        Returns:
            匹配的文章列表
        """
        try:
            if not account_name:
                # TODO: 实现全局搜索功能
                logger.warning("暂不支持全局文章搜索，请指定公众号名称")
                return []

            # 在指定公众号中查找
            _, articles = self.get_account_articles(account_name, count=self.config.BATCH_SEARCH_LIMIT)
            matched_articles = []

            for article in articles:
                if title.lower() in article.title.lower():  # 不区分大小写的包含匹配
                    matched_articles.append(article)
                    logger.info(f"在公众号 {account_name} 中找到匹配文章: {article.title}")

            logger.info(f"找到 {len(matched_articles)} 篇包含'{title}'的文章")
            return matched_articles

        except Exception as e:
            logger.error(f"查找文章失败: {e}")
            raise

    # ==================== 文章下载相关方法 ====================
    def download_article_content(self, url: str) -> ArticleContent:
        """下载文章内容

        Args:
            url: 文章链接

        Returns:
            文章内容
        """
        try:
            logger.info(f"下载文章内容: {url}")

            # 获取当前会话信息
            session_data = self.token_manager.get_current_session()
            cookies = session_data.get('cookies', []) if session_data else []
            token = session_data.get('token', '') if session_data else ''

            # 使用DrissionPage方法获取文章内容
            logger.info("使用DrissionPage方法获取文章内容...")
            article_data = get_article_content(
                url=url,
                wait_timeout=self.settings.wechat.browser.timeout,
                cookies=cookies,
                token=token
            )

            # 创建文章内容对象
            article = self._create_article_content_from_data(article_data)
            logger.info(f"文章下载完成: {article.title}")
            return article

        except Exception as e:
            logger.error(f"下载文章内容失败: {e}")
            raise

    def download_article_by_title(self, title: str, account_name: Optional[str] = None) -> Optional[ArticleContent]:
        """根据标题下载文章内容（完全匹配）

        Args:
            title: 文章标题
            account_name: 公众号名称（可选）

        Returns:
            文章内容，如果未找到返回None
        """
        try:
            # 先查找文章
            article_info = self.find_article_by_title(title, account_name)
            if not article_info or not article_info.link:
                return None

            # 下载文章内容
            return self.download_article_content(article_info.link)

        except Exception as e:
            logger.error(f"根据标题下载文章失败: {e}")
            raise

    def _create_article_content_from_data(self, article_data: Dict) -> ArticleContent:
        """从文章数据创建ArticleContent对象

        Args:
            article_data: 文章数据字典

        Returns:
            ArticleContent对象
        """
        # 转换videos为VideoInfo对象列表
        video_objects = []
        for video_url in article_data.get('videos', []):
            video_objects.append(VideoInfo(url=video_url))

        return ArticleContent(
            title=article_data['title'],
            author=article_data['author'],
            publish_time=article_data['publish_time'],
            content=article_data['content'],
            images=article_data['images'],
            videos=video_objects,
            url=article_data['url'],
            biz=article_data['biz']
        )

    def download_articles_by_title_contains(self, title: str, account_name: Optional[str] = None, save_html: bool = True) -> List[ArticleContent]:
        """根据标题关键词下载多篇文章内容（包含匹配）

        Args:
            title: 文章标题关键词
            account_name: 公众号名称（可选）
            save_html: 是否自动保存为HTML文件

        Returns:
            下载的文章内容列表
        """
        try:
            # 查找匹配的文章
            matched_articles = self.find_articles_by_title_contains(title, account_name)
            if not matched_articles:
                logger.warning(f"未找到包含'{title}'的文章")
                return []

            logger.info(f"开始批量下载 {len(matched_articles)} 篇文章")
            downloaded_articles = []

            # 依次下载每篇文章
            for i, article_info in enumerate(matched_articles, 1):
                try:
                    logger.info(f"正在下载第 {i}/{len(matched_articles)} 篇文章: {article_info.title}")

                    # 下载文章内容
                    article_content = self.download_article_content(article_info.link)
                    if article_content:
                        downloaded_articles.append(article_content)

                        # 如果需要保存HTML文件
                        if save_html:
                            self._save_article_html(article_content)

                        logger.info(f"第 {i} 篇文章下载成功: {article_content.title}")
                    else:
                        logger.warning(f"第 {i} 篇文章下载失败: {article_info.title}")

                except Exception as download_error:
                    logger.error(f"下载第 {i} 篇文章时出错: {download_error}")
                    continue

            logger.info(f"批量下载完成，成功下载 {len(downloaded_articles)}/{len(matched_articles)} 篇文章")
            return downloaded_articles

        except Exception as e:
            logger.error(f"批量下载文章失败: {e}")
            raise

    def _save_article_html(self, article_content: ArticleContent) -> None:
        """保存文章为HTML文件

        Args:
            article_content: 文章内容对象
        """
        try:
            file_service = FileService()
            saved_path = file_service.save_article_as_html(article_content)
            logger.info(f"文章已保存: {saved_path}")
        except Exception as save_error:
            logger.warning(f"保存文章失败: {save_error}")

    # ==================== 批量文章获取相关方法 ====================
    def get_mp_articles_by_fakeid(self, faker_id: str, max_pages: int = None, time_filter: str = None) -> List[Dict]:
        """根据 faker_id 获取公众号全部文章

        Args:
            faker_id: 公众号的 faker_id
            max_pages: 最大页数限制，None表示获取所有文章直到没有更多
            time_filter: 时间过滤器，如果提供则在获取过程中进行过滤

        Returns:
            文章列表
        """
        try:
            # 获取会话信息和请求配置
            token, cookies_str, user_agent = self._get_session_info()
            headers = self._create_request_headers(cookies_str, user_agent)

            # 解析时间过滤器
            time_filter_config = self._parse_time_filter_config(time_filter)

            # 设置分页配置
            max_pages = self._normalize_max_pages(max_pages, faker_id)

            # 执行分页获取
            articles = self._fetch_articles_with_pagination(
                faker_id, token, headers, max_pages, time_filter_config
            )

            # 处理文章数据：时间格式转换和去重
            processed_articles = self._process_articles(articles)

            logger.info(f"获取完成，原始文章数: {len(articles)}, 处理后文章数: {len(processed_articles)}")
            return processed_articles

        except Exception as e:
            logger.error(f"获取公众号文章失败: {e}")
            raise

    def _parse_time_filter_config(self, time_filter: str) -> Dict:
        """解析时间过滤器配置

        Args:
            time_filter: 时间过滤器字符串

        Returns:
            时间过滤器配置字典
        """
        config = {
            'month_filters': [],
            'date_ranges': [],
            'earliest_target_time': None
        }

        if not time_filter:
            return config

        month_filters, date_ranges = parse_time_filter(time_filter)
        config['month_filters'] = month_filters
        config['date_ranges'] = date_ranges

        logger.info(f"应用时间过滤器: {time_filter}")
        logger.info(f"月份过滤器: {month_filters}")
        logger.info(f"日期范围: {date_ranges}")

        # 计算目标时间范围的最早时间戳，用于智能停止
        earliest_target_time = calculate_earliest_target_time(month_filters, date_ranges)
        if earliest_target_time:
            config['earliest_target_time'] = earliest_target_time
            earliest_dt = datetime.fromtimestamp(earliest_target_time)
            logger.info(f"目标时间范围最早时间: {earliest_dt.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"启用智能分页停止机制")

        return config

    def _normalize_max_pages(self, max_pages: int, faker_id: str) -> int:
        """标准化最大页数配置

        Args:
            max_pages: 用户指定的最大页数
            faker_id: 公众号ID

        Returns:
            标准化后的最大页数
        """
        if max_pages is None or max_pages == 0:
            max_pages = self.config.DEFAULT_MAX_PAGES
            logger.info(f"开始获取公众号全部文章...")
            logger.info(f"faker_id: {faker_id}")
            logger.info(f"获取模式: 自动获取所有文章")
        else:
            logger.info(f"开始获取公众号文章...")
            logger.info(f"faker_id: {faker_id}")
            logger.info(f"最大页数限制: {max_pages}")

        return max_pages

    def _fetch_articles_with_pagination(self, faker_id: str, token: str, headers: Dict,
                                      max_pages: int, time_filter_config: Dict) -> List[Dict]:
        """使用分页获取文章

        Args:
            faker_id: 公众号ID
            token: 访问token
            headers: 请求头
            max_pages: 最大页数
            time_filter_config: 时间过滤器配置

        Returns:
            文章列表
        """
        import requests
        import time

        articles = []
        consecutive_empty_pages = 0

        for page in range(max_pages):
            begin = page * self.config.DEFAULT_COUNT
            params = self._create_api_params(faker_id, token, begin)

            logger.info(f"正在获取第 {page + 1} 页 (begin={begin})...")

            try:
                # 发送请求
                response = requests.get(
                    self.config.APPMSG_URL,
                    headers=headers,
                    params=params,
                    verify=False,
                    timeout=self.config.DEFAULT_TIMEOUT
                )

                # 处理响应
                result = self._handle_api_response(response)
                if not result:
                    break

                # 获取文章列表
                app_msg_list = result.get('app_msg_list', [])
                if not app_msg_list:
                    consecutive_empty_pages += 1
                    logger.info(f"第 {page + 1} 页没有文章 (连续空页: {consecutive_empty_pages})")

                    if consecutive_empty_pages >= self.config.MAX_CONSECUTIVE_EMPTY_PAGES:
                        logger.info(f"连续 {consecutive_empty_pages} 页没有文章，认为已获取完毕")
                        break
                    continue

                # 重置连续空页计数
                consecutive_empty_pages = 0
                logger.info(f"获取到 {len(app_msg_list)} 篇文章")

                # 处理当前页文章
                page_articles, should_stop = self._process_page_articles(
                    app_msg_list, time_filter_config
                )
                articles.extend(page_articles)

                # 检查是否需要停止
                if should_stop:
                    break

                # 添加延时避免被限制
                if page < max_pages - 1:
                    logger.debug(f"等待 {self.config.PAGE_DELAY} 秒...")
                    time.sleep(self.config.PAGE_DELAY)

            except requests.exceptions.Timeout:
                logger.error("请求超时")
                break
            except requests.exceptions.RequestException as e:
                logger.error(f"网络错误: {str(e)}")
                break
            except Exception as e:
                logger.error(f"未知错误: {str(e)}")
                break

        return articles

    def _create_api_params(self, faker_id: str, token: str, begin: int) -> Dict:
        """创建API请求参数

        Args:
            faker_id: 公众号ID
            token: 访问token
            begin: 开始位置

        Returns:
            请求参数字典
        """
        return {
            "action": "list_ex",
            "begin": str(begin),
            "count": str(self.config.DEFAULT_COUNT),
            "fakeid": faker_id,
            "type": "9",
            "token": token,
            "lang": "zh_CN",
            "f": "json",
            "ajax": "1"
        }

    def _handle_api_response(self, response) -> Optional[Dict]:
        """处理API响应

        Args:
            response: HTTP响应对象

        Returns:
            解析后的响应数据，失败时返回None
        """
        import json

        if response.status_code != 200:
            logger.error(f"HTTP错误: {response.status_code}")
            return None

        try:
            result = response.json()
        except json.JSONDecodeError:
            logger.error("响应不是有效的JSON格式")
            logger.debug(f"响应内容: {response.text[:200]}...")
            return None

        # 检查响应状态
        base_resp = result.get('base_resp', {})
        ret_code = base_resp.get('ret', -1)

        if ret_code in self.config.ERROR_CODES:
            error_msg = self.config.ERROR_CODES[ret_code]
            logger.warning(error_msg)
            if ret_code == 200003:  # 登录失效
                raise Exception(error_msg)
            return None
        elif ret_code != 0:
            err_msg = base_resp.get('err_msg', '未知错误')
            logger.error(f"API错误 (code: {ret_code}): {err_msg}")
            return None

        return result

    def _process_page_articles(self, app_msg_list: List[Dict], time_filter_config: Dict) -> Tuple[List[Dict], bool]:
        """处理当前页的文章

        Args:
            app_msg_list: 文章列表
            time_filter_config: 时间过滤器配置

        Returns:
            (处理后的文章列表, 是否应该停止获取)
        """
        articles = []
        page_filtered_count = 0
        page_oldest_time = None

        month_filters = time_filter_config.get('month_filters', [])
        date_ranges = time_filter_config.get('date_ranges', [])
        earliest_target_time = time_filter_config.get('earliest_target_time')
        has_time_filter = bool(month_filters or date_ranges)

        for i, item in enumerate(app_msg_list):
            article = self.processor.create_article_dict(item)

            # 记录当前页最旧文章时间（用于智能停止）
            create_time = article.get('create_time', 0)
            if create_time and i == len(app_msg_list) - 1:  # 最后一篇文章（最旧）
                page_oldest_time = create_time

            # 应用时间过滤器
            if has_time_filter and not self._matches_time_filter(article, month_filters, date_ranges):
                continue

            articles.append(article)
            page_filtered_count += 1

            # 显示文章标题
            title = article["title"][:50] + "..." if len(article["title"]) > 50 else article["title"]
            logger.debug(f"    - {title}")

        if has_time_filter:
            logger.info(f"本页过滤后文章数: {page_filtered_count}/{len(app_msg_list)}")

        # 智能分页停止检查
        should_stop = False
        if earliest_target_time and page_oldest_time and page_oldest_time < earliest_target_time:
            oldest_dt = datetime.fromtimestamp(page_oldest_time)
            target_dt = datetime.fromtimestamp(earliest_target_time)
            logger.info(f"智能停止：当前页最旧文章时间 {oldest_dt.strftime('%Y-%m-%d %H:%M:%S')} 早于目标范围 {target_dt.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"已获取 {len(articles)} 篇符合条件的文章，停止继续获取")
            should_stop = True

        return articles, should_stop

    def _matches_time_filter(self, article: Dict, month_filters: List, date_ranges: List) -> bool:
        """检查文章是否匹配时间过滤器

        Args:
            article: 文章字典
            month_filters: 月份过滤器列表
            date_ranges: 日期范围过滤器列表

        Returns:
            是否匹配
        """
        create_time = article.get('create_time', 0)
        if not create_time:
            return False

        try:
            dt = datetime.fromtimestamp(int(create_time))
            article_date = dt.date()
            article_year = dt.year
            article_month = dt.month

            # 检查月份过滤器
            if (article_year, article_month) in month_filters:
                return True

            # 检查日期范围过滤器
            if date_ranges:
                for start_date, end_date in date_ranges:
                    if start_date.date() <= article_date <= end_date.date():
                        return True

            return False

        except (ValueError, TypeError):
            # 时间格式错误，跳过
            return False

    # ==================== 其他辅助方法 ====================
    def get_first_page_articles(self, account_name: str = None, fakeid: str = None) -> Tuple[Optional[AccountInfo], List[Dict], str]:
        """获取公众号第一页文章

        Args:
            account_name: 公众号名称
            fakeid: 公众号fakeid

        Returns:
            (公众号信息, 文章列表, 实际使用的fakeid) 元组
        """
        try:
            account_info, actual_fakeid = self._resolve_account_info(account_name, fakeid)

            # 获取第一页文章（max_pages=1）
            logger.info(f"获取公众号第一页文章: fakeid={actual_fakeid}")
            articles = self.get_mp_articles_by_fakeid(
                faker_id=actual_fakeid,
                max_pages=1  # 只获取第一页
            )

            logger.info(f"成功获取第一页文章，共 {len(articles)} 篇")
            return account_info, articles, actual_fakeid

        except Exception as e:
            logger.error(f"获取公众号第一页文章失败: {e}")
            raise

    def _resolve_account_info(self, account_name: str, fakeid: str) -> Tuple[Optional[AccountInfo], str]:
        """解析账号信息

        Args:
            account_name: 公众号名称
            fakeid: 公众号fakeid

        Returns:
            (公众号信息, 实际使用的fakeid) 元组
        """
        account_info = None
        actual_fakeid = fakeid

        # 如果提供了account_name，需要先查找公众号信息
        if account_name and account_name.strip():
            logger.info(f"通过公众号名称查找: {account_name}")
            account_info = self.find_account_by_name(account_name.strip())
            if not account_info:
                logger.warning(f"未找到公众号: {account_name}")
                raise ValueError(f"未找到公众号: {account_name}")
            actual_fakeid = account_info.fakeid
            logger.info(f"找到公众号: {account_info.nickname} (fakeid: {actual_fakeid})")
        elif fakeid and fakeid.strip():
            # 如果只提供了fakeid，尝试通过搜索获取公众号信息（可选）
            actual_fakeid = fakeid.strip()
            logger.info(f"使用提供的fakeid: {actual_fakeid}")
        else:
            raise ValueError("必须提供account_name或fakeid")

        return account_info, actual_fakeid

    def _process_articles(self, articles: List[Dict]) -> List[Dict]:
        """处理文章数据：时间格式转换和去重

        Args:
            articles: 原始文章列表

        Returns:
            处理后的文章列表
        """
        # 添加格式化时间字段
        for article in articles:
            self.processor.add_formatted_times(article)

        # 去重处理
        processed_articles = self.processor.deduplicate_articles_by_title(articles)

        return processed_articles
