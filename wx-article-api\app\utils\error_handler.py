#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用错误处理工具
"""

from functools import wraps
from fastapi import HTTPException
from loguru import logger
from ..models.schemas import ErrorResponse, StatusCode


def handle_api_errors(operation_name: str):
    """API错误处理装饰器
    
    Args:
        operation_name: 操作名称，用于日志记录
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except HTTPException:
                # 重新抛出HTTP异常
                raise
            except Exception as e:
                logger.error(f"{operation_name}失败: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=ErrorResponse(
                        message=f"{operation_name}失败: {str(e)}",
                        error_code=StatusCode.INTERNAL_ERROR
                    ).model_dump()
                )
        return wrapper
    return decorator


def handle_service_errors(operation_name: str, raise_exception: bool = True):
    """服务层错误处理装饰器
    
    Args:
        operation_name: 操作名称，用于日志记录
        raise_exception: 是否重新抛出异常
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"{operation_name}失败: {e}")
                if raise_exception:
                    raise Exception(f"{operation_name}失败: {str(e)}")
                return None
        return wrapper
    return decorator


def log_and_ignore_errors(operation_name: str, default_return=None):
    """记录错误但不抛出异常的装饰器
    
    Args:
        operation_name: 操作名称，用于日志记录
        default_return: 发生错误时的默认返回值
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.warning(f"{operation_name}失败: {e}")
                return default_return
        return wrapper
    return decorator
