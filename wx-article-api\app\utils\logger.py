#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置模块
"""

import sys
from loguru import logger
from ..core.config import get_settings

def setup_logger():
    """设置日志配置"""
    settings = get_settings()

    # 移除默认处理器
    logger.remove()

    # 控制台输出
    logger.add(
        sys.stdout,
        level=settings.logging.level,
        format=settings.logging.format,
        colorize=True
    )

    # 文件输出
    log_file = settings.get_absolute_path(settings.logging.file)
    log_file.parent.mkdir(exist_ok=True)

    logger.add(
        str(log_file),
        level=settings.logging.level,
        format=settings.logging.format,
        rotation=settings.logging.max_size,
        retention=settings.logging.backup_count,
        encoding="utf-8"
    )

    logger.info("日志系统初始化完成")

# 在模块导入时自动设置日志
setup_logger()
