#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时目录管理工具
用于管理2号浏览器的临时目录，确保资源正确清理
"""

import os
import shutil
import uuid
import atexit
from pathlib import Path
from loguru import logger
from contextlib import contextmanager

class TempDirectoryManager:
    """临时目录管理器"""

    def __init__(self, base_dir: str = "./data"):
        """
        初始化临时目录管理器
        
        Args:
            base_dir: 基础目录，临时目录将在此目录下创建
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        self._temp_dirs = set()  # 跟踪所有创建的临时目录

        # 注册程序退出时的清理函数
        atexit.register(self.cleanup_all)

    def create_temp_dir(self, prefix: str = "browser_temp_") -> str:
        """
        创建临时目录
        
        Args:
            prefix: 目录名前缀
            
        Returns:
            临时目录的绝对路径
        """
        try:
            # 生成唯一的目录名
            unique_id = str(uuid.uuid4())[:8]
            temp_dir_name = f"{prefix}{unique_id}"
            temp_dir_path = self.base_dir / temp_dir_name

            # 创建目录
            temp_dir_path.mkdir(parents=True, exist_ok=True)
            temp_dir_str = str(temp_dir_path.absolute())

            # 记录临时目录
            self._temp_dirs.add(temp_dir_str)

            logger.info(f"创建临时目录: {temp_dir_str}")
            return temp_dir_str

        except Exception as e:
            logger.error(f"创建临时目录失败: {e}")
            raise

    def cleanup_temp_dir(self, temp_dir: str) -> bool:
        """
        清理指定的临时目录
        
        Args:
            temp_dir: 要清理的临时目录路径
            
        Returns:
            是否清理成功
        """
        try:
            if temp_dir and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                self._temp_dirs.discard(temp_dir)
                logger.info(f"已清理临时目录: {temp_dir}")
                return True
            else:
                logger.warning(f"临时目录不存在或已被清理: {temp_dir}")
                return False

        except Exception as e:
            logger.error(f"清理临时目录失败 {temp_dir}: {e}")
            return False

    def cleanup_all(self):
        """清理所有临时目录"""
        logger.info("开始清理所有临时目录...")

        for temp_dir in list(self._temp_dirs):
            self.cleanup_temp_dir(temp_dir)

        logger.info("临时目录清理完成")

    @contextmanager
    def temp_directory(self, prefix: str = "browser_temp_"):
        """
        上下文管理器，自动管理临时目录的创建和清理
        
        Args:
            prefix: 目录名前缀
            
        Yields:
            临时目录路径
        """
        temp_dir = None
        try:
            temp_dir = self.create_temp_dir(prefix)
            yield temp_dir
        finally:
            if temp_dir:
                self.cleanup_temp_dir(temp_dir)


# 全局临时目录管理器实例
_temp_manager = None


def get_temp_manager() -> TempDirectoryManager:
    """获取全局临时目录管理器实例"""
    global _temp_manager
    if _temp_manager is None:
        _temp_manager = TempDirectoryManager()
    return _temp_manager


def create_temp_browser_dir(prefix: str = "browser_temp_") -> str:
    """
    创建临时浏览器目录的便捷函数
    
    Args:
        prefix: 目录名前缀
        
    Returns:
        临时目录路径
    """
    return get_temp_manager().create_temp_dir(prefix)


def cleanup_temp_browser_dir(temp_dir: str) -> bool:
    """
    清理临时浏览器目录的便捷函数
    
    Args:
        temp_dir: 要清理的临时目录路径
        
    Returns:
        是否清理成功
    """
    return get_temp_manager().cleanup_temp_dir(temp_dir)


@contextmanager
def temp_browser_directory(prefix: str = "browser_temp_"):
    """
    临时浏览器目录的上下文管理器
    
    Args:
        prefix: 目录名前缀
        
    Yields:
        临时目录路径
    """
    with get_temp_manager().temp_directory(prefix) as temp_dir:
        yield temp_dir
