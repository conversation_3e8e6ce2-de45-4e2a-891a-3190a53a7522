#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间过滤工具模块
"""

from datetime import datetime
from typing import List, Dict, Tuple, Optional


def parse_single_time(time_str: str) -> Tuple[int, int, int]:
    """
    解析单个时间字符串
    
    Args:
        time_str: 时间字符串，如 '2025', '2025/06' 或 '2025/06/01'
        
    Returns:
        (年, 月, 日) 元组，月为0表示整年，日为0表示整月
    """
    time_str = time_str.strip()
    parts = time_str.split('/')
    
    if len(parts) == 1:
        # 只有年份：'2025'
        return int(parts[0]), 0, 0
    elif len(parts) == 2:
        # 年月：'2025/06'
        return int(parts[0]), int(parts[1]), 0
    elif len(parts) == 3:
        # 年月日：'2025/06/01'
        return int(parts[0]), int(parts[1]), int(parts[2])
    else:
        raise ValueError(f"无效的时间格式: {time_str}")


def parse_time_filter(time_filter: str) -> Tuple[List[Tuple[int, int]], List[Tuple]]:
    """
    解析时间过滤器，返回月份列表和日期范围列表
    
    Args:
        time_filter: 时间过滤字符串
        
    Returns:
        (月份列表, 日期范围列表) 元组
        - 月份列表: [(年, 月), ...] 用于月份级别的过滤
        - 日期范围列表: [(start_date, end_date), ...] 用于日期级别的过滤
    """
    if not time_filter:
        return [], []
    
    month_filters = []
    date_ranges = []
    
    # 处理逗号分隔的多个时间段
    segments = [s.strip() for s in time_filter.split(',')]
    
    for segment in segments:
        if '-' in segment:
            # 处理范围
            start, end = segment.split('-')
            start_year, start_month, start_day = parse_single_time(start.strip())
            end_year, end_month, end_day = parse_single_time(end.strip())
            
            # 检查是否是日期级别的范围
            if start_day > 0 and end_day > 0:
                # 日期范围：'2025/06/01-2025/06/27'
                start_date = datetime(start_year, start_month, start_day)
                end_date = datetime(end_year, end_month, end_day)
                date_ranges.append((start_date, end_date))
            else:
                # 月份范围：'2025/06-2025/08'
                # 如果是整年范围，转换为具体月份
                if start_month == 0:
                    start_month = 1
                if end_month == 0:
                    end_month = 12
                
                # 生成范围内的所有月份
                current_year, current_month = start_year, start_month
                while (current_year, current_month) <= (end_year, end_month):
                    month_filters.append((current_year, current_month))
                    current_month += 1
                    if current_month > 12:
                        current_month = 1
                        current_year += 1
        else:
            # 处理单个时间
            year, month, day = parse_single_time(segment)
            
            if day > 0:
                # 单个日期：'2025/06/01'
                target_date = datetime(year, month, day)
                date_ranges.append((target_date, target_date))
            elif month == 0:
                # 整年：'2025'
                for m in range(1, 13):
                    month_filters.append((year, m))
            else:
                # 单个月份：'2025/06'
                month_filters.append((year, month))
    
    return month_filters, date_ranges


def filter_articles_by_time(articles: List[Dict], time_filter: str) -> List[Dict]:
    """
    根据时间过滤器过滤文章
    
    Args:
        articles: 文章列表
        time_filter: 时间过滤字符串
        
    Returns:
        过滤后的文章列表
    """
    # 解析时间过滤器
    month_filters, date_ranges = parse_time_filter(time_filter)
    if not month_filters and not date_ranges:
        return articles
    
    filtered_articles = []
    
    for article in articles:
        create_time = article.get('create_time', 0)
        if not create_time:
            continue
            
        # 转换时间戳为datetime
        try:
            dt = datetime.fromtimestamp(int(create_time))
            article_date = dt.date()
            article_year = dt.year
            article_month = dt.month
            
            # 检查是否匹配
            matched = False
            
            # 检查月份过滤器
            if (article_year, article_month) in month_filters:
                matched = True
            
            # 检查日期范围过滤器
            if not matched and date_ranges:
                for start_date, end_date in date_ranges:
                    if start_date.date() <= article_date <= end_date.date():
                        matched = True
                        break
            
            if matched:
                filtered_articles.append(article)
                
        except (ValueError, TypeError):
            # 时间格式错误，跳过
            continue
    
    return filtered_articles


def calculate_earliest_target_time(month_filters: List[Tuple[int, int]], date_ranges: List[Tuple]) -> Optional[int]:
    """
    计算目标时间范围的最早时间戳，用于智能分页停止
    
    Args:
        month_filters: 月份过滤器列表 [(年, 月), ...]
        date_ranges: 日期范围列表 [(start_date, end_date), ...]
        
    Returns:
        最早时间戳，如果无法确定则返回None
    """
    earliest_timestamps = []
    
    try:
        # 处理月份过滤器
        for year, month in month_filters:
            # 月份的第一天
            earliest_timestamps.append(int(datetime(year, month, 1).timestamp()))
        
        # 处理日期范围过滤器
        for start_date, end_date in date_ranges:
            earliest_timestamps.append(int(start_date.timestamp()))
        
        # 返回最早的时间戳
        if earliest_timestamps:
            return min(earliest_timestamps)
        
    except Exception:
        pass
    
    return None
