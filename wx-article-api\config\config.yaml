# 微信公众号文章查询API配置文件

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000
  debug: false
  reload: false

# 微信配置
wechat:
  # 登录配置
  login:
    wait_time: 120  # 扫码等待时间（秒）
    qrcode_path: "data/wx_qrcode.png"  # 二维码保存路径
  
  # Token刷新配置
  token:
    refresh_interval: 3600  # 自动刷新间隔（秒），0表示不自动刷新
    auto_refresh: true  # 是否启用自动刷新
    auto_relogin_on_failure: true  # 刷新失败时自动重新登录
    
  # 浏览器配置
  browser:
    timeout: 30  # 页面加载超时时间
    implicit_wait: 10  # 隐式等待时间

    # 双浏览器架构配置
    dual_browser:
      enabled: true  # 启用双浏览器架构

      # 1号浏览器（登录专用）
      login_browser:
        port: 9111  # 调试端口
        data_dir: "data/browser_login"  # 固定数据目录
        persistent: true  # 持久化存储

      # 2号浏览器（下载专用）
      download_browser:
        port_start: 9222  # 起始端口
        data_dir_prefix: "data/browser_temp_"  # 临时目录前缀
        auto_cleanup: true  # 自动清理临时目录

  # 图像识别配置
  image_recognition:
    # Cloudflare Worker API配置
    worker_url: "https://ocr.ooxx.gq//api/recognize/base64"  # Worker API地址（固定不变）

    # authorization配置
    authorization: "sk-VemfapgJYzIRzKtt2RiP8UbfjKbbtVMp"

    # 自定义提示词（固定不变）
    custom_prompt: |
      请提取图片中的文字，并以json返回，禁止回复多于文字，格式如下：
      {
          title: 'xxx'(图片中字体最大的文字，一般只有一行，如果有两行，请将第二行的文字拼接到第一行文字的后面)
          temp: ['xx','xx','yyyy年mm月dd日','xx'](title下一行的文字中，提取空格之间的文字{最左边的第一组文字请舍弃}，剩余文字共有4组，分别代表作者的名字，公众号名称，文章的发布日期{只需要*年*月*日}，所在城市或者省份)
      }

    # API请求配置
    timeout: 90  # 请求超时时间（秒）

# Telegram通知配置
telegram:
  enabled: true
  bot_token: "**********:AAGwCzR2dV42AFdWmnFnPIBG8hJrNbiBLJk"  # 请填入您的Telegram Bot Token
  chat_id: "979948150"    # 请填入您的Telegram Chat ID
  
# 数据存储配置
storage:
  # Session数据存储
  session_file: "data/session.json"
  
  # 文章缓存配置
  cache:
    enabled: true
    expire_hours: 24  # 缓存过期时间（小时）
    max_size: 1000    # 最大缓存条目数

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "logs/app.log"
  max_size: "10MB"
  backup_count: 5
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# API配置
api:
  # 请求限制
  rate_limit:
    enabled: true
    requests_per_minute: 60
    
  # 响应配置
  response:
    timeout: 30  # API响应超时时间
    max_articles: 20  # 单次查询最大文章数量

# 安全配置
security:
  # 允许的来源（CORS）
  allowed_origins:
    - "*"

  # 允许的方法
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"

  # 允许的头部
  allowed_headers:
    - "*"
