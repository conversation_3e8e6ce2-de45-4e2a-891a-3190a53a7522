#!/bin/bash

echo "微信公众号文章查询API - 依赖安装脚本"
echo "========================================"

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

echo "✅ Python3 已安装: $(python3 --version)"

# 升级pip
echo "📦 升级pip..."
python3 -m pip install --upgrade pip

# 安装核心依赖
echo "📦 安装核心依赖包..."

# 逐个安装，确保每个都成功
packages=(
    "fastapi"
    "uvicorn"
    "pydantic"
    "requests"
    "beautifulsoup4"
    "DrissionPage"
    "Pillow"
    "pyyaml"
    "python-dotenv"
    "pytz"
    "loguru"
    "lxml"
    "python-multipart"
)

failed_packages=()

for package in "${packages[@]}"; do
    echo "安装 $package..."
    if pip install "$package"; then
        echo "✅ $package 安装成功"
    else
        echo "❌ $package 安装失败"
        failed_packages+=("$package")
    fi
done

# 检查安装结果
if [ ${#failed_packages[@]} -eq 0 ]; then
    echo ""
    echo "🎉 所有依赖包安装成功！"
    echo ""
    echo "现在可以启动应用："
    echo "python3 main.py"
    echo ""
    echo ""
    echo "⚠️ 以下包安装失败："
    for package in "${failed_packages[@]}"; do
        echo "  - $package"
    done
    echo ""
    echo "请手动安装失败的包："
    echo "pip install ${failed_packages[*]}"
fi
