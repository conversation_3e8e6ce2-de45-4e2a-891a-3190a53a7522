@echo off
chcp 65001 > nul
echo 手动安装依赖包
echo ================

:: 激活虚拟环境（如果存在）
if exist "venv\Scripts\activate.bat" (
    echo 激活虚拟环境...
    call venv\Scripts\activate.bat
)

:: 升级pip
echo 升级pip...
python -m pip install --upgrade pip

:: 逐个安装核心依赖
echo 安装核心依赖...
pip install fastapi
pip install uvicorn
pip install pydantic
pip install requests
pip install beautifulsoup4
pip install DrissionPage
pip install Pillow
pip install pyyaml
pip install python-dotenv
pip install pytz
pip install loguru
pip install lxml

pip install python-multipart

echo 依赖安装完成！
pause
