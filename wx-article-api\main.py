#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信公众号文章查询及下载API
主入口文件
"""

import uvicorn
from app.core.app import create_app
from app.core.config import get_settings

def main():
    """启动应用"""
    settings = get_settings()
    app = create_app()
    
    uvicorn.run(
        app,
        host=settings.server.host,
        port=settings.server.port,
        reload=settings.server.reload,
        log_level=settings.logging.level.lower()
    )

if __name__ == "__main__":
    main()
