@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo WeChat Public Account Article Query API Startup Script
echo ================================

:: Check if Python is installed
python --version >nul 2>&1
if !errorlevel! neq 0 (
    echo Error: Python not found, please install Python 3.8+
    pause
    exit /b 1
)

:: Check virtual environment
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if !errorlevel! neq 0 (
        echo Error: Failed to create virtual environment
        pause
        exit /b 1
    )
)

:: Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

:: Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

:: Install dependencies
echo Installing dependencies...
pip install -r requirements.txt
if !errorlevel! neq 0 (
    echo Trying to install core dependencies individually...
    pip install fastapi uvicorn pydantic requests beautifulsoup4 DrissionPage Pillow pyyaml python-dotenv pytz loguru lxml python-multipart
    if !errorlevel! neq 0 (
        echo Trying to install with full requirements.txt...
        pip install -r requirements.txt
        if !errorlevel! neq 0 (
            echo Error: Failed to install dependencies
            echo Please run manually: install_deps.bat
            pause
            exit /b 1
        )
    )
)

:: Check config file
if not exist ".env" (
    echo Warning: .env file not found, please copy .env.example to .env and configure
    echo Continuing with default configuration...
)

:: Create necessary directories
if not exist "data" mkdir data
if not exist "logs" mkdir logs

:: Start application
echo Starting WeChat Public Account Article Query API...
echo Access URL: http://localhost:8000
echo API Documentation: http://localhost:8000/docs
echo ================================
python main.py

pause