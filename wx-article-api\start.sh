#!/bin/bash

echo "微信公众号文章查询API启动脚本"
echo "================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "错误: 创建虚拟环境失败"
        exit 1
    fi
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "升级pip..."
python3 -m pip install --upgrade pip

# 安装依赖
echo "安装依赖包..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "尝试逐个安装核心依赖..."
    pip install fastapi uvicorn pydantic requests beautifulsoup4 DrissionPage Pillow pyyaml python-dotenv pytz loguru lxml python-multipart
    if [ $? -ne 0 ]; then
        echo "尝试使用完整requirements.txt安装..."
        pip install -r requirements.txt
        if [ $? -ne 0 ]; then
            echo "错误: 安装依赖失败"
            echo "请手动安装: pip install fastapi uvicorn pydantic requests beautifulsoup4 DrissionPage Pillow pyyaml python-dotenv pytz loguru lxml python-multipart"
            exit 1
        fi
    fi
fi

# 检查配置文件
if [ ! -f ".env" ]; then
    echo "警告: 未找到.env文件，请复制.env.example为.env并配置"
    echo "继续使用默认配置..."
fi

# 创建必要的目录
mkdir -p data logs

# 启动应用
echo "启动微信公众号文章查询API..."
echo "访问地址: http://localhost:8000"
echo "API文档: http://localhost:8000/docs"
echo "================================"
xvfb-run -a python main.py
