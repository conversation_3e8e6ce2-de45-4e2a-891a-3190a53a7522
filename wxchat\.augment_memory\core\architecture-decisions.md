# wxchat 架构决策记录 (ADR)

## 📋 决策记录概览

本文档记录了wxchat项目中的重要架构决策，包括决策背景、考虑因素、最终选择和影响分析。

## 🏗️ 核心技术选择

### ADR-001: 选择Cloudflare Workers作为运行环境

**决策日期**: 项目初期
**状态**: 已采用
**决策者**: 项目团队

#### 背景
需要选择一个适合文件传输应用的后端运行环境，要求低延迟、高可用、成本效益。

#### 考虑的选项
1. **传统VPS/云服务器** (如AWS EC2, 阿里云ECS)
2. **容器化部署** (如Docker + Kubernetes)
3. **Serverless函数** (如AWS Lambda, Vercel Functions)
4. **Cloudflare Workers** (Edge Computing)

#### 决策理由
选择Cloudflare Workers，原因：
- **全球边缘部署**: 200+数据中心，用户就近访问
- **冷启动优势**: <10ms启动时间，优于传统Serverless
- **成本效益**: 按请求计费，无闲置成本
- **生态集成**: D1、R2、KV等服务无缝集成
- **开发体验**: Wrangler工具链完善

#### 影响
- ✅ 全球用户低延迟访问
- ✅ 自动扩缩容，无需运维
- ✅ 开发部署流程简化
- ❌ 运行时限制（CPU时间、内存）
- ❌ 调试相对复杂

### ADR-002: 选择Hono.js作为Web框架

**决策日期**: 项目初期
**状态**: 已采用

#### 背景
需要选择适合Cloudflare Workers环境的轻量级Web框架。

#### 考虑的选项
1. **原生Workers API** (无框架)
2. **Hono.js** (轻量级框架)
3. **Worktop** (Workers专用框架)
4. **Itty-router** (极简路由库)

#### 决策理由
选择Hono.js，原因：
- **性能优异**: 专为Edge环境优化
- **TypeScript支持**: 类型安全和开发体验
- **中间件系统**: 灵活的请求处理管道
- **多平台兼容**: Workers、Node.js、Deno通用
- **活跃社区**: 持续更新和维护

#### 影响
- ✅ 开发效率提升
- ✅ 代码结构清晰
- ✅ 中间件复用性强
- ❌ 增加依赖大小（相对原生API）

### ADR-003: 选择D1作为数据库解决方案

**决策日期**: 项目初期
**状态**: 已采用

#### 背景
需要选择适合边缘计算环境的数据库解决方案。

#### 考虑的选项
1. **Cloudflare D1** (SQLite-based)
2. **Cloudflare KV** (键值存储)
3. **外部数据库** (如PlanetScale, Supabase)
4. **Durable Objects** (状态存储)

#### 决策理由
选择D1数据库，原因：
- **SQL兼容**: 标准SQLite语法，学习成本低
- **ACID事务**: 数据一致性保证
- **边缘分布**: 与Workers同地部署
- **成本优势**: 免费额度充足
- **关系型数据**: 适合消息和文件关联查询

#### 影响
- ✅ 标准SQL查询能力
- ✅ 事务支持保证数据一致性
- ✅ 与Workers集成度高
- ❌ Beta阶段，功能限制
- ❌ 单库大小限制

## 🎨 前端架构决策

### ADR-004: 选择Vanilla JavaScript而非前端框架

**决策日期**: 项目初期
**状态**: 已采用

#### 背景
需要选择前端技术栈，平衡开发效率和性能表现。

#### 考虑的选项
1. **React** (组件化框架)
2. **Vue.js** (渐进式框架)
3. **Svelte** (编译时框架)
4. **Vanilla JavaScript** (原生JS)

#### 决策理由
选择Vanilla JavaScript，原因：
- **性能优势**: 无框架运行时开销
- **加载速度**: 更快的首屏渲染
- **包大小**: 最小化资源体积
- **灵活性**: 完全控制代码结构
- **学习成本**: 团队技能匹配

#### 影响
- ✅ 最佳性能表现
- ✅ 完全控制代码逻辑
- ✅ 无框架版本依赖问题
- ❌ 开发效率相对较低
- ❌ 需要手动管理DOM操作

### ADR-005: 采用模块化设计模式

**决策日期**: 项目初期
**状态**: 已采用

#### 背景
需要组织前端代码结构，确保可维护性和可扩展性。

#### 考虑的选项
1. **单文件模式** (所有代码在一个文件)
2. **功能分组** (按功能划分文件)
3. **模块化设计** (独立模块，明确依赖)
4. **组件化架构** (类似框架的组件系统)

#### 决策理由
选择模块化设计，原因：
- **职责分离**: 每个模块专注单一功能
- **依赖清晰**: 明确的模块间依赖关系
- **易于测试**: 独立模块便于单元测试
- **团队协作**: 多人并行开发不冲突
- **代码复用**: 模块可在不同场景复用

#### 影响
- ✅ 代码结构清晰
- ✅ 维护成本降低
- ✅ 功能扩展容易
- ❌ 初期设计复杂度增加

## 🔄 实时通信决策

### ADR-006: 选择SSE + 长轮询的混合方案

**决策日期**: 项目中期
**状态**: 已采用

#### 背景
需要实现实时消息推送功能，确保用户体验和系统稳定性。

#### 考虑的选项
1. **WebSocket** (双向实时通信)
2. **Server-Sent Events** (单向推送)
3. **长轮询** (HTTP长连接)
4. **短轮询** (定时请求)

#### 决策理由
选择SSE + 长轮询混合方案，原因：
- **SSE优势**: 原生浏览器支持，自动重连
- **Workers兼容**: Cloudflare Workers支持SSE
- **降级策略**: 长轮询作为备选方案
- **实现简单**: 相比WebSocket更容易实现
- **资源效率**: 单向推送满足需求

#### 影响
- ✅ 良好的实时性体验
- ✅ 自动故障恢复
- ✅ 跨浏览器兼容性好
- ❌ 单向通信限制
- ❌ 连接数限制

## 🛡️ 安全架构决策

### ADR-007: 选择JWT作为认证方案

**决策日期**: 项目初期
**状态**: 已采用

#### 背景
需要实现用户认证和会话管理，适合无状态的边缘计算环境。

#### 考虑的选项
1. **Session + Cookie** (传统会话)
2. **JWT Token** (无状态令牌)
3. **OAuth 2.0** (第三方认证)
4. **API Key** (简单密钥认证)

#### 决策理由
选择JWT Token，原因：
- **无状态**: 适合分布式边缘环境
- **自包含**: Token包含所有必要信息
- **跨域支持**: 适合SPA应用
- **标准化**: 成熟的工业标准
- **性能**: 无需服务端存储会话

#### 影响
- ✅ 扩展性好，支持多实例
- ✅ 客户端存储，减少服务端压力
- ✅ 跨域和移动端友好
- ❌ Token撤销相对复杂
- ❌ 载荷大小限制

### ADR-008: 采用密码保护而非复杂认证

**决策日期**: 项目初期
**状态**: 已采用

#### 背景
需要平衡安全性和使用便利性，适合个人或小团队使用。

#### 考虑的选项
1. **用户名密码** (传统认证)
2. **单一密码** (简化认证)
3. **多因素认证** (高安全性)
4. **无认证** (完全开放)

#### 决策理由
选择单一密码保护，原因：
- **使用简单**: 一个密码即可访问
- **配置方便**: 环境变量配置
- **安全适中**: 满足个人使用需求
- **维护成本低**: 无复杂用户管理
- **快速部署**: 减少配置步骤

#### 影响
- ✅ 部署和使用简单
- ✅ 维护成本低
- ✅ 适合个人/小团队
- ❌ 安全级别相对较低
- ❌ 无用户权限区分

## 📊 存储架构决策

### ADR-009: 选择R2作为文件存储方案

**决策日期**: 项目初期
**状态**: 已采用

#### 背景
需要选择适合全球分发的文件存储解决方案。

#### 考虑的选项
1. **Cloudflare R2** (对象存储)
2. **AWS S3** (对象存储)
3. **本地存储** (Workers限制)
4. **第三方CDN** (如七牛云)

#### 决策理由
选择R2存储，原因：
- **成本优势**: 无出站流量费用
- **全球分发**: CDN加速访问
- **S3兼容**: 标准API接口
- **集成度高**: 与Workers无缝集成
- **性能优异**: 边缘存储访问

#### 影响
- ✅ 全球用户快速访问
- ✅ 成本控制优秀
- ✅ 开发集成简单
- ❌ 相对较新的服务
- ❌ 功能相比S3略少

## 🔄 决策演进

### 已废弃的决策

#### ADR-010: 移除PWA支持 (已废弃)
**原决策**: 支持PWA离线功能
**废弃原因**: 增加复杂度，用户需求不强
**当前状态**: 已移除相关代码

#### ADR-011: 移除AI功能 (已废弃)
**原决策**: 集成AI消息处理
**废弃原因**: 超出核心功能范围
**当前状态**: 保留接口，移除实现

### 待决策项目

#### 考虑中的决策
1. **TypeScript迁移**: 提升代码质量和开发体验
2. **多语言支持**: 国际化功能扩展
3. **移动端优化**: 响应式设计改进
4. **监控系统**: 性能和错误监控

## 📈 决策影响评估

### 正面影响
- **性能优异**: 边缘计算 + 轻量级技术栈
- **成本控制**: Serverless + 免费额度
- **开发效率**: 现代工具链 + 模块化设计
- **用户体验**: 实时通信 + 全球加速

### 负面影响
- **技术限制**: Workers运行时约束
- **调试复杂**: 分布式环境调试困难
- **依赖风险**: 依赖Cloudflare生态
- **功能限制**: 简化认证和权限系统

### 风险缓解
- **多环境测试**: 本地 + 预发布 + 生产
- **监控告警**: 性能和错误监控
- **备份方案**: 数据备份和恢复策略
- **文档完善**: 架构和操作文档
