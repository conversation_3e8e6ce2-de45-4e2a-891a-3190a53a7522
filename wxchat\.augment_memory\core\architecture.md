# wxchat 架构设计文档

## 🏛️ 整体架构概览

### 系统架构图
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   前端客户端     │    │  Cloudflare      │    │   数据存储层     │
│                │    │  Workers         │    │                │
│  ┌───────────┐  │    │  ┌─────────────┐ │    │  ┌───────────┐  │
│  │ HTML/CSS  │  │    │  │    Hono     │ │    │  │ D1 数据库  │  │
│  │ JavaScript│◄─┼────┼─►│   Router    │◄┼────┼─►│ (SQLite)  │  │
│  │ 模块化设计 │  │    │  │             │ │    │  └───────────┘  │
│  └───────────┘  │    │  └─────────────┘ │    │                │
│                │    │                  │    │  ┌───────────┐  │
│  ┌───────────┐  │    │  ┌─────────────┐ │    │  │ R2 存储   │  │
│  │ 实时通信   │◄─┼────┼─►│ SSE/轮询    │ │    │  │ (文件)    │  │
│  │ (SSE)     │  │    │  │ 处理器      │ │    │  └───────────┘  │
│  └───────────┘  │    │  └─────────────┘ │    │                │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🔧 核心组件设计

### 1. 前端架构 (客户端层)

#### 模块化设计
```javascript
// 核心模块依赖关系
config.js (配置中心)
    ↓
utils.js (工具函数)
    ↓
auth.js (认证模块) → api.js (API调用层)
    ↓                    ↓
ui.js (UI管理) ← messageHandler.js (消息处理)
    ↓                    ↓
fileUpload.js (文件上传) → realtime.js (实时通信)
    ↓
components/ (UI组件库)
search/ (搜索功能模块)
```

#### 关键设计模式
- **模块模式**: 每个功能独立模块，避免全局污染
- **观察者模式**: 事件驱动的UI更新机制
- **策略模式**: 多种文件上传和通信策略
- **单例模式**: 配置管理和API客户端

### 2. 后端架构 (Workers层)

#### 请求处理流程
```javascript
Request → CORS中间件 → 认证中间件 → 路由分发 → 业务处理 → Response
                                      ↓
                              ┌─────────────┐
                              │ API路由系统  │
                              ├─────────────┤
                              │ /api/auth/* │ (认证相关)
                              │ /api/messages│ (消息管理)
                              │ /api/files/* │ (文件操作)
                              │ /api/search  │ (搜索功能)
                              │ /api/events  │ (实时通信)
                              └─────────────┘
```

#### 中间件系统
1. **CORS中间件**: 跨域请求处理
2. **认证中间件**: JWT token验证
3. **错误处理中间件**: 统一异常处理
4. **日志中间件**: 请求日志记录

### 3. 数据层架构

#### D1数据库设计
```sql
-- 核心表结构关系
messages (消息主表)
    ├── id (主键)
    ├── type (消息类型: text/file)
    ├── content (文本内容)
    ├── file_id (外键 → files.id)
    ├── device_id (设备标识)
    └── timestamp (时间戳)

files (文件信息表)
    ├── id (主键)
    ├── original_name (原始文件名)
    ├── r2_key (R2存储键)
    ├── file_size (文件大小)
    └── mime_type (文件类型)

devices (设备管理表)
    ├── id (设备ID)
    ├── name (设备名称)
    └── last_active (最后活跃时间)
```

#### 数据访问层 (DAL)
- **查询优化**: 索引策略和查询计划
- **事务管理**: ACID特性保证
- **连接池**: Workers自动管理
- **缓存策略**: 查询结果缓存

## 🔄 核心业务流程

### 1. 用户认证流程
```
1. 用户输入密码 → 前端验证
2. 发送登录请求 → Workers认证
3. 生成JWT Token → 返回客户端
4. Token存储 → localStorage
5. 后续请求携带Token → 中间件验证
```

### 2. 消息发送流程
```
文本消息:
用户输入 → 前端验证 → API调用 → 数据库存储 → 实时推送

文件消息:
文件选择 → 前端验证 → R2上传 → 文件信息存储 → 消息记录 → 实时推送
```

### 3. 实时通信流程
```
SSE连接建立:
客户端 → /api/events → SSE流建立 → 心跳维持

消息推送:
新消息产生 → 检测活跃连接 → 推送事件 → 客户端更新UI

降级处理:
SSE失败 → 长轮询模式 → /api/poll → 定期检查更新
```

## 🛡️ 安全架构设计

### 1. 认证与授权
- **JWT认证**: 无状态token机制
- **密码保护**: 环境变量配置
- **会话管理**: 过期时间控制
- **权限控制**: 基于token的访问控制

### 2. 数据安全
- **SQL注入防护**: 参数化查询
- **XSS防护**: 内容转义处理
- **CSRF防护**: Token验证机制
- **文件安全**: 类型和大小限制

### 3. 传输安全
- **HTTPS强制**: Cloudflare SSL
- **CORS配置**: 跨域请求控制
- **请求限制**: 防止滥用攻击
- **错误处理**: 敏感信息隐藏

## 📊 性能架构优化

### 1. 前端性能
- **资源优化**: CSS/JS模块化加载
- **缓存策略**: 浏览器缓存利用
- **懒加载**: 按需加载组件
- **压缩优化**: 资源文件压缩

### 2. 后端性能
- **边缘计算**: Workers全球分布
- **数据库优化**: 索引和查询优化
- **文件存储**: R2 CDN加速
- **并发处理**: 自动扩缩容

### 3. 实时通信优化
- **连接复用**: 单一SSE连接
- **心跳机制**: 连接状态检测
- **降级策略**: 多种通信方式
- **缓冲机制**: 消息队列处理

## 🔮 架构扩展性

### 1. 水平扩展
- **无状态设计**: Workers天然支持
- **数据分片**: D1数据库分区
- **CDN分发**: 全球内容分发
- **负载均衡**: Cloudflare自动处理

### 2. 功能扩展
- **插件系统**: 模块化功能扩展
- **API版本**: 向后兼容设计
- **多租户**: 数据隔离机制
- **国际化**: 多语言支持框架

### 3. 监控与运维
- **日志系统**: 结构化日志记录
- **监控指标**: 性能和错误监控
- **告警机制**: 异常情况通知
- **备份恢复**: 数据安全保障
