# wxchat 最佳实践指南

## 🎯 开发最佳实践

### 1. 代码组织与结构

#### 前端模块化原则
```javascript
// ✅ 好的做法 - 明确的模块边界
const MessageHandler = {
    // 私有方法
    _validateMessage: function(message) { /* ... */ },
    
    // 公共接口
    sendMessage: function(content, type) { /* ... */ },
    receiveMessage: function(message) { /* ... */ }
};

// ❌ 避免的做法 - 全局变量污染
var currentMessage = null;
var messageList = [];
function sendMessage() { /* ... */ }
```

#### 后端路由组织
```javascript
// ✅ 好的做法 - 模块化路由
const authApi = new Hono()
authApi.post('/login', loginHandler)
authApi.get('/verify', verifyHandler)

const messageApi = new Hono()
messageApi.get('/', getMessagesHandler)
messageApi.post('/', createMessageHandler)

// 路由挂载
app.route('/api/auth', authApi)
app.route('/api/messages', messageApi)

// ❌ 避免的做法 - 单一路由文件
app.post('/api/auth/login', loginHandler)
app.get('/api/auth/verify', verifyHandler)
app.get('/api/messages', getMessagesHandler)
// ... 所有路由混在一起
```

### 2. 错误处理最佳实践

#### 统一错误处理
```javascript
// ✅ 前端错误处理
const APIClient = {
    async request(endpoint, options = {}) {
        try {
            const response = await fetch(endpoint, options);
            
            if (!response.ok) {
                const error = await response.json();
                throw new APIError(error.message, response.status);
            }
            
            return await response.json();
        } catch (error) {
            console.error(`API请求失败: ${endpoint}`, error);
            
            // 统一错误处理
            if (error.status === 401) {
                AuthManager.logout();
                window.location.href = '/login.html';
            }
            
            throw error;
        }
    }
};

// ✅ 后端错误处理
api.post('/messages', async (c) => {
    try {
        const { content, deviceId } = await c.req.json();
        
        // 业务逻辑
        const result = await createMessage(content, deviceId);
        
        return c.json({ success: true, data: result });
    } catch (error) {
        console.error('创建消息失败:', error);
        
        return c.json({
            success: false,
            error: error.message
        }, 500);
    }
});
```

#### 错误分类处理
```javascript
// 错误类型定义
class APIError extends Error {
    constructor(message, status, code) {
        super(message);
        this.status = status;
        this.code = code;
        this.name = 'APIError';
    }
}

class ValidationError extends Error {
    constructor(message, field) {
        super(message);
        this.field = field;
        this.name = 'ValidationError';
    }
}

// 错误处理策略
const ErrorHandler = {
    handle: function(error) {
        switch (error.name) {
            case 'APIError':
                this.handleAPIError(error);
                break;
            case 'ValidationError':
                this.handleValidationError(error);
                break;
            default:
                this.handleUnknownError(error);
        }
    }
};
```

### 3. 性能优化实践

#### 前端性能优化
```javascript
// ✅ 防抖处理
const SearchHandler = {
    searchTimeout: null,
    
    handleSearchInput: function(query) {
        clearTimeout(this.searchTimeout);
        
        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, 300); // 300ms防抖
    }
};

// ✅ 虚拟滚动（大量消息时）
const MessageList = {
    visibleRange: { start: 0, end: 50 },
    
    updateVisibleMessages: function() {
        const container = document.getElementById('messageList');
        const scrollTop = container.scrollTop;
        const itemHeight = 60; // 估算消息高度
        
        const start = Math.floor(scrollTop / itemHeight);
        const end = start + Math.ceil(container.clientHeight / itemHeight);
        
        this.visibleRange = { start, end };
        this.renderVisibleMessages();
    }
};

// ✅ 资源懒加载
const FilePreview = {
    loadImage: function(src, callback) {
        const img = new Image();
        img.onload = () => callback(null, img);
        img.onerror = (error) => callback(error);
        img.src = src;
    }
};
```

#### 后端性能优化
```javascript
// ✅ 数据库查询优化
const MessageRepository = {
    // 使用索引优化查询
    async findRecentMessages(limit = 50) {
        const stmt = DB.prepare(`
            SELECT m.*, f.original_name, f.file_size
            FROM messages m
            LEFT JOIN files f ON m.file_id = f.id
            WHERE m.timestamp >= datetime('now', '-7 days')
            ORDER BY m.timestamp DESC
            LIMIT ?
        `);
        return stmt.bind(limit).all();
    },
    
    // 分页查询
    async findMessagesPaginated(offset = 0, limit = 50) {
        const stmt = DB.prepare(`
            SELECT COUNT(*) as total FROM messages
        `);
        const countResult = await stmt.first();
        
        const dataStmt = DB.prepare(`
            SELECT * FROM messages
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?
        `);
        const messages = await dataStmt.bind(limit, offset).all();
        
        return {
            messages: messages.results,
            total: countResult.total,
            hasMore: offset + limit < countResult.total
        };
    }
};

// ✅ 缓存策略
const CacheManager = {
    cache: new Map(),
    
    get: function(key) {
        const item = this.cache.get(key);
        if (item && Date.now() < item.expiry) {
            return item.value;
        }
        this.cache.delete(key);
        return null;
    },
    
    set: function(key, value, ttl = 300000) { // 5分钟TTL
        this.cache.set(key, {
            value,
            expiry: Date.now() + ttl
        });
    }
};
```

### 4. 安全最佳实践

#### 输入验证与清理
```javascript
// ✅ 前端输入验证
const InputValidator = {
    validateMessage: function(content) {
        if (!content || typeof content !== 'string') {
            throw new ValidationError('消息内容不能为空');
        }
        
        if (content.length > 1000) {
            throw new ValidationError('消息内容不能超过1000字符');
        }
        
        // XSS防护 - 清理HTML标签
        return content.replace(/<[^>]*>/g, '');
    },
    
    validateFile: function(file) {
        if (!file) {
            throw new ValidationError('请选择文件');
        }
        
        if (file.size > CONFIG.FILE.MAX_SIZE) {
            throw new ValidationError('文件大小不能超过10MB');
        }
        
        // 文件类型验证
        const allowedTypes = ['image/', 'text/', 'application/pdf'];
        const isAllowed = allowedTypes.some(type => 
            file.type.startsWith(type)
        );
        
        if (!isAllowed) {
            throw new ValidationError('不支持的文件类型');
        }
        
        return true;
    }
};

// ✅ 后端参数验证
const RequestValidator = {
    validateCreateMessage: function(data) {
        const { content, deviceId, type } = data;
        
        if (!content || content.trim().length === 0) {
            throw new ValidationError('消息内容不能为空');
        }
        
        if (!deviceId || deviceId.length < 10) {
            throw new ValidationError('设备ID格式错误');
        }
        
        if (type && !['text', 'file'].includes(type)) {
            throw new ValidationError('消息类型错误');
        }
        
        return {
            content: content.trim().substring(0, 1000),
            deviceId: deviceId.trim(),
            type: type || 'text'
        };
    }
};
```

#### JWT安全处理
```javascript
// ✅ Token安全管理
const AuthManager = {
    TOKEN_KEY: 'wxchat_token',
    
    setToken: function(token) {
        // 使用sessionStorage而非localStorage提高安全性
        sessionStorage.setItem(this.TOKEN_KEY, token);
    },
    
    getToken: function() {
        return sessionStorage.getItem(this.TOKEN_KEY);
    },
    
    clearToken: function() {
        sessionStorage.removeItem(this.TOKEN_KEY);
        localStorage.removeItem(this.TOKEN_KEY); // 清理可能的旧数据
    },
    
    // Token过期检查
    isTokenExpired: function(token) {
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            return Date.now() >= payload.exp;
        } catch (error) {
            return true;
        }
    }
};
```

### 5. 实时通信最佳实践

#### SSE连接管理
```javascript
// ✅ 健壮的SSE实现
const RealtimeManager = {
    eventSource: null,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    reconnectDelay: 1000,
    
    connect: function() {
        const token = AuthManager.getToken();
        if (!token) return;
        
        const url = `/api/events?deviceId=${CONFIG.DEVICE_ID}&token=${token}`;
        
        this.eventSource = new EventSource(url);
        
        this.eventSource.onopen = () => {
            console.log('SSE连接已建立');
            this.reconnectAttempts = 0;
        };
        
        this.eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('SSE消息解析失败:', error);
            }
        };
        
        this.eventSource.onerror = () => {
            console.error('SSE连接错误');
            this.handleReconnect();
        };
    },
    
    handleReconnect: function() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('SSE重连次数超限，切换到长轮询');
            this.fallbackToPolling();
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        setTimeout(() => {
            console.log(`SSE重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
            this.connect();
        }, delay);
    }
};
```

### 6. 数据库操作最佳实践

#### 事务处理
```javascript
// ✅ 事务操作示例
async function uploadFileWithMessage(file, content, deviceId) {
    const { DB, R2 } = env;
    
    try {
        // 1. 上传文件到R2
        const r2Key = generateR2Key(file.name);
        await R2.put(r2Key, file.stream());
        
        // 2. 数据库事务操作
        const fileStmt = DB.prepare(`
            INSERT INTO files (original_name, r2_key, file_size, mime_type, upload_device_id)
            VALUES (?, ?, ?, ?, ?)
        `);
        
        const messageStmt = DB.prepare(`
            INSERT INTO messages (type, content, file_id, device_id)
            VALUES (?, ?, ?, ?)
        `);
        
        // 执行事务
        const fileResult = await fileStmt.bind(
            file.name, r2Key, file.size, file.type, deviceId
        ).run();
        
        await messageStmt.bind(
            'file', content, fileResult.meta.last_row_id, deviceId
        ).run();
        
        return { success: true, fileId: fileResult.meta.last_row_id };
        
    } catch (error) {
        // 回滚：删除已上传的R2文件
        try {
            await R2.delete(r2Key);
        } catch (deleteError) {
            console.error('R2文件删除失败:', deleteError);
        }
        
        throw error;
    }
}
```

### 7. 测试最佳实践

#### 单元测试示例
```javascript
// ✅ 前端单元测试
describe('MessageHandler', () => {
    beforeEach(() => {
        // 重置状态
        MessageHandler.reset();
    });
    
    test('应该正确验证消息内容', () => {
        expect(() => {
            MessageHandler.validateMessage('');
        }).toThrow('消息内容不能为空');
        
        expect(() => {
            MessageHandler.validateMessage('a'.repeat(1001));
        }).toThrow('消息内容不能超过1000字符');
        
        expect(MessageHandler.validateMessage('正常消息')).toBe('正常消息');
    });
    
    test('应该正确处理文件消息', async () => {
        const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });
        
        const result = await MessageHandler.handleFileMessage(mockFile, 'device-123');
        
        expect(result.type).toBe('file');
        expect(result.deviceId).toBe('device-123');
    });
});
```

### 8. 部署与运维最佳实践

#### 环境配置管理
```toml
# wrangler.toml - 生产环境配置
[env.production]
name = "wxchat-prod"
vars = { ENVIRONMENT = "production" }

[env.staging]
name = "wxchat-staging"
vars = { ENVIRONMENT = "staging" }

[env.development]
name = "wxchat-dev"
vars = { ENVIRONMENT = "development" }
```

#### 监控与日志
```javascript
// ✅ 结构化日志
const Logger = {
    log: function(level, message, context = {}) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            level,
            message,
            context,
            environment: env.ENVIRONMENT
        };
        
        console.log(JSON.stringify(logEntry));
    },
    
    error: function(message, error, context = {}) {
        this.log('ERROR', message, {
            ...context,
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack
            }
        });
    }
};

// 使用示例
Logger.error('文件上传失败', error, {
    userId: deviceId,
    fileName: file.name,
    fileSize: file.size
});
```

## 🚀 性能监控指标

### 关键指标
- **响应时间**: API请求平均响应时间 < 200ms
- **错误率**: 5xx错误率 < 1%
- **可用性**: 服务可用性 > 99.9%
- **文件上传**: 10MB文件上传时间 < 30s

### 监控实现
```javascript
// 性能监控中间件
const performanceMiddleware = async (c, next) => {
    const start = Date.now();
    
    await next();
    
    const duration = Date.now() - start;
    const path = c.req.path;
    const method = c.req.method;
    const status = c.res.status;
    
    // 记录性能指标
    Logger.log('PERFORMANCE', 'Request completed', {
        method,
        path,
        status,
        duration,
        timestamp: new Date().toISOString()
    });
    
    // 慢查询告警
    if (duration > 1000) {
        Logger.log('WARNING', 'Slow request detected', {
            method,
            path,
            duration
        });
    }
};
```
