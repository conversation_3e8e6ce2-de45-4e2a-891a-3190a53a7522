{"clientTcpRtt": 411, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 4134, "clientAcceptEncoding": "gzip, deflate, br", "verifiedBotCategory": "", "country": "CN", "isEUCountry": false, "region": "Shanghai", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "hC7d1u/69DasQa0cw9GrKTA/1RDFV4cc91YRBKKerXU=", "tlsExportedAuthenticator": {"clientFinished": "2fa495e5ed1fed6275d4f86b6c28dc3f5812374d2975afbb8bf634d20eab735df56bcc614761d3324ff4aed9cf46b765", "clientHandshake": "5c7f2e4ec20da1eb0573614da0cef8145e9f24f529b7a6fe35e88c869d2d2a0cade0582a483d9b960692746124abf29a", "serverHandshake": "739afef901d868b2e35628625cc4475b7a9546b4126df359d51f3a1c2dadeb06de4d3865c93305cb150a505739bff898", "serverFinished": "a0738e149e53123d49326e1fbe4598dd8bf4be30dadef01d31ac8a1ff2d505f6128e78980a559b06bb31914ca19152e2"}, "tlsClientHelloLength": "386", "colo": "SJC", "timezone": "Asia/Shanghai", "longitude": "121.45806", "latitude": "31.22222", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "200000", "city": "Shanghai", "tlsVersion": "TLSv1.3", "regionCode": "SH", "asOrganization": "CHINANET Guangdong province network", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}