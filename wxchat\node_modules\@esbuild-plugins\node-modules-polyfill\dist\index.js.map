{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,gFAAqD;AACrD,4CAAmB;AACnB,gDAAuB;AAEvB,2CAA+C;AAE/C,oEAAoE;AACpE,MAAM,IAAI,GAAG,wBAAwB,CAAA;AACrC,MAAM,SAAS,GAAG,IAAI,CAAA;AAEtB,SAAS,iBAAiB,CAAC,QAAQ;IAC/B,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACxC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;KACnC;IACD,OAAO,QAAQ,CAAA;AACnB,CAAC;AAOD,SAAgB,yBAAyB,CACrC,UAAgC,EAAE;IAElC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,OAAO,CAAA;IACtD,IAAI,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;QAChC,MAAM,IAAI,KAAK,CAAC,aAAa,SAAS,6BAA6B,CAAC,CAAA;KACvE;IACD,+IAA+I;IAC/I,MAAM,iBAAiB,GAAG,SAAS,GAAG,WAAW,CAAA;IACjD,MAAM,kBAAkB,GAAG,6BAAiB,EAAE,CAAA;IAC9C,MAAM,uBAAuB,GAAG,CAAC,GAAG,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAA;IAE9D,OAAO;QACH,IAAI;QACJ,KAAK,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE;;YACvD,uDAAuD;YACvD,IAAI,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,MAAM,KAAI,QAAC,cAAc,CAAC,MAAM,0CAAE,MAAM,CAAA,EAAE;gBAC1D,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAA;aACjD;iBAAM,IAAI,EAAC,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,MAAM,CAAA,EAAE;gBAChC,cAAc,CAAC,MAAM,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,CAAA;aACnD;YAED,iEAAiE;YACjE,SAAe,MAAM,CACjB,IAAwB;;oBAExB,IAAI;wBACA,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;wBAChD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;wBAEtD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CACnC,iBAAiB,CAAC,QAAQ,CAAC,CAC9B,CAAA;wBACD,MAAM,QAAQ,GAAG,MAAM,CACnB,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CACvC,CAAC,QAAQ,EAAE,CAAA;wBACZ,IAAI,UAAU,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;wBAEvC,IAAI,UAAU,EAAE;4BACZ,OAAO;gCACH,MAAM,EAAE,IAAI;gCACZ,QAAQ,EAAE,gBAAgB,CAAC;oCACvB,UAAU,EAAE,QAAQ;iCACvB,CAAC;gCACF,UAAU;6BACb,CAAA;yBACJ;wBACD,OAAO;4BACH,MAAM,EAAE,IAAI;4BACZ,QAAQ;4BACR,UAAU;yBACb,CAAA;qBACJ;oBAAC,OAAO,CAAC,EAAE;wBACR,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC,CAAA;wBACzC,OAAO;4BACH,QAAQ,EAAE,WAAW;4BACrB,MAAM,EAAE,IAAI;yBACf,CAAA;qBACJ;gBACL,CAAC;aAAA;YACD,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAA;YAC3C,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,iBAAiB,EAAE,EAAE,MAAM,CAAC,CAAA;YAC9D,MAAM,MAAM,GAAG,IAAI,MAAM,CACrB;gBACI,GAAG,uBAAuB;gBAC1B,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;aACrD;iBACI,GAAG,CAAC,8BAAkB,CAAC;iBACvB,IAAI,CAAC,GAAG,CAAC,CACjB,CAAA;YACD,SAAe,QAAQ,CAAC,IAAmB;;oBACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;oBAChD,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,KAAK,iBAAiB,CAAA;oBAE1D,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;wBACnC,OAAM;qBACT;oBAED,MAAM,UAAU,GACZ,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,CAAA;oBAElD,OAAO;wBACH,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS;wBACrD,IAAI,EAAE,QAAQ;qBACjB,CAAA;gBACL,CAAC;aAAA;YACD,SAAS,CAAC,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAA;YAC/B,mDAAmD;QACvD,CAAC;KACJ,CAAA;AACL,CAAC;AA1FD,8DA0FC;AAED,SAAS,gBAAgB,CAAC,EAAE,UAAU,EAAE;IACpC,OAAO;4BACiB,UAAU;;;;;;;;;;;;CAYrC,CAAA;AACD,CAAC;AAED,kBAAe,yBAAyB,CAAA"}