{"version": 3, "file": "generate-tasks.js", "sourceRoot": "", "sources": ["../../ts/build/generate-tasks.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,IAAI,CAAC;AACnC,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,KAAK,MAAM,YAAY,CAAC;AAC/B,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAE5B,MAAM,UAAU,GAAG,EAAE,CAAC;AAEtB,CAAC,GAAS,EAAE;IACV,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,oCAAoC,CAAC,CAAC;IAC9D,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE;QACX,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,MAAM,mCAAmC,CAAC,CAAC;KACnE;IAED,MAAM,QAAQ,GAA2C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;IAC1E,MAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAC;IAC/C,MAAM,UAAU,GAA8B,EAAE,CAAC;IACjD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;QAC9B,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,aAAa,IAAI,aAAa,GAAG,UAAU,EAAE;YAChD,MAAM;SACP;QAED,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;QACpD,IAAI,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;YACnC,SAAS;SACV;QAED,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;KAClD;IAED,MAAM,SAAS,GAAG;QAChB,IAAI,EAAE,mBAAmB;QACzB,EAAE,EAAE;YACF,IAAI,EAAE;gBACJ,QAAQ,EAAE,CAAC,iBAAiB,CAAC;aAC9B;SACF;QACD,IAAI,EAAE;YACJ,KAAK,EAAE;gBACL,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,kBAAkB;gBAC7B,QAAQ,EAAE;oBACR,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,gBAAgB,CAAC,EAAE;iBACpE;gBACD,KAAK,EAAE;oBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;oBACnC,EAAE,GAAG,EAAE,YAAY,EAAE;oBACrB;wBACE,IAAI,EAAE,yBAAyB;wBAC/B,IAAI,EAAE,EAAE,MAAM,EAAE,wBAAwB,EAAE,SAAS,EAAE,SAAS,EAAE;qBACjE;oBACD,GAAG,CAAC,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC;yBAC3B,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;wBACxC,EAAE,IAAI,EAAE,uBAAuB,EAAE,IAAI,EAAE,EAAE,cAAc,EAAE,WAAW,EAAE,EAAE;wBACxE;4BACE,uDAAuD;4BACvD,KAAK,EAAE,YAAY;4BACnB,IAAI,EAAE,4BAA4B;4BAClC,GAAG,EACD,oIAAoI;4BACtI,EAAE,EAAE,+BAA+B;yBACpC;wBACD,CAAC,KAAK,CAAC;4BACL,CAAC,CAAC,EAAE,GAAG,EAAE,6BAA6B,EAAE;4BACxC,CAAC,CAAC,EAAE,GAAG,EAAE,6CAA6C,EAAE;wBAC1D,EAAE,GAAG,EAAE,2CAA2C,EAAE,mBAAmB,EAAE,IAAI,EAAE;wBAC/E,EAAE,GAAG,EAAE,kDAAkD,aAAa,OAAO,EAAE;qBAChF,CAAC;yBACD,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;oBACzC;wBACE,IAAI,EAAE,4BAA4B;wBAClC,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;qBACrC;iBACF;aACF;SACF;KACF,CAAC;IAEF,aAAa,CACX,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,CAAC,EACrE,IAAI,CAAC,SAAS,CAAC,CAChB,CAAC;IACF,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACzF,CAAC,CAAA,CAAC,EAAE,CAAC"}