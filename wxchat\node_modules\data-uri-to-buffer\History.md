
2.0.0 / 2017-07-18
==================

  * More correct media type handling
  * add typings for Typescript

1.2.0 / 2017-07-18
==================

  * Essentially identical to v1.0.0. Reverting changes from v1.1.0 because they are breaking changes and should be part of a major version bump
  * Revert "More correct media type handling"
  * Revert "add typings for Typescript"

1.1.0 / 2017-07-17
==================

  * More correct media type handling
  * Add typings for Typescript

1.0.0 / 2017-06-09
==================

  * Bumping to v1.0.0 for semver semantics
  * random updates for newer Node.js versions
  * travis: test more node versions and fix v0.8

0.0.4 / 2015-06-29
==================

  * package: update "mocha" to v2
  * package: add RFC to the "keywords" section
  * travis: test node v0.8, v0.10, and v0.12
  * README: use SVG for Travis-CI badge
  * test: more tests

0.0.3 / 2014-01-08
==================

  * index: fix a URI with a comma in the data portion

0.0.2 / 2014-01-08
==================

  * index: use unescape() instead of decodeURIComponent()
  * test: add more tests from Mozilla

0.0.1 / 2014-01-02
==================

  * add `README.md`
  * index: default the `charset` property to "US-ASCII"
  * default encoding is "ascii"
  * default `type` to "text/plain" when none is given
  * initial commit
