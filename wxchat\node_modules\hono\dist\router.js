// src/router.ts
var METHOD_NAME_ALL = "ALL";
var METHOD_NAME_ALL_LOWERCASE = "all";
var METHODS = ["get", "post", "put", "delete", "options", "patch"];
var MESSAGE_MATCHER_IS_ALREADY_BUILT = "Can not add a route since the matcher is already built.";
var UnsupportedPathError = class extends Error {
};
export {
  MESSAGE_MATCHER_IS_ALREADY_BUILT,
  METHODS,
  METHOD_NAME_ALL,
  METHOD_NAME_ALL_LOWERCASE,
  UnsupportedPathError
};
