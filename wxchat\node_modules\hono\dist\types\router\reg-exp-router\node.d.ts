export declare const PATH_ERROR: unique symbol;
export type ParamAssocArray = [string, number][];
export interface Context {
    varIndex: number;
}
export declare class Node {
    index?: number;
    varIndex?: number;
    children: Record<string, Node>;
    insert(tokens: readonly string[], index: number, paramMap: ParamAssocArray, context: Context, pathErrorCheckOnly: boolean): void;
    buildRegExpStr(): string;
}
