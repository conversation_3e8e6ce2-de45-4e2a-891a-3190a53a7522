import type nodeAsyncHooks from "node:async_hooks";
export declare const createHook: typeof nodeAsyncHooks.createHook;
export declare const executionAsyncId: typeof nodeAsyncHooks.executionAsyncId;
export declare const executionAsyncResource: typeof nodeAsyncHooks.executionAsyncResource;
export declare const triggerAsyncId: typeof nodeAsyncHooks.triggerAsyncId;
export declare const asyncWrapProviders: typeof nodeAsyncHooks.asyncWrapProviders;
