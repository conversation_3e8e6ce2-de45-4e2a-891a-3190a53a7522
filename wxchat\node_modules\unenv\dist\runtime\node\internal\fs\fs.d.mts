import type nodeFs from "node:fs";
export declare const access: typeof nodeFs.access;
export declare const appendFile: typeof nodeFs.appendFile;
export declare const chown: typeof nodeFs.chown;
export declare const chmod: typeof nodeFs.chmod;
export declare const copyFile: typeof nodeFs.copyFile;
export declare const cp: typeof nodeFs.cp;
export declare const lchown: typeof nodeFs.lchown;
export declare const lchmod: typeof nodeFs.lchmod;
export declare const link: typeof nodeFs.link;
export declare const lstat: typeof nodeFs.lstat;
export declare const lutimes: typeof nodeFs.lutimes;
export declare const mkdir: typeof nodeFs.mkdir;
export declare const mkdtemp: typeof nodeFs.mkdtemp;
export declare const realpath: typeof nodeFs.realpath;
export declare const open: typeof nodeFs.open;
export declare const opendir: typeof nodeFs.opendir;
export declare const readdir: typeof nodeFs.readdir;
export declare const readFile: typeof nodeFs.readFile;
export declare const readlink: typeof nodeFs.readlink;
export declare const rename: typeof nodeFs.rename;
export declare const rm: typeof nodeFs.rm;
export declare const rmdir: typeof nodeFs.rmdir;
export declare const stat: typeof nodeFs.stat;
export declare const symlink: typeof nodeFs.symlink;
export declare const truncate: typeof nodeFs.truncate;
export declare const unlink: typeof nodeFs.unlink;
export declare const utimes: typeof nodeFs.utimes;
export declare const writeFile: typeof nodeFs.writeFile;
export declare const statfs: typeof nodeFs.statfs;
export declare const close: typeof nodeFs.close;
export declare const createReadStream: typeof nodeFs.createReadStream;
export declare const createWriteStream: typeof nodeFs.createWriteStream;
export declare const exists: typeof nodeFs.exists;
export declare const fchown: typeof nodeFs.fchown;
export declare const fchmod: typeof nodeFs.fchmod;
export declare const fdatasync: typeof nodeFs.fdatasync;
export declare const fstat: typeof nodeFs.fstat;
export declare const fsync: typeof nodeFs.fsync;
export declare const ftruncate: typeof nodeFs.ftruncate;
export declare const futimes: typeof nodeFs.futimes;
export declare const lstatSync: typeof nodeFs.lstatSync;
export declare const read: typeof nodeFs.read;
export declare const readv: typeof nodeFs.readv;
export declare const realpathSync: typeof nodeFs.realpathSync;
export declare const statSync: typeof nodeFs.statSync;
export declare const unwatchFile: typeof nodeFs.unwatchFile;
export declare const watch: typeof nodeFs.watch;
export declare const watchFile: typeof nodeFs.watchFile;
export declare const write: typeof nodeFs.write;
export declare const writev: typeof nodeFs.writev;
export declare const _toUnixTimestamp: unknown;
export declare const openAsBlob: typeof nodeFs.openAsBlob;
export declare const glob: typeof nodeFs.glob;
export declare const appendFileSync: unknown;
export declare const accessSync: unknown;
export declare const chownSync: unknown;
export declare const chmodSync: unknown;
export declare const closeSync: unknown;
export declare const copyFileSync: unknown;
export declare const cpSync: unknown;
export declare const existsSync: typeof nodeFs.existsSync;
export declare const fchownSync: unknown;
export declare const fchmodSync: unknown;
export declare const fdatasyncSync: unknown;
export declare const fstatSync: typeof nodeFs.fstatSync;
export declare const fsyncSync: unknown;
export declare const ftruncateSync: unknown;
export declare const futimesSync: unknown;
export declare const lchownSync: unknown;
export declare const lchmodSync: unknown;
export declare const linkSync: unknown;
export declare const lutimesSync: unknown;
export declare const mkdirSync: unknown;
export declare const mkdtempSync: typeof nodeFs.mkdtempSync;
export declare const openSync: unknown;
export declare const opendirSync: unknown;
export declare const readdirSync: typeof nodeFs.readdirSync;
export declare const readSync: unknown;
export declare const readvSync: unknown;
export declare const readFileSync: typeof nodeFs.readFileSync;
export declare const readlinkSync: typeof nodeFs.readlinkSync;
export declare const renameSync: unknown;
export declare const rmSync: unknown;
export declare const rmdirSync: unknown;
export declare const symlinkSync: unknown;
export declare const truncateSync: unknown;
export declare const unlinkSync: unknown;
export declare const utimesSync: unknown;
export declare const writeFileSync: unknown;
export declare const writeSync: unknown;
export declare const writevSync: unknown;
export declare const statfsSync: typeof nodeFs.statfsSync;
export declare const globSync: unknown;
